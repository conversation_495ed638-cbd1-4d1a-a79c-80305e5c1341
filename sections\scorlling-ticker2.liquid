{% schema %}
{
  "name": "Scrolling Ticker",
  "settings": [
    {
      "type": "text",
      "id": "ticker_text",
      "label": "Ticker Text",
      "default": "✨ New Arrivals ✨ Free Shipping ✨ 30-Day Guarantee"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "speed",
      "min": 5,
      "max": 30,
      "step": 1,
      "unit": "s",
      "label": "Scroll Speed",
      "default": 15
    }
  ],
  "presets": [
    {
      "name": "Scrolling Ticker",
      "category": "Promotional"
    }
  ]
}
{% endschema %}

{% stylesheet %}
  .scrolling-ticker-container {
    width: 100%;
    overflow: hidden;
    background-color: {{ section.settings.background_color }};
    padding: 10px 0;
  }
  
  .scrolling-ticker {
    display: inline-block;
    white-space: nowrap;
    color: {{ section.settings.text_color }};
    animation: scroll {{ section.settings.speed }}s linear infinite;
  }
  
  @keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
  }
{% endstylesheet %}

{% javascript %}
  // You can add custom JS here if needed
{% endjavascript %}

<div class="scrolling-ticker-container">
  <div class="scrolling-ticker">
    {{ section.settings.ticker_text | escape }} &nbsp;&nbsp; {{ section.settings.ticker_text | escape }}
  </div>
</div>