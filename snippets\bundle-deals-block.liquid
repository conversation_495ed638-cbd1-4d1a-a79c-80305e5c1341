{%- comment -%}
  Bundle Deals Block - Collection-based bundle system
  Only shows on products with "bundle" tag
  Hides default buy buttons when active
{%- endcomment -%}

{%- liquid
  assign show_bundle_block = false
  assign bundle_1_enabled = block.settings.bundle_1_enabled
  assign bundle_2_enabled = block.settings.bundle_2_enabled
  assign bundle_3_enabled = block.settings.bundle_3_enabled

  # Check if current product has "bundle" tag
  if product.tags contains 'bundle'
    assign show_bundle_block = true
  endif

  assign enabled_bundles = 0
  if bundle_1_enabled and block.settings.bundle_1_collection != blank
    assign enabled_bundles = enabled_bundles | plus: 1
  endif
  if bundle_2_enabled and block.settings.bundle_2_collection != blank
    assign enabled_bundles = enabled_bundles | plus: 1
  endif
  if bundle_3_enabled and block.settings.bundle_3_collection != blank
    assign enabled_bundles = enabled_bundles | plus: 1
  endif
-%}

{%- if show_bundle_block and enabled_bundles > 0 -%}
  <div class="bundle-deals-block" 
       style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem; --margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
       {{ block.shopify_attributes }}>
    
    {%- if block.settings.bundle_title != blank -%}
      <h3 class="bundle-deals__title">{{ block.settings.bundle_title }}</h3>
    {%- endif -%}
    
    <!-- Bundle Tabs -->
    <div class="bundle-deals__tabs">
      {%- if bundle_1_enabled -%}
        <button class="bundle-deals__tab bundle-deals__tab--active" data-bundle="1">
          {{ block.settings.bundle_1_name }}
        </button>
      {%- endif -%}
      {%- if bundle_2_enabled -%}
        <button class="bundle-deals__tab{% unless bundle_1_enabled %} bundle-deals__tab--active{% endunless %}" data-bundle="2">
          {{ block.settings.bundle_2_name }}
        </button>
      {%- endif -%}
      {%- if bundle_3_enabled -%}
        <button class="bundle-deals__tab{% unless bundle_1_enabled or bundle_2_enabled %} bundle-deals__tab--active{% endunless %}" data-bundle="3">
          {{ block.settings.bundle_3_name }}
        </button>
      {%- endif -%}
    </div>

    <!-- Bundle Content -->
    <div class="bundle-deals__content">
      
      {%- if bundle_1_enabled and block.settings.bundle_1_collection != blank -%}
        <div class="bundle-deals__panel bundle-deals__panel--active" data-bundle-panel="1">
          {% render 'bundle-panel',
             bundle_number: 1,
             bundle_name: block.settings.bundle_1_name,
             bundle_description: block.settings.bundle_1_description,
             bundle_collection: block.settings.bundle_1_collection,
             save_text: block.settings.bundle_1_save_text,
             section_id: section_id,
             block: block
          %}
        </div>
      {%- endif -%}

      {%- if bundle_2_enabled and block.settings.bundle_2_collection != blank -%}
        {%- liquid
          assign bundle_2_is_active = false
          unless bundle_1_enabled and block.settings.bundle_1_collection != blank
            assign bundle_2_is_active = true
          endunless
        -%}
        <div class="bundle-deals__panel{% if bundle_2_is_active %} bundle-deals__panel--active{% endif %}" data-bundle-panel="2">
          {% render 'bundle-panel',
             bundle_number: 2,
             bundle_name: block.settings.bundle_2_name,
             bundle_description: block.settings.bundle_2_description,
             bundle_collection: block.settings.bundle_2_collection,
             save_text: block.settings.bundle_2_save_text,
             section_id: section_id,
             block: block
          %}
        </div>
      {%- endif -%}

      {%- if bundle_3_enabled and block.settings.bundle_3_collection != blank -%}
        {%- liquid
          assign bundle_3_is_active = false
          assign bundle_1_has_collection = false
          assign bundle_2_has_collection = false

          if bundle_1_enabled and block.settings.bundle_1_collection != blank
            assign bundle_1_has_collection = true
          endif

          if bundle_2_enabled and block.settings.bundle_2_collection != blank
            assign bundle_2_has_collection = true
          endif

          unless bundle_1_has_collection or bundle_2_has_collection
            assign bundle_3_is_active = true
          endunless
        -%}
        <div class="bundle-deals__panel{% if bundle_3_is_active %} bundle-deals__panel--active{% endif %}" data-bundle-panel="3">
          {% render 'bundle-panel',
             bundle_number: 3,
             bundle_name: block.settings.bundle_3_name,
             bundle_description: block.settings.bundle_3_description,
             bundle_collection: block.settings.bundle_3_collection,
             save_text: block.settings.bundle_3_save_text,
             section_id: section_id,
             block: block
          %}
        </div>
      {%- endif -%}

    </div>
  </div>

  <!-- Hide default buy buttons when bundle block is active -->
  <style>
    /* Hide default buy buttons when bundle block is present */
    .bundle-deals-block ~ .product-form__buttons,
    .bundle-deals-block ~ [data-type="buy_buttons"],
    .product-form .product-form__buttons:has(~ .bundle-deals-block),
    .product-form__buttons {
      display: none !important;
    }

    .bundle-deals-block {
      margin-top: var(--margin-top);
      margin-bottom: var(--margin-bottom);
      border: 1px solid {{ block.settings.tab_border_color | default: '#e0e0e0' }};
      border-radius: 12px;
      padding: 24px;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      font-family: inherit;
      font-size: inherit;
      line-height: inherit;
    }

    .bundle-deals__title {
      text-align: center;
      margin-bottom: 20px;
      font-size: inherit;
      font-weight: inherit;
      color: {{ block.settings.heading_color | default: '#333333' }};
      font-family: inherit;
    }

    .bundle-deals__tabs {
      display: flex;
      border-bottom: 1px solid {{ block.settings.tab_border_color | default: '#e0e0e0' }};
      margin-bottom: 24px;
    }

    .bundle-deals__tab {
      flex: 1;
      padding: 12px 16px;
      border: none;
      background: none;
      cursor: pointer;
      font-family: inherit;
      font-size: inherit;
      font-weight: inherit;
      color: {{ block.settings.tab_text_color | default: '#666666' }};
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .bundle-deals__tab--active {
      color: {{ block.settings.tab_active_color | default: '#2c5aa0' }};
      border-bottom-color: {{ block.settings.tab_active_color | default: '#2c5aa0' }};
    }

    .bundle-deals__tab:hover {
      color: {{ block.settings.tab_active_color | default: '#2c5aa0' }};
    }

    .bundle-deals__panel {
      display: none;
    }

    .bundle-deals__panel--active {
      display: block;
    }

    @media (max-width: 768px) {
      .bundle-deals-block {
        padding: 16px;
      }

      .bundle-deals__tabs {
        flex-direction: row; /* Keep horizontal on mobile */
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
        margin-bottom: 1.5rem;
      }

      .bundle-deals__tab {
        text-align: center;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #ffffff;
        flex: 1;
        min-width: 0;
        padding: 0.75rem 1rem;
        font-size: 1.4rem;
      }

      .bundle-deals__tab--active {
        background: {{ block.settings.tab_active_color | default: '#2c5aa0' }};
        color: #ffffff;
        border-color: {{ block.settings.tab_active_color | default: '#2c5aa0' }};
      }
    }

    /* Main Badge Styling */
    .bundle-panel__main-badge {
      background: {{ block.settings.main_badge_background | default: '#5cb85c' }} !important;
      color: {{ block.settings.main_badge_text_color | default: '#ffffff' }} !important;
    }
  </style>

  <!-- Load Static Bundle System JavaScript -->
  <script src="{{ 'static-bundle-system.js' | asset_url }}" defer></script>
  <!-- Load Bundle Cart Display Override -->
  <script src="{{ 'bundle-cart-display.js' | asset_url }}" defer></script>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const tabs = document.querySelectorAll('.bundle-deals__tab');
      const panels = document.querySelectorAll('.bundle-deals__panel');

      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          const bundleNumber = this.dataset.bundle;

          // Remove active class from all tabs and panels
          tabs.forEach(t => t.classList.remove('bundle-deals__tab--active'));
          panels.forEach(p => p.classList.remove('bundle-deals__panel--active'));

          // Add active class to clicked tab and corresponding panel
          this.classList.add('bundle-deals__tab--active');
          document.querySelector(`[data-bundle-panel="${bundleNumber}"]`).classList.add('bundle-deals__panel--active');
        });
      });
    });
  </script>

{%- endif -%}
