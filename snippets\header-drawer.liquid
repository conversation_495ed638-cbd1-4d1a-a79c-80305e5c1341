<header-drawer data-breakpoint="{% if section.settings.menu_type_desktop == 'drawer' %}desktop{% else %}tablet{% endif %}" data-desktop-type="{{ section.settings.menu_type_desktop }}" data-drawer="{{ settings.animations_type }}">
  <details id="Details-menu-drawer-container" class="menu-drawer-container">
    <summary class="header__icon header__icon--menu header__icon--summary link focus-inset" aria-label="{{ 'sections.header.menu' | t }}">
      <span>
        {% render 'icon-hamburger' %}
        {% render 'icon-close' %}
      </span>
    </summary>
    <div id="menu-drawer" class="gradient menu-drawer motion-reduce color-{{ section.settings.menu_color_scheme }}" tabindex="-1">
      <div class="menu-drawer__inner-container">
        <div class='menu-drawer__mobile-content menu-drawer__title-and-close-btn'>
          <h3 class='menu-drawer__title'>{{ section.settings.mobile_menu_title }}</h3>
          <button class='menu-drawer__close-btn menu-drawer__close-menu-btn header__icon header__icon--menu header__icon--summary link focus-inset'>
            {% render 'icon-close' %}
          </button>
        </div>
        <div class="menu-drawer__navigation-container">
          <nav class="menu-drawer__navigation">
            <ul class="menu-drawer__menu has-submenu list-menu" role="list">
              {%- for link in section.settings.menu.links -%}
                <li>
                  {%- if link.links != blank -%}
                    <details id="Details-menu-drawer-menu-item-{{ forloop.index }}">
                      <summary class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.child_active %} menu-drawer__menu-item--active{% endif %}">
                        {{ link.title | escape }}
                        {% render 'icon-arrow' %}
                        {% render 'icon-caret' %}
                      </summary>
                      <div id="link-{{ link.handle | escape }}" class="menu-drawer__submenu has-submenu gradient motion-reduce" tabindex="-1">
                        <div class="menu-drawer__inner-submenu">
                          <button class="menu-drawer__close-button link link--text focus-inset" aria-expanded="true">
                            {% render 'icon-arrow' %}
                            {{ link.title | escape }}
                          </button>
                          <ul class="menu-drawer__menu list-menu" role="list" tabindex="-1">
                            {%- for childlink in link.links -%}
                              <li>
                                {%- if childlink.links != blank or section.settings.products_mega_menu_links contains link.title and section.settings.products_mega_menu_display_collection_products -%}
                                  <details id="Details-menu-drawer-submenu-{{ forloop.index }}">
                                    <summary class="menu-drawer__menu-item link link--text list-menu__item focus-inset">
                                      {% if section.settings.products_mega_menu_links contains link.title and childlink.url contains 'collections' and section.settings.products_mega_menu_display_collection_images_on_mobile %}
                                        {% assign link_collection_handle = childlink.url | remove: '/collections/' %}
                                        {% assign link_current_collection = collections[link_collection_handle] %}
                                        {% if link_current_collection.featured_image != blank %}
                                          <div class="header__menu-item__preview-image header__menu-item__preview-image--mobile">
                                            <img
                                              src="{{ link_current_collection.featured_image | image_url: width: 150 }}"
                                              alt=""
                                              width="auto"
                                              height="auto"
                                            >
                                          </div>
                                        {% endif %}
                                      {% endif %}
                                      {{ childlink.title | escape }}
                                      {% render 'icon-arrow' %}
                                      {% render 'icon-caret' %}
                                    </summary>
                                    <div id="childlink-{{ childlink.handle | escape }}" class="menu-drawer__submenu has-submenu gradient motion-reduce">
                                      <button class="menu-drawer__close-button link link--text focus-inset" aria-expanded="true">
                                        {% render 'icon-arrow' %}
                                        {{ childlink.title | escape }}
                                      </button>
                                      {% if section.settings.products_mega_menu_links contains link.title %}
                                        <ul class="products-mega-menu__cards products-mega-menu__cards--mobile">
                                          {% if section.settings.products_mega_menu_display_collection_products %}
                                            {% assign link_collection_handle = childlink.url | remove: '/collections/' %}
                                            {% assign link_current_collection = collections[link_collection_handle] %}
                                            {% for product in link_current_collection.products %}
                                              <li>
                                                {% render 'card-product',
                                                  card_product: product,
                                                  media_aspect_ratio: 'square',
                                                  show_secondary_image: true,
                                                  extend_height: false,
                                                  custom_color_scheme: 'background-1'
                                                %}
                                              </li>
                                            {% endfor %}
                                          {% endif %}
                                          {%- for grandchildlink in childlink.links -%}
                                            <li>
                                              {% if grandchildlink.url contains 'products' %}
                                                {% assign product_handle = grandchildlink.url | remove: '/products/' %}
                                                {% assign current_product = all_products[product_handle] %}
                                                {% render 'card-product',
                                                  card_product: current_product,
                                                  media_aspect_ratio: 'square',
                                                  show_secondary_image: true,
                                                  extend_height: false,
                                                  custom_color_scheme: 'background-1'
                                                %}
                                              {% elsif grandchildlink.url contains 'collections' %}
                                                {% assign collection_handle = grandchildlink.url | remove: '/collections/' %}
                                                {% assign current_collection = collections[collection_handle] %}
                                                {% render 'card-collection',
                                                  card_collection: current_collection,
                                                  media_aspect_ratio: 'square',
                                                  extend_height: false,
                                                  columns: 1,
                                                  custom_color_scheme: 'background-1'
                                                %}
                                              {% endif %}
                                            </li>
                                          {%- endfor -%}
                                        </ul>
                                      {% else %}
                                        <ul class="menu-drawer__menu list-menu" role="list" tabindex="-1">
                                          {%- for grandchildlink in childlink.links -%}
                                            <li>
                                              <a href="{{ grandchildlink.url }}" class="menu-drawer__menu-item link link--text list-menu__item focus-inset{% if grandchildlink.current %} menu-drawer__menu-item--active{% endif %}"{% if grandchildlink.current %} aria-current="page"{% endif %}>
                                                {{ grandchildlink.title | escape }}
                                              </a>
                                            </li>
                                          {%- endfor -%}
                                        </ul>
                                      {% endif %}
                                    </div>
                                  </details>
                                {%- else -%}
                                  <a href="{{ childlink.url }}" class="menu-drawer__menu-item link link--text list-menu__item focus-inset{% if childlink.current %} menu-drawer__menu-item--active{% endif %}"{% if childlink.current %} aria-current="page"{% endif %}>
                                    {{ childlink.title | escape }}
                                  </a>
                                {%- endif -%}
                              </li>
                            {%- endfor -%}
                          </ul>
                        </div>
                      </div>
                    </details>
                  {%- else -%}
                    <a href="{{ link.url }}" class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.current %} menu-drawer__menu-item--active{% endif %}"{% if link.current %} aria-current="page"{% endif %}>
                      {{ link.title | escape }}
                    </a>
                  {%- endif -%}
                </li>
              {%- endfor -%}
            </ul>
          </nav>
<!--                 start secondary nav -->
          {% if section.settings.secondary_menu != blank %}
            <nav class="menu-drawer__navigation menu-drawer__secondary-nav">
              <ul class="menu-drawer__menu has-submenu list-menu" role="list">
                {%- for link in section.settings.secondary_menu.links -%}
                  <li>
                    {%- if link.links != blank -%}
                      <details id="Details-menu-drawer-menu-item-{{ forloop.index }}">
                        <summary class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.child_active %} menu-drawer__menu-item--active{% endif %}">
                          {{ link.title | escape }}
                          {% render 'icon-arrow' %}
                          {% render 'icon-caret' %}
                        </summary>
                        <div id="link-{{ link.handle | escape }}" class="menu-drawer__submenu has-submenu gradient motion-reduce" tabindex="-1">
                          <div class="menu-drawer__inner-submenu">
                            <button class="menu-drawer__close-button link link--text focus-inset" aria-expanded="true">
                              {% render 'icon-arrow' %}
                              {{ link.title | escape }}
                            </button>
                            <ul class="menu-drawer__menu list-menu" role="list" tabindex="-1">
                              {%- for childlink in link.links -%}
                                <li>
                                  {%- if childlink.links == blank -%}
                                    <a href="{{ childlink.url }}" class="menu-drawer__menu-item link link--text list-menu__item focus-inset{% if childlink.current %} menu-drawer__menu-item--active{% endif %}"{% if childlink.current %} aria-current="page"{% endif %}>
                                      {{ childlink.title | escape }}
                                    </a>
                                  {%- else -%}
                                    <details id="Details-menu-drawer-submenu-{{ forloop.index }}">
                                      <summary class="menu-drawer__menu-item link link--text list-menu__item focus-inset">
                                        {{ childlink.title | escape }}
                                        {% render 'icon-arrow' %}
                                        {% render 'icon-caret' %}
                                      </summary>
                                      <div id="childlink-{{ childlink.handle | escape }}" class="menu-drawer__submenu has-submenu gradient motion-reduce">
                                        <button class="menu-drawer__close-button link link--text focus-inset" aria-expanded="true">
                                          {% render 'icon-arrow' %}
                                          {{ childlink.title | escape }}
                                        </button>
                                        <ul class="menu-drawer__menu list-menu" role="list" tabindex="-1">
                                          {%- for grandchildlink in childlink.links -%}
                                            <li>
                                              <a href="{{ grandchildlink.url }}" class="menu-drawer__menu-item link link--text list-menu__item focus-inset{% if grandchildlink.current %} menu-drawer__menu-item--active{% endif %}"{% if grandchildlink.current %} aria-current="page"{% endif %}>
                                                {{ grandchildlink.title | escape }}
                                              </a>
                                            </li>
                                          {%- endfor -%}
                                        </ul>
                                      </div>
                                    </details>
                                  {%- endif -%}
                                </li>
                              {%- endfor -%}
                            </ul>
                          </div>
                        </div>
                      </details>
                    {%- else -%}
                      <a href="{{ link.url }}" class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.current %} menu-drawer__menu-item--active{% endif %}"{% if link.current %} aria-current="page"{% endif %}>
                        {{ link.title | escape }}
                      </a>
                    {%- endif -%}
                  </li>
                {%- endfor -%}
              </ul>
            </nav>
          {% endif %}
<!--                 end secondary nav -->
          <div class="menu-drawer__utility-links">
            {%- if shop.customer_accounts_enabled -%}
              <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="menu-drawer__account link focus-inset h5">
                {% render 'icon-account' %}
                {%- liquid
                  if customer
                    echo 'customer.account_fallback' | t
                  else
                    echo 'customer.log_in' | t
                  endif
                -%}
              </a>
            {%- endif -%}
            <ul class="list list-social list-unstyled" role="list">
              {%- if settings.social_twitter_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_twitter_link }}" class="list-social__link link">
                    {%- render 'icon-twitter' -%}
                    <span class="visually-hidden">{{ 'general.social.links.twitter' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_facebook_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_facebook_link }}" class="list-social__link link">
                    {%- render 'icon-facebook' -%}
                    <span class="visually-hidden">{{ 'general.social.links.facebook' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_pinterest_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_pinterest_link }}" class="list-social__link link">
                    {%- render 'icon-pinterest' -%}
                    <span class="visually-hidden">{{ 'general.social.links.pinterest' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_instagram_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_instagram_link }}" class="list-social__link link">
                    {%- render 'icon-instagram' -%}
                    <span class="visually-hidden">{{ 'general.social.links.instagram' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_tiktok_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_tiktok_link }}" class="list-social__link link">
                    {%- render 'icon-tiktok' -%}
                    <span class="visually-hidden">{{ 'general.social.links.tiktok' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_tumblr_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_tumblr_link }}" class="list-social__link link">
                    {%- render 'icon-tumblr' -%}
                    <span class="visually-hidden">{{ 'general.social.links.tumblr' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_snapchat_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_snapchat_link }}" class="list-social__link link">
                    {%- render 'icon-snapchat' -%}
                    <span class="visually-hidden">{{ 'general.social.links.snapchat' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_youtube_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_youtube_link }}" class="list-social__link link">
                    {%- render 'icon-youtube' -%}
                    <span class="visually-hidden">{{ 'general.social.links.youtube' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_vimeo_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_vimeo_link }}" class="list-social__link link">
                    {%- render 'icon-vimeo' -%}
                    <span class="visually-hidden">{{ 'general.social.links.vimeo' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
            </ul>
          </div>
        </div>
      </div>
    </div>
  </details>
</header-drawer>