{%- style -%}
  @media screen and (max-width: 749px) {
    .collection-hero--with-image .collection-hero__inner {
      padding-bottom: calc({{ settings.media_shadow_vertical_offset | at_least: 0 }}px + 2rem);
    }
  }
{%- endstyle -%}

<div class="collection-hero{% if section.settings.show_collection_image and collection.image %} collection-hero--with-image{% endif %} color-{{ section.settings.color_scheme }} gradient">
  <div class="collection-hero__inner page-width">
    <div class="collection-hero__text-wrapper">
      <h1 class="collection-hero__title">
        <span class="visually-hidden">{{ 'sections.collection_template.title' | t }}: </span>
        {{- collection.title | escape -}}
      </h1>

      {%- if section.settings.show_collection_description -%}
        <div class="collection-hero__description rte center">{{ collection.description }}</div>
      {%- endif -%}
    </div>

    {%- if section.settings.show_collection_image and collection.image -%}
      <div class="collection-hero__image-container media gradient">
        <img
          srcset="
            {%- if collection.image.width >= 165 -%}{{ collection.image | image_url: width: 165 }} 165w,{%- endif -%}
            {%- if collection.image.width >= 360 -%}{{ collection.image | image_url: width: 360 }} 360w,{%- endif -%}
            {%- if collection.image.width >= 535 -%}{{ collection.image | image_url: width: 535 }} 535w,{%- endif -%}
            {%- if collection.image.width >= 750 -%}{{ collection.image | image_url: width: 750 }} 750w,{%- endif -%}
            {%- if collection.image.width >= 1070 -%}{{ collection.image | image_url: width: 1070 }} 1070w,{%- endif -%}
            {%- if collection.image.width >= 1500 -%}{{ collection.image | image_url: width: 1500 }} 1500w,{%- endif -%}
            {{ collection.image | image_url }} {{ collection.image.width }}w
          "
          src="{{ collection.image | image_url: width: 750 }}"
          sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc(50vw - 130px), calc(50vw - 55px)"
          alt=""
          width="{{ collection.image.width }}"
          height="{{ collection.image.height }}"
        >
      </div>
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-collection-banner.name",
  "class": "section",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.main-collection-banner.settings.paragraph.content"
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "default": true,
      "label": "t:sections.main-collection-banner.settings.show_collection_description.label"
    },
    {
      "type": "checkbox",
      "id": "show_collection_image",
      "default": false,
      "label": "t:sections.main-collection-banner.settings.show_collection_image.label",
      "info": "t:sections.main-collection-banner.settings.show_collection_image.info"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    }
  ]
}
{% endschema %}
