/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "general": {
    "password_page": {
      "login_form_heading": "Parolayı kullanarak mağazaya girin:",
      "login_password_button": "Parola kullanarak gir",
      "login_form_password_label": "Parola",
      "login_form_password_placeholder": "Parolan<PERSON>z",
      "login_form_error": "Yanlış parola!",
      "login_form_submit": "Gir",
      "admin_link_html": "<PERSON><PERSON><PERSON><PERSON> sahi<PERSON> misiniz? <a href=\"/admin\" class=\"link underlined-link\">B<PERSON>dan oturum açın</a>",
      "powered_by_shopify_html": "Bu mağaza {{ shopify }} tarafından desteklenir"
    },
    "social": {
      "alt_text": {
        "share_on_facebook": "Facebook'ta paylaş",
        "share_on_twitter": "Twitter'da tweet'le",
        "share_on_pinterest": "Pinterest'te pin ekle"
      },
      "links": {
        "twitter": "Twitter",
        "facebook": "Facebook",
        "pinterest": "Pinterest",
        "instagram": "Instagram",
        "tumblr": "Tumblr",
        "snapchat": "Snapchat",
        "youtube": "YouTube",
        "vimeo": "Vimeo",
        "tiktok": "TikTok"
      }
    },
    "continue_shopping": "Alışverişe devam et",
    "pagination": {
      "label": "Sayfalara ayırma",
      "page": "Sayfa {{ number }}",
      "next": "Sonraki sayfa",
      "previous": "Önceki sayfa"
    },
    "search": {
      "search": "Ara",
      "reset": "Arama terimini temizle"
    },
    "cart": {
      "view": "Sepeti göster ({{ count }})",
      "item_added": "Ürün sepetinize eklendi",
      "view_empty_cart": "Sepetimi görüntüle"
    },
    "share": {
      "copy_to_clipboard": "Bağlantıyı kopyala",
      "share_url": "Bağlantı",
      "success_message": "Bağlantı panoya kopyalandı",
      "close": "Paylaşımı kapat"
    },
    "slider": {
      "of": "/",
      "next_slide": "Sağa kaydır",
      "previous_slide": "Sola kaydır",
      "name": "Kaydırıcı"
    }
  },
  "newsletter": {
    "label": "E-posta",
    "success": "Abone olduğunuz için teşekkür ederiz",
    "button_label": "Abone ol"
  },
  "accessibility": {
    "skip_to_text": "İçeriğe atla",
    "close": "Kapat",
    "unit_price_separator": "/",
    "vendor": "Satıcı:",
    "error": "Hata",
    "refresh_page": "Bir seçim yapmanız sayfanın tamamının yenilenmesine neden olur.",
    "link_messages": {
      "new_window": "Yeni bir pencerede açılır.",
      "external": "Harici web sitesini açar."
    },
    "loading": "Yükleniyor...",
    "skip_to_product_info": "Ürün bilgisine atla",
    "total_reviews": "toplam değerlendirme",
    "star_reviews_info": "{{ rating_value }}/{{ rating_max }} yıldız",
    "collapsible_content_title": "Daraltılabilir içerik",
    "complementary_products": "Tamamlayıcı ürünler"
  },
  "blogs": {
    "article": {
      "blog": "Blog",
      "read_more_title": "Devamını okuyun: {{ title }}",
      "comments": {
        "one": "{{ count }} yorum",
        "other": "{{ count }} yorum"
      },
      "moderated": "Yorumların yayınlanabilmesi için onaylanması gerektiğini lütfen unutmayın.",
      "comment_form_title": "Yorum yapın",
      "name": "Ad",
      "email": "E-posta",
      "message": "Yorum",
      "post": "Yorumu paylaş",
      "back_to_blog": "Bloga dön",
      "share": "Bu makaleyi paylaş",
      "success": "Yorumunuz başarıyla paylaşıldı! Teşekkür ederiz.",
      "success_moderated": "Yorumunuz başarıyla paylaşıldı. Blogumuz denetlendiğinden yorumunuzu kısa bir süre sonra yayınlayacağız."
    }
  },
  "onboarding": {
    "product_title": "Örnek ürün başlığı",
    "collection_title": "Koleksiyonunuzun adı"
  },
  "products": {
    "product": {
      "add_to_cart": "Sepete ekle",
      "description": "Açıklama",
      "on_sale": "İndirim",
      "product_variants": "Ürün varyasyonları",
      "quantity": {
        "label": "Adet",
        "input_label": "{{ product }} için adet",
        "increase": "{{ product }} için adedi artırın",
        "decrease": "{{ product }} için adedi azaltın",
        "minimum_of": "Minimum: {{ quantity }}",
        "maximum_of": "Maksimum: {{ quantity }}",
        "multiples_of": "Artış değeri: {{ quantity }}",
        "in_cart_html": "Sepette: <span class=\"quantity-cart\">{{ quantity }}</span>"
      },
      "price": {
        "from_price_html": "Başlangıç fiyatı: {{ price }}",
        "regular_price": "Normal fiyat",
        "sale_price": "İndirimli fiyat",
        "unit_price": "Birim fiyat"
      },
      "share": "Bu ürünü paylaş",
      "sold_out": "Tükendi",
      "unavailable": "Kullanım dışı",
      "vendor": "Satıcı",
      "video_exit_message": "{{ title }} aynı pencerede tam ekran video açar.",
      "xr_button": "Kendi alanınızda görüntüleyin",
      "xr_button_label": "Alanınızda görüntüleyin; ürün, artırılmış gerçeklik penceresinde yüklenir",
      "pickup_availability": {
        "view_store_info": "Mağaza bilgilerini görüntüleyin",
        "check_other_stores": "Diğer mağazalardaki stok durumunu kontrol edin",
        "pick_up_available": "Teslim alım kullanılabilir",
        "pick_up_available_at_html": "Teslim alım <span class=\"color-foreground\">{{ location_name }}</span> konumunda kullanılabilir",
        "pick_up_unavailable_at_html": "Teslim alım <span class=\"color-foreground\">{{ location_name }}</span> konumunda şu anda kullanılamıyor",
        "unavailable": "Teslim alım stok durumu yüklenemedi",
        "refresh": "Yenile"
      },
      "media": {
        "open_media": "Medya {{ index }} modda oynatın",
        "play_model": "3B Görüntüleyici'yi Oynat",
        "play_video": "Videoyu oynat",
        "gallery_viewer": "Galeri Görüntüleyici",
        "load_image": "Görsel {{ index }} galeri görüntüleyicide yükleyin",
        "load_model": "3B Model {{ index }} galeri görüntüleyicide yükleyin",
        "load_video": "Video {{ index }} galeri görüntüleyicide oynatın",
        "image_available": "Görsel {{ index }} artık galeri görüntüleyicide kullanılabilir"
      },
      "view_full_details": "Tüm ayrıntıları görüntüle",
      "include_taxes": "Vergi dahildir.",
      "shipping_policy_html": "<a href=\"{{ link }}\">Kargo</a>, ödeme sayfasında hesaplanır.",
      "choose_options": "Seçenekleri belirle",
      "choose_product_options": "{{ product_name }} için seçenekleri belirle",
      "value_unavailable": "{{ option_value }} - Kullanılamıyor",
      "variant_sold_out_or_unavailable": "Varyasyon tükendi veya kullanılamıyor",
      "inventory_in_stock": "Stokta",
      "inventory_in_stock_show_count": "Stokta {{ quantity }} adet mevcut",
      "inventory_low_stock": "Stok düzeyi düşük",
      "inventory_low_stock_show_count": "Stok düzeyi düşük: {{ quantity }} adet kaldı",
      "inventory_out_of_stock": "Stokta yok",
      "inventory_out_of_stock_continue_selling": "Stokta",
      "sku": "SKU"
    },
    "modal": {
      "label": "Medya galerisi"
    },
    "facets": {
      "apply": "Uygula",
      "clear": "Temizle",
      "clear_all": "Tümünü kaldır",
      "from": "En düşük",
      "filter_and_sort": "Filtrele ve sırala",
      "filter_by_label": "Filtre:",
      "filter_button": "Filtrele",
      "filters_selected": {
        "one": "{{ count }} seçildi",
        "other": "{{ count }} seçildi"
      },
      "max_price": "En yüksek fiyat: {{ price }}",
      "product_count": {
        "one": "{{ count }}/{{ product_count }} ürün",
        "other": "{{ count }}/{{ product_count }} ürün"
      },
      "product_count_simple": {
        "one": "{{ count }} ürün",
        "other": "{{ count }} ürün"
      },
      "reset": "Sıfırla",
      "sort_button": "Sırala",
      "sort_by_label": "Sıralama ölçütü:",
      "to": "En yüksek",
      "clear_filter": "Filtreyi kaldır",
      "filter_selected_accessibility": "{{ type }} ({{ count }} filtre seçildi)",
      "show_more": "Daha fazla göster",
      "show_less": "Daha az göster"
    }
  },
  "templates": {
    "search": {
      "no_results": "\"{{ terms }}\" için sonuç bulunamadı. Yazım hatası olmadığını doğrulayın veya farklı bir kelime ya da ifade kullanın.",
      "results_with_count": {
        "one": "{{ count }} sonuç",
        "other": "{{ count }} sonuç"
      },
      "title": "Arama sonuçları",
      "page": "Sayfa",
      "products": "Ürünler",
      "search_for": "\"{{ terms }}\" için arama yap",
      "results_with_count_and_term": {
        "one": "\"{{ terms }}\" için {{ count }} sonuç bulundu",
        "other": "\"{{ terms }}\" için {{ count }} sonuç bulundu"
      },
      "results_pages_with_count": {
        "one": "{{ count }} sayfa",
        "other": "{{ count }} sayfa"
      },
      "results_products_with_count": {
        "one": "{{ count }} ürün",
        "other": "{{ count }} ürün"
      },
      "suggestions": "Öneriler",
      "pages": "Sayfalar",
      "results_suggestions_with_count": {
        "one": "{{ count }} öneri",
        "other": "{{ count }} öneri"
      }
    },
    "cart": {
      "cart": "Sepet"
    },
    "contact": {
      "form": {
        "name": "Ad",
        "email": "E-posta",
        "phone": "Telefon numarası",
        "comment": "Yorum",
        "send": "Gönder",
        "post_success": "Bizimle iletişime geçtiğiniz için teşekkür ederiz. Mümkün olan en kısa sürede size dönüş yapacağız.",
        "error_heading": "Lütfen aşağıdakileri düzenleyin:",
        "title": "İletişim formu"
      }
    },
    "404": {
      "title": "Sayfa bulunamadı",
      "subtext": "404"
    }
  },
  "sections": {
    "header": {
      "announcement": "Duyuru",
      "menu": "Menü",
      "cart_count": {
        "one": "{{ count }} ürün",
        "other": "{{ count }} ürün"
      }
    },
    "cart": {
      "title": "Sepetiniz",
      "caption": "Sepet ürünleri",
      "remove_title": "{{ title }} kanalını kaldır",
      "subtotal": "Alt toplam",
      "new_subtotal": "Yeni alt toplam",
      "note": "Siparişe özel talimatlar",
      "checkout": "Ödeme",
      "empty": "Sepetiniz boş",
      "cart_error": "Sepetiniz güncellenirken bir hata oluştu. Lütfen tekrar deneyin.",
      "cart_quantity_error_html": "Sepetinize bu üründen yalnızca {{ quantity }} adet ekleyebilirsiniz.",
      "taxes_and_shipping_policy_at_checkout_html": "Vergiler ve <a href=\"{{ link }}\">kargo</a>, ödeme sayfasında hesaplanır",
      "taxes_included_but_shipping_at_checkout": "Vergi dahildir ve kargo, ödeme sayfasında hesaplanır",
      "taxes_included_and_shipping_policy_html": "Vergi dahildir. <a href=\"{{ link }}\">Kargo</a>, ödeme sayfasında hesaplanır.",
      "taxes_and_shipping_at_checkout": "Vergiler ve kargo, ödeme sayfasında hesaplanır",
      "headings": {
        "product": "Ürün",
        "price": "Fiyat",
        "total": "Toplam",
        "quantity": "Adet",
        "image": "Ürün görseli"
      },
      "update": "Güncelle",
      "login": {
        "title": "Hesabınız var mı?",
        "paragraph_html": "Daha hızlı ödeme yapmak için <a href=\"{{ link }}\" class=\"link underlined-link\">oturum açın</a>."
      }
    },
    "footer": {
      "payment": "Ödeme yöntemleri"
    },
    "featured_blog": {
      "view_all": "Tümünü görüntüle",
      "onboarding_title": "Blog gönderisi",
      "onboarding_content": "Müşterilerinize blog gönderinizin özetini gösterin"
    },
    "featured_collection": {
      "view_all": "Tümünü görüntüle",
      "view_all_label": "{{ collection_name }} koleksiyonundaki tüm ürünleri görüntüle"
    },
    "collection_list": {
      "view_all": "Tümünü görüntüle"
    },
    "collection_template": {
      "title": "Koleksiyon",
      "empty": "Ürün bulunamadı",
      "use_fewer_filters_html": "Daha az filtre kullan veya <a class=\"{{ class }}\" href=\"{{ link }}\">tümünü kaldır</a>"
    },
    "video": {
      "load_video": "Videoyu yükle: {{ description }}"
    },
    "slideshow": {
      "load_slide": "Slaytı yükle",
      "previous_slideshow": "Önceki slayt",
      "next_slideshow": "Sonraki slayt",
      "pause_slideshow": "Slayt gösterisini duraklat",
      "play_slideshow": "Slayt gösterisini oynat",
      "carousel": "Döngü",
      "slide": "Slayt"
    },
    "page": {
      "title": "Sayfa başlığı"
    }
  },
  "localization": {
    "country_label": "Ülke/bölge",
    "language_label": "Dil",
    "update_language": "Dili güncelle",
    "update_country": "Ülke/bölge bilgisini güncelle"
  },
  "customer": {
    "account": {
      "title": "Hesap",
      "details": "Hesap bilgileri",
      "view_addresses": "Adresi görüntüle",
      "return": "Hesap bilgilerine geri dön"
    },
    "account_fallback": "Hesap",
    "activate_account": {
      "title": "Hesabı etkinleştirin",
      "subtext": "Hesabınızı etkinleştirmek için parolanızı oluşturun.",
      "password": "Parola",
      "password_confirm": "Parolayı doğrula",
      "submit": "Hesabı etkinleştir",
      "cancel": "Daveti reddet"
    },
    "addresses": {
      "title": "Adresler",
      "default": "Varsayılan",
      "add_new": "Yeni adres ekle",
      "edit_address": "Adresi düzenle",
      "first_name": "Ad",
      "last_name": "Soyadı",
      "company": "Şirket",
      "address1": "Adres 1",
      "address2": "Adres 2",
      "city": "Şehir",
      "country": "Ülke/bölge",
      "province": "İl",
      "zip": "Posta kodu",
      "phone": "Telefon",
      "set_default": "Varsayılan adres olarak ayarla",
      "add": "Adres ekle",
      "update": "Adresi güncelle",
      "cancel": "İptal Et",
      "edit": "Düzenleyin",
      "delete": "Sil",
      "delete_confirm": "Bu adresi silmek istediğinizden emin misiniz?"
    },
    "log_in": "Oturum aç",
    "log_out": "Oturumu kapat",
    "login_page": {
      "cancel": "İptal Et",
      "create_account": "Hesap oluştur",
      "email": "E-posta",
      "forgot_password": "Parolanızı mı unuttunuz?",
      "guest_continue": "Devam",
      "guest_title": "Misafir olarak devam edin",
      "password": "Parola",
      "title": "Oturum aç",
      "sign_in": "Giriş yapın",
      "submit": "Gönder"
    },
    "orders": {
      "title": "Sipariş geçmişi",
      "order_number": "Sipariş",
      "order_number_link": "Sipariş numarası {{ number }}",
      "date": "Tarih",
      "payment_status": "Ödeme durumu",
      "fulfillment_status": "Gönderim durumu",
      "total": "Toplam",
      "none": "Henüz sipariş vermediniz."
    },
    "recover_password": {
      "title": "Parolanızı sıfırlayın",
      "subtext": "Parolanızı sıfırlamanız için size bir e-posta göndereceğiz",
      "success": "Size parolanızı güncelleme bağlantısının bulunduğu bir e-posta gönderdik."
    },
    "register": {
      "title": "Hesap oluşturun",
      "first_name": "Ad",
      "last_name": "Soyadı",
      "email": "E-posta",
      "password": "Parola",
      "submit": "Oluştur"
    },
    "reset_password": {
      "title": "Hesap parolasını sıfırlayın",
      "subtext": "Yeni bir parola girin",
      "password": "Parola",
      "password_confirm": "Parolayı doğrula",
      "submit": "Parolayı sıfırla"
    },
    "order": {
      "title": "{{ name }} siparişi",
      "date_html": "Verilme Tarihi: {{ date }}",
      "cancelled_html": "Siparişin İptal Edilme Tarihi: {{ date }}",
      "cancelled_reason": "Neden: {{ reason }}",
      "billing_address": "Fatura Adresi",
      "payment_status": "Ödeme Durumu",
      "shipping_address": "Kargo Adresi",
      "fulfillment_status": "Gönderim Durumu",
      "discount": "İndirim",
      "shipping": "Kargo",
      "tax": "Vergi",
      "product": "Ürün",
      "sku": "SKU",
      "price": "Fiyat",
      "quantity": "Adet",
      "total": "Toplam",
      "fulfilled_at_html": "Gönderildi: {{ date }}",
      "track_shipment": "Kargoyu takip et",
      "tracking_url": "Takip bağlantısı",
      "tracking_company": "Kargo Şirketi",
      "tracking_number": "Takip numarası",
      "subtotal": "Alt toplam",
      "total_duties": "Gümrük vergileri",
      "total_refunded": "Para iadesi yapıldı"
    }
  },
  "gift_cards": {
    "issued": {
      "title": "İşte {{ shop }} için {{ value }} hediye kartınız!",
      "subtext": "Hediye kartınız",
      "gift_card_code": "Hediye kartı kodu",
      "shop_link": "Alışverişe devam et",
      "remaining_html": "{{ balance }} kaldı",
      "add_to_apple_wallet": "Apple Wallet'a ekle",
      "qr_image_alt": "QR kodu: Hediye kartını kullanmak için tarayın",
      "copy_code": "Kodu kopyala",
      "expired": "Süresi sona erdi",
      "copy_code_success": "Kod başarıyla kopyalandı",
      "print_gift_card": "Yazdır"
    }
  }
}
