/* Hidden Variant Bundles Section Styles */

.hidden-variant-bundles {
  max-width: 1200px;
  margin: 0 auto;
}

/* Bundle Tabs Navigation */
.bundle-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
}

.bundle-tab {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.6rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
  position: relative;
}

.bundle-tab:hover {
  color: rgb(var(--color-foreground));
}

.bundle-tab--active {
  color: rgb(var(--accent-color));
  border-bottom-color: rgb(var(--accent-color));
}

/* Bundle Content */
.bundle-content {
  position: relative;
}

.bundle-tab-content {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bundle-tab-content--active {
  display: block;
  opacity: 1;
}

/* Bundle Products Grid */
.bundle-products {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.bundle-product {
  border: 1px solid rgba(var(--color-foreground), 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  background: rgb(var(--color-background));
}

.bundle-product:hover {
  border-color: rgba(var(--accent-color), 0.3);
  box-shadow: 0 4px 12px rgba(var(--color-foreground), 0.1);
}

.bundle-product__image {
  margin-bottom: 1rem;
}

.bundle-product__image img {
  width: 100%;
  max-width: 150px;
  height: auto;
  border-radius: 4px;
}

.bundle-product__title {
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: rgb(var(--color-foreground));
  line-height: 1.3;
}

.bundle-product__price {
  font-size: 1.6rem;
  font-weight: 700;
}

.bundle-product__price-regular {
  color: rgba(var(--color-foreground), 0.6);
  text-decoration: line-through;
}

.bundle-product__price-bundle {
  color: rgb(var(--accent-color));
  margin-left: 0.5rem;
}

/* Bundle Summary */
.bundle-summary {
  background: rgba(var(--color-foreground), 0.02);
  border: 1px solid rgba(var(--color-foreground), 0.1);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}

.bundle-summary__total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.bundle-summary__label {
  color: rgb(var(--color-foreground));
}

.bundle-summary__price {
  color: rgb(var(--accent-color));
}

.bundle-add-to-cart {
  position: relative;
  min-height: 4.8rem;
}

.bundle-add-to-cart:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.bundle-add-to-cart__text {
  transition: opacity 0.3s ease;
}

.bundle-add-to-cart.loading .bundle-add-to-cart__text {
  opacity: 0;
}

.bundle-add-to-cart.loading .loading-overlay__spinner {
  display: block !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Empty State */
.bundle-empty {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(var(--color-foreground), 0.6);
  font-size: 1.6rem;
}

/* Mobile Responsive */
@media screen and (max-width: 749px) {
  .bundle-tabs {
    flex-direction: row; /* Keep horizontal on mobile */
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .bundle-tab {
    padding: 0.75rem 1rem;
    font-size: 1.4rem;
    border: 1px solid rgba(var(--color-foreground), 0.2);
    border-radius: 6px;
    background: rgb(var(--color-background));
    flex: 1;
    min-width: 0;
    text-align: center;
  }

  .bundle-tab--active {
    background: rgb(var(--accent-color));
    color: rgb(var(--color-background));
    border-color: rgb(var(--accent-color));
  }
  
  .bundle-products {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .bundle-product {
    padding: 1rem;
  }
  
  .bundle-summary {
    padding: 1.5rem;
  }
  
  .bundle-summary__total {
    font-size: 1.6rem;
  }
}

@media screen and (max-width: 480px) {
  .bundle-tab {
    font-size: 1.4rem;
    padding: 0.8rem;
  }
  
  .bundle-product__title {
    font-size: 1.3rem;
  }
  
  .bundle-product__price {
    font-size: 1.4rem;
  }
}

/* Loading States */
.bundle-product.loading {
  opacity: 0.6;
  pointer-events: none;
}

.bundle-product.error {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.bundle-product.error .bundle-product__title {
  color: #dc3545;
}

/* Animation Classes */
.animate-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.animate-item.animate--visible {
  opacity: 1;
  transform: translateY(0);
}

.animate-item--child {
  transition-delay: calc(var(--index, 0) * 0.1s);
}

/* Focus States */
.bundle-tab:focus-visible {
  outline: 2px solid rgb(var(--accent-color));
  outline-offset: 2px;
}

.bundle-add-to-cart:focus-visible {
  outline: 2px solid rgb(var(--accent-color));
  outline-offset: 2px;
}
