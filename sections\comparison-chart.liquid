<div class="bhigh-comparison-section">
  <div class="bigh-global-container">
    <div class="bhigh-comparison-header">
      <h2 class="bigh-global-40font">{{ section.settings.section_title}}</h2>
      <p class="bhigh-comparison-subtitle">{{ section.settings.section_subtitle}}</p>
    </div>
    <div class="bhigh-comparison-table">
      <div class="vscomparison-header">
        <div class="vscomparison-column"></div>
        <div class="vscomparison-column">
          <img src="{{ section.settings.table_heading_hero_logo | img_url: '200x200' }}" alt="">
        </div>
        <div class="vscomparison-column">{{ section.settings.table_heading_comp_name }}</div>
      </div>
      <div class="vscomparison-body">
        {% for block in section.blocks %}
          <div class="vscomparison-row">
            <div class="vscomparison-column">
              {{ block.settings.column_1 }}
            </div>
            <div class="vscomparison-column">
              {% if block.settings.column_2 == 'check' %}
                <!-- Check SVG -->
                <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12.3281 17L15.3281 20L20.3281 13M28.3281 16C28.3281 17.5759 28.0177 19.1363 27.4147 20.5922C26.8116 22.0481 25.9277 23.371 24.8134 24.4853C23.6991 25.5996 22.3762 26.4835 20.9203 27.0866C19.4644 27.6896 17.904 28 16.3281 28C14.7523 28 13.1918 27.6896 11.7359 27.0866C10.28 26.4835 8.95715 25.5996 7.84284 24.4853C6.72854 23.371 5.84463 22.0481 5.24157 20.5922C4.63851 19.1363 4.32812 17.5759 4.32813 16C4.32813 12.8174 5.59241 9.76516 7.84284 7.51472C10.0933 5.26428 13.1455 4 16.3281 4C19.5107 4 22.563 5.26428 24.8134 7.51472C27.0638 9.76516 28.3281 12.8174 28.3281 16Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>

              {% elsif block.settings.column_2 == 'cross' %}
                <!-- Cross SVG -->
                <svg width="26" height="27" viewBox="0 0 26 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 10.6958L16 16.6958M16 10.6958L10 16.6958M25 13.6958C25 15.2717 24.6896 16.8321 24.0866 18.288C23.4835 19.7439 22.5996 21.0668 21.4853 22.1811C20.371 23.2954 19.0481 24.1793 17.5922 24.7824C16.1363 25.3854 14.5759 25.6958 13 25.6958C11.4241 25.6958 9.86371 25.3854 8.4078 24.7824C6.95189 24.1793 5.62902 23.2954 4.51472 22.1811C3.40042 21.0668 2.5165 19.7439 1.91345 18.288C1.31039 16.8321 1 15.2717 1 13.6958C1 10.5132 2.26428 7.46096 4.51472 5.21052C6.76516 2.96008 9.8174 1.6958 13 1.6958C16.1826 1.6958 19.2348 2.96008 21.4853 5.21052C23.7357 7.46096 25 10.5132 25 13.6958Z" stroke="#DFDFDF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              {% elsif block.settings.column_2 == 'none' %}
                {{ block.settings.column_2_none_text }}
              {% endif %}
            </div>
            <div class="vscomparison-column">
              {% if block.settings.column_3 == 'check' %}
                <!-- Check SVG -->
                <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12.3281 17L15.3281 20L20.3281 13M28.3281 16C28.3281 17.5759 28.0177 19.1363 27.4147 20.5922C26.8116 22.0481 25.9277 23.371 24.8134 24.4853C23.6991 25.5996 22.3762 26.4835 20.9203 27.0866C19.4644 27.6896 17.904 28 16.3281 28C14.7523 28 13.1918 27.6896 11.7359 27.0866C10.28 26.4835 8.95715 25.5996 7.84284 24.4853C6.72854 23.371 5.84463 22.0481 5.24157 20.5922C4.63851 19.1363 4.32812 17.5759 4.32813 16C4.32813 12.8174 5.59241 9.76516 7.84284 7.51472C10.0933 5.26428 13.1455 4 16.3281 4C19.5107 4 22.563 5.26428 24.8134 7.51472C27.0638 9.76516 28.3281 12.8174 28.3281 16Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>


              {% elsif block.settings.column_3 == 'cross' %}
                <!-- Cross SVG -->
                <svg width="26" height="27" viewBox="0 0 26 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 10.6958L16 16.6958M16 10.6958L10 16.6958M25 13.6958C25 15.2717 24.6896 16.8321 24.0866 18.288C23.4835 19.7439 22.5996 21.0668 21.4853 22.1811C20.371 23.2954 19.0481 24.1793 17.5922 24.7824C16.1363 25.3854 14.5759 25.6958 13 25.6958C11.4241 25.6958 9.86371 25.3854 8.4078 24.7824C6.95189 24.1793 5.62902 23.2954 4.51472 22.1811C3.40042 21.0668 2.5165 19.7439 1.91345 18.288C1.31039 16.8321 1 15.2717 1 13.6958C1 10.5132 2.26428 7.46096 4.51472 5.21052C6.76516 2.96008 9.8174 1.6958 13 1.6958C16.1826 1.6958 19.2348 2.96008 21.4853 5.21052C23.7357 7.46096 25 10.5132 25 13.6958Z" stroke="#DFDFDF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              {% elsif block.settings.column_3 == 'none' %}
                {{ block.settings.column_3_none_text }}
              {% endif %}
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
</div>

{% schema %}
  {
    "name": "Pdp Comparison Section",
    "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Section Title"
    },
    {
      "type": "text",
      "id": "section_subtitle",
      "label": "Section Subtitle"
    },
    {
      "type": "image_picker",
      "label": "Table header Hero logo",
      "id": "table_heading_hero_logo",
    },
    {
      "type": "text",
      "label": "Table header competetion name",
      "id": "table_heading_comp_name",
    }
    ],
    "blocks": [

    {
      "type": "row",
      "name": "Comparison Row",
      "settings": [
        {
          "type": "text",
          "label": "Column 1 Text",
          "id": "column_1",
          "default": "Your Text Here"
        },
        {
          "type": "radio",
          "label": "Column 2 Option",
          "id": "column_2",
          "default": "none",
          "options": [
            { "value": "check", "label": "Check" },
            { "value": "cross", "label": "Cross" },
            { "value": "none", "label": "None" }
          ]
        },
        {
          "type": "text",
          "label": "Custom Text for Column 2 (if 'None' is selected)",
          "id": "column_2_none_text",
        },
        {
          "type": "radio",
          "label": "Column 3 Option",
          "id": "column_3",
          "default": "none",
          "options": [
            { "value": "check", "label": "Check" },
            { "value": "cross", "label": "Cross" },
            { "value": "none", "label": "None" }
          ]
        },
        {
          "type": "text",
          "label": "Custom Text for Column 3 (if 'None' is selected)",
          "id": "column_3_none_text",
        }
      ]
    }

    ],
    "presets": [
    {
      "name": "Pdp Comparison Section",
      "category": "Custom Sections"
    }
  ]
}
{% endschema %}

<style>

.bhigh-comparison-section {
    padding: 64px 0px calc(64px + 35px);
    position: relative;
    background: #F3EEED;
}

.bhigh-comparison-table {
    padding: 64px 0 0 0;
    max-width: 700px;
    margin: auto;
    position: relative;
    z-index: 9;
}

.vscomparison-body {
    background: #fff;
    border-radius: 4px;
    position: relative;
    z-index: 9;
}

.vscomparison-row, .vscomparison-header {
    display: grid;
    grid-template-columns: 38% 31% 31%;
}

.vscomparison-header .vscomparison-column {
    height: 84px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    text-align: center;
    padding: 12px;
    font-size: 16px;
    line-height: 19px;
    color: #4D1409;
}

.vscomparison-header .vscomparison-column:nth-child(2) {
    border-radius: 4px 4px 0 0;
    background: #4D1409;
}

.vscomparison-body .vscomparison-row .vscomparison-column {
    padding: 18px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 19px;
    text-transform: capitalize;
    color: #4D1409;
    border-bottom: 1px solid #E1E3D8;
}

.vscomparison-row .vscomparison-column:first-child {
    justify-content: flex-start;
}

.vscomparison-row:last-child {
    border: 0;
}

.vscomparison-row:last-child .vscomparison-column:nth-child(2) {
    position: relative;
}

.vscomparison-row:last-child .vscomparison-column:nth-child(2)::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    border-radius: 4px;
    z-index: -1;
    bottom: -35px;
    background: #4D1409;
}

.vscomparison-row:first-child .vscomparison-column:first-child,
.vscomparison-row:first-child .vscomparison-column:last-child{
    border-radius: 4px;
}

.vscomparison-row:last-child .vscomparison-column:first-child,
.vscomparison-row:last-child .vscomparison-column:last-child{
    border-radius: 4px;
}

.bhigh-comparison-header {
    text-align: center;
}

.bhigh-comparison-header .bigh-global-40font {
    color: #4D1409;
    margin: 0 0 4px 0;
}

.bhigh-comparison-header .bhigh-comparison-subtitle {
    font-weight: 500;
    font-size: 16px;
    line-height: 1.5;
    text-align: center;
    color: #4D1409;
    margin: 0;
}

.vscomparison-body .vscomparison-row .vscomparison-column svg {
    width: 24px;
    height: 24px;
}

.vscomparison-header .vscomparison-column img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.vscomparison-body .vscomparison-row .vscomparison-column:nth-child(2) {
    background-color: #A44E3E;
    border-color: transparent;
}

.vscomparison-body .vscomparison-row:nth-child(even) .vscomparison-column:nth-child(2) {
    background-color: #4D1409;
}







/*media queries starts*/
@media (max-width: 1399.98px) {

.bhigh-comparison-table {
    padding: 50px 0 0 0;
}






}


@media (max-width: 1199.98px) {



}


@media (max-width: 991.98px) {

.bhigh-comparison-table {
    padding: 45px 0 0 0;
}


}


@media (max-width: 767.98px) {

.bhigh-comparison-section {
    padding: 45px 20px calc(45px + 35px);
}

.bhigh-comparison-table {
    padding: 32px 0 0 0;
}

}


@media (max-width: 639px) {

.bhigh-comparison-section {
    padding: 32px 20px calc(32px + 35px);
}

.vscomparison-body .vscomparison-row .vscomparison-column {
    padding: 13px 16px;
    font-size: 14px;
    line-height: 17px;
    min-height: 60px;
}

.vscomparison-header .vscomparison-column {
    font-size: 14px;
    line-height: 17px;
}

}


@media (max-width: 479px) {}
</style>
