/**
 * Bundle Cart Display Override
 * Changes "Purchase Type: Regular" to "Purchase Type: Bundle" for bundle items
 */

class BundleCartDisplay {
  constructor() {
    this.init();
  }

  init() {
    console.log('🎨 Bundle Cart Display initialized');
    
    // Override cart display when cart updates
    document.addEventListener('cart:updated', () => {
      this.updateBundleDisplay();
    });

    // Also check on page load
    setTimeout(() => {
      this.updateBundleDisplay();
    }, 1000);

    // Monitor for cart drawer changes
    this.observeCartChanges();
  }

  updateBundleDisplay() {
    console.log('🔄 Updating bundle display in cart...');
    
    // Find all cart items
    const cartItems = document.querySelectorAll('[data-cart-item], .cart-item, .line-item');
    
    cartItems.forEach(item => {
      this.processBundleItem(item);
    });
  }

  processBundleItem(itemElement) {
    const allElements = itemElement.querySelectorAll('*');

    allElements.forEach(element => {
      if (element.textContent) {
        let text = element.textContent;

        if (text.includes('Purchase Type: Regular') && this.isBundleItem(itemElement)) {
          element.textContent = text.replace('Purchase Type: Regular', 'Purchase Type: Bundle');
        }

        if (text.includes('Bundle ID:')) {
          element.style.display = 'none';
        }

        if (text.includes('Purchase Type: Bundle') && !text.includes('Bundle Name:')) {
          element.style.display = 'none';
        }
      }
    });
  }

  isBundleItem(itemElement) {
    const textContent = itemElement.textContent || itemElement.innerText || '';

    return textContent.includes('Bundle ID:') ||
           textContent.includes('Bundle Name:') ||
           textContent.includes('Purchase Type: Bundle');
  }

  observeCartChanges() {
    // Create a MutationObserver to watch for cart changes
    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if cart items were added
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.matches && (node.matches('[data-cart-item]') || node.matches('.cart-item') || node.matches('.line-item'))) {
                shouldUpdate = true;
              }
            }
          });
        }
      });
      
      if (shouldUpdate) {
        setTimeout(() => {
          this.updateBundleDisplay();
        }, 100);
      }
    });

    // Observe the entire document for cart changes
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new BundleCartDisplay();
  });
} else {
  new BundleCartDisplay();
}
