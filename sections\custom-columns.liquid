{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
  .custom-columns-grid-{{ section.id }} {
    column-gap: {{ section.settings.column_gap_mobile | divided_by: 10.0 }}rem;
    row-gap: {{ section.settings.row_gap_mobile | divided_by: 10.0 }}rem;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
    .custom-columns-grid-{{ section.id }} {
      column-gap: {{ section.settings.column_gap_desktop | divided_by: 10.0 }}rem;
      row-gap: {{ section.settings.row_gap_desktop | divided_by: 10.0 }}rem;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class="section-id-btn button" data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width section-{{ section.id }}-padding">
    {% liquid
      assign columns_count = section.settings.columns_count
      assign desktop_widths_arr = '' | append: section.settings.col_1_desktop_width | append: ',' | append: section.settings.col_2_desktop_width | append: ',' | append: section.settings.col_3_desktop_width | append: ',' | append: section.settings.col_4_desktop_width | append: ',' | append: section.settings.col_5_desktop_width | append: ',' | append: section.settings.col_6_desktop_width | split: ','
      assign mobile_widths_arr = '' | append: section.settings.col_1_mobile_width | append: ',' | append: section.settings.col_2_mobile_width | append: ',' | append: section.settings.col_3_mobile_width | append: ',' | append: section.settings.col_4_mobile_width | append: ',' | append: section.settings.col_5_mobile_width | append: ',' | append: section.settings.col_6_mobile_width | split: ','
      assign visibilities_arr = '' | append: section.settings.col_1_visibility | append: ',' | append: section.settings.col_2_visibility | append: ',' | append: section.settings.col_3_visibility | append: ',' | append: section.settings.col_4_visibility | append: ',' | append: section.settings.col_5_visibility | append: ',' | append: section.settings.col_6_visibility | split: ','
    %}
    <div
      class="custom-columns"
      style="--columns-count:{{ columns_count }};--desktop-grid-parts:12;--mobile-grid-parts:4;--desktop-column-gap:{{ section.settings.column_gap_desktop | divided_by: 10.0 }}rem;--desktop-row-gap:{{ section.settings.row_gap_desktop | divided_by: 10.0 }}rem;--mobile-column-gap:{{ section.settings.column_gap_mobile | divided_by: 10.0 }}rem;--mobile-row-gap:{{ section.settings.row_gap_mobile | divided_by: 10.0 }}rem;--desktop-vertical-alignment:{{ section.settings.desktop_vertical_alignment }};--mobile-vertical-alignment:{{ section.settings.mobile_vertical_alignment }};"
    >
      {% for i in (1..columns_count) %}
        {% liquid
          assign desktop_width = desktop_widths_arr[forloop.index0]
          assign mobile_width = mobile_widths_arr[forloop.index0]
        %}
        <div
          class="custom-columns__column {{ visibilities_arr[forloop.index0] }} animate-item animate-item--child"
          style="--desktop-width:{{ desktop_width }};--mobile-width:{{ mobile_width }};--index: {{ forloop.index0 }};"
        >
          {% for block in section.blocks %}
            {% assign column = block.settings.column | remove: 'col_' | plus: 0 %}
            {% if column == i %}
              <div
                class="custom-columns__block custom-columns__block-{{ block.type }} {{ block.settings.visibility }}"
                style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                {{ block.shopify_attributes }}
              >
                {% case block.type %}
                  {%- when 'heading' -%}
                    <h2 class="custom-columns__title {{ block.settings.heading_size }} {{ block.settings.alignment }} {{ block.settings.mobile_alignment }} title-with-highlight" style='--hightlight-color:{{ block.settings.title_highlight_color }}'>
                      {{ block.settings.heading }}
                    </h2>
                  {% when 'rating_stars' %}
                    {% render 'rating-stars-block', block: block, margins: false, block_attributes: false %}
                  {% when 'trustpilot_stars' %}
                    {% render 'trustpilot-stars-block', block: block, margins: false, block_attributes: false %}
                  {%- when 'richtext' -%}
                    <div class="rte {{ block.settings.text_style }} {{ block.settings.alignment }} {{ block.settings.mobile_alignment }}">
                      {{ block.settings.text }}
                    </div>
                  {%- when 'buttons' -%}
                    <div
                      class="buttons-container"
                      style="--alignment:{{ block.settings.alignment }};--mobile-alignment:{{ block.settings.mobile_alignment }};"
                    >
                      {%- if block.settings.button_label_1 != blank -%}
                        <a
                          {% if block.settings.button_link_1 == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block.settings.button_link_1 }}"
                          {% endif %}
                          class="button{% if block.settings.button_style_secondary_1 %} button--secondary{% else %} button--primary{% endif %}{% if block.settings.full_width %} button--full-width{% endif %}"
                        >
                          {{- block.settings.button_label_1 | escape -}}
                        </a>
                      {%- endif -%}
                      {%- if block.settings.button_label_2 != blank -%}
                        <a
                          {% if block.settings.button_link_2 == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block.settings.button_link_2 }}"
                          {% endif %}
                          class="button{% if block.settings.button_style_secondary_2 %} button--secondary{% else %} button--primary{% endif %}{% if block.settings.full_width %} button--full-width{% endif %}"
                        >
                          {{- block.settings.button_label_2 | escape -}}
                        </a>
                      {%- endif -%}
                    </div>
                  {% when 'atc_button' %}
                    <div
                      class="buttons-container"
                      style="--alignment:{{ block.settings.alignment }};--mobile-alignment:{{ block.settings.mobile_alignment }};"
                    >
                      {% liquid
                        if block.settings.full_width
                          assign btn_class = 'button--full-width'
                        else
                          assign btn_class = ''
                        endif
                      %}
                      {% if block.settings.atc_product == blank %}
                        <button
                          id="SectionAtcBtn-{{ section.id }}"
                          type="button"
                          class="button main-product-atc {{ btn_class }} button--has-spinner"
                          {% if product.selected_or_first_available_variant.available == false %}
                            disabled
                          {% endif %}
                        >
                          {{ block.settings.button_label }}
                          <div class="loading-overlay__spinner">
                            <svg
                              aria-hidden="true"
                              focusable="false"
                              class="spinner"
                              viewBox="0 0 66 66"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                            </svg>
                          </div>
                        </button>
                      {% else %}
                        {% assign product_form_id = 'section-product-form-' | append: section.id | append: block.id %}
                        {% render 'separate-atc-btn',
                          product: block.settings.atc_product,
                          product_form_id: product_form_id,
                          class: btn_class,
                          label: block.settings.button_label,
                          skip_cart: block.settings.atc_skip_cart
                        %}
                      {% endif %}
                    </div>
                  {%- when 'text_with_icon' -%}
                    {% render 'text-with-icon-block', block: block, mobile_align: true %}
                  {% when 'icon_with_text' %}
                    {% liquid
                      if block.settings.image != blank or block.settings.icon != blank
                        assign has_icon = true
                      else
                        assign has_icon = false
                      endif
                    %}
                    <div class="icons-with-text__icon-item icons-with-text__icon-item--{{ block.settings.icon_position }} icons-with-text__icon-item--{{ block.settings.icon_text_alignment }}">
                      {% if block.settings.icon_position != 'next-to-title' and has_icon %}
                        <div class="icons-with-text__icon__icon icons-with-text__icon__icon--{{ block.settings.icon_size }} color-{{ block.settings.icon_color }}">
                          {%- if block.settings.image != blank -%}
                            <img
                              src="{{ block.settings.image | image_url: width: 300 }}"
                              {% if block.settings.image.alt != blank %}
                                alt="{{ block.settings.image.alt | escape }}"
                              {% else %}
                                role="presentation"
                              {% endif %}
                              height="auto"
                              width="auto"
                              loading="lazy"
                            >
                          {%- else -%}
                            {% render 'material-icon', icon: block.settings.icon, filled: block.settings.filled_icon %}
                          {%- endif -%}
                        </div>
                      {% endif %}
                      <div class="icons-with-text__icon__text">
                        <h3 class="icons-with-text__icon__title {{ block.settings.icon_heading_size }}">
                          {% if block.settings.icon_position == 'next-to-title' and has_icon %}
                            <div class="icons-with-text__icon__icon icons-with-text__icon__icon--title icons-with-text__icon__icon--{{ block.settings.icon_size }} color-{{ block.settings.icon_color }}">
                              {%- if block.settings.image != blank -%}
                                <img
                                  src="{{ block.settings.image | image_url: width: 300 }}"
                                  {% if block.settings.image.alt != blank %}
                                    alt="{{ block.settings.image.alt | escape }}"
                                  {% else %}
                                    role="presentation"
                                  {% endif %}
                                  height="auto"
                                  width="auto"
                                  loading="lazy"
                                >
                              {%- else -%}
                                {% render 'material-icon',
                                  icon: block.settings.icon,
                                  filled: block.settings.filled_icon
                                %}
                              {%- endif -%}
                            </div>
                          {% endif %}
                          <span>
                            {{ block.settings.title | escape }}
                          </span>
                        </h3>
                        {%- if block.settings.text != blank -%}
                          <div class="rte">
                            {{ block.settings.text }}
                          </div>
                        {%- endif -%}
                      </div>
                    </div>
                  {%- when 'accordion' -%}
                    <div
                      class="product__accordion accordion accordion--{{ block.settings.heading_size }}{% if block.settings.display_top_border %} accordion--top-border{% endif %} quick-add-hidden"
                      style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                      {{ block.shopify_attributes }}
                    >
                      <details class="accordion__details">
                        <summary class="accordion__summary">
                          <div class="summary__title">
                            {% if block.settings.custom_icon != blank %}
                              <img
                                src="{{ block.settings.custom_icon | image_url }}"
                                {% if block.settings.custom_icon.alt != blank %}
                                  alt="{{ block.settings.custom_icon.alt | escape }}"
                                {% else %}
                                  role="presentation"
                                {% endif %}
                                height="auto"
                                width="auto"
                                loading="lazy"
                              >
                            {% else %}
                              {% render 'material-icon',
                                icon: block.settings.icon,
                                filled: block.settings.filled_icon
                              %}
                            {% endif %}
                            <h2 class="h4 accordion__title">
                              {{ block.settings.heading | default: block.settings.page.title }}
                            </h2>
                          </div>
                          {% if block.settings.collapse_icon == 'carret' %}
                            {% render 'icon-caret' %}
                          {% else %}
                            {% render 'icon-plus' %}
                          {% endif %}
                        </summary>
                      </details>
                      <div class="accordion__content-wrapper">
                        <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                          {{ block.settings.content }}
                          {{ block.settings.page.content }}
                        </div>
                      </div>
                    </div>
                  {%- when 'payment_badges' -%}
                    <div
                      class="payment-badges-block"
                      style="--alignment:{{ block.settings.alignment }};--mobile-alignment:{{ block.settings.mobile_alignment }};"
                    >
                      <ul class="payment-badges" role="list">
                        {% assign enabled_payment_types = shop.enabled_payment_types %}
                        {% if block.settings.enabled_payment_types != blank %}
                          {% assign enabled_payment_types = block.settings.enabled_payment_types
                            | remove: ' '
                            | split: ','
                          %}
                        {% endif %}

                        {%- for type in enabled_payment_types -%}
                          {% assign payment_type = type | strip %}
                          <li class="list-payment__item">
                            {{ payment_type | payment_type_svg_tag: class: 'icon icon--full-color' }}
                          </li>
                        {%- endfor -%}
                      </ul>
                    </div>
                  {% when 'image' %}
                    <div
                      class="product-info__image-block product-info__image-block--mobile-alignment"
                      style="--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--mobile-image-alignment:{{ block.settings.mobile_alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                    >
                      {% if block.settings.image != blank %}
                        <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%">
                          {% assign width_decimal = block.settings.width | divided_by: 100.0 %}
                          {%- capture sizes -%}
                            (min-width: {{ settings.page_width }}px) calc({{ settings.page_width | divided_by: 12.0 | times: desktop_width }}px * {{ width_decimal }}),
                            (min-width: 750px) calc((100vw - 30px) / 12 * {{ desktop_width }} * {{ width_decimal }}), calc((100vw - 30px) / 4 * {{ mobile_width }} * {{ width_decimal }})
                          {%- endcapture -%}
                          {{
                            block.settings.image
                            | image_url: width: 1500
                            | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
                          }}
                        </div>
                      {% else %}
                        {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                      {% endif %}
                    </div>
                  {% when 'video' %}
                    <div
                      class="product-info__image-block product-info__image-block--mobile-alignment"
                      style="--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--mobile-image-alignment:{{ block.settings.mobile_alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                    >
                      {% if block.settings.video != blank %}
                        <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: block.settings.video.aspect_ratio | times: 100 }}%">
                          {% render 'video-player', block: block %}
                        </div>
                      {% else %}
                        {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                      {% endif %}
                    </div>
                  {% when 'email_signup' %}
                    <div>
                      <div class="footer-block__details-content footer-block__details-content-mb rte">
                        {{ block.settings.subtext }}
                      </div>
                      {%- form 'customer', id: 'ContactFooter', class: 'footer__newsletter newsletter-form' -%}
                        <input type="hidden" name="contact[tags]" value="newsletter">
                        <div class="newsletter-form__field-wrapper">
                          <div class="field">
                            <input
                              id="NewsletterForm--{{ section.id }}"
                              type="email"
                              name="contact[email]"
                              class="field__input"
                              value="{{ form.email }}"
                              aria-required="true"
                              autocorrect="off"
                              autocapitalize="off"
                              autocomplete="email"
                              {% if form.errors %}
                                autofocus
                                aria-invalid="true"
                                aria-describedby="ContactFooter-error"
                              {% elsif form.posted_successfully? %}
                                aria-describedby="ContactFooter-success"
                              {% endif %}
                              placeholder="{{ 'newsletter.label' | t }}"
                              required
                            >
                            <label class="field__label" for="NewsletterForm--{{ section.id }}">
                              {{ 'newsletter.label' | t }}
                            </label>
                            {% if block.settings.button_type == 'arrow' %}
                              <button
                                type="submit"
                                class="newsletter-form__button field__button"
                                name="commit"
                                id="Subscribe"
                                aria-label="{{ 'newsletter.button_label' | t }}"
                              >
                                {% render 'icon-arrow' %}
                              </button>
                            {% endif %}
                          </div>
                          {%- if form.errors -%}
                            <small class="newsletter-form__message form__message" id="ContactFooter-error">
                              {%- render 'icon-error' -%}
                              {{- form.errors.translated_fields.email | capitalize }}
                              {{ form.errors.messages.email -}}
                            </small>
                          {%- endif -%}
                        </div>
                        {% if block.settings.button_type == 'solid' %}
                          <button
                            type="submit"
                            class="button newsletter__solid-btn button--full-width{% if block.settings.button_style_secondary %} button--secondary{% endif %}"
                            name="commit"
                            id="Subscribe"
                            aria-label="{{ 'newsletter.button_label' | t }}"
                          >
                            {{ block.settings.button_label }}
                          </button>
                        {% endif %}
                        {%- if form.posted_successfully? -%}
                          <h3
                            class="newsletter-form__message newsletter-form__message--success form__message"
                            id="ContactFooter-success"
                            tabindex="-1"
                            autofocus
                          >
                            {% render 'icon-success' -%}
                            {{- 'newsletter.success' | t }}
                          </h3>
                        {%- endif -%}
                      {%- endform -%}
                    </div>
                  {%- when 'custom_liquid' -%}
                    {{ block.settings.custom_liquid }}
                {% endcase %}
              </div>
            {% endif %}
          {% endfor %}
        </div>
      {% endfor %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Custom columns",
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "range",
      "id": "columns_count",
      "min": 1,
      "max": 6,
      "step": 1,
      "label": "Number of columns",
      "default": 4
    },
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "range",
      "id": "column_gap_desktop",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Horizontal spacing",
      "default": 40
    },
    {
      "type": "range",
      "id": "row_gap_desktop",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Vertical spacing",
      "default": 40
    },
    {
      "type": "select",
      "id": "desktop_vertical_alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Top"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Bottom"
        }
      ],
      "label": "Vertical alignment",
      "default": "center"
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "range",
      "id": "column_gap_mobile",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Horizontal spacing",
      "default": 20
    },
    {
      "type": "range",
      "id": "row_gap_mobile",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Vertical spacing",
      "default": 30
    },
    {
      "type": "select",
      "id": "mobile_vertical_alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Top"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Bottom"
        }
      ],
      "label": "Vertical alignment",
      "default": "flex-start"
    },
    {
      "type": "header",
      "content": "First column"
    },
    {
      "type": "range",
      "id": "col_1_desktop_width",
      "max": 12,
      "min": 1,
      "step": 1,
      "default": 12,
      "label": "Desktop width"
    },
    {
      "type": "range",
      "id": "col_1_mobile_width",
      "max": 4,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Mobile width"
    },
    {
      "type": "select",
      "id": "col_1_visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Second column"
    },
    {
      "type": "range",
      "id": "col_2_desktop_width",
      "max": 12,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Desktop width"
    },
    {
      "type": "range",
      "id": "col_2_mobile_width",
      "max": 4,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Mobile width"
    },
    {
      "type": "select",
      "id": "col_2_visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Third column"
    },
    {
      "type": "range",
      "id": "col_3_desktop_width",
      "max": 12,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Desktop width"
    },
    {
      "type": "range",
      "id": "col_3_mobile_width",
      "max": 4,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Mobile width"
    },
    {
      "type": "select",
      "id": "col_3_visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Fourth column"
    },
    {
      "type": "range",
      "id": "col_4_desktop_width",
      "max": 12,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Desktop width"
    },
    {
      "type": "range",
      "id": "col_4_mobile_width",
      "max": 4,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Mobile width"
    },
    {
      "type": "select",
      "id": "col_4_visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Fifth column"
    },
    {
      "type": "range",
      "id": "col_5_desktop_width",
      "max": 12,
      "min": 1,
      "step": 1,
      "default": 3,
      "label": "Desktop width"
    },
    {
      "type": "range",
      "id": "col_5_mobile_width",
      "max": 4,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Mobile width"
    },
    {
      "type": "select",
      "id": "col_5_visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Sixth column"
    },
    {
      "type": "range",
      "id": "col_6_desktop_width",
      "max": 12,
      "min": 1,
      "step": 1,
      "default": 3,
      "label": "Desktop width"
    },
    {
      "type": "range",
      "id": "col_6_mobile_width",
      "max": 4,
      "min": 1,
      "step": 1,
      "default": 4,
      "label": "Mobile width"
    },
    {
      "type": "select",
      "id": "col_6_visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "Custom columns",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Desktop alignment"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "options": [
            {
              "value": "mobile-left",
              "label": "Left"
            },
            {
              "value": "mobile-center",
              "label": "Center"
            },
            {
              "value": "mobile-right",
              "label": "Right"
            }
          ],
          "default": "mobile-center",
          "label": "Mobile alignment"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "richtext",
      "name": "Rich text",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Use the flexible grid system to create any layout. Each block can be placed in any of the columns.</p>",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-with-text.blocks.text.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Desktop alignment"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "options": [
            {
              "value": "mobile-left",
              "label": "Left"
            },
            {
              "value": "mobile-center",
              "label": "Center"
            },
            {
              "value": "mobile-right",
              "label": "Right"
            }
          ],
          "default": "mobile-center",
          "label": "Mobile alignment"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "rating_stars",
      "name": "Rating stars",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 4.8
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "select",
          "id": "bg_stars_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "full",
              "label": "Full"
            }
          ],
          "label": "Background stars style",
          "default": "full"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#ececec",
          "label": "Background stars color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "trustpilot_stars",
      "name": "Trustpilot stars",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 5
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#00b67a",
          "label": "Active stars container color"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#c8c8c8",
          "label": "Background stars container color"
        },
        {
          "type": "color",
          "id": "star_symbol_color",
          "default": "#fff",
          "label": "Stars inside symbol color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "buttons",
      "name": "Buttons",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "text",
          "id": "button_label_1",
          "default": "Button label",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_1.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_1.info"
        },
        {
          "type": "url",
          "id": "button_link_1",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_1.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_1",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_1.label"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "default": "Button label",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_2",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_2.label"
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "default": false,
          "label": "Full buttons width"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start",
          "label": "Desktop alignment"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Mobile alignment"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "atc_button",
      "name":"Add to Cart button",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Add to Cart"
        },
        {
          "type": "product",
          "id": "atc_product",
          "label": "ATC Custom product",
          "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
        },
        {
          "type": "checkbox",
          "id": "atc_skip_cart",
          "label": "ATC Custom product skip cart"
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "default": false,
          "label": "Full button width"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Desktop alignment"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start",
          "label": "Mobile alignment"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "text_with_icon",
      "name": "Text with icon",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "header",
          "content": "Text"
        },
        {
          "type": "range",
          "id": "mobile_text_size",
          "min": 8,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 14,
          "label": "Mobile text size"
        },
        {
          "type": "range",
          "id": "desktop_text_size",
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Desktop text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "label": "Mobile text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "color",
          "id": "text_color",
          "default": "#121212",
          "label": "Text color"
        },
        {
          "type": "inline_richtext",
          "id": "text_1",
          "default": "Text with icon",
          "label": "Text #1"
        },
        {
          "type": "inline_richtext",
          "id": "text_2",
          "label": "Text #2"
        },
        {
          "type": "inline_richtext",
          "id": "text_3",
          "label": "Text #3"
        },
        {
          "type": "header",
          "content": "Icons"
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 100,
          "max": 200,
          "step": 5,
          "default": 120,
          "unit": "%",
          "label": "Icon scale",
          "info": "Relative to text size"
        },
        {
          "type": "color",
          "id": "icon_color",
          "default": "#121212",
          "label": "Icons color"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "check_circle",
          "label": "Icon #1",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "Filled icon #1"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon #1"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "check_circle",
          "label": "Icon #2",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "Filled icon #2"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon #2"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "check_circle",
          "label": "Icon #3",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_3",
          "default": false,
          "label": "Filled icon #3"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_3",
          "label": "Custom icon #3"
        },
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "width",
          "options": [
            {
              "value": "fit-content",
              "label": "Fit text"
            },
            {
              "value": "100%",
              "label": "Full"
            }
          ],
          "default": "100%",
          "label": "Width"
        },
        {
          "type": "select",
          "id": "direction",
          "options": [
            {
              "value": "horizontal",
              "label": "Horizontal"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "default": "horizontal",
          "label": "Stacking direction",
          "info": "Applied when multiple texts are added."
        },
        {
          "type": "range",
          "id": "column_gap",
          "min": 0,
          "max": 6,
          "step": 0.5,
          "label": "Stacking spacing",
          "default": 3
        },
        {
          "type": "checkbox",
          "id": "enable_bg",
          "default": false,
          "label": "Enable background",
          "info": "The following settings are applied when this option is enabled."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background color",
          "default": "#F3F3F3"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "t:settings_schema.global.settings.corner_radius.label",
          "default": 40,
          "info": "Relative to font size"
        },
        {
          "type": "range",
          "id": "padding",
          "min": 1,
          "max": 5,
          "step": 0.5,
          "label": "Padding",
          "default": 3
        },
        {
          "type": "range",
          "id": "border_size",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Border thickness",
          "unit": "px",
          "default": 0
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#B7B7B7"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "icon_with_text",
      "name": "Icon with content",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "text",
          "id": "icon",
          "default": "check_circle",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Custom icon"
        },
        {
          "type": "select",
          "id": "icon_size",
          "label": "Icon size",
          "options": [
            {
              "value": "xs",
              "label": "Extra small"
            },
            {
              "value": "s",
              "label": "Small"
            },
            {
              "value": "m",
              "label": "Medium"
            },
            {
              "value": "l",
              "label": "Large"
            },
            {
              "value": "xl",
              "label": "Extra large"
            },
            {
              "value": "xxl",
              "label": "Double extra large"
            }
          ],
          "default": "m"
        },
        {
          "type": "select",
          "id": "icon_position",
          "options": [
            {
              "value": "above",
              "label": "Above title & text"
            },
            {
              "value": "next-to-title",
              "label": "Next to title"
            },
            {
              "value": "next-to-text",
              "label": "Next to text & title"
            }
          ],
          "label": "Icon position",
          "default": "next-to-title"
        },
        {
          "type": "select",
          "id": "icon_color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "accent-1",
          "label": "Icon color"
        },
        {
          "type": "select",
          "id": "icon_heading_size",
          "options": [
            {
              "value": "h5",
              "label": "Extra small"
            },
            {
              "value": "h4",
              "label": "Small"
            },
            {
              "value": "h3",
              "label": "Medium"
            },
            {
              "value": "h2",
              "label": "Large"
            }
          ],
          "default": "h3",
          "label": "Heading size"
        },
        {
          "type": "select",
          "id": "icon_text_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Text alignment",
          "info": "Text is automatically centered if the icon position is set to above title & text."
        },
        {
          "type": "text",
          "id": "title",
          "default": "Icon with text",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an icon to focus on your chosen product, collection, or blog post</p>",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Width",
          "default": 100
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "label": "Mobile alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 0
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "image_picker",
          "id": "thumbnail",
          "label": "Video Thumbnail",
          "info": "If empty, the first frame of the video will be displayed"
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Video looping",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "muted_autoplay",
          "label": "Muted autoplay",
          "default": true,
          "info": "Use this instead of GIFs & animated WEBPs."
        },
        {
          "type": "checkbox",
          "id": "display_play_btn",
          "label": "Enable play & pause on click",
          "info": "Automatically enabled if autoplay is disabled.",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "btn_animation",
          "label": "Enable button ripple animation",
          "default": false
        },
        {
          "type": "select",
          "id": "btn_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1",
          "label": "Play button color scheme"
        },
        {
          "type": "checkbox",
          "id": "display_sound_btn",
          "label": "Display mute/unmute button",
          "default": false
        },
        {
          "type": "select",
          "id": "sound_btn_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "inverse",
          "label": "Sound button color scheme"
        },
        {
          "type": "checkbox",
          "id": "display_timeline",
          "label": "Display timeline",
          "default": false
        },
        {
          "type": "select",
          "id": "timeline_color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1",
          "label": "Timeline color"
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Width",
          "default": 100
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "label": "Mobile alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 0
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "accordion",
      "name": "Collapsible row",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "Heading size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium"
        },
        {
          "type": "text",
          "id": "icon",
          "default": "check_box",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon"
        },
        {
          "type": "select",
          "id": "collapse_icon",
          "label": "Collapse icon",
          "options": [
            {
              "value": "carret",
              "label": "Carret"
            },
            {
              "value": "plus",
              "label": "Plus"
            }
          ],
          "default": "carret"
        },
        {
          "type": "checkbox",
          "id": "display_top_border",
          "label": "Display top border",
          "default": true,
          "info": "This option is automatically optimized for stacked rows."
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 0
        }
      ]
    },
    {
      "type": "payment_badges",
      "name": "Payment Option Badges",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "text",
          "id": "enabled_payment_types",
          "label": "Custom payment icons to show",
          "info": "List of payments you want to show, split with a comma. Options are: afterpay, american_express, apple_pay, bitcoin, dankort, diners_club, discover, dogecoin, dwolla, facebook_pay, forbrugsforeningen, google_pay, ideal, jcb, klarna, klarna-pay-later, litecoin, maestro, master, paypal, shopify_pay, sofort, unionpay, visa"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start",
          "label": "Desktop alignment"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Mobile alignment"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "email_signup",
      "name": "Email signup",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "paragraph",
          "content": "t:sections.footer.settings.header__1.info"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Subscribe to our emails",
          "label": "t:sections.footer.blocks.link_list.settings.heading.label"
        },
        {
          "type": "richtext",
          "id": "subtext",
          "default": "<p>A short sentence encouraging customers to subscribe to your newsletter.</p>",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "button_type",
          "options": [
            {
              "value": "arrow",
              "label": "Arrow button"
            },
            {
              "value": "solid",
              "label": "Solid button"
            }
          ],
          "default": "solid",
          "label": "Submit button type"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Solid button label",
          "default": "Sign up"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
          "default": false
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.main-product.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "select",
          "id": "column",
          "options": [
            {
              "value": "col_1",
              "label": "First column"
            },
            {
              "value": "col_2",
              "label": "Second column"
            },
            {
              "value": "col_3",
              "label": "Third column"
            },
            {
              "value": "col_4",
              "label": "Fourth column"
            },
            {
              "value": "col_5",
              "label": "Fifth column"
            },
            {
              "value": "col_6",
              "label": "Sixth column"
            }
          ],
          "default": "col_1",
          "label": "Display in"
        },
        {
          "type": "select",
          "id": "visibility",
          "options": [
            {
              "value": "desktop-hidden",
              "label": "Mobile only"
            },
            {
              "value": "mobile-hidden",
              "label": "Desktop only"
            },
            {
              "value": "always-display",
              "label": "All devices"
            }
          ],
          "label": "Display on",
          "default": "always-display"
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.main-product.blocks.custom_liquid.settings.custom_liquid.label",
          "info": "t:sections.main-product.blocks.custom_liquid.settings.custom_liquid.info"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Top margin",
          "default": 20
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Bottom margin",
          "default": 20
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Custom columns",
      "blocks": [
        {
          "type": "heading",
          "settings": {
            "alignment": "center"
          }
        },
        {
          "type": "richtext",
          "settings": {
            "alignment": "center"
          }
        },
        {
          "type": "icon_with_text",
          "settings": {
            "column": "col_2",
            "margin_bottom": 30
          }
        },
        {
          "type": "icon_with_text",
          "settings": {
            "column": "col_2"
          }
        },
        {
          "type": "image",
          "settings": {
            "column": "col_3"
          }
        },
        {
          "type": "icon_with_text",
          "settings": {
            "column": "col_4",
            "margin_bottom": 30
          }
        },
        {
          "type": "icon_with_text",
          "settings": {
            "column": "col_4"
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
