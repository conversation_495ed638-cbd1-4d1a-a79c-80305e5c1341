{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .trustpilot-reviews-{{ section.id }} .trustpilot-review {
    --border-radius: {{ section.settings.cards_border_radius }}px;
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}

  .cards-color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_cards_colors_background.red }}, {{ section.settings.custom_cards_colors_background.green }}, {{ section.settings.custom_cards_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_cards_gradient_background != blank %}{{ section.settings.custom_cards_gradient_background }}{% else %}{{ section.settings.custom_cards_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_cards_colors_text.red }}, {{ section.settings.custom_cards_colors_text.green }}, {{ section.settings.custom_cards_colors_text.blue }};
  }
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class="section-id-btn button" data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  {%- liquid
    assign has_mobile_slider = false
    assign has_desktop_slider = false
    if section.settings.slider_mobile
      assign has_mobile_slider = true
    endif
    if section.settings.slider_desktop
      assign has_desktop_slider = true
    endif
  -%}
  <div
    class="trustpilot-reviews trustpilot-reviews-{{ section.id }} trustpilot-reviews--desktop-content-{{ section.settings.desktop_content_position }} page-width{% if section.settings.desktop_full_page %} desktop-full-page{% endif %}{% if has_mobile_slider %} mobile-full-page{% endif %} section-{{ section.id }}-padding isolate"
    style="--columns-desktop: {{ section.settings.columns_desktop }};--columns-mobile:1;--gap-desktop:{{ section.settings.desktop_spacing | divided_by: 10.0 }}rem;--gap-mobile:1.5rem;"
  >
    {% if section.settings.title != blank or section.settings.subtitle != blank or section.settings.text != blank %}
      <div class="trustpilot-reviews__content-container content-rte center desktop-{{ section.settings.desktop_content_alignment }}{% if has_mobile_slider %} mobile-page-width-padding{% endif %} animate-item animate-item--child index-0">
        {% unless section.settings.title == blank %}
          <h2 class="{{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
            {{ section.settings.title }}
          </h2>
        {% endunless %}
        {% unless section.settings.subtitle == blank %}
          {% capture rating_stars %}
            <div class='rating-stars__container rating-stars__container--underlay' style='--rating:{{ section.settings.stars_rating }};--star-color:{{ section.settings.star_color }};--bg-star-color:{{ section.settings.bg_star_color }};'>
              <svg height="469" width="2500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 96" class='trustpilot-stars-svg'>
                <path fill="{{ section.settings.bg_star_color }}" d="M0 0h96v96H0zM104 0h96v96h-96zM208 0h96v96h-96zM312 0h96v96h-96zM416 0h96v96h-96z"/>
                <path fill="{{ section.settings.star_symbol_color }}" d="M48 64.7L62.6 61l6.1 18.8zm33.6-24.3H55.9L48 16.2l-7.9 24.2H14.4l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM152 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L152 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM256 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L256 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM360 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L360 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM464 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L464 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2z" />
              </svg>
              <div class='rating-stars__container rating-stars__container--overlay'>
                <svg height="469" width="2500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 96" class='trustpilot-stars-svg'>
                  <path fill="{{ section.settings.star_color }}" d="M0 0h96v96H0zM104 0h96v96h-96zM208 0h96v96h-96zM312 0h96v96h-96zM416 0h96v96h-96z"/>
                  <path fill="{{ section.settings.star_symbol_color }}" d="M48 64.7L62.6 61l6.1 18.8zm33.6-24.3H55.9L48 16.2l-7.9 24.2H14.4l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM152 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L152 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM256 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L256 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM360 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L360 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM464 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L464 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2z" />
                </svg>
              </div>
            </div>
          {% endcapture %}
          {% capture rating_text %}
            <span style='font-size: 1.25em;'>{{ section.settings.subheading_rating_text }}</span>
          {% endcapture %}
          <h2 
            class="trustpilot-reviews__subheading trustpilot-reviews__subheading--{{ section.settings.subheading_font }} custom-font-size flex-center"
            style='--mobile-text-size: {{ section.settings.subheading_mobile_text_size | divided_by: 10.0 }}rem;--desktop-text-size: {{ section.settings.subheading_desktop_text_size | divided_by: 10.0 }}rem;'
          >
            {{ section.settings.subtitle | replace: '[rating_stars]', rating_stars | replace: '[rating]', rating_text }}
          </h2>
        {% endunless %}
        {% unless section.settings.text == blank %}
          <div class="rte">
            {{ section.settings.text }}
          </div>
        {% endunless %}
      </div>
    {% endif %}
    {% if has_mobile_slider or has_desktop_slider %}
      {% assign has_splide_component = true %}
      <splide-component
        data-type="{{ section.settings.type }}"
        data-autoplay="{{ section.settings.autoplay }}"
        data-autoplay-speed="{{ section.settings.autoplay_speed }}"
        data-arrows-color="{{ section.settings.arrows_color_scheme }}"
        data-dots-color="{{ section.settings.dots_color_scheme }}"
        data-slides-desktop="{{ section.settings.columns_desktop }}"
        data-per-move-desktop="{{ section.settings.per_move_desktop }}"
        data-gap-desktop="{{ section.settings.desktop_spacing }}"
        data-side-padding-desktop="{{ section.settings.desktop_side_padding }}"
        data-padding-calc-desktop="{{ section.settings.desktop_padding_calc }}"
        data-slides-mobile="1"
        data-gap-mobile="15"
        {% if section.settings.enable_mobile_preview %}
          data-side-padding-mobile="24"
        {% else %}
          data-side-padding-mobile="15"
        {% endif %}
        {% if has_desktop_slider == false %}
          data-destroy-desktop="true"
        {% elsif has_mobile_slider == false %}
          data-destroy-mobile="true"
        {% endif %}
        class='trustpilot__reviews-container'
      >
    {% endif %}
    <div
      class="splide{% unless has_splide_component %} trustpilot__reviews-container{% endunless %} splide--desktop-dots-{{ section.settings.desktop_dots_position }} splide--mobile-dots-{{ section.settings.mobile_dots_position }} splide--desktop-arrows-{{ section.settings.desktop_arrows_position }} splide--desktop-arrows-outside splide--mobile-arrows-{{ section.settings.mobile_arrows_position }}{% if section.settings.transparent_arrows %} splide--transparent-arrows{% endif %}{% if section.settings.vertical-alignment == 'center' %} splide--vertically-centered{% endif %}{% if has_desktop_slider == false %} splide--destroy-desktop{% endif %}{% if has_mobile_slider == false %} splide--destroy-mobile{% endif %}"
      style="--columns-desktop: {{ section.settings.columns_desktop }};--columns-mobile:1;--gap-desktop:{{ section.settings.desktop_spacing | divided_by: 10.0 }}rem;--gap-mobile:1.5rem;"
      {% if section.settings.desktop_adaptive_height and section.settings.slides_desktop == 1 and has_desktop_slider %}
        data-desktop-adaptive-height="true"
      {% endif %}
      {% if section.settings.mobile_adaptive_height and has_mobile_slider %}
        data-mobile-adaptive-height="true"
      {% endif %}
    >
      <div class="splide__track">
        <ul class="splide__list">
          {%- for block in section.blocks -%}
            <li class="splide__slide">
              <div
                class="splide__slide__container"
                style="--index:{{ forloop.index0 }};"
                {{ block.shopify_attributes }}
              >
                <div class='trustpilot-review custom-border-radius {{ section.settings.card_alignment }} cards-color-scheme-{{ section.id }} color-{{ section.settings.cards_color_scheme }}'>
                  {% if section.settings.show_stars %}
                    <div class='rating-stars__container rating-stars__container--underlay' style='--rating:{{ block.settings.stars_rating }};--star-color:{{ block.settings.star_color }};--bg-star-color:{{ block.settings.bg_star_color }};'>
                      <svg height="469" width="2500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 96" class='trustpilot-stars-svg'>
                        <path fill="{{ block.settings.bg_star_color }}" d="M0 0h96v96H0zM104 0h96v96h-96zM208 0h96v96h-96zM312 0h96v96h-96zM416 0h96v96h-96z"/>
                        <path fill="{{ block.settings.star_symbol_color }}" d="M48 64.7L62.6 61l6.1 18.8zm33.6-24.3H55.9L48 16.2l-7.9 24.2H14.4l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM152 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L152 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM256 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L256 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM360 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L360 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM464 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L464 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2z" />
                      </svg>
                      <div class='rating-stars__container rating-stars__container--overlay'>
                        <svg height="469" width="2500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 96" class='trustpilot-stars-svg'>
                          <path fill="{{ block.settings.star_color }}" d="M0 0h96v96H0zM104 0h96v96h-96zM208 0h96v96h-96zM312 0h96v96h-96zM416 0h96v96h-96z"/>
                          <path fill="{{ block.settings.star_symbol_color }}" d="M48 64.7L62.6 61l6.1 18.8zm33.6-24.3H55.9L48 16.2l-7.9 24.2H14.4l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM152 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L152 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM256 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L256 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM360 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L360 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2zM464 64.7l14.6-3.7 6.1 18.8zm33.6-24.3h-25.7L464 16.2l-7.9 24.2h-25.7l20.8 15-7.9 24.2 20.8-15 12.8-9.2z" />
                        </svg>
                      </div>
                    </div>
                  {% endif %}
                  {%- if block.settings.title != blank -%}
                    <h3 class='h4'>
                      {{ block.settings.title }}
                    </h3>
                  {%- endif -%}
                  {%- if block.settings.text != blank -%}
                    <div class="rte">
                      {{ block.settings.text }}
                    </div>
                  {%- endif -%}
                  {%- unless block.settings.author_avatar == blank and block.settings.author == blank -%}
                    <div class="testimonial-card__author-container">
                      {%- if block.settings.author_avatar != blank -%}
                        <div class="testimonial-card__avatar">
                          {{
                            block.settings.author_avatar
                            | image_url: width: 150
                            | image_tag: loading: 'lazy', width: 100
                          }}
                        </div>
                      {%- endif -%}
                      {%- if block.settings.author != blank -%}
                        <p class="testimonial-card__author margin-0">
                          {{ block.settings.author }}
                        </p>
                      {%- endif -%}
                    </div>
                  {%- endunless -%}
                </div>
              </div>
            </li>
          {%- endfor -%}
        </ul>
      </div>
      <div class="splide__dots-and-arrows">
        <ul class="splide__pagination"></ul>
        <div class="splide__arrows"></div>
      </div>
    </div>
    {% if has_mobile_slider or has_desktop_slider %}
      </splide-component>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Trustpilot reviews",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Content layout",
      "info": "Applies to the heading, subheading and text."
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "above",
          "label": "Above reviews"
        },
        {
          "value": "left",
          "label": "Left side of the reviews"
        },
        {
          "value": "right",
          "label": "Right side of the reviews"
        }
      ],
      "label": "Desktop content position",
      "default": "above"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "label": "Desktop content alignment",
      "default": "center"
    },
    {
      "type": "header",
      "content": "Heading"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Trustpilot reviews",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "header",
      "content": "Subheading"
    },
    {
      "type": "inline_richtext",
      "id": "subtitle",
      "default": "Excellent [rating] <strong>/ 5</strong> [rating_stars]",
      "label": "Subheading",
      "info": "Use [rating] to display the \"Rating text\" and [rating_stars] to display rating stars."
    },
    {
      "type": "inline_richtext",
      "id": "subheading_rating_text",
      "label": "Rating text",
      "default": "<strong>4.8</strong>",
      "info": "Displays a highlighted text that replaces [rating] in the Subtitle."
    },
    {
      "type": "range",
      "id": "subheading_mobile_text_size",
      "min": 12,
      "max": 28,
      "step": 1,
      "unit": "px",
      "default": 18,
      "label": "Mobile subheading text size"
    },
    {
      "type": "range",
      "id": "subheading_desktop_text_size",
      "min": 14,
      "max": 36,
      "step": 1,
      "unit": "px",
      "default": 20,
      "label": "Desktop subheading text size"
    },
    {
      "type": "select",
      "id": "subheading_font",
      "options": [
        {
          "value": "headings",
          "label": "Headings"
        },
        {
          "value": "body",
          "label": "Body"
        }
      ],
      "default": "body",
      "label": "Subheading font family"
    },
    {
      "type": "color",
      "id": "star_color",
      "default": "#00b67a",
      "label": "Active stars container color"
    },
    {
      "type": "color",
      "id": "bg_star_color",
      "default": "#c8c8c8",
      "label": "Background stars container color"
    },
    {
      "type": "color",
      "id": "star_symbol_color",
      "default": "#fff",
      "label": "Stars inside symbol color"
    },
    {
      "type": "range",
      "id": "stars_rating",
      "label": "Stars rating",
      "min": 1,
      "max": 5,
      "step": 0.5,
      "default": 5,
      "info": "Rating of the stars displayed in the Subheading."
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "header",
      "content": "Review cards"
    },
    {
      "type": "range",
      "id": "cards_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Corner radius",
      "default": 16
    },
    {
      "type": "select",
      "id": "card_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center",
      "label": "Text alignment"
    },
    {
      "type": "checkbox",
      "id": "show_stars",
      "default": true,
      "label": "Show stars",
      "info": "Stars rating & color is changed in block settings."
    },
    {
      "type": "select",
      "id": "cards_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-2",
      "label": "Cards color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Slider"
    },
    {
      "type": "paragraph",
      "content": "ATTENTION: ONLY in the theme editor, pagination dots might duplicate after changing section settings. To overcome this, simply click Save. This has NO EFFECT on the published website."
    },
    {
      "type": "select",
      "id": "type",
      "options": [
        {
          "value": "slide",
          "label": "Classic"
        },
        {
          "value": "loop",
          "label": "Infinite"
        }
      ],
      "default": "slide",
      "label": "Type"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": false
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 1,
      "max": 15,
      "step": 0.5,
      "default": 5,
      "unit": "sec",
      "label": "Autoplay speed"
    },
    {
      "type": "select",
      "id": "arrows_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "inverse",
      "label": "Arrows color scheme"
    },
    {
      "type": "checkbox",
      "id": "transparent_arrows",
      "label": "Transparent arrows",
      "default": true
    },
    {
      "type": "select",
      "id": "dots_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "inverse",
      "label": "Dots color scheme"
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "checkbox",
      "id": "desktop_full_page",
      "label": "Full page width",
      "default": false
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 3,
      "label": "Slides per page"
    },
    {
      "type": "checkbox",
      "id": "slider_desktop",
      "label": "Enable desktop slider",
      "default": false,
      "info": "The settings bellow are applied if this option is checked"
    },
    {
      "type": "range",
      "id": "per_move_desktop",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 1,
      "label": "Slides to scroll on one move"
    },
    {
      "type": "range",
      "id": "desktop_spacing",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Spacing",
      "default": 24
    },
    {
      "type": "range",
      "id": "desktop_side_padding",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Preview of prev & next slide",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "desktop_padding_calc",
      "label": "Disable empty preview on first & last slide",
      "default": true,
      "info": "Moves the first slide to the left edge adn last one to the right if Preview value is enabled. Visible if type is set to Slider."
    },
    {
      "type": "checkbox",
      "id": "desktop_adaptive_height",
      "label": "Adaptive height",
      "default": false,
      "info": "Only available if Slides per page is set to 1."
    },
    {
      "type": "select",
      "id": "desktop_dots_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        }
      ],
      "default": "under",
      "label": "Dots position"
    },
    {
      "type": "select",
      "id": "desktop_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        },
        {
          "value": "sides",
          "label": "On sides of the slider"
        }
      ],
      "default": "sides",
      "label": "Arrows position"
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "checkbox",
      "id": "slider_mobile",
      "label": "Enable mobile slider",
      "default": true,
      "info": "The settings bellow are applied if this option is checked"
    },
    {
      "type": "checkbox",
      "id": "enable_mobile_preview",
      "label": "Preview of prev & next slides",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "mobile_adaptive_height",
      "label": "Adaptive height",
      "default": false
    },
    {
      "type": "select",
      "id": "mobile_dots_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        }
      ],
      "default": "under",
      "label": "Dots position"
    },
    {
      "type": "select",
      "id": "mobile_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        },
        {
          "value": "sides",
          "label": "On sides of the slider"
        }
      ],
      "default": "under",
      "label": "Arrows position"
    },
    {
      "type": "header",
      "content": "Reviews"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "header",
      "content": "Custom cards color scheme",
      "info": "Applied if Cards color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_cards_colors_background",
      "default": "#F3F3F3",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_cards_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_cards_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "Review",
      "settings": [
        {
          "type": "color",
          "id": "star_color",
          "default": "#00b67a",
          "label": "Active stars container color"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#c8c8c8",
          "label": "Background stars container color"
        },
        {
          "type": "color",
          "id": "star_symbol_color",
          "default": "#fff",
          "label": "Stars inside symbol color"
        },
        {
          "type": "range",
          "id": "stars_rating",
          "label": "Stars rating",
          "min": 1,
          "max": 5,
          "step": 0.5,
          "default": 5,
          "info": "Rating of the stars displayed in the Subheading."
        },
        {
          "type": "text",
          "id": "title",
          "default": "Heading",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Share positive thoughts and feedback from your customer..</p>",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "image_picker",
          "id": "author_avatar",
          "label": "Author avatar"
        },
        {
          "type": "inline_richtext",
          "id": "author",
          "default": "<em><strong>Author</strong></em>",
          "label": "Author"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Trustpilot reviews",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}
