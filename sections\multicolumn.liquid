{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  #multicolumn-{{ section.id }} {
    --text-boxes-radius: {{ section.settings.cards_corner_radius }}px;
  }
  #multicolumn-{{ section.id }} .multicolumn-card h3 {
    font-size: {{ section.settings.desktop_heading_size }}px;
  }
  #multicolumn-{{ section.id }} .multicolumn-card .rte {
    font-size: {{ section.settings.desktop_text_size }}px;
  }
  #multicolumn-{{ section.id }} .multicolumn-card h3 + .rte {
    margin-top: {{ section.settings.desktop_text_top_margin }}px;
  }
  #multicolumn-{{ section.id }} .multicolumn-card__info {
    padding: {{ section.settings.desktop_container_padding_y }}px {{ section.settings.desktop_container_padding_x }}px;
  }

  @media screen and (max-width: 749px) {
    #multicolumn-{{ section.id }} .multicolumn-card h3 {
      font-size: {{ section.settings.mobile_heading_size }}px;
    }
    #multicolumn-{{ section.id }} .multicolumn-card .rte {
      font-size: {{ section.settings.mobile_text_size }}px;
    }
    #multicolumn-{{ section.id }} .multicolumn-card h3 + .rte {
      margin-top: {{ section.settings.mobile_text_top_margin }}px;
    }
    #multicolumn-{{ section.id }} .multicolumn-card__info {
      padding: {{ section.settings.mobile_container_padding_y }}px {{ section.settings.mobile_container_padding_x }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}

  .cards-color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_cards_colors_background.red }}, {{ section.settings.custom_cards_colors_background.green }}, {{ section.settings.custom_cards_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_cards_gradient_background != blank %}{{ section.settings.custom_cards_gradient_background }}{% else %}{{ section.settings.custom_cards_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_cards_colors_text.red }}, {{ section.settings.custom_cards_colors_text.green }}, {{ section.settings.custom_cards_colors_text.blue }};
  }
  .cards-color-scheme-{{ section.id }}.color-custom h3 {
    --color-foreground: {{ section.settings.custom_cards_colors_heading.red }}, {{ section.settings.custom_cards_colors_heading.green }}, {{ section.settings.custom_cards_colors_heading.blue }};
  }
{%- endstyle -%}

<div id="multicolumn-{{ section.id }}" class="multicolumn animate-section animate--hidden color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %} content-for-grouping {{ section.settings.visibility }}" data-mobile-columns="{{ section.settings.columns_mobile }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  {%- liquid
    assign has_mobile_slider = false
    assign has_desktop_slider = false
    if section.settings.slider_mobile
      assign has_mobile_slider = true
      if section.settings.mobile_dots_position == 'under' or section.settings.mobile_arrows_position == 'under'
        assign has_mobile_controls = true
      endif
    endif
    if section.settings.slider_desktop
      assign has_desktop_slider = true
      if section.settings.desktop_dots_position == 'under' or section.settings.desktop_arrows_position == 'under'
        assign has_desktop_controls = true
      endif
    endif
  
    assign highest_ratio = 0
    for block in section.blocks
      if block.settings.video.aspect_ratio > highest_ratio
        assign highest_ratio = block.settings.video.aspect_ratio
      elsif block.settings.image.aspect_ratio > highest_ratio
        assign highest_ratio = block.settings.image.aspect_ratio
      endif
    endfor
    assign bg_diff_class = ' multicolumn--same-bgs'
    unless section.settings.color_scheme == section.settings.cards_color_scheme
      assign bg_diff_class = ' multicolumn--diff-bgs'
    endunless
    if section.settings.color_scheme == 'custom' and section.settings.cards_color_scheme == 'custom'
      unless section.settings.custom_colors_background == section.settings.custom_cards_colors_background
        assign bg_diff_class = ' multicolumn--diff-bgs'
      endunless
    endif
  -%}
  <div class="page-width{% if section.settings.desktop_full_page %} desktop-full-page{% endif %}{% if has_mobile_slider %} mobile-full-page{% endif %} section-{{ section.id }}-padding isolate" style="--columns-desktop: {{ section.settings.columns_desktop }};--columns-mobile: {{ section.settings.columns_mobile }};--gap-desktop:{{ section.settings.desktop_spacing }}px;--gap-mobile:{{ section.settings.mobile_spacing }}px;--stretched-cards-alignment:{{ section.settings.stretched_cards_content_alignment }};">
    {% assign content_index = 0 %}
    {%- unless section.settings.title == blank -%}
      {% assign content_index = 1 %}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin animate-item animate-item--child index-0">
        <h2 class="title {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
          {{ section.settings.title  }}
        </h2>
        {%- if section.settings.button_label != blank -%}
          <a href="{{ section.settings.button_link }}" class="link underlined-link{% unless has_mobile_controls %} mobile-hidden{% endunless %}{% unless has_desktop_controls %} desktop-hidden{% endunless %}">
            {{- section.settings.button_label | escape -}}
          </a>
        {%- endif -%}
      </div>
    {%- endunless -%}
    {% if has_mobile_slider or has_desktop_slider %}
      <splide-component
        data-type='{{ section.settings.type }}'
        data-autoplay='{{ section.settings.autoplay }}'
        data-autoplay-speed='{{ section.settings.autoplay_speed }}'
        data-arrows-color="{{ section.settings.arrows_color_scheme }}"
        data-dots-color="{{ section.settings.dots_color_scheme }}"
        data-slides-desktop="{{ section.settings.columns_desktop }}"
        data-per-move-desktop='{{ section.settings.per_move_desktop }}'
        data-gap-desktop='{{ section.settings.desktop_spacing }}'
        data-side-padding-desktop='{{ section.settings.desktop_side_padding }}'
        data-padding-calc-desktop='{{ section.settings.desktop_padding_calc }}'
        data-slides-mobile="{{ section.settings.columns_mobile }}"
        data-gap-mobile='15'
        {% if section.settings.enable_mobile_preview %}
          data-side-padding-mobile='24'
        {% else %}
          data-side-padding-mobile='15'
        {% endif %}
        {% if has_desktop_slider == false %}
          data-destroy-desktop="true"
        {% elsif has_mobile_slider == false %}
          data-destroy-mobile="true"
        {% endif %}
        data-pause-videos='true'
      >
    {% endif %}
      <div 
        class='splide splide--desktop-dots-{{ section.settings.desktop_dots_position }} splide--mobile-dots-{{ section.settings.mobile_dots_position }} splide--desktop-arrows-{{ section.settings.desktop_arrows_position }} splide--desktop-arrows-outside splide--mobile-arrows-{{ section.settings.mobile_arrows_position }}{% if section.settings.transparent_arrows %} splide--transparent-arrows{% endif %}{% if section.settings.vertical-alignment == 'center' %} splide--vertically-centered{% endif %}{% if has_desktop_slider == false %} splide--destroy-desktop{% endif %}{% if has_mobile_slider == false %} splide--destroy-mobile{% endif %}{% if section.settings.stretch_cards %} splide--align-stretch{% endif %}' 
        {% if section.settings.desktop_adaptive_height and section.settings.slides_desktop == 1 and has_desktop_slider %}
          data-desktop-adaptive-height="true"
        {% endif %}
        {% if section.settings.mobile_adaptive_height and has_mobile_slider %}
          data-mobile-adaptive-height="true"
        {% endif %}
      >
        <div class="splide__track">
          <ul class="splide__list">
            {%- for block in section.blocks -%}
              {%- assign empty_content = '' -%}
              {%- if block.settings.title == blank
                and block.settings.text == blank
                and block.settings.link_label == blank
              -%}
                {%- assign empty_content = ' hidden' -%}
              {%- endif -%}
              <li class="splide__slide">
                <div class="splide__slide__container" style="--index:{{ forloop.index0 | plus: content_index }};" {{ block.shopify_attributes }}>
                  <div class="multicolumn-card multicolumn-card--media-{{ section.settings.media_position }} content-container color-{{ section.settings.cards_color_scheme }} cards-color-scheme-{{ section.id }} gradient{{ bg_diff_class }}{% if section.settings.column_alignment == 'center' %} center{% endif %} animate-item animate-item--child">
                    {%- if block.settings.image != blank or block.settings.video != blank -%}
                      {% if section.settings.image_ratio == 'circle' %}
                        {% assign spaced_image = true %}
                      {% endif %}
                      <div class="multicolumn-card__image-wrapper multicolumn-card__image-wrapper--{{ section.settings.image_width }}-width{% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}">
                        <div
                          class="media media--transparent media--{{ section.settings.image_ratio }}"
                          {% if section.settings.image_ratio == 'adapt' %}
                            style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"
                          {% endif %}
                        >
                          {% if block.settings.video == blank %}
                            {%- capture sizes -%}
                              (min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %}, (min-width:
                              750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %}, calc(100vw - 30px)
                            {%- endcapture -%}
                            {{
                              block.settings.image
                              | image_url: width: 1420
                              | image_tag:
                                loading: 'lazy',
                                sizes: sizes,
                                widths: '275, 550, 710, 1420',
                                class: 'multicolumn-card__image'
                            }}
                          {% else %}
                            {% liquid
                              assign action_on_inactive = "none"
                              if block.settings.muted_autoplay == false
                                assign action_on_inactive = "pause"
                              endif
                            %}
                            <internal-video
                              class="internal-video"
                              data-autoplay="{{ block.settings.muted_autoplay }}"
                              data-action-on-inactive='{{ action_on_inactive }}'
                              data-no-play-btn="{{ block.settings.muted_autoplay }}"
                            >
                              {% liquid
                                if block.settings.video_thumbnail != blank
                                  assign thumbnail = block.settings.video_thumbnail
                                else
                                  assign thumbnail = block.settings.video.preview_image
                                endif
                              %}
                              <video 
                                width="100%" 
                                height="auto" 
                                preload='metadata'
                                poster="{{ thumbnail | image_url }}"
                                {% if block.settings.muted_autoplay %}
                                  muted autoplay loop
                                {% endif %}
                                playsinline disablepictureinpicture
                              >
                                {% for source in block.settings.video.sources %}
                                  <source 
                                    {% if block.settings.muted_autoplay %}data-{% endif %}src="{{ source.url }}" 
                                    type="{{ source.mime_type }}"
                                  >
                                {% endfor %}
                              </video>
                              <button class="internal-video__play"{% if block.settings.muted_autoplay %} style='visibility: hidden;'{% endif %}>
                                <div class="play-button color-accent-1">
                                  {%- render 'icon-play' -%}
                                </div>
                              </button>
                              {% unless block.settings.muted_autoplay %}
                                <div class='internal-video__timeline'>&nbsp</div>
                              {% endunless %}
                            </internal-video>
                          {% endif %}
                        </div>
                      </div>
                    {%- endif -%}
                    <div class="multicolumn-card__info{{ empty_content }}">
                      {%- if block.settings.title != blank -%}
                        <h3>{{ block.settings.title }}</h3>
                      {%- endif -%}
                      {%- if block.settings.text != blank -%}
                        <div class="rte">{{ block.settings.text }}</div>
                      {%- endif -%}
                      {%- if block.settings.link_label != blank -%}
                        <a
                          class="link animate-arrow"
                          {% if block.settings.link == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block.settings.link }}"
                          {% endif %}
                        >
                          {{- block.settings.link_label | escape -}}
                          <span class="icon-wrap">&nbsp;{% render 'icon-arrow' %}</span></a
                        >
                      {%- endif -%}
                    </div>
                  </div>
                </div>
              </li>
            {%- endfor -%}
          </ul>
        </div>
        <div class='splide__dots-and-arrows'>
          <ul class="splide__pagination"></ul>
          <div class="splide__arrows"></div>
        </div>
      </div>
    {% if has_mobile_slider or has_desktop_slider %}
      </splide-component>
    {% endif %}
    <div class="center{% if has_mobile_controls %} mobile-hidden{% endif %}{% if has_desktop_controls %} desktop-hidden{% endif %} animate-item animate-item--child index-{{ content_index | plus: 1 }}">
      {%- if section.settings.button_label != blank -%}
        <a
          class="button button--primary"
          {% if section.settings.button_link == blank %}
            role="link" aria-disabled="true"
          {% else %}
            href="{{ section.settings.button_link }}"
          {% endif %}
        >
          {{ section.settings.button_label | escape }}
        </a>
      {%- endif -%}
      {%- if section.settings.atc_button_label != blank -%}
        {% if section.settings.atc_product == blank %}
          <button
            id="SectionAtcBtn-{{ section.id }}"
            type="button"
            class="button main-product-atc button--has-spinner"
            {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
              disabled
            {% endif %}
          >
            {{ section.settings.atc_button_label | escape }}
            <div class="loading-overlay__spinner">
              <svg
                aria-hidden="true"
                focusable="false"
                class="spinner"
                viewBox="0 0 66 66"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
              </svg>
            </div>
          </button>
        {% else %}
          {% assign product_form_id = 'section-product-form-'
            | append: section.id
          %}
          {% render 'separate-atc-btn',
            product: section.settings.atc_product,
            product_form_id: product_form_id,
            label: section.settings.atc_button_label,
            skip_cart: section.settings.atc_skip_cart
          %}
        {% endif %}
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.multicolumn.name",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Multicolumn",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.multicolumn.settings.button_label.label",
      "info": "Visible next to title when slider controls are under the slider."
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.multicolumn.settings.button_link.label"
    },
    {
      "type": "text",
      "id": "atc_button_label",
      "label": "Add to Cart button label",
      "info": "Visible next to title when slider controls are under the slider."
    },
    {
      "type": "product",
      "id": "atc_product",
      "label": "ATC Custom product",
      "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
    },
    {
      "type": "checkbox",
      "id": "atc_skip_cart",
      "label": "ATC Custom product skip cart"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Column cards"
    },
    {
      "type": "select",
      "id": "cards_color_scheme",
      "options": [
        {
          "value": "bg-overlay",
          "label": "Light overlay"
        },
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "bg-overlay",
      "label": "Cards color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "range",
      "id": "cards_corner_radius",
      "min": 0,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Corner radius",
      "default": 20
    },
    {
      "type": "checkbox",
      "id": "stretch_cards",
      "default": false,
      "label": "Make all cards same height"
    },
    {
      "type": "select",
      "id": "stretched_cards_content_alignment",
      "label": "Vertical alignment of stretched cards",
      "options": [
        {
          "label": "Top",
          "value": "flex-start"
        },
        {
          "label": "Center",
          "value": "center"
        },
        {
          "label": "Bottom",
          "value": "flex-end"
        },
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Columns media"
    },
    {
      "type": "select",
      "id": "image_width",
      "options": [
        {
          "value": "third",
          "label": "One-third width of column"
        },
        {
          "value": "half",
          "label": "Half width of column"
        },
        {
          "value": "full",
          "label": "Full width of column"
        }
      ],
      "default": "full",
      "label": "Media width"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to media"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "circle",
          "label": "Circle"
        }
      ],
      "default": "square",
      "label": "Media ratio"
    },
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "top",
      "label": "Media position"
    },
    {
      "type": "header",
      "content": "Column content"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multicolumn.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multicolumn.settings.column_alignment.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multicolumn.settings.column_alignment.label"
    },
    {
      "type": "range",
      "id": "desktop_heading_size",
      "min": 14,
      "max": 36,
      "step": 2,
      "unit": "px",
      "label": "Desktop headings size",
      "default": 24
    },
    {
      "type": "range",
      "id": "mobile_heading_size",
      "min": 12,
      "max": 32,
      "step": 1,
      "unit": "px",
      "label": "Mobile headings size",
      "default": 22
    },
    {
      "type": "range",
      "id": "desktop_text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Desktop text size",
      "default": 16
    },
    {
      "type": "range",
      "id": "mobile_text_size",
      "min": 8,
      "max": 22,
      "step": 1,
      "unit": "px",
      "label": "Mobile text size",
      "default": 15
    },
    {
      "type": "range",
      "id": "desktop_text_top_margin",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Desktop spacing between the heading & text",
      "default": 10
    },
    {
      "type": "range",
      "id": "mobile_text_top_margin",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Mobile spacing between the heading & text",
      "default": 10
    },
    {
      "type": "header",
      "content": "Column content containers"
    },
    {
      "type": "range",
      "id": "desktop_container_padding_y",
      "min": 0,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Desktop vertical padding",
      "default": 24
    },
    {
      "type": "range",
      "id": "desktop_container_padding_x",
      "min": 0,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Desktop horizontal padding",
      "default": 24
    },
    {
      "type": "range",
      "id": "mobile_container_padding_y",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Mobile vertical padding",
      "default": 24
    },
    {
      "type": "range",
      "id": "mobile_container_padding_x",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Mobile horizontal padding",
      "default": 24
    },
    {
      "type": "header",
      "content": "Slider"
    },
    {
      "type": "paragraph",
      "content": "ATTENTION: ONLY in the theme editor, pagination dots might duplicate after changing section settings. To overcome this, simply click Save. This has NO EFFECT on the published website."
    },
    {
      "type": "select",
      "id": "type",
      "options": [
        {
          "value": "slide",
          "label": "Classic"
        },
        {
          "value": "loop",
          "label": "Infinite"
        }
      ],
      "default": "slide",
      "label": "Type"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": false
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 1,
      "max": 15,
      "step": 0.5,
      "default": 5,
      "unit": "sec",
      "label": "Autoplay speed"
    },
    {
      "type": "select",
      "id": "arrows_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "inverse",
      "label": "Arrows color scheme"
    },
    {
      "type": "checkbox",
      "id": "transparent_arrows",
      "label": "Transparent arrows",
      "default": true
    },
    {
      "type": "select",
      "id": "dots_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Text"
        }
      ],
      "default": "inverse",
      "label": "Dots color scheme"
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "checkbox",
      "id": "desktop_full_page",
      "label": "Full page width",
      "default": false
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 3,
      "label": "Columns desktop"
    },
    {
      "type": "checkbox",
      "id": "slider_desktop",
      "label": "Enable desktop slider",
      "default": false,
      "info": "The settings bellow are applied if this option is checked"
    },
    {
      "type": "range",
      "id": "per_move_desktop",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 1,
      "label": "Slides to scroll on one move"
    },
    {
      "type": "range",
      "id": "desktop_spacing",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Spacing",
      "default": 40
    },
    {
      "type": "range",
      "id": "desktop_side_padding",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Preview of prev & next slide",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "desktop_padding_calc",
      "label": "Disable empty preview on first & last slide",
      "default": true,
      "info": "Moves the first slide to the left edge adn last one to the right if Preview value is enabled. Visible if type is set to Slider."
    },
    {
      "type": "checkbox",
      "id": "desktop_adaptive_height",
      "label": "Adaptive height",
      "default": false,
      "info": "Only available if Slides per page is set to 1."
    },
    {
      "type": "select",
      "id": "desktop_dots_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        }
      ],
      "default": "under",
      "label": "Dots position"
    },
    {
      "type": "select",
      "id": "desktop_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        },
        {
          "value": "sides",
          "label": "On sides of the slider"
        }
      ],
      "default": "sides",
      "label": "Arrows position"
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "slider_mobile",
      "label": "Enable mobile slider",
      "default": true,
      "info": "The settings bellow are applied if this option is checked"
    },
    {
      "type": "checkbox",
      "id": "enable_mobile_preview",
      "label": "Preview of prev & next slides",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "mobile_adaptive_height",
      "label": "Adaptive height",
      "default": false
    },
    {
      "type": "select",
      "id": "mobile_dots_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        }
      ],
      "default": "under",
      "label": "Dots position"
    },
    {
      "type": "select",
      "id": "mobile_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        },
        {
          "value": "sides",
          "label": "On sides of the slider"
        }
      ],
      "default": "under",
      "label": "Arrows position"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    },
    {
      "type": "header",
      "content": "Custom cards color scheme",
      "info": "Applied if Cards color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_cards_colors_background",
      "default": "#F3F3F3",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_cards_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_cards_colors_heading",
      "default": "#222126",
      "label": "Headings"
    },
    {
      "type": "color",
      "id": "custom_cards_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.multicolumn.blocks.column.name",
      "settings": [
        {
          "type": "header",
          "content": "Media",
          "info": "NOTE: Media is only available if Style is set to Cards"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "header",
          "content": "or"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "image_picker",
          "id": "video_thumbnail",
          "label": "Video thumbnail",
          "info": "If empty, the first frame of the video will be displayed."
        },
        {
          "type": "checkbox",
          "id": "muted_autoplay",
          "label": "Muted autoplay",
          "default": true,
          "info": "Use this instead of GIFs & animated WEBPs."
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Column",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image/video to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.multicolumn.blocks.column.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.multicolumn.blocks.column.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multicolumn.presets.name",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}
