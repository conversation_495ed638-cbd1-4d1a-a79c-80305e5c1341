.customer:not(.account):not(.order) {
  margin: 0 auto;
  max-width: 33.4rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  text-align: center;
}

@media screen and (min-width: 750px) {
  .customer:not(.account):not(.order) {
    max-width: 47.8rem;
  }
}

.customer > h1,
.customer__title {
  margin-top: 0;
}

.customer form {
  margin-top: 4rem;
}

.customer button {
  margin: 4rem 0 1.5rem;
}

.customer ul {
  line-height: calc(1 + 0.6 / var(--font-body-scale));
  padding-left: 4.4rem;
  text-align: left;
  margin-bottom: 4rem;
}

.customer ul a {
  display: inline;
}

.customer strong {
  font-weight: normal;
  color: rgb(var(--color-foreground));
}

.customer h2.form__message {
  font-size: calc(var(--font-heading-scale) * 1.8rem);
}

@media only screen and (min-width: 750px) {
  .customer h2.form__message {
    font-size: calc(var(--font-heading-scale) * 2.2rem);
  }
}

.customer .field {
  margin: 2rem 0 0 0;
}

.customer .field:first-of-type {
  margin-top: 0;
}

/* Customer Table */
.customer table {
  table-layout: auto;
  border-collapse: collapse;
  border-bottom: 0.01rem solid rgba(var(--color-foreground), 0.08);
  box-shadow: none;
  width: 100%;
  font-size: 1.6rem;
  position: relative;
}

@media screen and (min-width: 750px) {
  .customer table {
    border: none;
    box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.08);
  }
}

@media screen and (forced-colors: active) {
  .customer table {
    border-top: 0.1rem solid transparent;
    border-bottom: 0.1rem solid transparent;
  }
}

.customer tbody {
  color: rgb(var(--color-foreground));
}

.customer th,
.customer td {
  font-weight: 400;
  line-height: 1;
  border: none;
  padding: 0;
}

@media screen and (min-width: 750px) {
  .customer td {
    padding-right: 2.2rem;
  }
}

.customer tbody td {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.customer td:empty {
  display: initial;
}

.customer thead th {
  font-size: 1.2rem;
  letter-spacing: 0.07rem;
  text-transform: uppercase;
}

.customer tbody td:first-of-type {
  padding-top: 4rem;
}

@media screen and (min-width: 750px) {
  .customer th,
  .customer td:first-of-type {
    text-align: left;
    padding-left: 0;
    padding-right: 2.2rem;
  }

  .customer thead th,
  .customer tbody td {
    padding-top: 2.4rem;
    padding-bottom: 2.4rem;
  }

  .customer th:first-of-type,
  .customer td:first-of-type {
    padding-left: 2.2rem;
  }

  .customer tbody td {
    vertical-align: top;
  }

  .customer tbody td:first-of-type {
    padding-top: 2.4rem;
  }
}

.customer tbody td:last-of-type {
  padding-bottom: 4rem;
}

@media screen and (min-width: 750px) {
  .customer tbody td:last-of-type {
    padding-bottom: 0;
  }
}

.customer tbody tr {
  border-top: 0.01rem solid rgba(var(--color-foreground), 0.08);
}

@media screen and (min-width: 750px) {
  .customer tbody tr:first-of-type {
    border-top: none;
  }
}

@media screen and (forced-colors: active) {
  .customer tbody tr {
    border-top: 0.1rem solid transparent;
  }
}

.customer tfoot td:first-of-type,
.customer tfoot td {
  padding-top: 0.6rem;
  padding-bottom: 0.6rem;
}

.customer tfoot td:first-of-type {
  text-align: left;
}

.customer tfoot tr:first-of-type td {
  padding-top: 4rem;
}

@media screen and (min-width: 750px) {
  .customer tfoot tr:first-of-type td,
  .customer tfoot tr:first-of-type th {
    padding-top: 2.4rem;
  }
}

.customer tfoot tr:last-of-type td {
  padding-bottom: 4rem;
}

@media screen and (min-width: 750px) {
  .customer tfoot tr:last-of-type td,
  .customer tfoot tr:last-of-type th {
    padding-bottom: 2.4rem;
  }
}

/* works around colspan phantom border issues */
.customer thead::after,
.customer tfoot::before {
  content: " ";
  height: 0.1rem;
  width: 100%;
  display: block;
  position: absolute;
  left: 0;
  background: rgba(var(--color-foreground), 0.08);
}

@media screen and (forced-colors: active) {
  .customer thead::after,
  .customer tfoot::before {
    background: CanvasText;
  }
}

/* mobile table overrides */
@media screen and (max-width: 749px) {
  .customer thead,
  .customer th,
  .customer tfoot td:first-of-type {
    display: none;
  }

  .customer td {
    display: flex;
    text-align: right;
  }

  .customer td::before {
    color: rgba(var(--color-foreground), 0.9);
    content: attr(data-label);
    font-size: 1.4rem;
    padding-right: 2rem;
    text-transform: uppercase;
    flex-grow: 1;
    text-align: left;
  }

  .customer td:first-of-type {
    display: flex;
    align-items: center;
  }

  .customer tr {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%;
  }
}

/* Pagination */
.customer .pagination {
  margin-top: 5rem;
  margin-bottom: 7rem;
}

@media screen and (min-width: 990px) {
  .customer .pagination {
    margin-top: 7rem;
    margin-bottom: 10rem;
  }
}

.customer .pagination ul {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
}

.customer .pagination li {
  flex: 1 1;
  max-width: 4rem;
}

.customer .pagination li:not(:last-child) {
  margin-right: 1rem;
}

.customer .pagination li :first-child {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 4rem;
  width: 100%;
  padding: 0;
  text-decoration: none;
}

.customer .pagination li :first-child svg {
  height: 0.6rem;
}

.customer .pagination li:first-of-type svg {
  margin-left: -0.2rem;
  transform: rotate(90deg);
}

.customer .pagination li:last-of-type svg {
  margin-right: -0.2rem;
  transform: rotate(-90deg);
}

.customer .pagination li [aria-current]::after {
  content: "";
  display: block;
  width: 2rem;
  height: 0.01rem;
  position: absolute;
  bottom: 0.08rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: currentColor;
}

/* Login */
.login a {
  display: block;
  margin: 0 auto;
  width: fit-content;
}

.login a[href="#recover"] {
  margin-left: 0;
  margin-right: 0;
}

.login .field + a {
  margin-top: 1rem;
}

.login p {
  margin: 1.5rem 0;
}

.login h3 {
  margin-top: 1.5rem;
  text-align: left;
  font-size: calc(var(--font-heading-scale) * 1.6rem);
}

#customer_login_guest button {
  margin-top: 0;
}

#recover,
#recover + div {
  display: none;
}

#recover:target {
  display: block;
}

#recover:target + div {
  display: block;
}

#recover:target ~ #login,
#recover:target ~ #login + div {
  display: none;
}

#recover,
#login {
  scroll-margin-top: 20rem;
}

#recover {
  margin-bottom: 0;
}

.activate button[name="decline"],
.addresses li > button,
.addresses form button[type] {
  background-color: rgba(
    var(--color-background),
    var(--alpha-button-background)
  );
  color: rgb(var(--color-link));
}

@media only screen and (min-width: 750px) {
  .activate button[name="decline"] {
    margin-top: inherit;
    margin-left: 1rem;
  }
}

/* Account/Order */
:is(.account, .order) {
  margin: 0 auto;
  max-width: var(--page-width);
  padding-left: 2rem;
  padding-right: 2rem;
}

@media screen and (min-width: 750px) {
  :is(.account, .order) {
    padding-left: 5rem;
    padding-right: 5rem;
  }
}

@media screen and (min-width: 990px) {
  :is(.account, .order) > div:nth-of-type(2) {
    display: flex;
    margin-top: 5rem;
  }
}

@media screen and (min-width: 990px) {
  :is(.account, .order) > div:nth-of-type(2) > div:first-of-type {
    flex-grow: 1;
    padding-right: 3.2rem;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .order > div:nth-of-type(2) > div:last-of-type {
    display: flex;
  }

  .order > div:nth-of-type(2) > div:last-of-type div {
    padding-right: 3.2rem;
  }
}

:is(.account, .order) p {
  margin: 0 0 2rem;
  font-size: 1.6rem;
}

:is(.account, .order) h1 {
  margin-bottom: 1rem;
}

:is(.account, .order) h2 {
  margin-top: 4rem;
  margin-bottom: 1rem;
}

@media screen and (min-width: 990px) {
  :is(.account, .order) h2 {
    margin-top: 0;
  }
}

.account h1 + a {
  display: inline-flex;
  align-items: center;
}

.account a svg {
  width: 1.5rem;
  margin-bottom: -0.03rem;
  margin-right: 1rem;
}

@media screen and (min-width: 750px) {
  .account thead th:last-child,
  .account td:last-child {
    text-align: right;
  }

  .account table td:first-of-type {
    padding-top: 1.2rem;
    padding-bottom: 1.2rem;
  }
}

.account table td:first-of-type a {
  padding: 1.1rem 1.5rem;
  text-decoration: none;
  box-shadow: 0 0 0 0.1rem rgba(var(--color-link), 0.2);
  border: 0.1rem solid transparent;
  font-size: 1.2rem;
}

.account table td:first-of-type a:hover {
  box-shadow: 0 0 0 0.2rem rgba(var(--color-link), 0.2);
}

.order td:first-of-type {
  align-items: initial;
}

@media screen and (min-width: 750px) {
  .order thead th:nth-last-child(-n + 3),
  .order td:nth-last-child(-n + 3) {
    text-align: right;
  }
}

.order tfoot tr:last-of-type td,
.order tfoot tr:last-of-type th {
  font-size: 2.2rem;
  padding-top: 1.5rem;
  padding-bottom: 4rem;
}

@media screen and (min-width: 750px) {
  .order tfoot tr:last-of-type td,
  .order tfoot tr:last-of-type th {
    padding-bottom: 2.4rem;
  }
}

.order tfoot tr:last-of-type td:before {
  font-size: 2.2rem;
}

.order table p,
.order > div:nth-of-type(2) > div:first-of-type h2,
.order > div:nth-of-type(2) > div:last-of-type h2 + p {
  margin-bottom: 0;
}

.order > div:nth-of-type(2) > div:first-of-type h2 ~ p {
  margin-bottom: 0;
  font-size: 1.4rem;
}

.order > div:nth-of-type(2) > div:first-of-type h2 ~ p:last-of-type {
  margin-bottom: 3rem;
}

.order .item-props {
  font-size: 1.4rem;
  margin-top: 0.05px;
  display: flex;
  flex-direction: column;
}

.order .item-props > span {
  word-break: break-all;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
}

.order .fulfillment {
  width: fit-content;
  border: 0.01rem solid rgba(var(--color-foreground), 0.2);
  padding: 1rem;
  margin-top: 1rem;
  font-size: 1.4rem;
  text-align: left;
}

.order .fulfillment a {
  margin: 0.7rem 0;
}

.order .fulfillment span {
  display: block;
}

.order .cart-discount {
  display: block;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

@media screen and (min-width: 750px) {
  .order td .cart-discount {
    display: none;
  }
}

.order tbody ul {
  list-style: none;
  font-size: 1.2rem;
  text-align: right;
  padding-left: 0;
  margin-top: 1rem;
  margin-bottom: 0;
}

@media screen and (min-width: 750px) {
  .order tbody ul {
    text-align: left;
  }
}

.order table a {
  line-height: calc(1 + 0.3 / var(--font-body-scale));
}

.order tbody tr:first-of-type td:first-of-type > div {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

@media screen and (min-width: 750px) {
  .order tbody tr:first-of-type td:first-of-type > div {
    align-items: flex-start;
  }
}

.order .properties {
  font-size: 1.4rem;
  margin-top: 1rem;
}

.order .properties span {
  display: block;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
}

.order svg {
  width: 1.1rem;
  color: rgb(var(--color-base-accent-2));
  margin-right: 0.5rem;
}

.order dl {
  margin: 0;
}

.order dd {
  margin-left: 0;
  line-height: calc(1 + 0.3 / var(--font-body-scale));
}

.order dd s {
  color: rgba(var(--color-foreground), 0.7);
}

.order .unit-price {
  font-size: 1.1rem;
  letter-spacing: 0.07rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
  margin-top: 0.2rem;
  text-transform: uppercase;
  color: rgba(var(--color-foreground), 0.7);
}

.order .regular-price {
  font-size: 1.3rem;
}

/* Addresses */
.addresses li > button {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.addresses li > button + button,
.addresses form button + button {
  margin-top: 0rem;
}

@media screen and (min-width: 750px) {
  .addresses li > button:first-of-type {
    margin-top: 3rem;
  }
}

.addresses form button:first-of-type {
  margin-right: 1rem;
}

label[for="AddressCountryNew"],
label[for="AddressProvinceNew"] {
  display: block;
  font-size: 1.4rem;
  margin-bottom: 0.6rem;
}

.addresses form {
  display: flex;
  flex-flow: row wrap;
}

.addresses form > div {
  width: 100%;
  text-align: left;
}

.addresses ul {
  list-style: none;
  padding-left: 0;
  text-align: center;
}

li[data-address] {
  margin-top: 5rem;
}

.addresses [aria-expanded="false"] ~ div[id] {
  display: none;
}

.addresses [aria-expanded="true"] ~ div[id] {
  display: block;
}

.addresses h2 {
  text-align: left;
}

li[data-address] > h2 {
  text-align: center;
  font-size: calc(var(--font-heading-scale) * 1.8rem);
  margin-bottom: 0;
}

@media only screen and (min-width: 750px) {
  li[data-address] > h2 {
    font-size: calc(var(--font-heading-scale) * 2.2rem);
  }
}

.addresses ul p {
  margin-bottom: 0;
}

.addresses input[type="checkbox"] {
  margin-top: 2rem;
  margin-left: 0;
}

@media only screen and (min-width: 750px) {
  .addresses form > div:nth-of-type(1) {
    margin-right: 2rem;
  }

  .addresses form > div:nth-of-type(2) {
    margin-top: 0;
  }

  .addresses form > div:nth-of-type(1),
  .addresses form > div:nth-of-type(2) {
    box-sizing: border-box;
    flex-basis: calc(50% - 1rem);
  }
}

.addresses form > div:nth-of-type(7),
.addresses form > div:nth-of-type(7) + div[id] {
  margin-top: 1.5rem;
}
