/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Farver",
      "settings": {
        "colors_solid_button_labels": {
          "label": "Udfyldt knaptekst",
          "info": "Bruges som forgrundsfarve på markeringsfarver."
        },
        "colors_accent_1": {
          "label": "Markering 1",
          "info": "Bruges til udfyldt knapbaggrund."
        },
        "colors_accent_2": {
          "label": "Markering 2"
        },
        "header__1": {
          "content": "Primære farver"
        },
        "header__2": {
          "content": "Sekundære farver"
        },
        "colors_text": {
          "label": "Tekstfarve",
          "info": "Bruges som forgrundsfarve på baggrundsfarver."
        },
        "colors_outline_button_labels": {
          "label": "Rammeknap",
          "info": "Bruges også til tekstlinks."
        },
        "colors_background_1": {
          "label": "Baggrund 1"
        },
        "colors_background_2": {
          "label": "Baggrund 2"
        },
        "gradient_accent_1": {
          "label": "Markering 1 – graduering"
        },
        "gradient_accent_2": {
          "label": "Markering 2 – graduering"
        },
        "gradient_background_1": {
          "label": "Baggrund 1 – graduering"
        },
        "gradient_background_2": {
          "label": "Baggrund 2 – graduering"
        }
      }
    },
    "typography": {
      "name": "Typografi",
      "settings": {
        "type_header_font": {
          "label": "Skrifttype",
          "info": "Valget af en anden skrifttype kan påvirke hastigheden i din butik. [Få mere at vide om systemskrifttyper.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "header__1": {
          "content": "Overskrifter"
        },
        "header__2": {
          "content": "Brødtekst"
        },
        "type_body_font": {
          "label": "Skrifttype",
          "info": "Valget af en anden skrifttype kan påvirke hastigheden i din butik. [Få mere at vide om systemskrifttyper.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "heading_scale": {
          "label": "Størrelsesskala for skrifttype"
        },
        "body_scale": {
          "label": "Størrelsesskala for skrifttype"
        }
      }
    },
    "styles": {
      "name": "Ikoner",
      "settings": {
        "accent_icons": {
          "options__3": {
            "label": "Rammeknap"
          },
          "options__4": {
            "label": "Tekst"
          },
          "label": "Farve"
        }
      }
    },
    "social-media": {
      "name": "Sociale medier",
      "settings": {
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https://twitter.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "https://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "SoMe-konti"
        }
      }
    },
    "currency_format": {
      "name": "Valutaformat",
      "settings": {
        "content": "Valutakoder",
        "currency_code_enabled": {
          "label": "Vis valutakoder"
        },
        "paragraph": "Priser i indkøbskurv og betalingsproces viser altid valutakoder. Eksempel: 1,00 USD."
      }
    },
    "layout": {
      "name": "Layout",
      "settings": {
        "page_width": {
          "label": "Sidebredde"
        },
        "spacing_sections": {
          "label": "Mellemrum mellem skabelonafsnit"
        },
        "header__grid": {
          "content": "Gitter"
        },
        "paragraph__grid": {
          "content": "Påvirker områder med flere kolonner eller rækker."
        },
        "spacing_grid_horizontal": {
          "label": "Lodret afstand"
        },
        "spacing_grid_vertical": {
          "label": "Vandret afstand"
        }
      }
    },
    "search_input": {
      "name": "Søgeadfærd",
      "settings": {
        "header": {
          "content": "Søgningsforslag"
        },
        "predictive_search_enabled": {
          "label": "Aktivér søgningsforslag"
        },
        "predictive_search_show_vendor": {
          "label": "Vis produktforhandler",
          "info": "Synlig, når søgningsforslag er aktiveret."
        },
        "predictive_search_show_price": {
          "label": "Vis produktpris",
          "info": "Synlig, når søgningsforslag er aktiveret."
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Kant"
        },
        "header__shadow": {
          "content": "Skygge"
        },
        "blur": {
          "label": "Slør"
        },
        "corner_radius": {
          "label": "Hjørneradius"
        },
        "horizontal_offset": {
          "label": "Vandret forskydning"
        },
        "vertical_offset": {
          "label": "Lodret forskydning"
        },
        "thickness": {
          "label": "Tykkelse"
        },
        "opacity": {
          "label": "Uigennemsigtighed"
        },
        "image_padding": {
          "label": "Billedmargen"
        },
        "text_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Tekstjustering"
        }
      }
    },
    "badges": {
      "name": "Badges",
      "settings": {
        "position": {
          "options__1": {
            "label": "Nederst til venstre"
          },
          "options__2": {
            "label": "Nederst til højre"
          },
          "options__3": {
            "label": "Øverst til venstre"
          },
          "options__4": {
            "label": "Øverst til højre"
          },
          "label": "Placering på kort"
        },
        "sale_badge_color_scheme": {
          "label": "Farveskema for udsalg-badges"
        },
        "sold_out_badge_color_scheme": {
          "label": "Farveskema for udsolgt-badges"
        }
      }
    },
    "buttons": {
      "name": "Knapper"
    },
    "variant_pills": {
      "name": "Variantetiketter",
      "paragraph": "Variantetiketter er en måde at vise dine produktvarianter på. [Få mere at vide](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"
    },
    "inputs": {
      "name": "Inputs"
    },
    "content_containers": {
      "name": "Objektbeholder til indhold"
    },
    "popups": {
      "name": "Rullemenuer og pop-ops",
      "paragraph": "Påvirker områder som navigationsrullemenuer, pop op-modusser og indkøbskurve som pop-ops."
    },
    "media": {
      "name": "Medie"
    },
    "drawers": {
      "name": "Skuffer"
    },
    "cart": {
      "name": "Indkøbskurv",
      "settings": {
        "cart_type": {
          "label": "Indkøbskurvtype",
          "drawer": {
            "label": "Skuffe"
          },
          "page": {
            "label": "Side"
          },
          "notification": {
            "label": "Pop op-meddelelse"
          }
        },
        "show_vendor": {
          "label": "Vis forhandler"
        },
        "show_cart_note": {
          "label": "Aktivér bemærkning til indkøbskurv"
        },
        "cart_drawer": {
          "header": "Indkøbskurvskuffe",
          "collection": {
            "label": "Kollektion",
            "info": "Synlig, når indkøbskurvskuffen er tom."
          }
        }
      }
    },
    "cards": {
      "name": "Produktkort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "collection_cards": {
      "name": "Kollektionskort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "blog_cards": {
      "name": "Blogkort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Logobredde på computer",
          "info": "Logobredden optimeres automatisk til mobiltelefoner."
        },
        "favicon": {
          "label": "Billede for favoritikon",
          "info": "Skaleres ned til 32 x 32 px"
        }
      }
    },
    "brand_information": {
      "name": "Brandoplysninger",
      "settings": {
        "brand_headline": {
          "label": "Overskrift"
        },
        "brand_description": {
          "label": "Beskrivelse"
        },
        "brand_image": {
          "label": "Billede"
        },
        "brand_image_width": {
          "label": "Billedbredde"
        },
        "paragraph": {
          "content": "Føj en brandbeskrivelse til din butiks sidefod."
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Indre margen, afsnit",
        "padding_top": "Indre margen, top",
        "padding_bottom": "Indre margen, bund"
      },
      "spacing": "Mellemrum",
      "colors": {
        "accent_1": {
          "label": "Markering 1"
        },
        "accent_2": {
          "label": "Markering 2"
        },
        "background_1": {
          "label": "Baggrund 1"
        },
        "background_2": {
          "label": "Baggrund 2"
        },
        "inverse": {
          "label": "Omvendt"
        },
        "label": "Farveskema",
        "has_cards_info": "Hvis du vil ændre farveskemaet for kort, skal du opdatere dine temaindstillinger."
      },
      "heading_size": {
        "label": "Størrelse for overskrift",
        "options__1": {
          "label": "Lille"
        },
        "options__2": {
          "label": "Medium"
        },
        "options__3": {
          "label": "Stor"
        },
        "options__4": {
          "label": "Ekstra stor"
        }
      }
    },
    "announcement-bar": {
      "name": "Meddelelseslinje",
      "blocks": {
        "announcement": {
          "name": "Meddelelse",
          "settings": {
            "text": {
              "label": "Tekstfarve"
            },
            "text_alignment": {
              "label": "Tekstjustering",
              "options__1": {
                "label": "Venstre"
              },
              "options__2": {
                "label": "Centreret"
              },
              "options__3": {
                "label": "Højre"
              }
            },
            "link": {
              "label": "Link"
            }
          }
        }
      }
    },
    "collage": {
      "name": "Collage",
      "settings": {
        "heading": {
          "label": "Overskrift"
        },
        "desktop_layout": {
          "label": "Skrivebordslayout",
          "options__1": {
            "label": "Stor blok til venstre"
          },
          "options__2": {
            "label": "Stor blok til højre"
          }
        },
        "mobile_layout": {
          "label": "Mobillayout",
          "options__1": {
            "label": "Collage"
          },
          "options__2": {
            "label": "Kolonne"
          }
        },
        "card_styles": {
          "label": "Kortstilart",
          "info": "Produkt-,\nkollektion- og blogkortstil kan opdateres i temaindstillingerne.",
          "options__1": {
            "label": "Brug individuel kortstil"
          },
          "options__2": {
            "label": "Style alle som produktkort"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Billede",
          "settings": {
            "image": {
              "label": "Billede"
            }
          }
        },
        "product": {
          "name": "Produkt",
          "settings": {
            "product": {
              "label": "Produkt"
            },
            "secondary_background": {
              "label": "Vis sekundær baggrund"
            },
            "second_image": {
              "label": "Vis sekundær baggrund, når der peges"
            }
          }
        },
        "collection": {
          "name": "Kollektion",
          "settings": {
            "collection": {
              "label": "Kollektion"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Coverbillede"
            },
            "video_url": {
              "label": "Webadresse",
              "info": "Videoer afspilles i et pop op-vindue, hvis afsnittet indeholder andre blokke.",
              "placeholder": "Brug en YouTube- eller Vimeo-webadresse"
            },
            "description": {
              "label": "Alternativ tekst til video",
              "info": "Beskriv videoen for kunder med en skærmlæser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"
            }
          }
        }
      },
      "presets": {
        "name": "Collage"
      }
    },
    "collection-list": {
      "name": "Kollektionsliste",
      "settings": {
        "title": {
          "label": "Overskrift"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Firkantet"
          },
          "info": "Tilføj billeder ved at redigere dine kollektioner. [Få mere at vide](https://help.shopify.com/manual/products/collections)"
        },
        "swipe_on_mobile": {
          "label": "Aktivér swipe på mobilen"
        },
        "show_view_all": {
          "label": "Aktivér knappen \"Se alle\", hvis en liste indeholder flere kollektioner, end der vises"
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Antallet af kolonner på mobil",
          "options__1": {
            "label": "1 kolonne"
          },
          "options__2": {
            "label": "2 kolonner"
          }
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Kollektion",
          "settings": {
            "collection": {
              "label": "Kollektion"
            }
          }
        }
      },
      "presets": {
        "name": "Kollektionsliste"
      }
    },
    "contact-form": {
      "name": "Kontaktformular",
      "presets": {
        "name": "Kontaktformular"
      }
    },
    "custom-liquid": {
      "name": "Tilpasset Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Tilpasset Liquid",
          "info": "Tilføj appkodestykker eller anden Liquid-kode for at oprette avancerede tilpasninger."
        }
      },
      "presets": {
        "name": "Tilpasset Liquid"
      }
    },
    "featured-blog": {
      "name": "Blogopslag",
      "settings": {
        "heading": {
          "label": "Overskrift"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Antallet af blogopslag, der skal vises"
        },
        "show_view_all": {
          "label": "Aktivér knappen \"Se alle\", hvis bloggen indeholder flere blogopslag, end der vises"
        },
        "show_image": {
          "label": "Vis udvalgt billede",
          "info": "Brug et billede med et højde-bredde-forhold på 3:2 for at opnå det bedste resultat. [Få mere at vide](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "show_date": {
          "label": "Vis dato"
        },
        "show_author": {
          "label": "Vis forfatter"
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        }
      },
      "presets": {
        "name": "Blogopslag"
      }
    },
    "featured-collection": {
      "name": "Udvalgt kollektion",
      "settings": {
        "title": {
          "label": "Overskrift"
        },
        "collection": {
          "label": "Kollektion"
        },
        "products_to_show": {
          "label": "Maksimalt antal produkter, der skal vises"
        },
        "show_view_all": {
          "label": "Aktivér “Se alle”, hvis kollektionen indeholder flere produkter, end der vises"
        },
        "header": {
          "content": "Produktkort"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Firkantet"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundær baggrund, når der peges"
        },
        "show_vendor": {
          "label": "Vis forhandler"
        },
        "show_rating": {
          "label": "Vis produktbedømmelser",
          "info": "Hvis du vil vise bedømmelser, skal du tilføje en app til produktbedømmelse. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "enable_quick_buy": {
          "label": "Aktivér knap til hurtig tilføjelse",
          "info": "Optimal med indkøbskurv som pop op-vindue eller skuffe."
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        },
        "description": {
          "label": "Beskrivelse"
        },
        "show_description": {
          "label": "Vis kollektionsbeskrivelse fra administratoren"
        },
        "description_style": {
          "label": "Beskrivelsesstil",
          "options__1": {
            "label": "Brødtekst"
          },
          "options__2": {
            "label": "Underoverskrift"
          },
          "options__3": {
            "label": "Store bogstaver"
          }
        },
        "view_all_style": {
          "options__1": {
            "label": "Link"
          },
          "options__2": {
            "label": "Rammeknap"
          },
          "options__3": {
            "label": "Udfyldt knap"
          },
          "label": "Stilarten “Se alle”"
        },
        "enable_desktop_slider": {
          "label": "Aktivér karrusel på computer"
        },
        "full_width": {
          "label": "Gør produkter til fuld bredde"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Antallet af kolonner på mobil",
          "options__1": {
            "label": "1 kolonne"
          },
          "options__2": {
            "label": "2 kolonner"
          }
        },
        "swipe_on_mobile": {
          "label": "Aktivér swipe på mobilen"
        }
      },
      "presets": {
        "name": "Udvalgt kollektion"
      }
    },
    "footer": {
      "name": "Sidefod",
      "blocks": {
        "link_list": {
          "name": "Menu",
          "settings": {
            "heading": {
              "label": "Overskrift"
            },
            "menu": {
              "label": "Menu",
              "info": "Viser kun menupunkter på øverste niveau."
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "heading": {
              "label": "Overskrift"
            },
            "subtext": {
              "label": "Undertekst"
            }
          }
        },
        "brand_information": {
          "name": "Brandoplysninger",
          "settings": {
            "paragraph": {
              "content": "Denne blok vil vise dine brandoplysninger. (Rediger brandoplysninger.](/editor?context=theme&category=brand%20information)"
            },
            "header__1": {
              "content": "Ikoner for sociale medier"
            },
            "show_social": {
              "label": "Vis ikoner for sociale medier",
              "info": "Hvis du vil vise dine konti på sociale medier, skal du linke dem i dine [temaindstillinger](/editor?context=theme&category=social%20media)."
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Vis tilmelding med mail"
        },
        "newsletter_heading": {
          "label": "Overskrift"
        },
        "header__1": {
          "content": "Tilmelding med mail",
          "info": "Abonnenter, der automatisk er føjet til kundelisten “Accepterer markedsføring”. [Få mere at vide](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "header__2": {
          "content": "Ikoner for sociale medier",
          "info": "Hvis du vil vise dine konti på sociale medier, skal du linke dem i dine [temaindstillinger](/editor?context=theme&category=social%20media)."
        },
        "show_social": {
          "label": "Vis ikoner for sociale medier"
        },
        "header__3": {
          "content": "Land/område-vælger"
        },
        "header__4": {
          "info": "Hvis du vil tilføje et land/område, skal du gå til dine [markedsindstillinger.](/admin/settings/markets)"
        },
        "enable_country_selector": {
          "label": "Aktivér land/område-vælger"
        },
        "header__5": {
          "content": "Sprogvælger"
        },
        "header__6": {
          "info": "Hvis du vil tilføje et sprog, skal du gå til dine [sprogindstillinger](/admin/settings/languages)"
        },
        "enable_language_selector": {
          "label": "Aktivér sprogvælger"
        },
        "header__7": {
          "content": "Betalingsmetoder"
        },
        "payment_enable": {
          "label": "Vis betalingsikoner"
        },
        "margin_top": {
          "label": "Øverste margen"
        },
        "header__8": {
          "content": "Links til politikker",
          "info": "Gå til dine [politikindstillinger](/admin/settings/legal) for at tilføje butikspolitikker."
        },
        "show_policy": {
          "label": "Vis links til politikker"
        },
        "header__9": {
          "content": "Følg på Shop",
          "info": "Vis følgknappen for din butiksfacade i Shop-appen. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        },
        "enable_follow_on_shop": {
          "label": "Aktivér Følg på Shop"
        }
      }
    },
    "header": {
      "name": "Sidehoved",
      "settings": {
        "logo_position": {
          "label": "Placering af logo på computer",
          "options__1": {
            "label": "Midt på til venstre"
          },
          "options__2": {
            "label": "Øverst til venstre"
          },
          "options__3": {
            "label": "Øverst i midten"
          },
          "options__4": {
            "label": "Midt på centreret"
          }
        },
        "menu": {
          "label": "Menu"
        },
        "show_line_separator": {
          "label": "Vis adskillelseslinje"
        },
        "margin_bottom": {
          "label": "Nederste margen"
        },
        "menu_type_desktop": {
          "label": "Computermenutype",
          "info": "Menutypen optimeres automatisk til mobil.",
          "options__1": {
            "label": "Rullemenu"
          },
          "options__2": {
            "label": "Megamenu"
          }
        },
        "mobile_layout": {
          "content": "Mobillayout"
        },
        "mobile_logo_position": {
          "label": "Placering af logo på mobiltelefon",
          "options__1": {
            "label": "Centreret"
          },
          "options__2": {
            "label": "Venstre"
          }
        },
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Rediger dit logo i [temaindstillinger](/editor?context=theme&category=logo)."
        },
        "sticky_header_type": {
          "label": "Fastgjort sidehoved",
          "options__1": {
            "label": "Ingen"
          },
          "options__2": {
            "label": "Ved oprulning"
          },
          "options__3": {
            "label": "Altid"
          },
          "options__4": {
            "label": "Altid, reducer størrelsen på logo"
          }
        }
      }
    },
    "image-banner": {
      "name": "Billedbanner",
      "settings": {
        "image": {
          "label": "Første billede"
        },
        "image_2": {
          "label": "Andet billede"
        },
        "color_scheme": {
          "info": "Synlig, når containeren vises."
        },
        "stack_images_on_mobile": {
          "label": "Stabl billeder på mobilenheder"
        },
        "adapt_height_first_image": {
          "label": "Tilpas afsnitshøjden til størrelsen af det første billede",
          "info": "Overskriver indstillingerne for højden på billedbanneret, når det markeres."
        },
        "show_text_box": {
          "label": "Vis container på skrivebord"
        },
        "image_overlay_opacity": {
          "label": "Billedoverlejringens uigennemsigtighed"
        },
        "header": {
          "content": "Mobillayout"
        },
        "show_text_below": {
          "label": "Vis container på mobiltelefon"
        },
        "image_height": {
          "label": "Bannerhøjde",
          "options__1": {
            "label": "Tilpas til første billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Mellem"
          },
          "info": "Brug et billede med et højde-bredde-forhold på 3:2 for at opnå det bedste resultat. [Få mere at vide](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
          "options__4": {
            "label": "Stor"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Øverst til venstre"
          },
          "options__2": {
            "label": "Øverst i midten"
          },
          "options__3": {
            "label": "Øverst til højre"
          },
          "options__4": {
            "label": "Midt på til venstre"
          },
          "options__5": {
            "label": "Midt på centreret"
          },
          "options__6": {
            "label": "Midt på til højre"
          },
          "options__7": {
            "label": "Nederst til venstre"
          },
          "options__8": {
            "label": "Nederst i midten"
          },
          "options__9": {
            "label": "Nederst til højre"
          },
          "label": "Placering af indhold på computer"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på computer"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på mobil"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift"
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Beskrivelse"
            },
            "text_style": {
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "options__3": {
                "label": "Store bogstaver"
              },
              "label": "Teksttypografi"
            }
          }
        },
        "buttons": {
          "name": "Knapper",
          "settings": {
            "button_label_1": {
              "label": "Første knaptekst",
              "info": "Lad feltet være tomt for at skjule knappen."
            },
            "button_link_1": {
              "label": "Første knaplink"
            },
            "button_style_secondary_1": {
              "label": "Brug rammeknaptypografi"
            },
            "button_label_2": {
              "label": "Anden knaptekst",
              "info": "Lad feltet være tomt for at skjule knappen."
            },
            "button_link_2": {
              "label": "Andet knaplink"
            },
            "button_style_secondary_2": {
              "label": "Brug rammeknaptypografi"
            }
          }
        }
      },
      "presets": {
        "name": "Billedbanner"
      }
    },
    "image-with-text": {
      "name": "Billede med tekst",
      "settings": {
        "image": {
          "label": "Billede"
        },
        "height": {
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Mellem"
          },
          "label": "Billedhøjde",
          "options__4": {
            "label": "Stor"
          }
        },
        "layout": {
          "options__1": {
            "label": "Billede først"
          },
          "options__2": {
            "label": "Andet billede"
          },
          "label": "Billedplacering på computer",
          "info": "Billede først er standardmobillayoutet."
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Lille"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Billedbredde på computer",
          "info": "Billedet er automatisk optimeret til mobiltelefoner."
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på computer",
          "options__2": {
            "label": "Centreret"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "I midten"
          },
          "options__3": {
            "label": "Bund"
          },
          "label": "Placering af indhold på computer"
        },
        "content_layout": {
          "options__1": {
            "label": "Ingen overlapning"
          },
          "options__2": {
            "label": "Overlapning"
          },
          "label": "Indholdslayout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på mobil",
          "options__2": {
            "label": "Centreret"
          }
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift"
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Indhold"
            },
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              }
            }
          }
        },
        "button": {
          "name": "Knap",
          "settings": {
            "button_label": {
              "label": "Knaptekst",
              "info": "Lad feltet være tomt for at skjule knappen."
            },
            "button_link": {
              "label": "Knaplink"
            }
          }
        },
        "caption": {
          "name": "Billedtekst",
          "settings": {
            "text": {
              "label": "Tekst"
            },
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Underoverskrift"
              },
              "options__2": {
                "label": "Store bogstaver"
              }
            },
            "caption_size": {
              "label": "Tekststørrelse",
              "options__1": {
                "label": "Lille"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Stor"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Billede med tekst"
      }
    },
    "main-article": {
      "name": "Blogopslag",
      "blocks": {
        "featured_image": {
          "name": "Udvalgt billede",
          "settings": {
            "image_height": {
              "label": "Højde på udvalgt billede",
              "options__1": {
                "label": "Tilpas til billede"
              },
              "options__2": {
                "label": "Lille"
              },
              "options__3": {
                "label": "Medium"
              },
              "info": "Brug et billede med et højde-bredde-forhold på 16:9 for at opnå det bedste resultat. [Få mere at vide](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
              "options__4": {
                "label": "Stor"
              }
            }
          }
        },
        "title": {
          "name": "Titel",
          "settings": {
            "blog_show_date": {
              "label": "Vis dato"
            },
            "blog_show_author": {
              "label": "Vis forfatter"
            }
          }
        },
        "content": {
          "name": "Indhold"
        },
        "share": {
          "name": "Del",
          "settings": {
            "featured_image_info": {
              "content": "Hvis du inkluderer et link i opslag på sociale medier, vil sidens udvalgte billede blive vist som billedeksempel. [Få mere at vide](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Der er inkluderet en butikstitel og -beskrivelse med billedeksemplet. [Få mere at vide](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Tekst"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blogopslag",
      "settings": {
        "header": {
          "content": "Kort med blogopslag"
        },
        "show_image": {
          "label": "Vis udvalgt billede"
        },
        "paragraph": {
          "content": "Skift uddrag ved at redigere dine blogopslag. [Få mere at vide](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"
        },
        "show_date": {
          "label": "Vis dato"
        },
        "show_author": {
          "label": "Vis forfatter"
        },
        "layout": {
          "label": "Layout til computer",
          "options__1": {
            "label": "Gitter"
          },
          "options__2": {
            "label": "Kollage"
          },
          "info": "Opslag stables på mobilen."
        },
        "image_height": {
          "label": "Højde på udvalgt billede",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Stor"
          },
          "info": "Brug et billede med et højde-bredde-forhold på 3:2 for at opnå det bedste resultat. [Få mere at vide](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-cart-footer": {
      "name": "Subtotal",
      "blocks": {
        "subtotal": {
          "name": "Subtotal"
        },
        "buttons": {
          "name": "Betalingsknap"
        }
      }
    },
    "main-cart-items": {
      "name": "Varer"
    },
    "main-collection-banner": {
      "name": "Kollektionsbanner",
      "settings": {
        "paragraph": {
          "content": "Tilføj en beskrivelse eller et billede ved at redigere din kollektion. [Få mere at vide](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Vis kollektionsbeskrivelse"
        },
        "show_collection_image": {
          "label": "Vis kollektionsbillede",
          "info": "Brug et billede med et højde-bredde-forhold på 16:9 for at opnå det bedste resultat. [Få mere at vide](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Produktgitter",
      "settings": {
        "products_per_page": {
          "label": "Produkter pr. side"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Firkantet"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundær baggrund, når der peges"
        },
        "show_vendor": {
          "label": "Vis forhandler"
        },
        "enable_tags": {
          "label": "Aktivér filtrering",
          "info": "Tilpas filtrene med appen Search & Discovery. [Få mere at vide](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_filtering": {
          "label": "Aktivér filtrering",
          "info": "Tilpas filtrene med appen Search & Discovery. [Få mere at vide](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Aktivér sortering"
        },
        "header__1": {
          "content": "Filtrering og sortering"
        },
        "header__3": {
          "content": "Produktkort"
        },
        "show_rating": {
          "label": "Vis produktbedømmelser",
          "info": "Hvis du vil vise bedømmelser, skal du tilføje en app til produktbedømmelse. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"
        },
        "enable_quick_buy": {
          "label": "Aktivér knap til hurtig tilføjelse",
          "info": "Optimal med indkøbskurv som pop op-vindue eller skuffe."
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Antallet af kolonner på mobil",
          "options__1": {
            "label": "1 kolonne"
          },
          "options__2": {
            "label": "2 kolonner"
          }
        },
        "filter_type": {
          "label": "Layout til computerfilter",
          "options__1": {
            "label": "Vandret"
          },
          "options__2": {
            "label": "Lodret"
          },
          "options__3": {
            "label": "Skuffe"
          },
          "info": "Skuffe er standardlayoutet til mobil."
        }
      }
    },
    "main-list-collections": {
      "name": "Siden Kollektionsliste",
      "settings": {
        "title": {
          "label": "Overskrift"
        },
        "sort": {
          "label": "Sortér kollektioner efter:",
          "options__1": {
            "label": "Alfabetisk, A-Å"
          },
          "options__2": {
            "label": "Alfabetisk, Å-A"
          },
          "options__3": {
            "label": "Dato, nyere til ældre"
          },
          "options__4": {
            "label": "Dato, ældre til nyere"
          },
          "options__5": {
            "label": "Produktantal, høj til lav"
          },
          "options__6": {
            "label": "Produktantal, lav til høj"
          }
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Firkantet"
          },
          "info": "Tilføj billeder ved at redigere dine kollektioner. [Få mere at vide](https://help.shopify.com/manual/products/collections)"
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Antallet af kolonner på mobil",
          "options__1": {
            "label": "1 kolonne"
          },
          "options__2": {
            "label": "2 kolonner"
          }
        }
      }
    },
    "main-page": {
      "name": "Side"
    },
    "main-password-footer": {
      "name": "Sidefod på adgangskodeside"
    },
    "main-password-header": {
      "name": "Sidehoved på adgangskodesiden",
      "settings": {
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Rediger dit logo i temaindstillinger."
        }
      }
    },
    "main-product": {
      "name": "Produktoplysninger",
      "blocks": {
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekstfarve"
            },
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Pris"
        },
        "quantity_selector": {
          "name": "Antalsvælger"
        },
        "variant_picker": {
          "name": "Variantvælger",
          "settings": {
            "picker_type": {
              "label": "Type",
              "options__1": {
                "label": "Rullemenu"
              },
              "options__2": {
                "label": "Etiketter"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Køb-knapper",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Vis dynamiske betalingsknapper",
              "info": "Via de tilgængelige betalingsmetoder i din butik ser kunderne deres foretrukne mulighed, som f.eks. PayPal eller Apple Pay. [Få mere at vide](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "pickup_availability": {
          "name": "Mulighed for afhentning"
        },
        "description": {
          "name": "Beskrivelse"
        },
        "share": {
          "name": "Del",
          "settings": {
            "featured_image_info": {
              "content": "Hvis du inkluderer et link i opslag på sociale medier, vil sidens udvalgte billede blive vist som billedeksempel. [Få mere at vide](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Der er inkluderet en butikstitel og -beskrivelse med billedeksemplet. [Få mere at vide](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Tekst"
            }
          }
        },
        "collapsible_tab": {
          "name": "Række, der kan skjules",
          "settings": {
            "heading": {
              "info": "Inkluder en overskrift, der forklarer indholdet.",
              "label": "Overskrift"
            },
            "content": {
              "label": "Rækkeindhold"
            },
            "page": {
              "label": "Rækkeindhold fra side"
            },
            "icon": {
              "label": "Ikon",
              "options__1": {
                "label": "Ingen"
              },
              "options__2": {
                "label": "Æble"
              },
              "options__3": {
                "label": "Banan"
              },
              "options__4": {
                "label": "Flaske"
              },
              "options__5": {
                "label": "Æske"
              },
              "options__6": {
                "label": "Gulerod"
              },
              "options__7": {
                "label": "Chatboble"
              },
              "options__8": {
                "label": "Flueben"
              },
              "options__9": {
                "label": "Clipboard"
              },
              "options__10": {
                "label": "Mejeri"
              },
              "options__11": {
                "label": "Laktosefri"
              },
              "options__12": {
                "label": "Tørrer"
              },
              "options__13": {
                "label": "Øje"
              },
              "options__14": {
                "label": "Ild"
              },
              "options__15": {
                "label": "Glutenfri"
              },
              "options__16": {
                "label": "Hjerte"
              },
              "options__17": {
                "label": "Jern"
              },
              "options__18": {
                "label": "Blad"
              },
              "options__19": {
                "label": "Læder"
              },
              "options__20": {
                "label": "Lyn"
              },
              "options__21": {
                "label": "Læbestift"
              },
              "options__22": {
                "label": "Lås"
              },
              "options__23": {
                "label": "Kortnål"
              },
              "options__24": {
                "label": "Nøddefri"
              },
              "options__25": {
                "label": "Bukser"
              },
              "options__26": {
                "label": "Poteaftryk"
              },
              "options__27": {
                "label": "Peber"
              },
              "options__28": {
                "label": "Parfume"
              },
              "options__29": {
                "label": "Fly"
              },
              "options__30": {
                "label": "Plante"
              },
              "options__31": {
                "label": "Prismærke"
              },
              "options__32": {
                "label": "Spørgsmålstegn"
              },
              "options__33": {
                "label": "Genanvendelse"
              },
              "options__34": {
                "label": "Returnering"
              },
              "options__35": {
                "label": "Lineal"
              },
              "options__36": {
                "label": "Serveringsfad"
              },
              "options__37": {
                "label": "Skjorte"
              },
              "options__38": {
                "label": "Sko"
              },
              "options__39": {
                "label": "Silhuet"
              },
              "options__40": {
                "label": "Snefnug"
              },
              "options__41": {
                "label": "Stjerne"
              },
              "options__42": {
                "label": "Stopur"
              },
              "options__43": {
                "label": "Lastbil"
              },
              "options__44": {
                "label": "Vask"
              }
            }
          }
        },
        "popup": {
          "name": "Pop-op",
          "settings": {
            "link_label": {
              "label": "Navn på link"
            },
            "page": {
              "label": "Side"
            }
          }
        },
        "custom_liquid": {
          "name": "Tilpasset Liquid",
          "settings": {
            "custom_liquid": {
              "label": "Tilpasset Liquid",
              "info": "Tilføj appkodestykker eller anden Liquid-kode for at oprette avancerede tilpasninger."
            }
          }
        },
        "rating": {
          "name": "Produktvurdering",
          "settings": {
            "paragraph": {
              "content": "Hvis du vil vise bedømmelser, skal du tilføje en app til produktbedømmelse. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Supplerende produkter",
          "settings": {
            "paragraph": {
              "content": "Tilføj Search & Discovery-appen for at vælge supplerende produkter. [Få mere at vide](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Overskrift"
            },
            "make_collapsible_row": {
              "label": "Vis som række, der kan skjules"
            },
            "icon": {
              "info": "Synlig, når rækken, der kan skjules, vises."
            },
            "product_list_limit": {
              "label": "Maksimalt antal produkter, der skal vises"
            },
            "products_per_page": {
              "label": "Antal produkter pr. side"
            },
            "pagination_style": {
              "label": "Sideinddelingsstil",
              "options": {
                "option_1": "Prikker",
                "option_2": "Tæller",
                "option_3": "Tal"
              }
            },
            "product_card": {
              "heading": "Produktkort"
            },
            "image_ratio": {
              "label": "Billedforhold",
              "options": {
                "option_1": "Stående",
                "option_2": "Kvadrat"
              }
            },
            "enable_quick_add": {
              "label": "Aktivér knap til hurtig tilføjelse"
            }
          }
        },
        "icon_with_text": {
          "name": "Ikon med tekst",
          "settings": {
            "layout": {
              "label": "Layout",
              "options__1": {
                "label": "Vandret"
              },
              "options__2": {
                "label": "Lodret"
              }
            },
            "content": {
              "label": "Indhold",
              "info": "Vælg et ikon, eller tilføj et billede for hver kolonne eller række."
            },
            "heading": {
              "info": "Lad overskriftsfeltet være tomt for at skjule ikonkolonnen."
            },
            "icon_1": {
              "label": "Første ikon"
            },
            "image_1": {
              "label": "Første billede"
            },
            "heading_1": {
              "label": "Første overskrift"
            },
            "icon_2": {
              "label": "Andet ikon"
            },
            "image_2": {
              "label": "Andet billede"
            },
            "heading_2": {
              "label": "Anden overskrift"
            },
            "icon_3": {
              "label": "Tredje ikon"
            },
            "image_3": {
              "label": "Tredje billede"
            },
            "heading_3": {
              "label": "Tredje overskrift"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Underoverskrift"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        },
        "inventory": {
          "name": "Lagerstatus",
          "settings": {
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Underoverskrift"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            },
            "inventory_threshold": {
              "label": "Lav grænse for lagerbeholdning",
              "info": "Vælg 0 for altid at vise på lager, hvis tilgængelig."
            },
            "show_inventory_quantity": {
              "label": "Vis lagerantal"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Medie",
          "info": "Få mere at vide om [medietyper.](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Aktivér looping af videoer"
        },
        "enable_sticky_info": {
          "label": "Aktivér klæbende indhold på computer"
        },
        "hide_variants": {
          "label": "Skjul andre varianters medier, når du har valgt en variant"
        },
        "gallery_layout": {
          "label": "Layout til computer",
          "options__1": {
            "label": "Stablet"
          },
          "options__2": {
            "label": "2 kolonner"
          },
          "options__3": {
            "label": "Miniaturer"
          },
          "options__4": {
            "label": "Miniaturekarussel"
          }
        },
        "media_size": {
          "label": "Computermediets bredde",
          "options__1": {
            "label": "Lille"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Stor"
          },
          "info": "Medieindhold optimeres automatisk til mobil."
        },
        "mobile_thumbnails": {
          "label": "Mobillayout",
          "options__1": {
            "label": "2 kolonner"
          },
          "options__2": {
            "label": "Vis miniaturer"
          },
          "options__3": {
            "label": "Skjul miniaturer"
          }
        },
        "media_position": {
          "label": "Placering af medie på computer",
          "info": "Placeringen optimeres automatisk til mobil.",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Højre"
          }
        },
        "image_zoom": {
          "label": "Billedzoom",
          "info": "Klik og hold musen over standarder for at åbne lightbox på mobilen.",
          "options__1": {
            "label": "Åbn lightbox"
          },
          "options__2": {
            "label": "Klik og hold musen over"
          },
          "options__3": {
            "label": "Ingen zoom"
          }
        },
        "constrain_to_viewport": {
          "label": "Begræns mediet til skærmhøjden"
        },
        "media_fit": {
          "label": "Medietilpasning",
          "options__1": {
            "label": "Oprindelig"
          },
          "options__2": {
            "label": "Udfyld"
          }
        }
      }
    },
    "main-search": {
      "name": "Søgeresultater",
      "settings": {
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Firkantet"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundær baggrund, når der peges"
        },
        "show_vendor": {
          "label": "Vis forhandler"
        },
        "header__1": {
          "content": "Produktkort"
        },
        "header__2": {
          "content": "Blogkort",
          "info": "Blogkortstile anvendes også på sidekort i søgeresultater. For at ændre kortstile skal du opdatere dine temaindstillinger."
        },
        "article_show_date": {
          "label": "Vis dato"
        },
        "article_show_author": {
          "label": "Vis forfatter"
        },
        "show_rating": {
          "label": "Vis produktbedømmelser",
          "info": "Hvis du vil vise bedømmelser, skal du tilføje en app til produktbedømmelse. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Antallet af kolonner på mobil",
          "options__1": {
            "label": "1 kolonne"
          },
          "options__2": {
            "label": "2 kolonner"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Flere kolonner",
      "settings": {
        "title": {
          "label": "Overskrift"
        },
        "image_width": {
          "label": "Billedbredde",
          "options__1": {
            "label": "En tredjedel af kolonnens bredde"
          },
          "options__2": {
            "label": "Halvdelen af kolonnens bredde"
          },
          "options__3": {
            "label": "Hele kolonnens bredde"
          }
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Firkantet"
          },
          "options__4": {
            "label": "Cirkel"
          }
        },
        "column_alignment": {
          "label": "Kolonnejustering",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          }
        },
        "background_style": {
          "label": "Sekundær baggrund",
          "options__1": {
            "label": "Ingen"
          },
          "options__2": {
            "label": "Vis som kolonnebaggrund"
          }
        },
        "button_label": {
          "label": "Knaptekst"
        },
        "button_link": {
          "label": "Knaplink"
        },
        "swipe_on_mobile": {
          "label": "Aktivér swipe på mobilen"
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Antallet af kolonner på mobil",
          "options__1": {
            "label": "1 kolonne"
          },
          "options__2": {
            "label": "2 kolonner"
          }
        }
      },
      "blocks": {
        "column": {
          "name": "Kolonne",
          "settings": {
            "image": {
              "label": "Billede"
            },
            "title": {
              "label": "Overskrift"
            },
            "text": {
              "label": "Beskrivelse"
            },
            "link_label": {
              "label": "Navn på link"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Flere kolonner"
      }
    },
    "newsletter": {
      "name": "Tilmelding med mail",
      "settings": {
        "full_width": {
          "label": "Gør afsnittet til fuld bredde"
        },
        "paragraph": {
          "content": "Alle mailabonnementer opretter en kundekonto. [Få mere at vide](https://help.shopify.com/manual/customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift"
            }
          }
        },
        "paragraph": {
          "name": "Underoverskrift",
          "settings": {
            "paragraph": {
              "label": "Beskrivelse"
            }
          }
        },
        "email_form": {
          "name": "Mailformular"
        }
      },
      "presets": {
        "name": "Tilmelding med mail"
      }
    },
    "page": {
      "name": "Side",
      "settings": {
        "page": {
          "label": "Side"
        }
      },
      "presets": {
        "name": "Side"
      }
    },
    "rich-text": {
      "name": "RTF",
      "settings": {
        "full_width": {
          "label": "Gør afsnittet til fuld bredde"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Placering af indhold på computer",
          "info": "Placeringen optimeres automatisk til mobil."
        },
        "content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Indholdsjustering"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift"
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Beskrivelse"
            }
          }
        },
        "buttons": {
          "name": "Knapper",
          "settings": {
            "button_label_1": {
              "label": "Første knaptekst",
              "info": "Lad feltet være tomt for at skjule knappen."
            },
            "button_link_1": {
              "label": "Første knaplink"
            },
            "button_style_secondary_1": {
              "label": "Brug rammeknaptypografi"
            },
            "button_label_2": {
              "label": "Anden knaptekst",
              "info": "Lad feltet være tomt for at skjule knappen."
            },
            "button_link_2": {
              "label": "Andet knaplink"
            },
            "button_style_secondary_2": {
              "label": "Brug rammeknaptypografi"
            }
          }
        },
        "caption": {
          "name": "Billedtekst",
          "settings": {
            "text": {
              "label": "Tekst"
            },
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Underoverskrift"
              },
              "options__2": {
                "label": "Store bogstaver"
              }
            },
            "caption_size": {
              "label": "Tekststørrelse",
              "options__1": {
                "label": "Lille"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Stor"
              }
            }
          }
        }
      },
      "presets": {
        "name": "RTF"
      }
    },
    "apps": {
      "name": "Apps",
      "settings": {
        "include_margins": {
          "label": "Gør afsnitsmargener til det samme som tema"
        }
      },
      "presets": {
        "name": "Apps"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Overskrift"
        },
        "cover_image": {
          "label": "Coverbillede"
        },
        "video_url": {
          "label": "Webadresse",
          "placeholder": "Brug en YouTube- eller Vimeo-webadresse",
          "info": "Video afspiller på siden."
        },
        "description": {
          "label": "Alternativ tekst til video",
          "info": "Beskriv videoen for kunder med en skærmlæser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"
        },
        "image_padding": {
          "label": "Tilføj billedmargen",
          "info": "Vælg billedmargen, hvis du ikke vil have, at dit coverbillede bliver beskåret."
        },
        "full_width": {
          "label": "Gør afsnittet til fuld bredde"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Fremhævet produkt",
      "blocks": {
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekst"
            },
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Pris"
        },
        "quantity_selector": {
          "name": "Antalsvælger"
        },
        "variant_picker": {
          "name": "Variantvælger",
          "settings": {
            "picker_type": {
              "label": "Type",
              "options__1": {
                "label": "Rullemenu"
              },
              "options__2": {
                "label": "Etiketter"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Køb-knapper",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Vis dynamiske betalingsknapper",
              "info": "Via de tilgængelige betalingsmetoder i din butik ser kunderne deres foretrukne mulighed, som f.eks. PayPal eller Apple Pay. [Få mere at vide](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Beskrivelse"
        },
        "share": {
          "name": "Del",
          "settings": {
            "featured_image_info": {
              "content": "Hvis du inkluderer et link i opslag på sociale medier, vil sidens udvalgte billede blive vist som billedeksempel. [Få mere at vide](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Der er inkluderet en butikstitel og -beskrivelse med billedeksemplet. [Få mere at vide](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Tekst"
            }
          }
        },
        "custom_liquid": {
          "name": "Tilpasset Liquid",
          "settings": {
            "custom_liquid": {
              "label": "Tilpasset Liquid"
            }
          }
        },
        "rating": {
          "name": "Produktbedømmelser",
          "settings": {
            "paragraph": {
              "content": "Hvis du vil vise bedømmelser, skal du tilføje en app til produktbedømmelse. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Underoverskrift"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Produkt"
        },
        "secondary_background": {
          "label": "Vis sekundær baggrund"
        },
        "header": {
          "content": "Medie",
          "info": "Få mere at vide om [medietyper](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Aktivér looping af videoer"
        },
        "hide_variants": {
          "label": "Skjul medier for ikke-valgte varianter på computer"
        },
        "media_position": {
          "label": "Placering af medie på computer",
          "info": "Placeringen optimeres automatisk til mobil.",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Højre"
          }
        }
      },
      "presets": {
        "name": "Fremhævet produkt"
      }
    },
    "email-signup-banner": {
      "name": "Banner for tilmelding med mail",
      "settings": {
        "paragraph": {
          "content": "Alle mailabonnementer opretter en kundekonto. [Få mere at vide](https://help.shopify.com/manual/customers)"
        },
        "image": {
          "label": "Baggrundsbillede"
        },
        "show_background_image": {
          "label": "Vis baggrundsbillede"
        },
        "show_text_box": {
          "label": "Vis container på skrivebord"
        },
        "image_overlay_opacity": {
          "label": "Billedoverlejringens uigennemsigtighed"
        },
        "color_scheme": {
          "info": "Synlig, når objektbeholderen vises."
        },
        "show_text_below": {
          "label": "Vis indhold under billede på mobiltelefon",
          "info": "Brug et billede med et højde-bredde-forhold på 16:9 for at opnå det bedste resultat. [Få mere at vide](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "image_height": {
          "label": "Bannerhøjde",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Stor"
          },
          "info": "Brug et billede med et højde-bredde-forhold på 16:9 for at opnå det bedste resultat. [Få mere at vide](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "desktop_content_position": {
          "options__4": {
            "label": "Midt på til venstre"
          },
          "options__5": {
            "label": "Midt på centreret"
          },
          "options__6": {
            "label": "Midt på til højre"
          },
          "options__7": {
            "label": "Nederst til venstre"
          },
          "options__8": {
            "label": "Nederst i midten"
          },
          "options__9": {
            "label": "Nederst til højre"
          },
          "options__1": {
            "label": "Øverst til venstre"
          },
          "options__2": {
            "label": "Øverst i midten"
          },
          "options__3": {
            "label": "Øverst til højre"
          },
          "label": "Placering af indhold på computer"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på computer"
        },
        "header": {
          "content": "Mobillayout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på mobil"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift"
            }
          }
        },
        "paragraph": {
          "name": "Afsnit",
          "settings": {
            "paragraph": {
              "label": "Beskrivelse"
            },
            "text_style": {
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "label": "Teksttypografi"
            }
          }
        },
        "email_form": {
          "name": "Mailformular"
        }
      },
      "presets": {
        "name": "Banner for tilmelding med mail"
      }
    },
    "slideshow": {
      "name": "Diasshow",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Fuld bredde"
          },
          "options__2": {
            "label": "Gitter"
          }
        },
        "slide_height": {
          "label": "Diashøjde",
          "options__1": {
            "label": "Tilpas til første side"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Stor"
          }
        },
        "slider_visual": {
          "label": "Sideinddeling – stil",
          "options__1": {
            "label": "Tæller"
          },
          "options__2": {
            "label": "Prikker"
          },
          "options__3": {
            "label": "Tal"
          }
        },
        "auto_rotate": {
          "label": "Roter automatisk slides"
        },
        "change_slides_speed": {
          "label": "Skift slide hver"
        },
        "mobile": {
          "content": "Mobillayout"
        },
        "show_text_below": {
          "label": "Vis indhold under billeder på mobiltelefoner"
        },
        "accessibility": {
          "content": "Tilgængelighed",
          "label": "Beskrivelse af diasshow",
          "info": "Beskriv diasshowet for kunder med en skærmlæser."
        }
      },
      "blocks": {
        "slide": {
          "name": "Slide",
          "settings": {
            "image": {
              "label": "Billede"
            },
            "heading": {
              "label": "Overskrift"
            },
            "subheading": {
              "label": "Underoverskrift"
            },
            "button_label": {
              "label": "Knaptekst",
              "info": "Lad feltet være tomt for at skjule knappen."
            },
            "link": {
              "label": "Knaplink"
            },
            "secondary_style": {
              "label": "Brug rammeknaptypografi"
            },
            "box_align": {
              "label": "Placering af indhold på computer",
              "options__1": {
                "label": "Øverst til venstre"
              },
              "options__2": {
                "label": "Øverst i midten"
              },
              "options__3": {
                "label": "Øverst til højre"
              },
              "options__4": {
                "label": "Midt på til venstre"
              },
              "options__5": {
                "label": "Midt på centreret"
              },
              "options__6": {
                "label": "Midt på til højre"
              },
              "options__7": {
                "label": "Nederst til venstre"
              },
              "options__8": {
                "label": "Nederst i midten"
              },
              "options__9": {
                "label": "Nederst til højre"
              },
              "info": "Placeringen optimeres automatisk til mobil."
            },
            "show_text_box": {
              "label": "Vis container på skrivebord"
            },
            "text_alignment": {
              "label": "Justering af indhold på computer",
              "option_1": {
                "label": "Venstre"
              },
              "option_2": {
                "label": "Centreret"
              },
              "option_3": {
                "label": "Højre"
              }
            },
            "image_overlay_opacity": {
              "label": "Billedoverlejringens uigennemsigtighed"
            },
            "color_scheme": {
              "info": "Synlig, når containeren vises."
            },
            "text_alignment_mobile": {
              "label": "Justering af indhold på mobil",
              "options__1": {
                "label": "Venstre"
              },
              "options__2": {
                "label": "Centreret"
              },
              "options__3": {
                "label": "Højre"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Diasshow"
      }
    },
    "collapsible_content": {
      "name": "Indhold, der kan skjules",
      "settings": {
        "caption": {
          "label": "Billedtekst"
        },
        "heading": {
          "label": "Overskrift"
        },
        "heading_alignment": {
          "label": "Justering af overskrift",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          }
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Ingen beholder"
          },
          "options__2": {
            "label": "Objektbeholder til række"
          },
          "options__3": {
            "label": "Objektbeholder til afsnit"
          }
        },
        "open_first_collapsible_row": {
          "label": "Åbn første række, der kan skjules"
        },
        "header": {
          "content": "Billedlayout"
        },
        "image": {
          "label": "Billede"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Stor"
          }
        },
        "desktop_layout": {
          "label": "Skrivebordslayout",
          "options__1": {
            "label": "Billede først"
          },
          "options__2": {
            "label": "Billede efterfølgende"
          },
          "info": "Billedet er altid først på mobiler."
        },
        "container_color_scheme": {
          "label": "Objektbeholder til farveskema",
          "info": "Synlig, når Layout er angivet til objektbeholder til Række eller Afsnit."
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Række, der kan skjules",
          "settings": {
            "heading": {
              "info": "Inkluder en overskrift, der forklarer indholdet.",
              "label": "Overskrift"
            },
            "row_content": {
              "label": "Rækkeindhold"
            },
            "page": {
              "label": "Rækkeindhold fra side"
            },
            "icon": {
              "label": "Ikon",
              "options__1": {
                "label": "Ingen"
              },
              "options__2": {
                "label": "Æble"
              },
              "options__3": {
                "label": "Banan"
              },
              "options__4": {
                "label": "Flaske"
              },
              "options__5": {
                "label": "Æske"
              },
              "options__6": {
                "label": "Gulerod"
              },
              "options__7": {
                "label": "Chatboble"
              },
              "options__8": {
                "label": "Flueben"
              },
              "options__9": {
                "label": "Clipboard"
              },
              "options__10": {
                "label": "Mejeri"
              },
              "options__11": {
                "label": "Laktosefri"
              },
              "options__12": {
                "label": "Tørrer"
              },
              "options__13": {
                "label": "Øje"
              },
              "options__14": {
                "label": "Ild"
              },
              "options__15": {
                "label": "Glutenfri"
              },
              "options__16": {
                "label": "Hjerte"
              },
              "options__17": {
                "label": "Jern"
              },
              "options__18": {
                "label": "Blad"
              },
              "options__19": {
                "label": "Læder"
              },
              "options__20": {
                "label": "Lyn"
              },
              "options__21": {
                "label": "Læbestift"
              },
              "options__22": {
                "label": "Lås"
              },
              "options__23": {
                "label": "Kortnål"
              },
              "options__24": {
                "label": "Nøddefri"
              },
              "options__25": {
                "label": "Bukser"
              },
              "options__26": {
                "label": "Poteaftryk"
              },
              "options__27": {
                "label": "Peber"
              },
              "options__28": {
                "label": "Parfume"
              },
              "options__29": {
                "label": "Fly"
              },
              "options__30": {
                "label": "Plante"
              },
              "options__31": {
                "label": "Prismærke"
              },
              "options__32": {
                "label": "Spørgsmålstegn"
              },
              "options__33": {
                "label": "Genanvendelse"
              },
              "options__34": {
                "label": "Returnering"
              },
              "options__35": {
                "label": "Lineal"
              },
              "options__36": {
                "label": "Serveringsfad"
              },
              "options__37": {
                "label": "Skjorte"
              },
              "options__38": {
                "label": "Sko"
              },
              "options__39": {
                "label": "Silhuet"
              },
              "options__40": {
                "label": "Snefnug"
              },
              "options__41": {
                "label": "Stjerne"
              },
              "options__42": {
                "label": "Stopur"
              },
              "options__43": {
                "label": "Lastbil"
              },
              "options__44": {
                "label": "Vask"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Indhold, der kan skjules"
      }
    },
    "main-account": {
      "name": "Konto"
    },
    "main-activate-account": {
      "name": "Aktivering af konto"
    },
    "main-addresses": {
      "name": "Adresser"
    },
    "main-login": {
      "name": "Login"
    },
    "main-order": {
      "name": "Ordre"
    },
    "main-register": {
      "name": "Registrering"
    },
    "main-reset-password": {
      "name": "Nulstilling af adgangskode"
    },
    "related-products": {
      "name": "Relaterede produkter",
      "settings": {
        "heading": {
          "label": "Overskrift"
        },
        "products_to_show": {
          "label": "Maksimalt antal produkter, der skal vises"
        },
        "columns_desktop": {
          "label": "Antallet af kolonner på computer"
        },
        "paragraph__1": {
          "content": "Dynamiske anbefalinger bruger ordre- og produktoplysninger til at foretage ændringer og forbedringer med tiden. [Få mere at vide](https://help.shopify.com/themes/development/recommended-products)"
        },
        "header__2": {
          "content": "Produktkort"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundært billede, når der peges"
        },
        "show_vendor": {
          "label": "Vis forhandler"
        },
        "show_rating": {
          "label": "Vis produktbedømmelser",
          "info": "Hvis du vil vise bedømmelser, skal du tilføje en app til produktbedømmelse. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Antallet af kolonner på mobil",
          "options__1": {
            "label": "1 kolonne"
          },
          "options__2": {
            "label": "2 kolonner"
          }
        }
      }
    },
    "multirow": {
      "name": "Flere rækker",
      "settings": {
        "image": {
          "label": "Billede"
        },
        "image_height": {
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Mellem"
          },
          "options__4": {
            "label": "Stor"
          },
          "label": "Billedhøjde"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Lille"
          },
          "options__2": {
            "label": "Mellem"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Billedbredde på computer",
          "info": "Billedet er automatisk optimeret til mobiltelefoner."
        },
        "heading_size": {
          "options__1": {
            "label": "Lille"
          },
          "options__2": {
            "label": "Mellem"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Størrelse for overskrift"
        },
        "text_style": {
          "options__1": {
            "label": "Brødtekst"
          },
          "options__2": {
            "label": "Underoverskrift"
          },
          "label": "Teksttypografi"
        },
        "button_style": {
          "options__1": {
            "label": "Udfyldt knap"
          },
          "options__2": {
            "label": "Rammeknap"
          },
          "label": "Knaptypografi"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på computer"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "I midten"
          },
          "options__3": {
            "label": "Bund"
          },
          "label": "Placering af indhold på computer",
          "info": "Placeringen optimeres automatisk til mobil."
        },
        "image_layout": {
          "options__1": {
            "label": "Skift fra venstre"
          },
          "options__2": {
            "label": "Skift fra højre"
          },
          "options__3": {
            "label": "Venstrejusteret"
          },
          "options__4": {
            "label": "Højrejusteret"
          },
          "label": "Billedplacering på computer",
          "info": "Placeringen er automatisk optimeret til mobiltelefoner."
        },
        "container_color_scheme": {
          "label": "Objektbeholder til farveskema"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af indhold på mobil"
        },
        "header_mobile": {
          "content": "Mobillayout"
        }
      },
      "blocks": {
        "row": {
          "name": "Række",
          "settings": {
            "image": {
              "label": "Billede"
            },
            "caption": {
              "label": "Billedtekst"
            },
            "heading": {
              "label": "Overskrift"
            },
            "text": {
              "label": "Sms"
            },
            "button_label": {
              "label": "Knaptekst"
            },
            "button_link": {
              "label": "Knaplink"
            }
          }
        }
      },
      "presets": {
        "name": "Flere rækker"
      }
    }
  }
}
