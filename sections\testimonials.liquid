{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}

  .cards-color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_cards_colors_background.red }}, {{ section.settings.custom_cards_colors_background.green }}, {{ section.settings.custom_cards_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_cards_gradient_background != blank %}{{ section.settings.custom_cards_gradient_background }}{% else %}{{ section.settings.custom_cards_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_cards_colors_text.red }}, {{ section.settings.custom_cards_colors_text.green }}, {{ section.settings.custom_cards_colors_text.blue }};
  }
{%- endstyle -%}

<div class="multicolumn color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %} content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  {%- liquid
    assign has_mobile_slider = false
    assign has_desktop_slider = false
    if section.settings.slider_mobile
      assign has_mobile_slider = true
    endif
    if section.settings.slider_desktop
      assign has_desktop_slider = true
    endif
  
    assign highest_ratio = 0
    for block in section.blocks
      if block.settings.video.aspect_ratio > highest_ratio
        assign highest_ratio = block.settings.video.aspect_ratio
      elsif block.settings.image.aspect_ratio > highest_ratio
        assign highest_ratio = block.settings.image.aspect_ratio
      endif
    endfor
    assign bg_diff_class = ' multicolumn--same-bgs'
    unless section.settings.color_scheme == section.settings.cards_color_scheme
      assign bg_diff_class = ' multicolumn--diff-bgs'
    endunless
    if section.settings.color_scheme == 'custom' and section.settings.cards_color_scheme == 'custom'
      unless section.settings.custom_colors_background == section.settings.custom_cards_colors_background
        assign bg_diff_class = ' multicolumn--diff-bgs'
      endunless
    endif
  -%}
  <div class="page-width{% if section.settings.desktop_full_page %} desktop-full-page{% endif %}{% if has_mobile_slider %} mobile-full-page{% endif %} section-{{ section.id }}-padding isolate" style="--columns-desktop: {{ section.settings.columns_desktop }};--columns-mobile:1;--gap-desktop:{{ section.settings.desktop_spacing | divided_by: 10.0 }}rem;--gap-mobile:1.5rem;">
    <div class='animate-item animate-item--child index-0'>
      {% assign content_index = 0 %}
      {%- unless section.settings.title == blank -%}
        {% assign content_index = 1 %}
        <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin{% if section.settings.text != blank %} multicolumn-title-with-text{% endif %}">
          <h2 class="title {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
            {{ section.settings.title }}
          </h2>
        </div>
      {%- endunless -%}
      {% if section.settings.text != blank %}
        {% assign content_index = 1 %}
        <div class="multicolumn-text">
          {{ section.settings.text }}
        </div>
      {% endif %}
    </div>
    {% if has_mobile_slider or has_desktop_slider %}
      <splide-component
        data-type='{{ section.settings.type }}'
        data-autoplay='{{ section.settings.autoplay }}'
        data-autoplay-speed='{{ section.settings.autoplay_speed }}'
        data-arrows-color="{{ section.settings.arrows_color_scheme }}"
        data-dots-color="{{ section.settings.dots_color_scheme }}"
        data-slides-desktop="{{ section.settings.columns_desktop }}"
        data-per-move-desktop='{{ section.settings.per_move_desktop }}'
        data-gap-desktop='{{ section.settings.desktop_spacing }}'
        data-side-padding-desktop='{{ section.settings.desktop_side_padding }}'
        data-padding-calc-desktop='{{ section.settings.desktop_padding_calc }}'
        data-slides-mobile="1"
        data-gap-mobile='15'
        {% if section.settings.enable_mobile_preview %}
          data-side-padding-mobile='24'
        {% else %}
          data-side-padding-mobile='15'
        {% endif %}
        {% if has_desktop_slider == false %}
          data-destroy-desktop="true"
        {% elsif has_mobile_slider == false %}
          data-destroy-mobile="true"
        {% endif %}
        data-pause-videos='true'
      >
    {% endif %}
      <div 
        class='splide splide--desktop-dots-{{ section.settings.desktop_dots_position }} splide--mobile-dots-{{ section.settings.mobile_dots_position }} splide--desktop-arrows-{{ section.settings.desktop_arrows_position }} splide--desktop-arrows-outside splide--mobile-arrows-{{ section.settings.mobile_arrows_position }}{% if section.settings.transparent_arrows %} splide--transparent-arrows{% endif %}{% if section.settings.vertical-alignment == 'center' %} splide--vertically-centered{% endif %}{% if has_desktop_slider == false %} splide--destroy-desktop{% endif %}{% if has_mobile_slider == false %} splide--destroy-mobile{% endif %}' 
        style="--columns-desktop: {{ section.settings.columns_desktop }};--columns-mobile:1;--gap-desktop:{{ section.settings.desktop_spacing | divided_by: 10.0 }}rem;--gap-mobile:1.5rem;"
        {% if section.settings.desktop_adaptive_height and section.settings.slides_desktop == 1 and has_desktop_slider %}
          data-desktop-adaptive-height="true"
        {% endif %}
        {% if section.settings.mobile_adaptive_height and has_mobile_slider %}
          data-mobile-adaptive-height="true"
        {% endif %}
      >
        <div class="splide__track">
          <ul class="splide__list">
            {%- for block in section.blocks -%}
              <li class="splide__slide">
                <div class="splide__slide__container" style="--index:{{ forloop.index0 | plus: content_index }};" {{ block.shopify_attributes }}>
                  <div class="multicolumn-card content-container testimonial-card color-{{ section.settings.cards_color_scheme }} cards-color-scheme-{{ section.id }} gradient{{ bg_diff_class }}{% if section.settings.column_alignment == 'center' %} center{% endif %}{% unless block.settings.author_avatar == blank and block.settings.author == blank %} testimonial-card--has-author{% endunless %} animate-item animate-item--child">
                    {%- if block.settings.image != blank or block.settings.video != blank -%}
                      {% if section.settings.image_ratio == 'adapt' or section.settings.image_ratio == 'circle' %}
                        {% assign spaced_image = true %}
                      {% endif %}
                      <div class="multicolumn-card__image-wrapper multicolumn-card__image-wrapper--{{ section.settings.image_width }}-width{% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}">
                        <div
                          class="media media--transparent media--{{ section.settings.image_ratio }}"
                          {% if section.settings.image_ratio == 'adapt' %}
                            style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"
                          {% endif %}
                        >
                          {% if block.settings.video == blank %}
                            {%- capture sizes -%}
                              (min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %}, (min-width:
                              750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %}, calc(100vw - 30px)
                            {%- endcapture -%}
                            {{
                              block.settings.image
                              | image_url: width: 1420
                              | image_tag:
                                loading: 'lazy',
                                sizes: sizes,
                                widths: '275, 550, 710, 1420',
                                class: 'multicolumn-card__image'
                            }}
                          {% else %}
                            <internal-video class="internal-video" data-action-on-inactive='pause'>
                              {% liquid
                                if block.settings.video_thumbnail != blank
                                  assign thumbnail = block.settings.video_thumbnail
                                else
                                  assign thumbnail = block.settings.video.preview_image
                                endif
                              %}
                              <video 
                                width="100%" 
                                height="auto" 
                                preload='metadata'
                                poster="{{ thumbnail | image_url }}"
                                playsinline disablepictureinpicture
                              >
                                {% for source in block.settings.video.sources %}
                                  <source 
                                    {% if block.settings.muted_autoplay %}data-{% endif %}src="{{ source.url }}" 
                                    type="{{ source.mime_type }}"
                                  >
                                {% endfor %}
                              </video>
                              <button class="internal-video__play">
                                <div class="play-button color-accent-1">
                                  {%- render 'icon-play' -%}
                                </div>
                              </button>
                              <div class='internal-video__timeline'>&nbsp</div>
                            </internal-video>
                          {% endif %}
                        </div>
                      </div>
                    {%- endif -%}
                    <div class="multicolumn-card__info{% if block.settings.image == blank and section.settings.show_stars == false %} testimonial-card__info--no-image-no-stars{% endif %}{% if block.settings.image != blank and section.settings.show_stars == false %} testimonial-card__info--image-no-stars{% endif %}">
                      {%- if section.settings.show_stars -%}
                        <p class="testimonial-card__stars" style='--stars-color:{{ section.settings.stars_color }}'>★★★★★</p>
                      {%- endif -%}
                      {%- if section.settings.show_quotes -%}
                        <div class="testimonial-card__quotes flex-center{% if block.settings.image == blank %} testimonial-card__quotes--image-blank{% endif %} color-{{ section.settings.quotes_color_scheme }}">
                          <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                            <path fill="currentColor" d="M119.472 66.59C53.489 66.59 0 120.094 0 186.1c0 65.983 53.489 119.487 119.472 119.487c0 0-0.578 44.392-36.642 108.284c-4.006 12.802 3.135 26.435 15.945 30.418c9.089 2.859 18.653 0.08 24.829-6.389c82.925-90.7 115.385-197.448 115.385-251.8C238.989 120.094 185.501 66.59 119.472 66.59z"/>
                            <path fill="currentColor" d="M392.482 66.59c-65.983 0-119.472 53.505-119.472 119.51c0 65.983 53.489 119.487 119.472 119.487c0 0-0.578 44.392-36.642 108.284c-4.006 12.802 3.136 26.435 15.945 30.418c9.089 2.859 18.653 0.08 24.828-6.389C479.539 347.2 512 240.452 512 186.1C512 120.094 458.511 66.59 392.482 66.59z"/>
                          </svg>
                        </div>
                      {%- endif -%}
                      {%- if block.settings.title != blank -%}
                        <h3>{{ block.settings.title }}</h3>
                      {%- endif -%}
                      {%- if block.settings.text != blank -%}
                        <div class="rte">{{ block.settings.text }}</div>
                      {%- endif -%}
                      {%- unless block.settings.author_avatar == blank and block.settings.author == blank -%}
                        <div class="testimonial-card__author-container">
                          {%- if block.settings.author_avatar != blank -%}
                            <div class="testimonial-card__avatar">
                              {{
                                block.settings.author_avatar
                                | image_url: width: 150
                                | image_tag: loading: 'lazy', width: 100
                              }}
                            </div>
                          {%- endif -%}
                          {%- if block.settings.author != blank -%}
                            <p class="testimonial-card__author">
                              {{ block.settings.author }}
                            </p>
                          {%- endif -%}
                        </div>
                      {%- endunless -%}
                    </div>
                  </div>
                </div>
              </li>
            {%- endfor -%}
          </ul>
        </div>
        <div class='splide__dots-and-arrows'>
          <ul class="splide__pagination"></ul>
          <div class="splide__arrows"></div>
        </div>
      </div>
    {% if has_mobile_slider or has_desktop_slider %}
      </splide-component>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Testimonials",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Testimonials",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Cards style"
    },
    {
      "type": "select",
      "id": "image_width",
      "options": [
        {
          "value": "third",
          "label": "One-third width of column"
        },
        {
          "value": "half",
          "label": "Half width of column"
        },
        {
          "value": "full",
          "label": "Full width of column"
        }
      ],
      "default": "full",
      "label": "Media width"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to media"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "circle",
          "label": "Circle"
        }
      ],
      "default": "square",
      "label": "Media ratio"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multicolumn.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multicolumn.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "Text alignment"
    },
    {
      "type": "checkbox",
      "id": "show_stars",
      "default": true,
      "label": "Show stars"
    },
    {
      "type": "color",
      "id": "stars_color",
      "label": "Stars color",
      "default": "#ffd700"
    },
    {
      "type": "checkbox",
      "id": "show_quotes",
      "default": true,
      "label": "Show quote marks"
    },
    {
      "type": "select",
      "id": "quotes_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "accent-2",
      "label": "Quotes color scheme"
    },
    {
      "type": "select",
      "id": "cards_color_scheme",
      "options": [
        {
          "value": "bg-overlay",
          "label": "Light overlay"
        },
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "bg-overlay",
      "label": "Cards color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Slider"
    },
    {
      "type": "paragraph",
      "content": "ATTENTION: ONLY in the theme editor, pagination dots might duplicate after changing section settings. To overcome this, simply click Save. This has NO EFFECT on the published website."
    },
    {
      "type": "select",
      "id": "type",
      "options": [
        {
          "value": "slide",
          "label": "Classic"
        },
        {
          "value": "loop",
          "label": "Infinite"
        }
      ],
      "default": "slide",
      "label": "Type"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": false
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 1,
      "max": 15,
      "step": 0.5,
      "default": 5,
      "unit": "sec",
      "label": "Autoplay speed"
    },
    {
      "type": "select",
      "id": "arrows_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "inverse",
      "label": "Arrows color scheme"
    },
    {
      "type": "checkbox",
      "id": "transparent_arrows",
      "label": "Transparent arrows",
      "default": true
    },
    {
      "type": "select",
      "id": "dots_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Text"
        }
      ],
      "default": "inverse",
      "label": "Dots color"
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "checkbox",
      "id": "desktop_full_page",
      "label": "Full page width",
      "default": false
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 3,
      "label": "Slides per page"
    },
    {
      "type": "checkbox",
      "id": "slider_desktop",
      "label": "Enable desktop slider",
      "default": false,
      "info": "The settings bellow are applied if this option is checked"
    },
    {
      "type": "range",
      "id": "per_move_desktop",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 1,
      "label": "Slides to scroll on one move"
    },
    {
      "type": "range",
      "id": "desktop_spacing",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Spacing",
      "default": 40
    },
    {
      "type": "range",
      "id": "desktop_side_padding",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Preview of prev & next slide",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "desktop_padding_calc",
      "label": "Disable empty preview on first & last slide",
      "default": true,
      "info": "Moves the first slide to the left edge adn last one to the right if Preview value is enabled. Visible if type is set to Slider."
    },
    {
      "type": "checkbox",
      "id": "desktop_adaptive_height",
      "label": "Adaptive height",
      "default": false,
      "info": "Only available if Slides per page is set to 1."
    },
    {
      "type": "select",
      "id": "desktop_dots_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        }
      ],
      "default": "under",
      "label": "Dots position"
    },
    {
      "type": "select",
      "id": "desktop_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        },
        {
          "value": "sides",
          "label": "On sides of the slider"
        }
      ],
      "default": "sides",
      "label": "Arrows position"
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "checkbox",
      "id": "slider_mobile",
      "label": "Enable mobile slider",
      "default": true,
      "info": "The settings bellow are applied if this option is checked"
    },
    {
      "type": "checkbox",
      "id": "enable_mobile_preview",
      "label": "Preview of prev & next slides",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "mobile_adaptive_height",
      "label": "Adaptive height",
      "default": false
    },
    {
      "type": "select",
      "id": "mobile_dots_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        }
      ],
      "default": "under",
      "label": "Dots position"
    },
    {
      "type": "select",
      "id": "mobile_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "under",
          "label": "Under the slider"
        },
        {
          "value": "sides",
          "label": "On sides of the slider"
        }
      ],
      "default": "under",
      "label": "Arrows position"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "header",
      "content": "Custom cards color scheme",
      "info": "Applied if Cards color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_cards_colors_background",
      "default": "#F3F3F3",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_cards_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_cards_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "Testimonial",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "header",
          "content": "or"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "image_picker",
          "id": "video_thumbnail",
          "label": "Video thumbnail",
          "info": "If empty, the first frame of the video will be displayed."
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Heading",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Share positive thoughts and feedback from your customer..</p>",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "image_picker",
          "id": "author_avatar",
          "label": "Author avatar"
        },
        {
          "type": "inline_richtext",
          "id": "author",
          "default": "<em><strong>Author</strong></em>",
          "label": "Author"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Testimonials",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}
