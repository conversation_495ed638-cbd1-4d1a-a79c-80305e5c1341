<div class="page-width{% if cart == empty %} is-empty{% endif %}" id="main-cart-footer" data-id="{{ section.id }}">
  <div>
    <div class="cart__footer">
      {%- if settings.show_cart_note -%}
        <cart-note class="cart__note field">
          <label for="Cart-note">{{ 'sections.cart.note' | t }}</label>
          <textarea
            class="text-area field__input"
            name="note"
            form="cart"
            id="Cart-note"
            placeholder="{{ 'sections.cart.note' | t }}"
          >{{ cart.note }}</textarea>
        </cart-note>
      {%- endif -%}

      <div class="cart__blocks">
        {% for block in section.blocks %}
          {%- case block.type -%}
            {%- when '@app' -%}
              {% render block %}
            {%- when 'subtotal' -%}
              <div class="js-contents" {{ block.shopify_attributes }}>
                <div class="totals">
                  <h2 class="totals__subtotal">{{ 'sections.cart.subtotal' | t }}</h2>
                  <p class="totals__subtotal-value">{{ cart.total_price | money_with_currency }}</p>
                </div>

                <div>
                  {%- if cart.cart_level_discount_applications.size > 0 -%}
                    <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
                      {%- for discount in cart.cart_level_discount_applications -%}
                        <li class="discounts__discount discounts__discount--position">
                          {%- render 'icon-discount' -%}
                          {{ discount.title }}
                          (-{{ discount.total_allocated_amount | money }})
                        </li>
                      {%- endfor -%}
                    </ul>
                  {%- endif -%}
                </div>
              </div>
            {% when 'discount_field' %}
              {% render 'cart-discount-field', block: block %}
            {%- else -%}
              <div class="cart__ctas" {{ block.shopify_attributes }}>
                <noscript>
                  <button type="submit" class="cart__update-button button button--secondary" form="cart">
                    {{ 'sections.cart.update' | t }}
                  </button>
                </noscript>

                <button
                  type="submit"
                  id="checkout"
                  class="cart__checkout-button button"
                  name="checkout"
                  {% if cart == empty %}
                    disabled
                  {% endif %}
                  form="cart"
                >
                  {{ 'sections.cart.checkout' | t }}
                </button>
              </div>

              {%- if additional_checkout_buttons -%}
                <div class="cart__dynamic-checkout-buttons additional-checkout-buttons">
                  {{ content_for_additional_checkout_buttons }}
                </div>
              {%- endif -%}
          {%- endcase -%}
        {% endfor %}

        <div id="cart-errors"></div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    function isIE() {
      const ua = window.navigator.userAgent;
      const msie = ua.indexOf('MSIE ');
      const trident = ua.indexOf('Trident/');

      return msie > 0 || trident > 0;
    }

    if (!isIE()) return;
    const cartSubmitInput = document.createElement('input');
    cartSubmitInput.setAttribute('name', 'checkout');
    cartSubmitInput.setAttribute('type', 'hidden');
    document.querySelector('#cart').appendChild(cartSubmitInput);
    document.querySelector('#checkout').addEventListener('click', function (event) {
      document.querySelector('#cart').submit();
    });
  });
</script>

{% schema %}
{
  "name": "t:sections.main-cart-footer.name",
  "class": "cart__footer-wrapper",
  "blocks": [
    {
      "type": "discount_field",
      "name": "Discount field",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "placeholder",
          "label": "Input placeholder",
          "default": "Enter discount code"
        },
        {
          "type": "text",
          "id": "btn_label",
          "label": "Button label",
          "default": "ADD"
        },
        {
          "type": "text",
          "id": "error_msg",
          "label": "Error message",
          "default": "Please enter a discount code!",
          "info": "Displayed if add button is clicked while the input is empty."
        }
      ]
    },
    {
      "type": "subtotal",
      "name": "t:sections.main-cart-footer.blocks.subtotal.name",
      "limit": 1
    },
    {
      "type": "buttons",
      "name": "t:sections.main-cart-footer.blocks.buttons.name",
      "limit": 1
    },
    {
      "type": "@app"
    }
  ]
}
{% endschema %}
