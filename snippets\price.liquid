{% comment %}
  Renders a list of product's price (regular, sale)

  Accepts:
  - product: {Object} Product Liquid object (optional)
  - use_variant: {Boolean} Renders selected or first variant price instead of overall product pricing (optional)
  - show_badges: {<PERSON>olean} Renders 'Sale' and 'Sold Out' tags if the product matches the condition (optional)
  - price_class: {String} Adds a price class to the price element (optional)
  - hide_currency_code: {Boolean} hide currency code regardless of what setting is chosen

  Usage:
  {% render 'price', product: product %}
{% endcomment %}
{%- liquid
  if use_variant
    assign target = product.selected_or_first_available_variant
  else
    assign target = product
  endif

  assign compare_at_price = target.compare_at_price
  assign price = target.price | default: 1999
  assign available = target.available | default: false
  assign money_compare_at_price = compare_at_price | money
  assign money_price = price | money
  unless hide_currency_code
    if settings.currency_code_enabled
      assign money_price = price | money_with_currency
      assign money_compare_at_price = compare_at_price | money_with_currency
    endif
  endunless

  if target == product and product.price_varies
    assign money_price = 'products.product.price.from_price_html' | t: price: money_price
  endif
-%}

<div
  class="
    price
    {%- if price_class %} {{ price_class }}{% endif -%}
    {%- if available == false %} price--sold-out {% endif -%}
    {%- if compare_at_price > price %} price--on-sale {% endif -%}
    {%- if product.price_varies == false and product.compare_at_price_varies %} price--no-compare{% endif -%}
    {%- if show_badges %} price--show-badge{% endif -%}
  "
>
  <div class="price__container">
    {%- comment -%}
      Explanation of description list:
        - div.price__regular: Displayed when there are no variants on sale
        - div.price__sale: Displayed when a variant is a sale
    {%- endcomment -%}
    <div class="price__regular">
      <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.regular_price' | t }}</span>
      <span class="price-item price-item--regular{% if main_price %} main-price accent-color-{{ block.settings.price_color }}{% endif %}">
        {{ money_price }}
      </span>
    </div>
    <div class="price__sale {% if price_second %} price__sale--price-second{% endif %}">
      <span class="visually-hidden visually-hidden--inline compare-price-label">
        {{- 'products.product.price.regular_price' | t -}}
      </span>
      <span class="price-item price-item--sale price-item--last{% if main_price %} main-price accent-color-{{ block.settings.price_color }}{% endif %}">
        {{ money_price }}
      </span>
      {%- unless product.price_varies == false and product.compare_at_price_varies %}
        <span class="visually-hidden visually-hidden--inline regular-price-label">
          {{- 'products.product.price.sale_price' | t -}}
        </span>
        <span class="price__compare-price">
          <s class="price-item price-item--regular{% if main_price %} main-comapre-price accent-color-{{ block.settings.compare_price_color }}{% endif %}">
            {{ money_compare_at_price }}
          </s>
        </span>
      {%- endunless -%}
    </div>
    <small class="unit-price caption{% if product.selected_or_first_available_variant.unit_price_measurement == nil %} hidden{% endif %}">
      <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
      <span class="price-item price-item--last">
        <span>{{- product.selected_or_first_available_variant.unit_price | money -}}</span>
        <span aria-hidden="true">/</span>
        <span class="visually-hidden">&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span>
        <span>
          {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
            {{- product.selected_or_first_available_variant.unit_price_measurement.reference_value -}}
          {%- endif -%}
          {{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}
        </span>
      </span>
    </small>
  </div>
  {%- if show_badges -%}
    {% assign percentage_saved = compare_at_price | minus: price | times: 100.0 | divided_by: compare_at_price | floor | append: '%' %}
    {% capture money_saved %}
      {{ compare_at_price | minus: price | money_without_trailing_zeros }}
    {% endcapture %}
    {% if main_price != true or block.settings.displayed_badge == 'sale' %}
      <span class="badge price__badge-sale color-{{ settings.sale_badge_color_scheme }}">
        {% if settings.sale_basge_discount_icon %}{% render 'icon-discount' %}{% endif %}<span class='nowrap'>{{ settings.sale_badge_text | replace: '[percentage]', percentage_saved | replace: '[amount]', money_saved }}</span>
      </span>
    {% elsif block.settings.displayed_badge == 'custom' %}
      {% capture icon_1 %}
        {% if block.settings.custom_icon_1 != blank %}
          <img
            src="{{ block.settings.custom_icon_1 | image_url }}"
            alt="{{ block.settings.custom_icon_1.alt }}"
            width="auto"
            height="auto"
            loading="lazy"
          >
        {% elsif block.settings.icon_1 != blank %}
          {% render 'material-icon', icon: block.settings.icon_1, filled: block.settings.filled_icon_1 %}
        {% endif %}
      {% endcapture %}
      {% capture icon_2 %}
        {% if block.settings.custom_icon_2 != blank %}
          <img
            src="{{ block.settings.custom_icon_2 | image_url }}"
            alt="{{ block.settings.custom_icon_2.alt }}"
            width="auto"
            height="auto"
            loading="lazy"
          >
        {% elsif block.settings.icon_2 != blank %}
          {% render 'material-icon', icon: block.settings.icon_2, filled: block.settings.filled_icon_2 %}
        {% endif %}
      {% endcapture %}
      <span class="badge price__badge-custom color-{{ settings.sale_badge_color_scheme }} nowrap" style="--icon-size: {{ block.settings.icon_scale | divided_by: 100.0 }}em;">
        {{ block.settings.custom_badge_text | replace: '[percentage]', percentage_saved | replace: '[amount]', money_saved | replace: '[icon_1]', icon_1 | replace: '[icon_2]', icon_2 }}
      </span>
    {% endif %}
    <span class="badge price__badge-sold-out color-{{ settings.sold_out_badge_color_scheme }}">
      {{ 'products.product.sold_out' | t }}
    </span>
  {%- endif -%}
</div>
