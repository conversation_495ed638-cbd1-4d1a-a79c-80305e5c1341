/**
 * Purchase Type Handler
 * Sets default Purchase Type to "Regular" on regular product pages
 * where the Purchase Type option is hidden
 */

class PurchaseTypeHandler {
  constructor() {
    this.init();
  }

  init() {
    console.log('🔧 Purchase Type Handler initialized');
    
    // Run on page load
    this.setDefaultPurchaseType();
    
    // Also run when variant selectors are updated
    document.addEventListener('DOMContentLoaded', () => {
      this.setDefaultPurchaseType();
    });
  }

  setDefaultPurchaseType() {
    // Check if we're on a product page
    if (!window.location.pathname.includes('/products/')) {
      return;
    }

    // Check if this is a bundle product (has bundle tag)
    const isBundleProduct = this.checkIfBundleProduct();
    
    if (isBundleProduct) {
      console.log('📦 Bundle product detected - Purchase Type option should be visible');
      return;
    }

    console.log('🏷️ Regular product detected - setting default Purchase Type to Regular');
    
    // Find all variant selectors
    const variantSelectors = document.querySelectorAll('variant-selects, variant-selector');
    
    variantSelectors.forEach(selector => {
      this.setRegularAsDefault(selector);
    });

    // Also check for direct form elements
    this.setRegularAsDefaultInForms();
  }

  checkIfBundleProduct() {
    // Check if product has bundle tag (this would be set in the template)
    const productData = document.querySelector('[data-product-tags]');
    if (productData) {
      const tags = productData.dataset.productTags || '';
      return tags.toLowerCase().includes('bundle');
    }

    // Fallback: check for bundle-related elements on the page
    const bundleElements = document.querySelectorAll('[data-bundle], .bundle-deals, .bundle-offer');
    return bundleElements.length > 0;
  }

  setRegularAsDefault(container) {
    // Find Purchase Type selects
    const purchaseTypeSelects = container.querySelectorAll('select[name*="Purchase Type"], select[name*="purchase type"]');
    
    purchaseTypeSelects.forEach(select => {
      const regularOption = Array.from(select.options).find(option => 
        option.value.toLowerCase().includes('regular') || 
        option.textContent.toLowerCase().includes('regular')
      );
      
      if (regularOption && !regularOption.selected) {
        console.log('✅ Setting Purchase Type to Regular');
        regularOption.selected = true;
        
        // Trigger change event to update variant selection
        select.dispatchEvent(new Event('change', { bubbles: true }));
      }
    });

    // Find Purchase Type radio buttons
    const purchaseTypeRadios = container.querySelectorAll('input[type="radio"][name*="Purchase Type"], input[type="radio"][name*="purchase type"]');
    
    purchaseTypeRadios.forEach(radio => {
      if (radio.value.toLowerCase().includes('regular') && !radio.checked) {
        console.log('✅ Setting Purchase Type radio to Regular');
        radio.checked = true;
        
        // Trigger change event
        radio.dispatchEvent(new Event('change', { bubbles: true }));
      }
    });
  }

  setRegularAsDefaultInForms() {
    // Find all product forms
    const productForms = document.querySelectorAll('form[action*="/cart/add"], [id*="product-form"]');
    
    productForms.forEach(form => {
      this.setRegularAsDefault(form);
    });
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new PurchaseTypeHandler();
  });
} else {
  new PurchaseTypeHandler();
}
