{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} color-scheme-{{ section.id }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="video-section isolate{% unless section.settings.full_width %} page-width{% endunless %} section-{{ section.id }}-padding animate-item">
    <div
      {% if section.settings.full_width %}
        class="page-width"
      {% endif %}
    >
      {%- unless section.settings.title == blank -%}
        <div class="title-wrapper title-wrapper--no-top-margin">
          <h2 class="title {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
            {{ section.settings.title }}
          </h2>
        </div>
      {%- endunless -%}
    </div>
    <noscript>
      <div
        class="video-section__media"
        {% if section.settings.cover_image != blank %}
          style="padding-bottom: {{ 1 | divided_by: section.settings.cover_image.aspect_ratio | times: 100 }}%;"
        {% endif %}
      >
        <a
          href="{{ section.settings.video_url }}"
          class="video-section__poster media media--transparent media--landscape{% if section.settings.cover_image == blank %} video-section__placeholder{% endif %}"
        >
          {%- if section.settings.cover_image != blank -%}
            {%- capture sizes -%}
              {% if section.settings.full_width -%}
                100vw
              {%- else -%}
                (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px)
                calc(100vw - 10rem), 100vw
              {%- endif %}
            {%- endcapture -%}
            {%- assign alt = 'sections.video.load_video' | t: description: section.settings.description | escape -%}
            {{
              section.settings.cover_image
              | image_url: width: 3840
              | image_tag:
                loading: 'lazy',
                sizes: sizes,
                widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840',
                alt: alt
            }}
          {%- else -%}
            {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
          {%- endif -%}
        </a>
      </div>
    </noscript>
    <deferred-media
      class="video-section__media deferred-media no-js-hidden gradient global-media-settings{% if section.settings.full_width %} global-media-settings--full-width{% endif %}"
      data-media-id="{{ section.settings.video_url.id }}"
      {% if section.settings.cover_image != blank %}
        style="padding-bottom: {{ 1 | divided_by: section.settings.cover_image.aspect_ratio | times: 100 }}%;"
      {% endif %}
    >
      <button
        id="Deferred-Poster-Modal-{{ section.settings.video_url.id }}"
        class="video-section__poster media deferred-media__poster media--landscape"
        type="button"
        aria-label="{{ 'sections.video.load_video' | t: description: section.settings.description | escape }}"
      >
        {%- if section.settings.cover_image != blank -%}
          {%- capture sizes -%}
            {% if section.settings.full_width -%}
              100vw
            {%- else -%}
              (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px)
              calc(100vw - 10rem), 100vw
            {%- endif %}
          {%- endcapture -%}
          {%- assign alt = 'sections.video.load_video' | t: description: section.settings.description | escape -%}
          {{
            section.settings.cover_image
            | image_url: width: 3840
            | image_tag: loading: 'lazy', sizes: sizes, widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840', alt: alt
          }}
        {%- else -%}
          {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
        {%- endif -%}
        <span class="deferred-media__poster-button motion-reduce">
          {%- render 'icon-play' -%}
        </span>
      </button>
      <template>
        {%- if section.settings.video_url.type == 'youtube' -%}
          <iframe
            src="https://www.youtube.com/embed/{{ section.settings.video_url.id }}?enablejsapi=1"
            class="js-youtube"
            allow="autoplay; encrypted-media"
            allowfullscreen
            title="{{ section.settings.description | escape }}"
          ></iframe>
        {%- else -%}
          <iframe
            src="https://player.vimeo.com/video/{{ section.settings.video_url.id }}"
            class="js-vimeo"
            allow="autoplay; encrypted-media"
            allowfullscreen
            title="{{ section.settings.description | escape }}"
          ></iframe>
        {%- endif -%}
      </template>
    </deferred-media>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.video.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Video",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "t:sections.video.settings.cover_image.label"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": ["youtube", "vimeo"],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "label": "t:sections.video.settings.video_url.label",
      "placeholder": "t:sections.video.settings.video_url.placeholder",
      "info": "t:sections.video.settings.video_url.info"
    },
    {
      "type": "text",
      "id": "description",
      "label": "t:sections.video.settings.description.label",
      "info": "t:sections.video.settings.description.info"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.video.settings.full_width.label",
      "default": false
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#121212",
      "label": "Text"
    }
  ],
  "presets": [
    {
      "name": "t:sections.video.presets.name"
    }
  ]
}
{% endschema %}
