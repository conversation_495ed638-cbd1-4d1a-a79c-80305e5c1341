/**
 * Bundle Cart Grouping System
 * Handles visual grouping of bundle items in cart and synchronized removal
 */

class BundleCartGrouping {
  constructor() {
    this.init();
  }

  init() {
    console.log('📦 Bundle Cart Grouping initialized');
    
    // Load CSS
    this.loadCSS();
    
    // Listen for cart updates to refresh grouping
    document.addEventListener('cart:updated', () => {
      console.log('🔄 Cart updated, refreshing bundle grouping...');
      this.waitForCartDrawerAndRefresh();
    });

    // Listen for bundle removal events
    document.addEventListener('bundle:removed', (event) => {
      this.handleBundleRemoved(event.detail);
    });

    // Listen for DOM changes in cart drawer
    this.observeCartDrawer();

    // Listen for cart drawer opening
    this.listenForCartDrawerOpen();

    // Initial grouping - single attempt only
    setTimeout(() => {
      this.refreshBundleGrouping(0);
    }, 1000);
  }

  loadCSS() {
    // Check if CSS is already loaded
    if (document.querySelector('link[href*="bundle-cart-grouping.css"]')) {
      return;
    }

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/assets/bundle-cart-grouping.css';
    document.head.appendChild(link);
    console.log('🎨 Bundle grouping CSS loaded');
  }

  observeCartDrawer() {
    // Watch for changes in cart drawer
    const observer = new MutationObserver((mutations) => {
      let shouldRefresh = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check if cart items were added/removed
          const addedNodes = Array.from(mutation.addedNodes);
          const removedNodes = Array.from(mutation.removedNodes);

          if (addedNodes.some(node => node.classList && node.classList.contains('cart-item')) ||
              removedNodes.some(node => node.classList && node.classList.contains('cart-item'))) {
            shouldRefresh = true;
          }
        }
      });

      if (shouldRefresh) {
        console.log('🔄 Cart DOM changed, refreshing bundle grouping...');
        setTimeout(() => this.refreshBundleGrouping(), 100);
      }
    });

    // Observe cart containers
    const cartContainers = [
      document.querySelector('.cart-drawer'),
      document.querySelector('#CartDrawer-CartItems'),
      document.querySelector('.drawer__cart-items-wrapper')
    ].filter(Boolean);

    cartContainers.forEach(container => {
      observer.observe(container, {
        childList: true,
        subtree: true
      });
    });

    console.log(`👀 Observing ${cartContainers.length} cart containers for changes`);
  }

  waitForCartDrawerAndRefresh() {
    console.log('⏳ Waiting for cart drawer to be visible...');

    const checkCartDrawer = (attempt = 1) => {
      if (attempt > 10) {
        console.log('❌ Cart drawer wait timeout, stopping');
        return;
      }

      const cartDrawer = document.querySelector('cart-drawer');
      const cartDrawerOpen = cartDrawer && cartDrawer.classList.contains('is-open');

      console.log(`Attempt ${attempt}: Cart drawer open: ${cartDrawerOpen}`);

      if (cartDrawerOpen) {
        console.log('✅ Cart drawer ready, refreshing bundle grouping...');
        this.refreshBundleGrouping(0); // Reset retry count
      } else {
        setTimeout(() => checkCartDrawer(attempt + 1), 200);
      }
    };

    checkCartDrawer();
  }

  listenForCartDrawerOpen() {
    // Listen for clicks on cart icon/button
    document.addEventListener('click', (event) => {
      const cartTriggers = [
        '.cart-count-bubble',
        '.header__icon--cart',
        '[data-cart-drawer-toggle]',
        '.cart-icon',
        '#cart-icon-bubble'
      ];

      if (cartTriggers.some(selector => event.target.closest(selector))) {
        console.log('🛒 Cart drawer trigger clicked, waiting for drawer...');
        setTimeout(() => {
          this.waitForCartDrawerAndRefresh();
        }, 300);
      }
    });

    // Also listen for cart drawer state changes
    const cartDrawer = document.querySelector('cart-drawer');
    if (cartDrawer) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const isOpen = cartDrawer.classList.contains('is-open');
            if (isOpen) {
              console.log('🛒 Cart drawer opened, refreshing bundle grouping...');
              setTimeout(() => this.refreshBundleGrouping(), 300);
            }
          }
        });
      });

      observer.observe(cartDrawer, {
        attributes: true,
        attributeFilter: ['class']
      });

      console.log('👀 Observing cart drawer state changes');
    }
  }

  refreshBundleGrouping(retryCount = 0) {
    console.log(`🔄 Refreshing bundle grouping display... (attempt ${retryCount + 1})`);

    // Stop infinite retries
    if (retryCount > 5) {
      console.log('❌ Max retries reached, stopping bundle grouping attempts');
      return;
    }

    // Wait for cart to be fully rendered
    setTimeout(() => {
      // Find cart containers with more specific selectors
      const cartContainers = [
        document.querySelector('.drawer__cart-items-wrapper .cart-items'),
        document.querySelector('#CartDrawer-CartItems .cart-items'),
        document.querySelector('.cart-drawer .cart-items'),
        document.querySelector('.cart-page .cart-items'),
        document.querySelector('ul.cart-items'),
        document.querySelector('.cart-items')
      ].filter(Boolean);

      console.log(`Found ${cartContainers.length} cart containers`);

      if (cartContainers.length === 0 && retryCount < 3) {
        console.log(`⚠️ No cart containers found, retrying in 500ms... (${retryCount + 1}/3)`);
        setTimeout(() => this.refreshBundleGrouping(retryCount + 1), 500);
        return;
      } else if (cartContainers.length === 0) {
        console.log('❌ No cart containers found after 3 retries, stopping');
        return;
      }

      cartContainers.forEach(container => {
        this.processCartContainer(container);
      });
    }, 200);
  }

  processCartContainer(container) {
    if (!container) return;

    console.log('Processing cart container:', container);

    // Remove existing bundle groups to avoid duplicates
    const existingGroups = container.querySelectorAll('.cart-bundle-group');
    existingGroups.forEach(group => group.remove());

    // Show all items first (in case they were hidden)
    const allItems = container.querySelectorAll('.cart-item, .cart-drawer-item');
    allItems.forEach(item => {
      item.style.display = '';
      item.classList.remove('bundle-grouped');
    });

    const cartItems = container.querySelectorAll('.cart-item, .cart-drawer-item');
    const bundleGroups = new Map();
    const regularItems = [];

    console.log(`Found ${cartItems.length} cart items`);

    // Group items by bundle ID
    cartItems.forEach(item => {
      const bundleId = this.getBundleIdFromElement(item);
      console.log(`Item ${item.id} has bundle ID: ${bundleId}`);

      if (bundleId) {
        if (!bundleGroups.has(bundleId)) {
          bundleGroups.set(bundleId, []);
        }
        bundleGroups.get(bundleId).push(item);
      } else {
        regularItems.push(item);
      }
    });

    console.log(`Found ${bundleGroups.size} bundle groups:`, Array.from(bundleGroups.keys()));

    // Create bundle groups
    bundleGroups.forEach((items, bundleId) => {
      this.createBundleGroup(container, bundleId, items);
    });

    console.log(`📦 Created ${bundleGroups.size} bundle groups`);
  }

  getBundleIdFromElement(element) {
    // Check data attribute first
    const bundleId = element.getAttribute('data-bundle-id');
    if (bundleId) {
      console.log(`Found bundle ID from data attribute: ${bundleId}`);
      return bundleId;
    }

    // Check for bundle properties in the element's content
    const bundleInfo = element.querySelector('.cart-item__bundle-info');
    if (bundleInfo) {
      console.log('Found bundle info element');
      // Try to extract from nearby elements or data
      const parent = element.closest('[data-bundle-id]');
      if (parent) return parent.getAttribute('data-bundle-id');
    }

    // Check for bundle properties in product options
    const productOptions = element.querySelectorAll('.product-option');
    for (const option of productOptions) {
      const text = option.textContent;
      if (text.includes('Bundle ID:')) {
        const match = text.match(/Bundle ID:\s*([^\s,]+)/);
        if (match) {
          console.log(`Found bundle ID from properties: ${match[1]}`);
          return match[1];
        }
      }
    }

    console.log('No bundle ID found for element:', element);
    return null;
  }

  createBundleGroup(container, bundleId, items) {
    if (items.length === 0) return;

    console.log(`Creating bundle group for ${bundleId} with ${items.length} items`);

    // Check if bundle group already exists
    const existingGroup = container.querySelector(`[data-bundle-id="${bundleId}"].cart-bundle-group`);
    if (existingGroup) {
      console.log('Bundle group already exists, skipping');
      return; // Already grouped
    }

    // Get bundle info from first item
    const firstItem = items[0];
    const bundleName = this.getBundleNameFromItem(firstItem) || 'Bundle';

    // Calculate total price
    const totalPrice = this.calculateBundleTotal(items);

    console.log(`Bundle: ${bundleName}, Total: ${totalPrice}`);

    // Create bundle group container
    const bundleGroup = document.createElement('li');
    bundleGroup.className = 'cart-drawer-item cart-item cart-bundle-group';
    bundleGroup.setAttribute('data-bundle-id', bundleId);
    
    bundleGroup.innerHTML = `
      <div class="cart-bundle-header">
        <h4 class="cart-bundle-title">📦 ${bundleName}</h4>
        <span class="cart-bundle-count">(${items.length} items)</span>
        <button type="button" class="cart-bundle-remove" data-bundle-id="${bundleId}" aria-label="Remove entire bundle">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" class="icon icon-remove">
            <path d="M14 3h-3.53a3.07 3.07 0 00-.6-1.65C9.44.82 8.8.5 8 .5s-1.44.32-1.87.85A3.06 3.06 0 005.53 3H2a.5.5 0 000 1h1.25v10c0 .28.22.5.5.5h8.5a.5.5 0 00.5-.5V4H14a.5.5 0 000-1zM6.91 1.98c.23-.29.58-.48 1.09-.48s.85.19 1.09.48c.2.24.3.6.36 1.02h-2.9c.05-.42.17-.78.36-1.02zm4.84 11.52h-7.5V4h7.5v9.5z" fill="currentColor"/>
            <path d="M6.55 5.25a.5.5 0 00-.5.5v6a.5.5 0 001 0v-6a.5.5 0 00-.5-.5zM9.45 5.25a.5.5 0 00-.5.5v6a.5.5 0 001 0v-6a.5.5 0 00-.5-.5z" fill="currentColor"/>
          </svg>
        </button>
      </div>
      <div class="cart-bundle-items"></div>
      <div class="cart-bundle-summary">
        <span>Bundle Total:</span>
        <span class="cart-bundle-total-price">${totalPrice}</span>
      </div>
    `;

    // Move items into bundle group
    const bundleItemsContainer = bundleGroup.querySelector('.cart-bundle-items');
    items.forEach((item, index) => {
      console.log(`Processing bundle item ${index + 1}:`, item);

      // Clone the item and modify for bundle display
      const bundleItem = item.cloneNode(true);
      bundleItem.className = 'cart-bundle-item';
      bundleItem.removeAttribute('id'); // Remove duplicate IDs

      // Hide individual remove buttons in the cloned item
      const removeButtons = bundleItem.querySelectorAll('.cart-remove, [data-cart-remove], cart-remove-button');
      removeButtons.forEach(btn => btn.style.display = 'none');

      // Add bundle item styling
      bundleItem.style.borderBottom = '1px solid #eee';
      bundleItem.style.padding = '0.5rem';
      bundleItem.style.background = 'white';

      bundleItemsContainer.appendChild(bundleItem);

      // Hide original item
      item.style.display = 'none';
      item.classList.add('bundle-grouped');

      console.log(`Hidden original item and added to bundle group`);
    });

    // Insert bundle group before first item
    const firstOriginalItem = items[0];
    firstOriginalItem.parentNode.insertBefore(bundleGroup, firstOriginalItem);

    console.log(`✅ Bundle group created and inserted for ${bundleId}`);
  }

  getBundleNameFromItem(item) {
    // Try to find bundle name in properties or data
    const bundleInfo = item.querySelector('.cart-item__bundle-info');
    if (bundleInfo) {
      const nameElement = bundleInfo.querySelector('[data-bundle-name]');
      if (nameElement) return nameElement.textContent;
    }

    // Try to extract from item properties (if available in DOM)
    const propertiesElement = item.querySelector('.product-option');
    if (propertiesElement && propertiesElement.textContent.includes('Bundle Name:')) {
      const match = propertiesElement.textContent.match(/Bundle Name:\s*([^,\n]+)/);
      if (match) return match[1].trim();
    }

    return 'Bundle';
  }

  calculateBundleTotal(items) {
    let total = 0;
    
    items.forEach(item => {
      const priceElement = item.querySelector('.cart-item__final-price, .cart-item__price, .price');
      if (priceElement) {
        const priceText = priceElement.textContent.replace(/[^\d.,]/g, '');
        const price = parseFloat(priceText.replace(',', '.'));
        if (!isNaN(price)) {
          total += price;
        }
      }
    });

    // Format as currency (basic formatting)
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'GBP' // Adjust based on store currency
    }).format(total);
  }

  handleBundleRemoved(detail) {
    const { bundleId } = detail;
    console.log(`🗑️ Bundle removed: ${bundleId}`);
    
    // Remove bundle group from DOM
    const bundleGroups = document.querySelectorAll(`[data-bundle-id="${bundleId}"].cart-bundle-group`);
    bundleGroups.forEach(group => {
      group.remove();
    });

    // Show original items (in case they're still in DOM)
    const originalItems = document.querySelectorAll(`[data-bundle-id="${bundleId}"]:not(.cart-bundle-group)`);
    originalItems.forEach(item => {
      item.style.display = '';
    });
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.bundleCartGrouping = new BundleCartGrouping();
  });
} else {
  window.bundleCartGrouping = new BundleCartGrouping();
}

// Export for manual initialization
window.BundleCartGrouping = BundleCartGrouping;

// Add global function for manual testing
window.refreshBundleGrouping = function() {
  console.log('🔧 Manual bundle grouping refresh triggered');
  if (window.bundleCartGrouping) {
    window.bundleCartGrouping.refreshBundleGrouping();
  } else {
    console.log('Bundle grouping system not initialized');
  }
};

// Add debug function to check cart state
window.debugBundleGrouping = function() {
  console.log('🔍 Debug: Cart containers found:');
  const containers = document.querySelectorAll('.cart-items, ul.cart-items, .drawer__cart-items-wrapper');
  containers.forEach((container, index) => {
    console.log(`Container ${index + 1}:`, container);
    const items = container.querySelectorAll('.cart-item, .cart-drawer-item');
    console.log(`  - Items: ${items.length}`);
    items.forEach((item, itemIndex) => {
      const bundleId = item.getAttribute('data-bundle-id');
      console.log(`    Item ${itemIndex + 1}: ${item.id} - Bundle ID: ${bundleId || 'NONE'}`);
    });
  });

  console.log('🔍 Debug: Cart drawer state:');
  const cartDrawer = document.querySelector('cart-drawer');
  console.log('Cart drawer element:', cartDrawer);
  console.log('Cart drawer open:', cartDrawer && cartDrawer.classList.contains('is-open'));
};
