html {scroll-behavior: smooth;}img, video {max-width: 100%;height: auto;vertical-align: bottom;}button {background-color: transparent;outline: none;border: none;color: rgb(var(--color-foreground));}button:not([disabled]) {cursor: pointer;}a {color: inherit;}a:not(:hover) {text-decoration: none;}@media screen and (max-device-width: 480px) {body {-webkit-text-size-adjust: 100%;}}.display-block, splide-component {display: block;}.grid, .list-unstyled, summary {list-style: none;}.customer a, .link, .share-button__button:hover {text-decoration: underline;text-underline-offset: 0.3rem;}.link-with-icon, .nowrap {white-space: nowrap;}.js details[open]:not(.menu-opening) > .header__icon--menu .icon-close, .quantity__rules-cart .loading-overlay:not(.hidden) ~ *, .visibility-hidden, product-info .loading-overlay:not(.hidden) ~ * {visibility: hidden;}details[open] > .header__submenu, details[open] > .search-modal, details[open] > .share-button__fallback {animation: animateMenuOpen var(--duration-default) ease;}.header__menu-item:hover .header__active-menu-item, details[open]:hover > .header__menu-item {text-decoration-thickness: 0.2rem;}.badge, .break, .header__heading-link {word-break: break-word;}.color-background-1, :root {--color-foreground: var(--color-base-text);--color-background: var(--color-base-background-1);--gradient-background: var(--gradient-base-background-1);--color-link: var(--color-base-outline-button-labels);--alpha-link: 0.85;--color-button: var(--color-base-accent-1);--color-button-text: var(--color-base-solid-button-labels);--alpha-button-background: 1;--alpha-button-border: 1;--color-badge-background: var(--color-background);--color-badge-border: var(--color-foreground);--alpha-badge-border: 0.1;--accent-color: var(--color-base-accent-1);--border-color: var(--color-foreground);}.color-background-2 {--color-foreground: var(--color-base-text);--color-background: var(--color-base-background-2);--gradient-background: var(--gradient-base-background-2);--accent-color: var(--color-base-accent-1);}.color-inverse {--color-foreground: var(--color-base-background-1);--color-background: var(--color-base-text);--gradient-background: rgb(var(--color-base-text));--accent-color: var(--color-base-accent-1);}.color-accent-1, .color-accent-2 {--color-foreground: var(--color-base-solid-button-labels);--accent-color: var(--color-foreground);}.color-accent-1 {--color-background: var(--color-base-accent-1);--gradient-background: var(--gradient-base-accent-1);}.color-accent-2 {--color-background: var(--color-base-accent-2);--gradient-background: var(--gradient-base-accent-2);}.color-foreground-outline-button {--color-foreground: var(--color-base-outline-button-labels);}.color-foreground-accent-1 {--color-foreground: var(--color-base-accent-1);}.color-foreground-accent-2 {--color-foreground: var(--color-base-accent-2);}.color-foreground-text {--color-foreground: var(--color-base-text);}.accent-color-outline-button {--accent-color: var(--color-base-outline-button-labels);}.accent-color-accent-1 {--accent-color: var(--color-base-accent-1);}.accent-color-accent-2 {--accent-color: var(--color-base-accent-2);}.accent-color-background-1 {--accent-color: var(--color-base-background-1);}.accent-color-background-2 {--accent-color: var(--color-base-background-2);}.accent-color-text {--accent-color: var(--color-base-text);}.accent-2-color-outline-button {--accent-2-color: var(--color-base-outline-button-labels);}.accent-2-color-accent-1 {--accent-2-color: var(--color-base-accent-1);}.accent-2-color-accent-2 {--accent-2-color: var(--color-base-accent-2);}.accent-2-color-background-1 {--accent-2-color: var(--color-base-background-1);}.accent-2-color-background-2 {--accent-2-color: var(--color-base-background-2);}.accent-2-color-text {--accent-2-color: var(--color-base-text);}.text-color-outline-button {color: rgb(var(--color-base-outline-button-labels));}.text-color-accent-1 {color: rgb(var(--color-base-accent-1));}.text-color-accent-2 {color: rgb(var(--color-base-accent-2));}.text-color-text {color: rgb(var(--color-base-text));}.color-accent-1, .color-accent-2, .color-background-2, .color-inverse {--color-link: var(--color-foreground);--alpha-link: 0.7;--color-button: var(--color-foreground);--color-button-text: var(--color-background);--color-badge-background: var(--color-background);--color-badge-border: var(--color-background);--alpha-badge-border: 1;}.button--secondary, .button--tertiary {--color-button-text: var(--color-base-outline-button-labels);}.button--secondary {--color-button: var(--color-base-outline-button-labels);--color-button: var(--color-background);--alpha-button-background: 0;}.color-accent-1 .button--secondary, .color-accent-2 .button--secondary, .color-background-2 .button--secondary, .color-inverse .button--secondary {--color-button: var(--color-background);--color-button-text: var(--color-foreground);}.button--tertiary {--color-button: var(--color-base-outline-button-labels);--alpha-button-background: 0;--alpha-button-border: 0.2;}.color-accent-1 .button--tertiary, .color-accent-2 .button--tertiary, .color-background-2 .button--tertiary, .color-inverse .button--tertiary {--color-button: var(--color-foreground);--color-button-text: var(--color-foreground);}.color-background-1, .color-background-2, :root {--color-card-hover: var(--color-base-text);}.color-inverse {--color-card-hover: var(--color-base-background-1);}.color-accent-1, .color-accent-2 {--color-card-hover: var(--color-base-solid-button-labels);}.color-icon-text, :root {--color-icon: rgb(var(--color-base-text));}.color-icon-accent-1 {--color-icon: rgb(var(--color-base-accent-1));}.color-icon-accent-2 {--color-icon: rgb(var(--color-base-accent-2));}.color-icon-outline-button {--color-icon: rgb(var(--color-base-outline-button-labels));}.material-icon--custom-color {color: var(--color-icon);}.contains-card--product, .product-card-wrapper .card {--border-radius: var(--product-card-corner-radius);--border-width: var(--product-card-border-width);--border-opacity: var(--product-card-border-opacity);--shadow-horizontal-offset: var(--product-card-shadow-horizontal-offset);--shadow-vertical-offset: var(--product-card-shadow-vertical-offset);--shadow-blur-radius: var(--product-card-shadow-blur-radius);--shadow-opacity: var(--product-card-shadow-opacity);--shadow-visible: var(--product-card-shadow-visible);--image-padding: var(--product-card-image-padding);--text-alignment: var(--product-card-text-alignment);}.collection-card-wrapper .card, .contains-card--collection {--border-radius: var(--collection-card-corner-radius);--border-width: var(--collection-card-border-width);--border-opacity: var(--collection-card-border-opacity);--shadow-horizontal-offset: var(--collection-card-shadow-horizontal-offset);--shadow-vertical-offset: var(--collection-card-shadow-vertical-offset);--shadow-blur-radius: var(--collection-card-shadow-blur-radius);--shadow-opacity: var(--collection-card-shadow-opacity);--shadow-visible: var(--collection-card-shadow-visible);--image-padding: var(--collection-card-image-padding);--text-alignment: var(--collection-card-text-alignment);}.article-card-wrapper .card, .contains-card--article {--border-radius: var(--blog-card-corner-radius);--border-width: var(--blog-card-border-width);--border-opacity: var(--blog-card-border-opacity);--shadow-horizontal-offset: var(--blog-card-shadow-horizontal-offset);--shadow-vertical-offset: var(--blog-card-shadow-vertical-offset);--shadow-blur-radius: var(--blog-card-shadow-blur-radius);--shadow-opacity: var(--blog-card-shadow-opacity);--shadow-visible: var(--blog-card-shadow-visible);--image-padding: var(--blog-card-image-padding);--text-alignment: var(--blog-card-text-alignment);}.contains-content-container, .content-container {--border-radius: var(--text-boxes-radius);--border-width: var(--text-boxes-border-width);--border-opacity: var(--text-boxes-border-opacity);--shadow-horizontal-offset: var(--text-boxes-shadow-horizontal-offset);--shadow-vertical-offset: var(--text-boxes-shadow-vertical-offset);--shadow-blur-radius: var(--text-boxes-shadow-blur-radius);--shadow-opacity: var(--text-boxes-shadow-opacity);--shadow-visible: var(--text-boxes-shadow-visible);}.contains-media, .global-media-settings {--border-radius: var(--media-radius);--border-width: var(--media-border-width);--border-opacity: var(--media-border-opacity);--shadow-horizontal-offset: var(--media-shadow-horizontal-offset);--shadow-vertical-offset: var(--media-shadow-vertical-offset);--shadow-blur-radius: var(--media-shadow-blur-radius);--shadow-opacity: var(--media-shadow-opacity);--shadow-visible: var(--media-shadow-visible);}.hidden, .no-js-inline, .no-js:not(html), html.no-js .no-js-hidden {display: none !important;}html.no-js .no-js:not(html) {display: block !important;}html.no-js .no-js-inline {display: inline-block !important;}.page-width {max-width: var(--page-width);margin: 0 auto;padding: 0 1.5rem;}.page-width-desktop {padding: 0;margin: 0 auto;}.page-width--full {max-width: none;}.isolate {position: relative;z-index: 0;}.circle {border-radius: 50%;overflow: hidden;}.full-width {width: 100%;}.margin-0 {margin: 0;}.flex {display: flex;}.flex-center {display: flex;align-items: center;justify-content: center;}.flex-space-between {display: flex;align-items: center;justify-content: space-between;}.flex-align-center {align-items: center;}.flex-justify-start, .flex-justify-left {justify-content: flex-start;}.flex-justify-center {justify-content: center;}.flex-justify-end, .flex-justify-right {justify-content: flex-end;}.flex-grow {flex-grow: 1;}.flex-shrink-0 {flex-shrink: 0;}.text--bold {font-weight: var(--font-body-weight-bold);}.section + .section {margin-top: var(--spacing-sections-mobile);}.element-margin-top {margin-top: 5rem;}.pointer-events--none {pointer-events: none;}.color-accent-1, .color-accent-2, .color-background-1, .color-background-2, .color-inverse, .color-custom, body {color: rgba(var(--color-foreground), 0.9);background-color: rgb(var(--color-background));}.custom-border {border: solid var(--border-thickness) rgba(var(--border-color), var(--border-opacity));}.custom-border-hex {border: solid var(--border-thickness) var(--border-color);}.custom-border-radius {border-radius: var(--border-radius);}.custom-bg {background: var(--bg-color);}.no-background {background: none;}.index-0 {--index: 0;}.index-1 {--index: 1;}.index-2 {--index: 2;}.index-3 {--index: 3;}.index-4 {--index: 4;}@media screen and (min-width: 750px) {.desktop-index-0 {--index: 0;}.desktop-index-1 {--index: 1;}.desktop-index-2 {--index: 2;}.desktop-index-3 {--index: 3;}.desktop-index-4 {--index: 4;}.desktop-hidden {display: none !important;}.desktop-full-page {max-width: none;padding: 0;}}@media screen and (max-width: 749px) {.mobile-index-0 {--index: 0;}.mobile-index-1 {--index: 1;}.mobile-index-2 {--index: 2;}.mobile-index-3 {--index: 3;}.mobile-index-4 {--index: 4;}.mobile-hidden {display: none !important;}.mobile-full-page {max-width: none;padding: 0;}.mobile-page-width-padding {padding-left: 1.5rem;padding-right: 1.5rem;}}.track-order-form {padding-bottom: 2rem;margin: 0 auto;max-width: 60rem;}.track-order-form .field {margin-top: 3rem;}.background-secondary {background-color: rgba(var(--color-foreground), 0.04);}.grid-auto-flow {display: grid;grid-auto-flow: column;}.page-margin, .shopify-challenge__container {margin: 7rem auto;}.rte-width {max-width: 82rem;margin: 0 auto 2rem;}.list-unstyled {margin: 0;padding: 0;}.visually-hidden {position: absolute !important;overflow: hidden;width: 1px;height: 1px;margin: -1px;padding: 0;border: 0;clip: rect(0 0 0 0);word-wrap: normal !important;}.visually-hidden--inline {margin: 0;height: 1em;}.overflow-hidden, .overflow-hidden-mobile, .overflow-hidden-tablet {overflow: hidden;}.skip-to-content-link:focus {z-index: 9999;position: inherit;overflow: auto;width: auto;height: auto;clip: auto;}.icon-arrow, .title-wrapper-with-link .link-with-icon svg {width: 1.5rem;}.full-width-link {position: absolute;top: 0;right: 0;bottom: 0;left: 0;z-index: 2;}.disclosure-has-popup, .media, summary {position: relative;}::selection {background-color: rgba(var(--color-foreground), 0.2);}.text-body {font-size: 1.5rem;letter-spacing: 0.06rem;line-height: calc(1 + 0.8 / var(--font-body-scale));font-family: var(--font-body-family);font-style: var(--font-body-style);font-weight: var(--font-body-weight);}.h0, .h1, .h2, .h3, .h4, .h5, h1, h2, h3, h4, h5 {font-family: var(--font-heading-family);font-style: var(--font-heading-style);font-weight: var(--font-heading-weight);letter-spacing: calc( var(--font-heading-scale) * var(--font-heading-letter-spacing) );color: rgb(var(--color-foreground));line-height: calc( 1 + var(--font-heading-line-height) / max(1, var(--font-heading-scale)) );word-break: break-word;}.h6, .link--text:hover, blockquote, h6 {color: rgba(var(--color-foreground), 0.9);}.hxl {font-size: calc(var(--font-heading-scale) * 4rem);}.h0 {font-size: calc(var(--font-heading-scale) * 3.25rem);}.h1, h1 {font-size: calc(var(--font-heading-scale) * 2.25rem);}.h2, h2 {font-size: calc(var(--font-heading-scale) * 2rem);}.h3, h3 {font-size: calc(var(--font-heading-scale) * 1.7rem);}.h4, h4 {font-size: calc(var(--font-heading-scale) * 1.45rem);}.h5, h5 {font-size: calc(var(--font-heading-scale) * 1.2rem);}.font-size--desktop-auto {font-size: var(--font-size);}@media only screen and (min-width: 750px) {.hxl {font-size: calc(var(--font-heading-scale) * 5.5rem);}.h0 {font-size: calc(var(--font-heading-scale) * 4.25rem);}.h1, h1 {font-size: calc(var(--font-heading-scale) * 3rem);}.h2, h2 {font-size: calc(var(--font-heading-scale) * 2.4rem);}.h3, h3 {font-size: calc(var(--font-heading-scale) * 1.8rem);}.h5, h5 {font-size: calc(var(--font-heading-scale) * 1.3rem);}.font-size--desktop-auto {font-size: calc(var(--font-size) * 1.15);}}.h6, h6 {margin-block-start: 1.67em;margin-block-end: 1.67em;}blockquote {font-style: italic;border-left: 0.2rem solid rgba(var(--color-foreground), 0.2);padding-left: 1rem;}.caption {font-size: 1rem;letter-spacing: 0.07rem;line-height: calc(1 + 0.7 / var(--font-body-scale));}.caption-with-letter-spacing {font-size: 1rem;letter-spacing: 0.13rem;line-height: calc(1 + 0.2 / var(--font-body-scale));text-transform: uppercase;}.caption-with-letter-spacing--medium {font-size: 1.2rem;letter-spacing: 0.16rem;}.caption-with-letter-spacing--large {font-size: 1.4rem;letter-spacing: 0.18rem;}.caption-large, .customer .field input, .customer select, .field__input, .form__label, .select__select {font-size: 1.3rem;line-height: calc(1 + 0.5 / var(--font-body-scale));letter-spacing: 0.04rem;}.color-foreground, .link--text {color: rgb(var(--color-foreground));}table:not([class]) {table-layout: fixed;border-collapse: collapse;font-size: 1.4rem;border-style: hidden;box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2);}table:not([class]) td, table:not([class]) th {padding: 1em;border: 0.1rem solid rgba(var(--color-foreground), 0.2);}.left {text-align: left;}.center {text-align: center;}.right {text-align: right;}.order-first {order: -1;}@media screen and (max-width: 749px) {.mobile-left {text-align: left;}.mobile-center {text-align: center;}.mobile-right {text-align: right;}.mobile-order-first {order: -1;}}@media screen and (min-width: 750px) {.desktop-left {text-align: left;}.desktop-center {text-align: center;}.desktop-right {text-align: right;}.desktop-order-first {order: -1;}}.uppercase {text-transform: uppercase;}.light {opacity: 0.7;}.circle-divider:last-of-type::after, a:empty, article:empty, div:empty, dl:empty, h1:empty, h2:empty, h3:empty, h4:empty, h5:empty, h6:empty, p:empty, section:empty, ul:empty {display: none;}.customer a, .link {cursor: pointer;display: inline-block;border: none;box-shadow: none;color: rgb(var(--color-link));background-color: transparent;font-size: 1.4rem;font-family: inherit;}.music-player__btn[disabled], a:not([href]) {cursor: not-allowed;}.link-with-icon {display: inline-flex;font-size: 1.4rem;font-weight: 600;letter-spacing: 0.1rem;text-decoration: none;margin-bottom: 4.5rem;}.link-with-icon .icon {width: 1.5rem;margin-left: 1rem;}.circle-divider::after {content: "\2022";margin: 0 1.3rem 0 1.5rem;}hr {border: none;height: 0.1rem;background-color: rgba(var(--color-foreground), 0.2);display: block;margin: 5rem 0;}.full-unstyled-link {text-decoration: none;color: currentColor;display: block;}.placeholder {background-color: rgba(var(--color-foreground), 0.04);color: rgba(var(--color-foreground), 0.55);fill: rgba(var(--color-foreground), 0.55);}details > * {box-sizing: border-box;}:root {--duration-short: 100ms;--duration-default: 200ms;--duration-long: 500ms;}.customer a, .underlined-link {color: rgba(var(--color-link), var(--alpha-link));text-underline-offset: 0.3rem;text-decoration-thickness: 0.1rem;transition: text-decoration-thickness 0.1s;}.customer a:hover, .underlined-link:hover {color: rgb(var(--color-link));text-decoration-thickness: 0.2rem;}.h3 .icon-arrow, h3 .icon-arrow {width: calc(var(--font-heading-scale) * 1.5rem);}.animate-arrow .icon-arrow path {transform: translateX(-0.25rem);transition: transform var(--duration-short) ease;}.animate-arrow:hover .icon-arrow path {transform: translateX(-0.05rem);}.cart-discount {border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);}.cart-discount-form {margin: 1.5rem 0;}.cart-discount-form__row {display: flex;align-items: center;}.cart-discount-form .button {min-width: auto;min-height: 4.7rem;padding: 0 2.5rem;margin-left: 0.5rem;}.cart-discount-form .field input {padding-left: 1.25rem;}.cart-discount-form .field input:focus ~ label, .cart-discount-form .field label, .cart-discount-form .field__input:not(:placeholder-shown) ~ .field__label {left: calc(var(--inputs-border-width) + 1.25rem);}.cart-discount-form__error {margin: 0;font-size: 1.4rem;display: none;color: #dd1d1d;}.shopify-policy__container {padding-bottom: 3rem;}summary {cursor: pointer;}summary .icon-caret {position: absolute;height: 0.6rem;right: 1.5rem;top: calc(50% - 0.2rem);}summary::-webkit-details-marker {display: none;}.disclosure-has-popup[open] > summary::before {position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 2;display: block;cursor: default;content: " ";background: 0 0;}.disclosure-has-popup > summary::before {display: none;}.disclosure-has-popup[open] > summary + * {z-index: 100;}.no-js .focus-inset:focus:not(:focus-visible), .no-js .focus-offset:focus:not(:focus-visible), .no-js :focus:not(:focus-visible), :focus {outline: 0;box-shadow: none;}:focus-visible {outline: 0.2rem solid rgba(var(--color-foreground), 0.5);outline-offset: 0.3rem;box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);}.focused, .no-js :focus {outline: 0.2rem solid rgba(var(--color-foreground), 0.5);outline-offset: 0.3rem;box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);}.focus-inset:focus-visible {outline: 0.2rem solid rgba(var(--color-foreground), 0.5);outline-offset: -0.2rem;box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3);}.focused.focus-inset, .no-js .focus-inset:focus {outline: 0.2rem solid rgba(var(--color-foreground), 0.5);outline-offset: -0.2rem;box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3);}.focus-none {box-shadow: none !important;outline: 0 !important;}.focus-offset:focus-visible {outline: 0.2rem solid rgba(var(--color-foreground), 0.5);outline-offset: 1rem;box-shadow: 0 0 0 1rem rgb(var(--color-background)), 0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);}.focus-offset.focused, .no-js .focus-offset:focus {outline: 0.2rem solid rgba(var(--color-foreground), 0.5);outline-offset: 1rem;box-shadow: 0 0 0 1rem rgb(var(--color-background)), 0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);}.rte:after {clear: both;content: "";display: block;}.rte > p:first-child {margin-top: 0;}.rte > p:last-child {margin-bottom: 0;}.rte table {table-layout: fixed;}@media screen and (min-width: 750px) {.rte table td {padding-left: 1.2rem;padding-right: 1.2rem;}}.rte img {border: var(--media-border-width) solid rgba(var(--color-foreground), var(--media-border-opacity));border-radius: var(--media-radius);box-shadow: var(--media-shadow-horizontal-offset) var(--media-shadow-vertical-offset) var(--media-shadow-blur-radius) rgba(var(--color-shadow), var(--media-shadow-opacity));margin-bottom: var(--media-shadow-vertical-offset);}.rte ul, .rte ol {list-style-position: inside;padding-left: 2rem;}.rte li {list-style: inherit;}.rte li:last-child {margin-bottom: 0;}.rte a {color: rgba(var(--color-link), var(--alpha-link));text-underline-offset: 0.3rem;text-decoration-thickness: 0.1rem;transition: text-decoration-thickness var(--duration-short) ease;}.rte a:hover {color: rgb(var(--color-link));text-decoration-thickness: 0.2rem;}.rte blockquote {display: inline-flex;}.rte blockquote > * {margin: -0.5rem 0 -0.5rem 0;}.content-rte > * {margin: 0;}.content-rte > * + * {margin-top: 2rem;}.content-rte > product-form {display: block;}.title, .title-wrapper-with-link {margin: 3rem 0 2rem;}.title-wrapper-with-link .title {margin: 0;}.title .link {font-size: inherit;}.title-wrapper {margin-bottom: 3rem;}.title-wrapper-with-link {display: flex;justify-content: center;align-items: flex-end;gap: 1rem;margin-bottom: 3rem;flex-wrap: wrap;}.title-wrapper-with-link--spacing {justify-content: space-between;}.title--primary {margin: 4rem 0;}.title-wrapper--self-padded-mobile, .title-wrapper--self-padded-tablet-down {padding-left: 1.5rem;padding-right: 1.5rem;}.title-wrapper-with-link .link-with-icon {margin: 0;flex-shrink: 0;display: flex;align-items: center;}.title-wrapper-with-link a {color: rgb(var(--color-link));margin-top: 0;flex-shrink: 0;}.title-wrapper--no-top-margin, .title-wrapper--no-top-margin > .title {margin-top: 0;}.title-with-highlight {--hightlight-color: rgb(var(--accent-color));}.title-with-highlight strong {color: var(--hightlight-color);font-weight: var(--font-heading-weight);}.subtitle {font-size: 1.8rem;line-height: calc(1 + 0.8 / var(--font-body-scale));letter-spacing: 0.06rem;color: rgba(var(--color-foreground), 0.7);}.subtitle--small {font-size: 1.4rem;letter-spacing: 0.1rem;}.subtitle--medium {font-size: 1.6rem;letter-spacing: 0.08rem;}.collage-wrapper-title, .collection-hero__title, .collection__title, .main-page-title, .page-title, .title--primary, .title-wrapper, .title-wrapper--no-top-margin, .title-wrapper--self-padded-mobile, .title-wrapper--self-padded-tablet-down, .title-wrapper-with-link {text-align: center;}section-group {display: block;}.section-group-grid {display: grid;grid-template-columns: repeat(2, 1fr);place-items: center;}.section-group__container .page-width, .section-group__container div[class*="-padding"] {padding: 0;}.section-group__container .section-group__container__child-grid {grid-template-columns: repeat(1, 1fr);}.section-group__container {width: 100%;min-width: 0;}@media screen and (max-width: 999px) {.section-group-grid {grid-template-columns: repeat(1, 1fr);}.section-group-grid--mobile-reverse .section-group__section-two-container {order: -1;}}.grid {display: flex;flex-wrap: wrap;align-items: flex-start;margin-bottom: 2rem;padding: 0;column-gap: var(--grid-mobile-horizontal-spacing);row-gap: var(--grid-mobile-vertical-spacing);}.grid--stretch, .grid--stretch ul.splide__list {align-items: stretch;}.grid--stretch .splide__slide__container {height: 100%;}.grid:last-child, .mb-0 {margin-bottom: 0;}.grid__item {width: calc(25% - var(--grid-mobile-horizontal-spacing) * 3 / 4);max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);flex-grow: 1;flex-shrink: 0;}.grid--gapless.grid {column-gap: 0;row-gap: 0;}.grid--1-col .grid__item {max-width: 100%;width: 100%;}.grid--3-col .grid__item {width: calc(33.33% - var(--grid-mobile-horizontal-spacing) * 2 / 3);}@media screen and (min-width: 750px) {.page-width {padding: 0 5rem;}.page-width--narrow {padding: 0 9rem;}.page-width-desktop {padding: 0;}.page-width-tablet {padding: 0 5rem;}.desktop-full-page.desktop-full-page-no-padding {padding: 0;}.section + .section {margin-top: var(--spacing-sections-desktop);}.element-margin {margin-top: calc(5rem + var(--page-width-margin));}blockquote {padding-left: 1.5rem;}.caption {font-size: 1.2rem;}hr {margin: 7rem 0;}.disclosure-has-popup[open] > summary + * {z-index: 4;}.facets .disclosure-has-popup[open] > summary + * {z-index: 2;}.title-wrapper--self-padded-mobile {padding-left: 0;padding-right: 0;}.grid {column-gap: var(--grid-desktop-horizontal-spacing);row-gap: var(--grid-desktop-vertical-spacing);}.grid__item {width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}.grid--3-col .grid__item {width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);}}.grid--2-col .grid__item {width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);}@media screen and (min-width: 750px) {.grid--2-col .grid__item {width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}.grid--4-col-tablet .grid__item {width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);}.grid--3-col-tablet .grid__item {width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);}.grid--2-col-tablet .grid__item {width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}}@media screen and (max-width: 989px) {.grid--1-col-tablet-down .grid__item {width: 100%;max-width: 100%;}.slider--tablet.grid--peek {margin: 0;width: 100%;}.slider--tablet.grid--peek .grid__item {box-sizing: content-box;margin: 0;}}@media screen and (min-width: 990px) {.page-width--narrow {max-width: 72.6rem;padding: 0;}.page-width-desktop {max-width: var(--page-width);padding: 0 5rem;}.large-up-hide {display: none !important;}.title, .title-wrapper-with-link {margin: 5rem 0 3rem;}.title--primary {margin: 2rem 0;}.title-wrapper-with-link {align-items: center;}.title-wrapper-with-link .title {margin-bottom: 0;}.title-wrapper--self-padded-tablet-down {padding-left: 0;padding-right: 0;}.grid--6-col-desktop .grid__item {width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);max-width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);}.grid--5-col-desktop .grid__item {width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);max-width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);}.grid--4-col-desktop .grid__item {width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);max-width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);}.grid--3-col-desktop .grid__item {width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);}.grid--2-col-desktop .grid__item {width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}.grid--1-col-desktop {flex: 0 0 100%;max-width: 100%;}.grid--1-col-desktop .grid__item {width: 100%;max-width: 100%;}}@media screen and (max-width: 749px) {.small-hide {display: none !important;}.grid__item.slider__slide--full-width {width: 100%;max-width: none;}.grid--peek.slider--mobile {margin: 0;width: 100%;}.grid--peek.slider--mobile .grid__item {box-sizing: content-box;margin: 0;}.grid--peek .grid__item {min-width: 35%;}.grid--peek.slider .grid__item:first-of-type {margin-left: 1.5rem;}.grid--peek.slider:after {margin-left: calc(-1 * var(--grid-mobile-horizontal-spacing));}.grid--2-col-tablet-down .grid__item {width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);}.grid--peek .grid__item, .slider--tablet.grid--peek.grid--2-col-tablet-down .grid__item {width: calc(50% - var(--grid-mobile-horizontal-spacing) - 3rem);}.slider--mobile.grid--peek.grid--1-col-tablet-down .grid__item, .slider--tablet.grid--peek.grid--1-col-tablet-down .grid__item {width: calc(100% - var(--grid-mobile-horizontal-spacing) - 3rem);}}@media screen and (min-width: 750px) and (max-width: 989px) {.medium-hide {display: none !important;}.slider--tablet.grid--peek .grid__item {width: calc(25% - var(--grid-desktop-horizontal-spacing) - 3rem);}.slider--tablet.grid--peek.grid--3-col-tablet .grid__item {width: calc(33.33% - var(--grid-desktop-horizontal-spacing) - 3rem);}.slider--tablet.grid--peek.grid--2-col-tablet .grid__item, .slider--tablet.grid--peek.grid--2-col-tablet-down .grid__item {width: calc(50% - var(--grid-desktop-horizontal-spacing) - 3rem);}.slider--tablet.grid--peek .grid__item:first-of-type {margin-left: 1.5rem;}.grid--2-col-tablet-down .grid__item {width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}.grid--1-col-tablet-down.grid--peek .grid__item {width: calc(100% - var(--grid-desktop-horizontal-spacing) - 3rem);}}.media {display: block;background-color: rgba(var(--color-foreground), 0.1);overflow: hidden;}.media--transparent {background-color: transparent;}.media model-viewer, .media > :not(.zoom):not(.deferred-media__poster-button) {display: block;max-width: 100%;position: absolute;top: 0;left: 0;height: 100%;width: 100%;}.media > img, .media > video, .media > internal-video video {object-fit: cover;object-position: center center;transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);}.media > internal-video video {width: 100%;height: 100%;}.media--fit-contain > img, .media--fit-contain > video, .media--fit-contain > internal-video video {object-fit: contain;}.media--square {padding-bottom: 100%;}.media--portrait {padding-bottom: 125%;}.media--landscape {padding-bottom: 66.6%;}.media--cropped {padding-bottom: 56%;}.media--16-9 {padding-bottom: 56.25%;}.media--circle {padding-bottom: 100%;border-radius: 50%;}.media.media--hover-effect > img + img {opacity: 0;}deferred-media, details[open] .modal-overlay {display: block;}.button, .cart__dynamic-checkout-buttons [role="button"], .cart__dynamic-checkout-buttons iframe, .customer button, .shopify-challenge__button, .shopify-payment-button [role="button"], button.shopify-payment-button__button--unbranded {--shadow-horizontal-offset: var(--buttons-shadow-horizontal-offset);--shadow-vertical-offset: var(--buttons-shadow-vertical-offset);--shadow-blur-radius: var(--buttons-shadow-blur-radius);--shadow-opacity: var(--buttons-shadow-opacity);--shadow-visible: var(--buttons-shadow-visible);--border-offset: var(--buttons-border-offset);--border-opacity: calc(1 - var(--buttons-border-opacity));border-radius: var(--buttons-radius-outset);position: relative;font-weight: 700;}.button, .customer button, .shopify-challenge__button, button.shopify-payment-button__button--unbranded {min-width: calc(12rem + var(--buttons-border-width) * 2);min-height: calc(4.5rem + var(--buttons-border-width) * 2);font-weight: 700;}.shopify-payment-button__button--branded {z-index: auto;}.cart__dynamic-checkout-buttons iframe {box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius) rgba(var(--color-base-text), var(--shadow-opacity));}.button, .customer button, .shopify-challenge__button {display: inline-flex;justify-content: center;align-items: center;border: 0;padding: 0 3rem;font: inherit;text-decoration: none;color: rgb(var(--color-button-text));transition: box-shadow var(--duration-short) ease;-webkit-appearance: none;appearance: none;background-color: rgba(var(--color-button), var(--alpha-button-background));font-weight: 700;z-index: 0;}.button span {position: relative;z-index: 2;}.button--prefix-icon .button__label::before, .button--suffix-icon .button__label::after {content: "";font-size: inherit;display: block;width: var(--icon-scale);height: var(--icon-scale);background-size: cover;background-position: center;position: absolute;top: 50%;transform: translateY(-50%);}.button--prefix-icon .button__label {padding-left: calc(var(--icon-scale) + var(--icon-spacing));}.button--prefix-icon .button__label::before {left: 0;}.button--suffix-icon .button__label {padding-right: calc(var(--icon-scale) + var(--icon-spacing));}.button--suffix-icon .button__label::after {right: 0;}.button.loading .button__label::before, .button.loading .button__label::after {display: none;}.music-player__btn, .scroll-to-top-btn {display: flex;border-radius: 50%;box-shadow: 0.1rem 0.1rem 1rem rgba(0, 0, 0, 0.3);}.select__select, .text-area {font-family: var(--font-body-family);font-style: var(--font-body-style);font-weight: var(--font-body-weight);}.button:before, .cart__dynamic-checkout-buttons [role="button"]:before, .customer button:before, .shopify-challenge__button:before, .shopify-payment-button [role="button"]:before, .shopify-payment-button__button--unbranded:before {content: "";position: absolute;top: 0;right: 0;bottom: 0;left: 0;z-index: -1;border-radius: var(--buttons-radius-outset);box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius) rgba(var(--color-shadow), var(--shadow-opacity));}.button:after, .customer button:after, .shopify-challenge__button:after, .shopify-payment-button__button--unbranded:after {content: "";position: absolute;top: var(--buttons-border-width);right: var(--buttons-border-width);bottom: var(--buttons-border-width);left: var(--buttons-border-width);z-index: 1;border-radius: var(--buttons-radius);box-shadow: 0 0 0 calc(var(--buttons-border-width) + var(--border-offset)) rgba(var(--color-button-text), var(--border-opacity)), 0 0 0 var(--buttons-border-width) rgba(var(--color-button), var(--alpha-button-background));transition: box-shadow var(--duration-short) ease;}.button:not([disabled]):hover::after, .customer button:hover::after, .shopify-challenge__button:hover::after, .shopify-payment-button__button--unbranded:hover::after {--border-offset: 1.3px;box-shadow: 0 0 0 calc(var(--buttons-border-width) + var(--border-offset)) rgba(var(--color-button-text), var(--border-opacity)), 0 0 0 calc(var(--buttons-border-width) + 1px) rgba(var(--color-button), var(--alpha-button-background));}.button--secondary:before, .button--secondary:after {--border-opacity: var(--buttons-border-opacity);}.button.focused, .button:focus, .button:focus-visible, .shopify-payment-button [role="button"]:focus, .shopify-payment-button [role="button"]:focus-visible, .shopify-payment-button__button--unbranded:focus, .shopify-payment-button__button--unbranded:focus-visible {outline: 0;box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.5rem rgba(var(--color-foreground), 0.5), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);}.button:focus:not(:focus-visible):not(.focused), .shopify-payment-button [role="button"]:focus:not(:focus-visible):not(.focused), .shopify-payment-button__button--unbranded:focus:not(:focus-visible):not( .focused ) {box-shadow: inherit;}.button::selection, .customer button::selection, .shopify-challenge__button::selection {background-color: rgba(var(--color-button-text), 0.3);}.button, .button-label, .customer button, .shopify-challenge__button {font-size: 1.6rem;letter-spacing: 0.1rem;line-height: calc(1 + 0.2 / var(--font-body-scale));}.button--tertiary {font-size: 1.2rem;padding: 1rem 1.5rem;min-width: calc(9rem + var(--buttons-border-width) * 2);min-height: calc(3.5rem + var(--buttons-border-width) * 2);}.button--small {padding: 1.2rem 2.6rem;}.button--has-spinner:not(.loading) .loading-overlay__spinner {display: none;}.mb-1em {margin-bottom: 1em;}.btn--mt-center {margin: 1em auto 0;width: fit-content;display: flex;}.scroll-to-top-btn {width: 4rem;height: 4rem;justify-content: center;align-items: center;padding: 0.5rem;overflow: hidden;border: none;}.scroll-to-top-btn svg {width: 2.2rem;height: 2.2rem;pointer-events: none;}.music-player, .scroll-to-top-btn {--sticky-atc-offset: 0px;position: fixed;transition: 0.15s ease-in-out;bottom: calc(var(--offset-y) + var(--sticky-atc-offset));z-index: 2;}.music-player-bottom-left, .scroll-to-top-btn-bottom-left {left: var(--offset-x);}.music-player-bottom-center, .scroll-to-top-btn-bottom-center {left: 50%;transform: translateX(-50%);}.music-player-bottom-right, .scroll-to-top-btn-bottom-right {right: var(--offset-x);}.music-player {background: 0 0;}.music-player__audio {width: 0;height: 0;}.music-player__btn {width: 5rem;height: 5rem;justify-content: center;align-items: center;position: relative;z-index: 0;background: rgb(var(--color-background));border: none;}.music-player__btn svg {color: rgb(var(--color-foreground));height: 2.5rem;pointer-events: none;}.ripple-animation::after, .ripple-animation::before {content: "";display: block;position: absolute;top: 0;left: 0;bottom: 0;right: 0;z-index: -1;border-radius: 50%;border: solid 1px rgb(var(--color-background));animation: 2s linear infinite ripple;}.ripple-animation::after {animation-delay: 1s;}.music-player__btn--paused .music-player__btn__paused-icon, .music-player__btn--playing .music-player__btn__playing-icon, .share-button__close:not(.hidden) + .share-button__copy {display: none;}.music-player__btn__playing-icon {transform: translateX(10%);}@keyframes ripple {0% {opacity: 0;transform: none;}20%, 70% {opacity: 1;}100% {opacity: 0;transform: scale(1.5);}}.button.disabled, .button:disabled, .button[aria-disabled="true"], .customer button.disabled, .customer button:disabled, .customer button[aria-disabled="true"], .quantity__button.disabled {cursor: not-allowed;opacity: 0.5;}.button--full-width {display: flex;width: 100%;}.button--large-text {font-size: 1.9rem;}.button.loading {color: transparent;position: relative;}.button.loading > .loading-overlay__spinner {top: 50%;left: 50%;transform: translate(-50%, -50%);position: absolute;z-index: 2;height: 100%;display: flex;align-items: center;}.header__icon:hover .icon, .modal__close-button:hover .icon, .share-button__fallback button:hover svg {transform: scale(1.07);}.button.loading > .loading-overlay__spinner .spinner, .share-button details {width: fit-content;}.button.loading > .loading-overlay__spinner .path {stroke: rgb(var(--color-button-text));}@media (hover: hover) {.link-btns--arrow a.button:not([aria-disabled="true"]), .action-btns--arrow button.button:not([disabled], [disabled="true"], .button--tertiary) {transition: 0.3s ease-in-out;overflow: hidden;}.link-btns--arrow a.button:not([aria-disabled="true"])::before, .action-btns--arrow button.button:not( [disabled], [disabled="true"], .button--tertiary )::before {top: 50%;right: 3rem;left: auto;bottom: auto;z-index: 1;box-shadow: none;border-radius: 0;background: rgb(var(--color-button-text));transform: translate(calc(3rem + 100%), -50%);height: 0.65em;width: 2em;clip-path: polygon( 0% 32%, 70% 32%, 70% 0%, 100% 50%, 70% 100%, 70% 68%, 0% 68% );transition: transform 0.3s ease-in-out;}.link-btns--arrow a.button:not([aria-disabled="true"]):hover, .action-btns--arrow button.button:not([disabled], [disabled="true"], .button--tertiary):hover {padding-right: 6rem;}.link-btns--arrow a.button:not([aria-disabled="true"]):hover::before, .action-btns--arrow button.button:not( [disabled], [disabled="true"], .button--tertiary ):hover::before {transform: translate(50%, -50%);}.link-btns--center a.button:not([aria-disabled="true"])::before, .link-btns--left a.button:not([aria-disabled="true"])::before, .action-btns--center button.button:not([disabled], [disabled="true"], .button--tertiary)::before, .action-btns--left button.button:not( [disabled], [disabled="true"], .button--tertiary )::before {--alpha-button-background: 1;background-color: rgba( var(--color-button-text), var(--alpha-button-background) );background: none;z-index: -2;box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius) rgba(var(--color-shadow), var(--shadow-opacity)), inset 0 0 0 calc(var(--buttons-border-width) + var(--border-offset)) rgba(var(--color-button-text), var(--border-opacity)), inset 0 0 0 var(--buttons-border-width) rgba(var(--color-button), var(--alpha-button-background));}.link-btns--center a.button:not([aria-disabled="true"]), .link-btns--left a.button:not([aria-disabled="true"]), .action-btns--center button.button:not([disabled], [disabled="true"], .button--tertiary), .action-btns--left button.button:not([disabled], [disabled="true"], .button--tertiary) {--transition-duration: 0.3s;background-color: transparent;transition: color var(--transition-duration);}.link-btns--center a.button:not([aria-disabled="true"])::after, .link-btns--left a.button:not([aria-disabled="true"])::after, .action-btns--center button.button:not([disabled], [disabled="true"], .button--tertiary)::after, .action-btns--left button.button:not([disabled], [disabled="true"], .button--tertiary)::after {background-color: rgba(var(--color-button), var(--alpha-button-background));z-index: -1;transition: transform var(--transition-duration);box-shadow: none;}.link-btns--center a.button--secondary:not([aria-disabled="true"])::after, .link-btns--left a.button--secondary:not([aria-disabled="true"])::after, .action-btns--center button.button--secondary:not( [disabled], [disabled="true"], .button--tertiary )::after, .action-btns--left button.button--secondary:not( [disabled], [disabled="true"], .button--tertiary )::after {background-color: rgb(var(--color-button-text));transform: scaleX(0);}.link-btns--center a.button:not([aria-disabled="true"], .button--secondary)::after, .link-btns--left a.button:not([aria-disabled="true"], .button--secondary)::after, .action-btns--center button.button:not( [disabled], [disabled="true"], .button--secondary, .button--tertiary )::after, .action-btns--left button.button:not( [disabled], [disabled="true"], .button--secondary, .button--tertiary )::after {top: 0;right: 0;bottom: 0;left: 0;}.link-btns--center a.button:not([aria-disabled="true"]):hover, .link-btns--left a.button:not([aria-disabled="true"]):hover, .action-btns--center button.button:not([disabled], [disabled="true"], .button--tertiary):hover, .action-btns--left button.button:not([disabled], [disabled="true"], .button--tertiary):hover {color: rgb(var(--color-button));}.link-btns--center a.button:not([aria-disabled="true"]):hover::after, .link-btns--left a.button:not([aria-disabled="true"]):hover::after, .action-btns--center button.button:not( [disabled], [disabled="true"], .button--tertiary ):hover::after, .action-btns--left button.button:not( [disabled], [disabled="true"], .button--tertiary ):hover::after {transform: scaleX(0);}.link-btns--center a.button--secondary:not([aria-disabled="true"]):hover::after, .link-btns--left a.button--secondary:not([aria-disabled="true"]):hover::after, .action-btns--center button.button--secondary:not( [disabled], [disabled="true"], .button--tertiary ):hover::after, .action-btns--left button.button--secondary:not( [disabled], [disabled="true"], .button--tertiary ):hover::after {transform: scaleX(1);}.link-btns--left a.button:not([aria-disabled="true"]), .action-btns--left button.button:not([disabled], [disabled="true"], .button--tertiary) {--transition-duration: 0.4s;}.link-btns--left a.button:not([aria-disabled="true"])::after, .action-btns--left button.button:not([disabled], [disabled="true"], .button--tertiary)::after {transform-origin: left;}.link-btns--left a.button:not([aria-disabled="true"]):hover::after, .action-btns--left button.button:not( [disabled], [disabled="true"], .button--tertiary ):hover::after {transform-origin: right;}.action-btns--arrow button.button.loading:not(.button--tertiary)::before {background: none;}.action-btns--arrow button.button.loading:not(.button--tertiary), .action-btns--center button.button.loading:not(.button--tertiary), .action-btns--left button.button.loading:not(.button--tertiary) {color: transparent;transition: color 0s;}.action-btns--center button.button.loading:not(.button--tertiary):hover .path, .action-btns--left button.button.loading:not(.button--tertiary):hover .path {stroke: rgb(var(--color-button));}}.share-button {display: block;position: relative;}.share-button__button {font-size: 1.4rem;display: flex;align-items: center;color: rgb(var(--color-link));margin-left: 0;padding-left: 0;min-height: 2.5rem;}.share-button__button, .share-button__fallback button {cursor: pointer;background-color: transparent;border: none;}.share-button__button .icon-share {height: 1.2rem;margin-right: 1rem;min-width: 1.3rem;}.share-button__fallback {display: flex;align-items: center;position: absolute;top: 3rem;left: 0.1rem;z-index: 3;width: 100%;min-width: max-content;border-radius: var(--inputs-radius);border: 0;}.customer .field:after, .customer select:after, .field:after, .localization-form__select:after, .quantity:after, .select:after, .share-button__fallback:after {pointer-events: none;content: "";position: absolute;top: var(--inputs-border-width);right: var(--inputs-border-width);bottom: var(--inputs-border-width);left: var(--inputs-border-width);border: 0.1rem solid transparent;border-radius: var(--inputs-radius);box-shadow: 0 0 0 var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-border-opacity));transition: box-shadow var(--duration-short) ease;z-index: 1;}.field, .select, .text-area {position: relative;width: 100%;}.quantity:before, .share-button__fallback:before {background: rgb(var(--color-background));pointer-events: none;content: "";position: absolute;top: 0;right: 0;bottom: 0;left: 0;border-radius: var(--inputs-radius-outset);box-shadow: var(--inputs-shadow-horizontal-offset) var(--inputs-shadow-vertical-offset) var(--inputs-shadow-blur-radius) rgba(var(--color-base-text), var(--inputs-shadow-opacity));z-index: -1;}.share-button__fallback button {width: 4.4rem;height: 4.4rem;padding: 0;flex-shrink: 0;display: flex;justify-content: center;align-items: center;position: relative;right: var(--inputs-border-width);}.share-button__fallback button:hover {color: rgba(var(--color-foreground), 0.9);}.share-button__close, .share-button__copy {background-color: transparent;color: rgb(var(--color-foreground));}.share-button__close:focus-visible, .share-button__copy:focus-visible {background-color: rgb(var(--color-background));z-index: 2;}.share-button__close:focus, .share-button__copy:focus {background-color: rgb(var(--color-background));z-index: 2;}.field:not(:focus-visible):not(.focused) + .share-button__close:not(:focus-visible):not(.focused), .field:not(:focus-visible):not(.focused) + .share-button__copy:not(:focus-visible):not(.focused) {background-color: inherit;}.share-button__fallback .field:after, .share-button__fallback .field:before {content: none;}.share-button__fallback .field {border-radius: 0;min-width: auto;min-height: auto;transition: none;}.share-button__fallback .field__input:-webkit-autofill, .share-button__fallback .field__input:focus {outline: 0.2rem solid rgba(var(--color-foreground), 0.5);outline-offset: 0.1rem;box-shadow: 0 0 0 0.1rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);}.share-button__fallback .field__input {box-shadow: none;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;filter: none;min-width: auto;min-height: auto;}.share-button__fallback .field__input:hover {box-shadow: none;}.share-button__fallback .icon {width: 1.5rem;height: 1.5rem;}.share-button__message:not(:empty) {display: flex;align-items: center;width: 100%;height: 100%;margin-top: 0;padding: 0.8rem 0 0.8rem 1.5rem;margin: var(--inputs-border-width);}.share-button__message:not(:empty):not(.hidden) ~ * {display: none;}.list-social {display: flex;flex-wrap: wrap;justify-content: flex-end;}@media only screen and (max-width: 749px) {.list-social {justify-content: center;}}.list-social__item .icon {height: 1.8rem;width: 1.8rem;}.list-social__link {align-items: center;display: flex;padding: 1.3rem;color: rgb(var(--color-foreground));}.list-social__link:hover .icon {transform: scale(1.07);}.customer .field input, .customer select, .field__input, .select__select {-webkit-appearance: none;appearance: none;background-color: rgb(var(--color-background));color: rgb(var(--color-foreground));font-size: 1.6rem;width: 100%;box-sizing: border-box;transition: box-shadow var(--duration-short) ease;border-radius: var(--inputs-radius);height: 4.5rem;min-height: calc(var(--inputs-border-width) * 2);min-width: calc(7rem + (var(--inputs-border-width) * 2));position: relative;border: 0;}.customer .field:before, .customer select:before, .field:before, .localization-form__select:before, .select:before {pointer-events: none;content: "";position: absolute;top: 0;right: 0;bottom: 0;left: 0;border-radius: var(--inputs-radius-outset);box-shadow: var(--inputs-shadow-horizontal-offset) var(--inputs-shadow-vertical-offset) var(--inputs-shadow-blur-radius) rgba(var(--color-base-text), var(--inputs-shadow-opacity));z-index: -1;}.select__select {font-size: 1.4rem;color: rgba(var(--color-foreground), 0.9);}.customer .field:hover.field:after, .customer select:hover.select:after, .field:hover.field:after, .localization-form__select:hover.localization-form__select:after, .select:hover.select:after, .select__select:hover.select__select:after {box-shadow: 0 0 0 var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-hover-border-opacity));outline: 0;border-radius: var(--inputs-radius);}.customer .field input:focus-visible, .customer select:focus-visible, .field__input:focus-visible, .localization-form__select:focus-visible.localization-form__select:after, .select__select:focus-visible {box-shadow: 0 0 0 var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-hover-border-opacity));outline: 0;border-radius: var(--inputs-radius);}.customer .field input:focus, .customer select:focus, .field__input:focus, .localization-form__select:focus.localization-form__select:after, .select__select:focus {box-shadow: 0 0 0 var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-hover-border-opacity));outline: 0;border-radius: var(--inputs-radius);}.localization-form__select:focus {outline: 0;box-shadow: none;}.select, .text-area {display: flex;}.customer select + svg, .select .icon-caret {height: 0.6rem;pointer-events: none;position: absolute;top: 50%;transform: translateY(-50%);right: calc(var(--inputs-border-width) + 1.5rem);}.customer select, .select__select {cursor: pointer;line-height: calc(1 + 0.6 / var(--font-body-scale));padding: 0 calc(var(--inputs-border-width) + 3rem) 0 2rem;margin: var(--inputs-border-width);min-height: calc(var(--inputs-border-width) * 2);}.select {--inputs-radius: var(--pickers-radius);--inputs-border-width: var(--pickers-border-width);--inputs-border-opacity: var(--pickers-border-opacity);--inputs-shadow-opacity: var(--pickers-shadow-opacity);--inputs-shadow-horizontal-offset: var(--pickers-shadow-horizontal-offset);--inputs-margin-offset: var(--pickers-margin-offset);--inputs-shadow-vertical-offset: var(--pickers-shadow-vertical-offset);--inputs-shadow-blur-radius: var(--pickers-shadow-blur-radius);--inputs-radius-outset: var(--pickers-radius-outset);--inputs-hover-border-opacity: var(--pickers-hover-border-opacity);}.select::after {--color-foreground: var(--pickers-border-color);}.select:hover {--pickers-overlay-opacity: var(--pickers-hover-overlay-opacity);}.select__select {background: linear-gradient( rgba(var(--accent-color), var(--pickers-overlay-opacity)), rgba(var(--accent-color), var(--pickers-overlay-opacity)) ), rgb(var(--color-background));color: rgb(var(--accent-2-color));}.select__select--no-bg, facet-filters-form .select__select {background: none;}.select__select:focus-visible, .select__select:focus {box-shadow: 0 0 0 var(--inputs-border-width) rgba(var(--pickers-border-color), var(--inputs-hover-border-opacity));}.select .icon-caret {color: rgb(var(--accent-2-color));}.variant-selects--small {display: flex;gap: 1rem;flex-wrap: wrap;}.variant-selects--small .product-form__input--dropdown {flex: 0 0 auto;margin: 0;}.variant-selects--small .form__label {text-align: left;margin-bottom: 0;}.select--small {--inputs-radius: var(--pickers-small-radius);}.select--small .select__select {font-size: 1.2rem;height: 2.25em;padding: 0 2.5rem 0 0.8rem;}.select--small .icon-caret {right: calc(var(--inputs-border-width) + 0.75rem);}[data-skip-non-existent="true"] .non-existent, [data-skip-non-existent="true"] .non-existent + label, [data-skip-unavailable="true"] .unavailable, [data-skip-unavailable="true"] .unavailable + label {display: none !important;}.field {display: flex;transition: box-shadow var(--duration-short) ease;}.customer .field {display: flex;}.field--with-error {flex-wrap: wrap;}.customer .field input, .field__input {flex-grow: 1;text-align: left;padding: 1.5rem;margin: var(--inputs-border-width);transition: box-shadow var(--duration-short) ease;}.customer .field label, .field__label {font-size: 1.6rem;left: calc(var(--inputs-border-width) + 2rem);top: calc(1rem + var(--inputs-border-width));margin-bottom: 0;pointer-events: none;position: absolute;transition: top var(--duration-short) ease, font-size var(--duration-short) ease;color: rgba(var(--color-foreground), 0.9);letter-spacing: 0.1rem;line-height: 1.5;}.customer .field input:-webkit-autofill ~ label, .customer .field input:focus ~ label, .customer .field input:not(:placeholder-shown) ~ label, .field__input:-webkit-autofill ~ .field__label, .field__input:focus ~ .field__label, .field__input:not(:placeholder-shown) ~ .field__label {font-size: 1rem;top: calc(var(--inputs-border-width) + 0.5rem);left: calc(var(--inputs-border-width) + 2rem);letter-spacing: 0.04rem;}.customer .field input:-webkit-autofill, .customer .field input:focus, .customer .field input:not(:placeholder-shown), .field__input:-webkit-autofill, .field__input:focus, .field__input:not(:placeholder-shown) {padding: 2.2rem 1.5rem 0.8rem 2rem;margin: var(--inputs-border-width);}.customer .field input::-webkit-search-cancel-button, .field__input::-webkit-search-cancel-button {display: none;}.customer .field input::placeholder, .field__input::placeholder {opacity: 0;}.field__button {align-items: center;background-color: transparent;border: 0;color: currentColor;cursor: pointer;display: flex;height: 4.4rem;justify-content: center;overflow: hidden;padding: 0;position: absolute;right: 0;top: 0;width: 4.4rem;}.quantity, .quantity__rules, .quantity__rules-cart, details-disclosure > details, header-menu > details {position: relative;}.field__button > svg {height: 2.5rem;width: 2.5rem;}.customer .field input:-webkit-autofill ~ label, .field__input:-webkit-autofill ~ .field__button, .field__input:-webkit-autofill ~ .field__label {color: #000;}.text-area {min-height: 10rem;resize: none;}input[type="checkbox"] {display: inline-block;width: auto;margin-right: 0.5rem;}.form__label {display: block;margin-bottom: 0.6rem;}.form__message {align-items: center;display: flex;font-size: 1.4rem;line-height: 1;margin-top: 1rem;}.form-status, .form__message--large {font-size: 1.6rem;}.customer .field .form__message {font-size: 1.4rem;text-align: left;}.customer .form__message svg, .form__message .icon {flex-shrink: 0;height: 1.3rem;margin-right: 0.5rem;width: 1.3rem;}.customer .form__message svg, .form__message--large .icon {height: 1.5rem;width: 1.5rem;margin-right: 1rem;}.customer .field .form__message svg {align-self: start;}.form-status {margin: 0;}.form-status-list {padding: 0;margin: 2rem 0 4rem;}.form-status-list li {list-style-position: inside;}.form-status-list .link::first-letter {text-transform: capitalize;}.quantity {color: rgba(var(--color-foreground));width: calc(14rem / var(--font-body-scale) + var(--inputs-border-width) * 2);display: flex;border-radius: var(--inputs-radius);min-height: calc((var(--inputs-border-width) * 2) + 4.5rem);}.quantity__input {color: currentColor;font-size: 1.4rem;font-weight: 500;text-align: center;padding: 0 0.5rem;width: 100%;flex-grow: 1;-webkit-appearance: none;appearance: none;border: none;}.quantity__button {width: calc(4.5rem / var(--font-body-scale));flex-shrink: 0;font-size: 1.8rem;cursor: pointer;display: flex;align-items: center;justify-content: center;color: rgb(var(--color-foreground));padding: 0;}@media screen and (max-width: 749px) {.quantity {width: calc( 12.5rem / var(--font-body-scale) + var(--inputs-border-width) * 2 );min-height: calc((var(--inputs-border-width) * 2) + 4rem);}.quantity__button {width: calc(4rem / var(--font-body-scale));}.quantity:after, .quantity {border-radius: calc((var(--inputs-radius) / 4) * 3);}.quantity:before {border-radius: calc( ((var(--inputs-radius) / 4) * 3) + var(--inputs-border-width) );}cart-remove-button .button {min-width: calc(4rem / var(--font-body-scale));min-height: 4rem;}}.quantity--full {width: 100%;}.announcement-bar, .header-wrapper--border-bottom, .search-modal {border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);}.quantity__button:first-child {margin-left: calc(var(--inputs-border-width));}.quantity__button:last-child {margin-right: calc(var(--inputs-border-width));}.quantity__button svg {width: 1rem;pointer-events: none;}.quantity__input:-webkit-autofill, .quantity__input:-webkit-autofill:active, .quantity__input:-webkit-autofill:hover {box-shadow: 0 0 0 10rem rgb(var(--color-background)) inset !important;-webkit-box-shadow: 0 0 0 10rem rgb(var(--color-background)) inset !important;}.quantity__input::-webkit-inner-spin-button, .quantity__input::-webkit-outer-spin-button {-webkit-appearance: none;margin: 0;}.quantity__input[type="number"] {-moz-appearance: textfield;}.main-quantity {--inputs-radius: var(--quantity-radius);--inputs-radius-inner: var(--inputs-radius);--inputs-border-width: var(--quantity-border-width);--inputs-border-opacity: var(--quantity-border-opacity);--inputs-shadow-opacity: var(--quantity-shadow-opacity);--inputs-shadow-horizontal-offset: var(--quantity-shadow-horizontal-offset);--inputs-margin-offset: var(--quantity-margin-offset);--inputs-shadow-vertical-offset: var(--quantity-shadow-vertical-offset);--inputs-shadow-blur-radius: var(--quantity-shadow-blur-radius);--inputs-radius-outset: var(--quantity-radius-outset);--inputs-hover-border-opacity: var(--quantity-hover-border-opacity);}.main-quantity::before {background: none;}.main-quantity::after {--color-foreground: var(--quantity-border-color);}.main-quantity:hover {--quantity-overlay-opacity: var(--quantity-hover-overlay-opacity);}.main-quantity .quantity__button, .main-quantity .quantity__input {background: linear-gradient( rgba(var(--accent-color), var(--quantity-overlay-opacity)), rgba(var(--accent-color), var(--quantity-overlay-opacity)) ), rgb(var(--color-background));color: rgb(var(--accent-2-color));margin-top: calc(var(--inputs-border-width));margin-bottom: calc(var(--inputs-border-width));}.main-quantity .quantity__button:first-child {border-top-left-radius: var(--inputs-radius-inner);border-bottom-left-radius: var(--inputs-radius-inner);}.main-quantity .quantity__button:last-child {border-top-right-radius: var(--inputs-radius-inner);border-bottom-right-radius: var(--inputs-radius-inner);}.main-quantity .quantity__input:focus-visible, .main-quantity .quantity__input:focus {box-shadow: 0 0 0 var(--inputs-border-width) rgba(var(--quantity-border-color), var(--inputs-hover-border-opacity));box-shadow: none;outline: none;}.main-quantity .icon {color: rgb(var(--accent-2-color));}.quantity__rules {margin-top: 1.2rem;font-size: 1.2rem;}.quantity__rules .caption {display: inline-block;margin-top: 0;margin-bottom: 0;}.cart-count-bubble:empty, .header:not(.header--has-menu) * > .header__search, .modal__toggle-close, .no-js .modal__close-button.link, .no-js details[open] .modal__toggle-open, menu-drawer + .header__search {display: none;}.quantity__rules .divider + .divider::before {content: "\2022";margin: 0 0.5rem;}.modal__toggle {list-style-type: none;}.no-js details[open] .modal__toggle {position: absolute;z-index: 5;}.no-js details[open] svg.modal__toggle-close {display: flex;z-index: 1;height: 1.7rem;width: 1.7rem;}.modal__toggle-open {display: flex;}.modal__close-button.link {display: flex;justify-content: center;align-items: center;padding: 0;height: 4.4rem;width: 4.4rem;background-color: transparent;}.modal__close-button .icon {width: 1.7rem;height: 1.7rem;}.modal__content {position: absolute;top: 0;left: 0;right: 0;bottom: 0;background: rgb(var(--color-background));z-index: 4;display: flex;justify-content: center;align-items: center;}.media-modal {cursor: zoom-out;}.media-modal .deferred-media {cursor: initial;}.cart-count-bubble {position: absolute;background-color: rgb(var(--color-button));color: rgb(var(--color-button-text));height: 1.4rem;width: 1.4rem;border-radius: 100%;display: flex;justify-content: center;align-items: center;font-size: 0.8rem;bottom: 0.8rem;left: 2.2rem;}.announcement-bar, .announcement-bar__link:hover, .header__heading-link:hover .h2, .header__icon {color: rgb(var(--color-foreground));}.cart-count-bubble span {line-height: 1.4rem;}.announcement-bar__item {display: flex;justify-content: center;align-items: center;position: relative;}.announcement-bar__item-left {justify-content: flex-start;}.announcement-bar__item-left .announcement-bar__message {text-align: left;}.announcement-bar__item-right {justify-content: flex-end;}.announcement-bar__item-right .announcement-bar__message {text-align: right;}.announcement-bar__separator::before {content: "";display: block;height: 100%;width: 1px;background-color: rgb(var(--color-foreground));position: absolute;right: calc(var(--gap-desktop) / -2);top: 0;}li:last-child .announcement-bar__separator::before {display: none;}.splide--loop.is-active .announcement-bar__separator::before {display: black;}.announcement-bar__link {display: block;padding: 0;text-decoration: none;}.announcement-bar__link:hover {background-color: rgba(var(--color-card-hover), 0.06);}.announcement-bar__link .icon-arrow {display: inline-block;pointer-events: none;margin-left: 0.8rem;vertical-align: middle;margin-bottom: 0.2rem;}.announcement-bar__link .announcement-bar__message {padding: 0;}.announcement-bar__message {padding: 0;margin: 0;letter-spacing: 0.1rem;line-height: 1.3;font-size: var(--mobile-text-size);display: flex;align-items: center;text-align: center;}.announcement-bar__message .material-icon {font-size: var(--mobile-icon-size);flex-shrink: 0;}.announcement-bar__message__custom-icon {height: var(--mobile-icon-size);width: auto;flex-shrink: 0;}.announcement-bar__message--mobile-horizontal .material-icon, .announcement-bar__message--mobile-horizontal .announcement-bar__message__custom-icon {margin-right: 0.65rem;}.announcement-bar__message--mobile-vertical {flex-direction: column;}.announcement-bar__message--mobile-vertical .material-icon, .announcement-bar__message--mobile-vertical .announcement-bar__message__custom-icon {margin-bottom: 0.2rem;}.announcement-bar__message .list-social {column-gap: calc(var(--mobile-icon-size) / 2);}.announcement-bar__message .link.list-social__link {padding: 0;}.announcement-bar__message .list-social__item .icon {width: var(--mobile-icon-size);height: var(--mobile-icon-size);}.announcement-bar__discount {background: rgba(var(--color-foreground), 0.1);outline: none;border: solid 0.2rem rgb(var(--color-foreground));border-radius: var(--border-radius);position: relative;z-index: 0;}.announcement-bar__discount p {margin: 0;padding: 0 1em;line-height: 2.2;color: rgb(var(--color-foreground));white-space: nowrap;transition: all 0.2s;display: flex;align-items: center;justify-content: center;column-gap: 0.4em;}.announcement-bar__discount__left {border-right: solid 0.2rem rgb(var(--color-foreground));}.announcement-bar__discount__right:hover {background: rgb(var(--color-foreground));color: rgb(var(--color-background));}.announcement-bar__discount__success {position: absolute;top: 0;left: 0;width: 100%;text-align: center;opacity: 0;z-index: -1;}.announcement-bar__discount.success p {opacity: 0;}.announcement-bar__discount.success .announcement-bar__discount__success {opacity: 1;}@media screen and (min-width: 750px) {.announcement-bar .splide .splide__track .splide__list.announcement-bar__desktop-grid {display: grid;align-items: center;grid-template-columns: repeat(var(--columns-desktop), 1fr);column-gap: var(--gap-desktop);row-gap: 1rem;}.announcement-bar .splide .splide__track .splide__list.announcement-bar__desktop-grid[data-columns-desktop="0"] {display: flex;justify-content: center;flex-wrap: wrap;}.announcement-bar__desktop-grid .splide__slide {width: auto !important;margin-right: 0 !important;}.announcement-bar__message--desktop-horizontal {flex-direction: row;}.announcement-bar__message--desktop-horizontal .material-icon, .announcement-bar__message--desktop-horizontal .announcement-bar__message__custom-icon {margin-right: 1rem;margin-bottom: 0;}.announcement-bar__message--desktop-vertical {flex-direction: column;}.announcement-bar__message--desktop-vertical .material-icon, .announcement-bar__message--desktop-vertical .announcement-bar__message__custom-icon {margin-bottom: 0.4rem;margin-right: 0;}.announcement-bar__message {font-size: var(--desktop-text-size);}.announcement-bar__message .material-icon {font-size: var(--desktop-icon-size);}.announcement-bar__message__custom-icon {height: var(--desktop-icon-size);}.announcement-bar__message .list-social {column-gap: calc(var(--desktop-icon-size) / 2);}.announcement-bar__message .list-social__item .icon {width: var(--desktop-icon-size);height: var(--desktop-icon-size);}.announcement-bar__item {padding-left: calc(var(--padding-left) * 1.25);padding-right: calc(var(--padding-right) * 1.25);}}@media screen and (max-width: 749px) {.announcement-bar .splide .splide__track .splide__list.announcement-bar__mobile-grid {display: grid;align-items: center;grid-template-columns: repeat(var(--columns-mobile), 1fr);column-gap: var(--gap-mobile);row-gap: 1rem;}.announcement-bar .splide .splide__track .splide__list.announcement-bar__mobile-grid[data-columns-mobile="0"] {display: flex;justify-content: center;flex-wrap: wrap;}.announcement-bar__mobile-grid .splide__slide {width: auto !important;margin-right: 0 !important;}.announcement-bar__separator::before {right: calc(var(--gap-mobile) / -2);}}.section-header.shopify-section-group-header-group {z-index: 5;margin: 0;}.shopify-section-header-sticky {position: sticky;top: 0;}.shopify-section-header-hidden {top: calc(-1 * var(--header-height));}.shopify-section-header-hidden.menu-open {top: 0;}.section-header.animate {transition: top 0.15s ease-out;}.shopify-section-group-header-group {z-index: 4;}.section-header ~ .shopify-section-group-header-group {z-index: initial;}.header-wrapper {display: block;position: relative;background-color: rgb(var(--color-background));}.header {display: grid;grid-template-areas: "left-icons heading icons";grid-template-columns: 1fr 2fr 1fr;align-items: center;}@media screen and (min-width: 990px) {.media--cropped {padding-bottom: 63%;}.header {grid-template-columns: 1fr auto 1fr;}.header--top-left, .header--middle-left:not(.header--has-menu) {grid-template-areas: "heading icons" "navigation navigation";grid-template-columns: 1fr auto;}.header--top-left.drawer-menu, .header--middle-left.drawer-menu {grid-template-areas: "navigation heading icons";grid-template-columns: auto 1fr auto;column-gap: 1rem;}.header--middle-left {grid-template-areas: "heading navigation icons";grid-template-columns: auto auto 1fr;column-gap: 2rem;}.header--middle-center:not(.drawer-menu) {grid-template-areas: "navigation heading icons";grid-template-columns: 1fr auto 1fr;column-gap: 2rem;}.header--middle-center a.header__heading-link {text-align: center;}.header--top-center {grid-template-areas: "left-icons heading icons" "navigation navigation navigation";}.header--top-center.drawer-menu {grid-template-areas: "left-icons heading icons";grid-template-columns: 1fr auto 1fr;}.header:not(.header--middle-left, .header--middle-center) .header__inline-menu {margin-top: 1.05rem;}}.header [tabindex="-1"]:focus {outline: 0;}.header__heading {margin: 0;line-height: 0;}.header > .header__heading-link {line-height: 0;}.header__heading, .header__heading-link {grid-area: heading;justify-self: center;}.header__heading-link {display: inline-block;padding: 0.75rem;text-decoration: none;word-break: break-word;}.header__heading-link.media {padding: 0;}.header__heading-link .h2 {line-height: 1;color: rgba(var(--color-foreground), 0.9);}.header__heading-logo {height: auto;width: 100%;transition: width 0.3s cubic-bezier(0.52, 0, 0.61, 0.99);}.header__icons {display: flex;grid-area: icons;justify-self: end;}.header__icon span, .header__icon:not(.header__icon--summary) {display: flex;align-items: center;justify-content: center;}.header__icon span {height: 100%;}.header__icon::after {content: none;}.header__icon .icon {height: 2rem;width: 2rem;fill: none;vertical-align: middle;}.header__icon, .header__icon--cart .icon {height: 4.4rem;width: 4.4rem;padding: 0;}.header__icon--cart {position: relative;margin-right: -1.2rem;}.header__icon--cart .icon-cart {width: 2.4rem;height: 2.4rem;}.header__icon--cart svg circle, .header__icon--cart svg polyline {color: rgba(var(--color-foreground), 0.85);}@media screen and (max-width: 989px) {.header__heading, .header__heading-link {text-align: center;}.header--mobile-left .header__heading, .header--mobile-left .header__heading-link {text-align: left;justify-self: start;}.header--mobile-left {grid-template-columns: auto 2fr 1fr;}menu-drawer ~ .header__icons .header__icon--account {display: none;}}.header__icon--menu[aria-expanded="true"]::before {content: "";top: 100%;left: 0;height: calc( var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%)) );width: 100%;display: block;position: absolute;background: rgba(var(--color-base-text), 0.5);}menu-drawer + .header__search {display: none;}.header > .header__search {grid-area: left-icons;justify-self: start;}.header--top-center.drawer-menu > .header__search {margin-left: 3.2rem;}.header--top-center header-drawer {grid-area: left-icons;}.header:not(.header--has-menu) * > .header__search {display: none;}.header__search {display: inline-flex;line-height: 0;}.header--top-center > .header__search, .no-js .predictive-search {display: none;}.header--top-center * > .header__search {display: inline-flex;}@media screen and (min-width: 990px) {.header:not(.header--top-center) * > .header__search, .header--top-center > .header__search {display: inline-flex;}.header:not(.header--top-center) > .header__search, .header--top-center * > .header__search {display: none;}}details[open] > .search-modal {opacity: 1;}details[open] .modal-overlay::after {position: absolute;content: "";background-color: rgb(var(--color-foreground), 0.5);top: 100%;left: 0;right: 0;height: 100vh;}.no-js details[open] > .header__icon--search {top: 1rem;right: 0.5rem;}.search-modal {opacity: 0;min-height: calc( 100% + var(--inputs-margin-offset) + (2 * var(--inputs-border-width)) );height: 100%;}.search-modal__content {display: flex;align-items: center;justify-content: center;width: 100%;height: 100%;padding: 0 5rem 0 1rem;line-height: calc(1 + 0.8 / var(--font-body-scale));position: relative;}.search-modal__content-bottom {bottom: calc((var(--inputs-margin-offset) / 2));}.search-modal__content-top {top: calc((var(--inputs-margin-offset) / 2));}.search-modal__form {width: 100%;}.search-modal__close-button {position: absolute;right: 0.3rem;}.header__icon--menu .icon {display: block;position: absolute;opacity: 1;transform: scale(1);transition: transform 150ms, opacity 150ms;}details:not([open]) > .header__icon--menu .icon-close, details[open] > .header__icon--menu .icon-hamburger {visibility: hidden;opacity: 0;transform: scale(0.8);}.js details[open]:not(.menu-opening) > .header__icon--menu .icon-hamburger {visibility: visible;opacity: 1;transform: scale(1.07);}.js details > .header__submenu {opacity: 0;transform: translateY(-1.5rem);}details[open] > .header__submenu {animation-fill-mode: forwards;z-index: 1;}@media (prefers-reduced-motion) {.motion-reduce {transition: none !important;animation: none !important;}details[open] > .header__submenu {opacity: 1;transform: translateY(0);}}.header__inline-menu {margin-left: -1.2rem;grid-area: navigation;display: none;}.header--top-center .header__heading-link, .header--top-center .header__inline-menu {margin-left: 0;}.header__menu {padding: 0 1rem;}.header__menu-item {padding: 1.2rem;text-decoration: none;color: rgba(var(--color-foreground), 0.9);}details[open] > .header__menu-item {text-decoration: underline;}.header__menu-item:hover {transition: color var(--duration-short) ease;color: rgb(var(--accent-color));}details[open] > .header__menu-item .icon-caret {transform: rotate(180deg);}.header__active-menu-item {transition: text-decoration-thickness var(--duration-short) ease;text-underline-offset: 0.3rem;}.header__active-menu-item-v2 {font-weight: 700;padding: 0 0.7em;position: relative;z-index: 0;}.header__active-menu-item-v2::before {content: "";display: block;position: absolute;top: 0;left: 0;width: 100%;height: 100%;transform: scaleY(1.4);background: rgb(var(--color-background));z-index: -1;border-radius: 0.3rem;}.header__menu-item:hover .header__active-menu-item-v2 {text-decoration: none;text-underline-offset: 0;}.header__submenu {transition: opacity var(--duration-default) ease, transform var(--duration-default) ease;}.global-settings-popup, .header__submenu.global-settings-popup {border-radius: var(--popup-corner-radius);border-color: rgba(var(--color-foreground), var(--popup-border-opacity));border-style: solid;border-width: var(--popup-border-width);box-shadow: var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius) rgba(var(--color-shadow), var(--popup-shadow-opacity));z-index: -1;}.header__submenu.list-menu {padding: 2.4rem 0;}.header__submenu .header__submenu {background-color: rgba(var(--color-foreground), 0.03);padding: 0.5rem 0;margin: 0.5rem 0;}.header__submenu .header__menu-item:after {right: 2rem;}.header__submenu .header__menu-item {justify-content: space-between;padding: 0.8rem 2.4rem;}.header__submenu .header__submenu .header__menu-item {padding-left: 3.4rem;}.header__menu-item .icon-caret {right: 0.8rem;}.header__submenu .icon-caret {flex-shrink: 0;margin-left: 1rem;position: static;}@keyframes animateMenuOpen {0% {opacity: 0;transform: translateY(-1.5rem);}100% {opacity: 1;transform: translateY(0);}}@media screen and (min-width: 750px) {.search-modal__close-button {right: 1rem;}.search-modal__content {padding: 0 6rem;}.overflow-hidden-mobile {overflow: auto;}}@media screen and (min-width: 990px) {.header__heading-link {margin-left: -0.75rem;}.header__heading, .header__heading-link {justify-self: start;}.header--top-center .header__heading, .header--top-center .header__heading-link {justify-self: center;text-align: center;}.header--top-center > .header__search, .header:not(.header--top-center) * > .header__search {display: inline-flex;}.header--top-center * > .header__search, .header:not(.header--top-center) > .header__search {display: none;}.search-modal__form {max-width: 74.2rem;}.search-modal__close-button {position: initial;margin-left: 0.5rem;}.header__inline-menu {display: block;}.header--top-center .header__inline-menu {justify-self: center;}.header--top-center .header__inline-menu > .list-menu--inline {justify-content: center;}.header--middle-left .header__inline-menu {margin-left: 0;}.overflow-hidden-tablet {overflow: auto;}}.badge {border: 1px solid transparent;border-radius: var(--badge-corner-radius);font-size: 1.2rem;letter-spacing: 0.1rem;text-align: center;background-color: rgb(var(--color-badge-background));border-color: rgba(var(--color-badge-border), var(--alpha-badge-border));font-weight: 700;line-height: 1.75em;padding: 0 0.5em;display: inline-flex;align-items: center;column-gap: 0.4em;}.badge svg {height: 1em;}.gradient {background: rgb(var(--color-background));background: var(--gradient-background);}@media screen and (min-width: 750px) {.gradient {background-attachment: fixed;}}@media screen and (forced-colors: active) {.button.loading {color: rgb(var(--color-foreground));}.icon {color: CanvasText;fill: CanvasText !important;}.icon-close-small path {stroke: CanvasText;}}.ratio {display: flex;position: relative;align-items: stretch;}.ratio::before {content: "";width: 0;height: 0;padding-bottom: var(--ratio-percent);}.content-container {border-radius: var(--text-boxes-radius);border: var(--text-boxes-border-width) solid rgba(var(--color-foreground), var(--text-boxes-border-opacity));position: relative;}.content-container:after {content: "";position: absolute;top: calc(var(--text-boxes-border-width) * -1);right: calc(var(--text-boxes-border-width) * -1);bottom: calc(var(--text-boxes-border-width) * -1);left: calc(var(--text-boxes-border-width) * -1);border-radius: var(--text-boxes-radius);box-shadow: var(--text-boxes-shadow-horizontal-offset) var(--text-boxes-shadow-vertical-offset) var(--text-boxes-shadow-blur-radius) rgba(var(--color-shadow), var(--text-boxes-shadow-opacity));z-index: -1;}.content-container--full-width:after {left: 0;right: 0;border-radius: 0;}@media screen and (max-width: 749px) {.content-container--full-width-mobile {border-left: none;border-right: none;border-radius: 0;}.content-container--full-width-mobile:after {display: none;}}.global-media-settings {position: relative;border: var(--media-border-width) solid rgba(var(--color-foreground), var(--media-border-opacity));border-radius: var(--media-radius);overflow: visible !important;background-color: rgb(var(--color-background));}.global-media-settings:after {position: absolute;top: calc(var(--media-border-width) * -1);right: calc(var(--media-border-width) * -1);bottom: calc(var(--media-border-width) * -1);left: calc(var(--media-border-width) * -1);border-radius: var(--media-radius);box-shadow: var(--media-shadow-horizontal-offset) var(--media-shadow-vertical-offset) var(--media-shadow-blur-radius) rgba(var(--color-shadow), var(--media-shadow-opacity));z-index: -1;pointer-events: none;}.global-media-settings--no-shadow {overflow: hidden !important;}.global-media-settings--no-shadow:after {content: none;}.global-media-settings iframe, .global-media-settings img, .global-media-settings model-viewer, .global-media-settings video {border-radius: calc(var(--media-radius) - var(--media-border-width));}.content-container--full-width, .global-media-settings--full-width, .global-media-settings--full-width img {border-radius: 0;border-left: none;border-right: none;}@supports not (inset: 10px) {.grid {margin-left: calc(-1 * var(--grid-mobile-horizontal-spacing));}.grid__item {padding-left: var(--grid-mobile-horizontal-spacing);padding-bottom: var(--grid-mobile-vertical-spacing);}.grid--gapless .grid__item {padding-left: 0;padding-bottom: 0;}@media screen and (min-width: 749px) {.grid--peek .grid__item {padding-left: var(--grid-mobile-horizontal-spacing);}}.product-grid .grid__item {padding-bottom: var(--grid-mobile-vertical-spacing);}@media screen and (min-width: 750px) {.grid {margin-left: calc(-1 * var(--grid-desktop-horizontal-spacing));}.grid__item {padding-left: var(--grid-desktop-horizontal-spacing);padding-bottom: var(--grid-desktop-vertical-spacing);}.product-grid .grid__item {padding-bottom: var(--grid-desktop-vertical-spacing);}}}.font-body-bold {font-weight: var(--font-body-weight-bold);}@media (forced-colors: active) {.button, .customer button, .shopify-challenge__button {border: 1px solid transparent;}.button.focused, .button:focus, .button:focus-visible, .shopify-payment-button [role="button"]:focus, .shopify-payment-button [role="button"]:focus-visible, .shopify-payment-button__button--unbranded:focus, .shopify-payment-button__button--unbranded:focus-visible {outline: transparent solid 1px;}.customer .field input:focus, .customer select:focus, .field__input:focus, .localization-form__select:focus.localization-form__select:after, .select__select:focus {outline: transparent solid 1px;}.localization-form__select:focus {outline: transparent solid 1px;}}internal-video {display: block;position: relative;z-index: 0;}internal-video video {width: 100%;}.internal-video__play {position: absolute;top: 0;left: 0;height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;background: none;border: none;outline: none;z-index: 1;cursor: pointer;}.play-button {width: 5rem;height: 5rem;display: flex;justify-content: center;align-items: center;border-radius: 50%;position: relative;z-index: 0;background: rgb(var(--color-background));outline: none;border: none;cursor: pointer;}.internal-video--playing .play-button {opacity: 0;}.play-button svg {color: rgb(var(--color-foreground));height: 50%;width: 50%;}.internal-video--loading::before {content: "";display: block;width: 4rem;height: 4rem;border: 0.4rem solid rgba(255, 255, 255, 0.3);border-radius: 50%;border-top-color: #fff;animation: spin 1s linear infinite;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);}@keyframes spin {0% {transform: translate(-50%, -50%) rotate(0deg);}100% {transform: translate(-50%, -50%) rotate(360deg);}}.internal-video__timeline {position: absolute;bottom: 0;left: 0;z-index: 2;height: 10px;width: 100%;cursor: grab;}.internal-video__timeline::after {content: "";position: absolute;bottom: 0;left: 0;height: 5px;width: var(--completed, 0%);background: rgb(var(--accent-color));}.internal-video__timeline--top, .internal-video__timeline--top::after {top: 0;bottom: auto;}.internal-video__sound-btn {padding: 0;position: absolute;bottom: 1rem;right: 1rem;z-index: 3;border-radius: 50%;font-size: 3rem;width: 2.8rem;height: 2.8rem;}.internal-video__timeline + .internal-video__sound-btn {transform: translateY(-5px);}.internal-video__sound-btn svg {width: 1.2rem;height: 1.2rem;}.internal-video:not(.internal-video--muted) .internal-video__sound-btn svg:nth-child(1), .internal-video.internal-video--muted .internal-video__sound-btn svg:nth-child(2) {display: none;}.custom-columns {--column-gap: var(--desktop-column-gap);--row-gap: var(--desktop-row-gap);--total-parts: var(--desktop-grid-parts);--vertical-alignment: var(--desktop-vertical-alignment);display: flex;flex-wrap: wrap;align-items: var(--vertical-alignment);row-gap: var(--row-gap);margin-left: calc(var(--column-gap) / -2);margin-left: calc(var(--column-gap) / -2);}.custom-columns__column {--width: var(--desktop-width);padding: 0 calc(var(--column-gap) / 2);width: calc(100% / var(--total-parts) * var(--width));}.custom-columns__block {margin-top: var(--margin-top);margin-bottom: var(--margin-bottom);}.custom-columns__block:first-child {margin-top: 0;}.custom-columns__block:last-child {margin-bottom: 0;}.custom-columns__title {margin: 0;}.buttons-container {display: flex;flex-wrap: wrap;gap: 1rem;word-break: break-word;justify-content: var(--alignment);}.custom-columns__block .payment-badges {justify-content: var(--alignment);}.custom-columns__block-accordion + .custom-columns__block-accordion, .custom-columns__block > .accordion {margin-top: 0;border-top: none;}.custom-font-size {font-size: var(--desktop-text-size);}.custom-font-size-all {font-size: var(--text-size);}.custom-image-height {height: var(--desktop-image-height);width: auto;}.custom-image-width {width: var(--desktop-width);}@media screen and (max-width: 749px) {.custom-columns {--column-gap: var(--mobile-column-gap);--row-gap: var(--mobile-row-gap);--total-parts: var(--mobile-grid-parts);--vertical-alignment: var(--mobile-vertical-alignment);}.custom-columns__column {--width: var(--mobile-width);}.buttons-container, .custom-columns__block .payment-badges {justify-content: var(--mobile-alignment);}.custom-font-size {font-size: var(--mobile-text-size);}.custom-image-height {height: var(--mobile-image-height);}.custom-image-width {width: var(--mobile-width);}}.regular-price {font-weight: 700;color: rgb(var(--accent-color));letter-spacing: 0.1rem;}.compare-price {text-decoration: line-through;color: rgba(var(--color-foreground), 0.7);letter-spacing: 0.1rem;font-size: 0.8em;}.section-id-btn {display: flex;width: fit-content;margin: 0 auto;cursor: pointer;}.section-id-btn[data-success="false"] .copy-success, .section-id-btn[data-success="true"] .copy-text {display: none;}.image-slider {position: relative;}.image-slide {overflow: hidden;}.image-slide__image svg {width: 100%;}.image-slide__desc p {margin: 0;line-height: 1.3;}.image-slide__desc {padding: 1rem;}.splide__container {box-sizing: border-box;position: relative;}.splide__list {backface-visibility: hidden;display: -ms-flexbox;display: flex;height: 100%;margin: 0 !important;padding: 0 !important;}.splide--ltr .splide__list, .splide--rtl .splide__list {align-items: flex-start;}.splide--align-stretch .splide__list {align-items: stretch;}.splide--vertically-centered .splide__list {align-items: center;}.splide.splide--inactive .splide__list {display: block;}.splide__pagination {-ms-flex-align: center;align-items: center;display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;-ms-flex-pack: center;justify-content: center;margin: 0;pointer-events: none;}.splide__pagination li {display: inline-block;line-height: 1;list-style-type: none;margin: 0;pointer-events: auto;}.splide:not(.is-overflow) .splide__pagination {display: none;}.splide__progress__bar {width: 0;}.splide {position: relative;}.splide.is-initialized, .splide.is-rendered {visibility: visible;}.splide__slide {backface-visibility: hidden;box-sizing: border-box;-ms-flex-negative: 0;flex-shrink: 0;list-style-type: none !important;margin: 0;position: relative;}.splide--precalc-width .splide__slide {--columns: var(--columns-mobile);--gap: var(--gap-mobile);width: calc(((100% + var(--gap)) / var(--columns)) - var(--gap));margin-right: var(--gap);}.splide--precalc-width.splide--rtl .splide__slide {margin-right: 0;margin-left: var(--gap);}.splide--precalc-padding .splide__track {padding-left: var(--padding-mobile);padding-right: var(--padding-mobile);}.splide__spinner {animation: splide-loading 1s linear infinite;border: 2px solid #999;border-left-color: transparent;border-radius: 50%;bottom: 0;contain: strict;display: inline-block;height: 20px;left: 0;margin: auto;position: absolute;right: 0;top: 0;width: 20px;}.splide__sr {clip: rect(0 0 0 0);border: 0;height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}.splide__toggle.is-active .splide__toggle__play, .splide__toggle__pause {display: none;}.splide__toggle.is-active .splide__toggle__pause {display: inline;}.splide__track {overflow: hidden;position: relative;z-index: 0;}@keyframes splide-loading {0% {transform: rotate(0);}to {transform: rotate(1turn);}}.splide__track--draggable {-webkit-touch-callout: none;-webkit-user-select: none;-ms-user-select: none;user-select: none;}.splide__track--fade > .splide__list > .splide__slide {margin: 0 !important;opacity: 0;z-index: 0;}.splide__track--fade > .splide__list > .splide__slide:not(.is-active) {pointer-events: none;}.splide__track--fade > .splide__list > .splide__slide.is-active {opacity: 1;z-index: 1;}.splide--rtl {direction: rtl;}.splide__track--ttb > .splide__list {display: block;}.splide__arrow {-ms-flex-align: center;align-items: center;border: 0;border-radius: var(--slider-arrow-border-radius);display: -ms-flexbox;display: flex;font-size: 3rem;font-size: var(--slider-arrow-size);height: 1em;width: 1em;-ms-flex-pack: center;justify-content: center;opacity: 0.7;padding: 0;position: absolute;top: 50%;transform: translateY(-50%);z-index: 1;background-color: rgb(var(--color-background));flex-shrink: 0;}.splide--transparent-arrows .splide__arrow {background-color: transparent;color: rgb(var(--color-background));}.splide__arrow svg {fill: currentColor;height: var(--slider-arrow-icon-size);width: var(--slider-arrow-icon-size);}.splide--small-pagination .splide__arrow svg {height: calc(var(--slider-arrow-icon-size) * 0.8);width: calc(var(--slider-arrow-icon-size) * 0.8);}.splide__arrow:hover:not(:disabled) {opacity: 0.9;}.splide__arrow:disabled {opacity: 0.3;}.splide__arrow:focus-visible {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: 3px;}.splide__arrow--prev {left: 1rem;}.splide__arrow--prev svg {transform: scaleX(-1);}.splide__arrow--next {right: 1rem;}.splide.is-focus-in .splide__arrow:focus {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: 3px;}.splide__pagination {bottom: 0.5rem;left: 0;padding: 0 1rem;position: absolute;right: 0;z-index: 1;}.splide__pagination__page {--dot-color: rgb(var(--color-foreground));border-radius: 50%;display: inline-block;width: var(--pagination-dot-width);height: var(--pagination-dot-height);padding: calc(var(--pagination-dot-spacing) / 2);position: relative;box-sizing: content-box;background: transparent;}.splide__pagination__page.dots-custom-color {--dot-color: rgb(var(--color-background));}.splide__pagination__page::before {content: "";display: block;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);border-radius: var(--pagination-dot-radius);width: var(--pagination-dot-width);height: var(--pagination-dot-height);background: var(--dot-color);transition: transform 0.2s linear;opacity: 0.5;}.splide__pagination__page:hover::before {opacity: 0.9;}.splide__pagination__page.is-active::before {opacity: 1;width: calc(var(--pagination-dot-width) * var(--pagination-dot-active-scale));height: calc( var(--pagination-dot-height) * var(--pagination-dot-active-scale) );border-radius: calc( var(--pagination-dot-radius) * var(--pagination-dot-active-scale) );}.splide__pagination__page:focus-visible {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: 3px;}.splide.is-focus-in .splide__pagination__page:focus {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: 3px;}.splide__progress__bar {background: #ccc;height: 3px;}.splide__slide {-webkit-tap-highlight-color: rgba(0, 0, 0, 0);}.splide__slide:focus {outline: 0;}@supports (outline-offset: -3px) {.splide__slide:focus-visible {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: -3px;}}@media screen and (-ms-high-contrast: none) {.splide__slide:focus-visible {border: 3px solid rgba(var(--color-foreground), 0.5);}}@supports (outline-offset: -3px) {.splide.is-focus-in .splide__slide:focus {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: -3px;}}@media screen and (-ms-high-contrast: none) {.splide.is-focus-in .splide__slide:focus {border: 3px solid rgba(var(--color-foreground), 0.5);}.splide.is-focus-in .splide__track > .splide__list > .splide__slide:focus {border-color: rgba(var(--color-foreground), 0.5);}}.splide__toggle {cursor: pointer;}.splide__toggle:focus-visible {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: 3px;}.splide.is-focus-in .splide__toggle:focus {outline: 3px solid rgba(var(--color-foreground), 0.5);outline-offset: 3px;}.splide__track--nav > .splide__list > .splide__slide {border: 3px solid transparent;cursor: pointer;}.splide__track--nav > .splide__list > .splide__slide.is-active {border: 3px solid #000;}.splide__arrows--rtl .splide__arrow--prev {left: auto;right: 1rem;}.splide__arrows--rtl .splide__arrow--prev svg {transform: scaleX(1);}.splide__arrows--rtl .splide__arrow--next {left: 1rem;right: auto;}.splide__arrows--rtl .splide__arrow--next svg {transform: scaleX(-1);}.splide__arrows--ttb .splide__arrow {left: 50%;transform: translate(-50%);}.splide__arrows--ttb .splide__arrow--prev {top: 1em;}.splide__arrows--ttb .splide__arrow--prev svg {transform: rotate(-90deg);}.splide__arrows--ttb .splide__arrow--next {bottom: 1em;top: auto;}.splide__arrows--ttb .splide__arrow--next svg {transform: rotate(90deg);}.splide__pagination--ttb {bottom: 0;display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;left: auto;padding: 1em 0;right: 0.5em;top: 0;}.splide--number-pagination .splide__pagination {counter-reset: pagination-num;}.splide--number-pagination .splide__pagination__page:before {counter-increment: pagination-num;content: counter(pagination-num);}@media screen and (min-width: 750px) {.splide.splide--destroy-desktop .splide__list {display: grid;grid-template-columns: repeat(var(--columns-desktop), 1fr);column-gap: var(--grid-desktop-horizontal-spacing);row-gap: var(--grid-desktop-vertical-spacing);overflow: visible;}.splide.splide--destroy-desktop .splide__track {overflow: visible;}.splide--precalc-width-desktop .splide__slide {width: calc( ((100% + var(--gap-desktop)) / var(--columns-desktop)) - var(--gap-desktop) );margin-right: var(--gap-desktop);}.splide--rtl.splide--precalc-width-desktop .splide__slide {margin-left: var(--gap-desktop);margin-right: 0;}.splide--precalc-width .splide__slide {--columns: var(--columns-desktop);--gap: var(--gap-desktop);}.splide--precalc-padding .splide__track {padding-left: var(--padding-desktop);padding-right: var(--padding-desktop);}.splide--precalc-padding-desktop .splide__track {padding-left: var(--left-padding-desktop);padding-right: var(--right-padding-desktop);}.splide--desktop-dots-under .splide__pagination {position: relative;bottom: 0;padding-top: 1.5rem;}.splide--desktop-dots-hidden .splide__pagination, .splide--desktop-arrows-hidden .splide__arrows {display: none;}.splide.is-initialized[data-desktop-adaptive-height="true"] .splide__track {height: var(--active-slide-height);transition: height 0.2s ease-in-out;}.splide--desktop-arrows-sides.splide--desktop-arrows-outside .splide__arrow--prev {left: -4rem;}.splide--desktop-arrows-sides.splide--desktop-arrows-outside .splide__arrow--next {right: -4rem;}.splide--desktop-arrows-sides.splide--desktop-arrows-outside .splide__arrows--rtl .splide__arrow--prev {left: auto;right: -4rem;}.splide--desktop-arrows-sides.splide--desktop-arrows-outside .splide__arrows--rtl .splide__arrow--next {right: auto;left: -4rem;}.splide--desktop-arrows-under:not(.splide--desktop-dots-under) .splide__arrows {display: flex;justify-content: center;column-gap: 3rem;padding-top: 0.8rem;}.splide--desktop-arrows-under:not(.splide--desktop-dots-under) .splide__arrow {position: relative;top: 0;left: 0;right: 0;transform: none;}.splide--desktop-dots-over.splide--desktop-arrows-under .splide__pagination {bottom: 4.6rem;}.splide--desktop-dots-under.splide--desktop-arrows-under .splide__dots-and-arrows {width: fit-content;margin-left: auto;margin-right: auto;position: relative;}.splide--desktop-dots-under.splide--desktop-arrows-under .splide__arrow--prev {left: -1em;margin-top: 1rem;}.splide--desktop-dots-under.splide--desktop-arrows-under .splide__arrow--next {right: -1em;margin-top: 1rem;}.splide--desktop-dots-under.splide--desktop-arrows-under .splide__arrows--rtl .splide__arrow--prev {left: auto;right: -1em;margin-top: 1rem;}.splide--desktop-dots-under.splide--desktop-arrows-under .splide__arrows--rtl .splide__arrow--next {right: auto;left: -1em;margin-top: 1rem;}.splide--desktop-arrows-under .splide__dots-and-arrows {margin-top: 1.25rem;}.splide--small-pagination.splide--desktop-arrows-under .splide__dots-and-arrows {margin-top: -1rem;}.splide--small-pagination.splide--desktop-arrows-hidden.splide--desktop-dots-under .splide__pagination {padding-top: 0.4rem;}.splide.is-initialized.desktop-destroy--flex:not(.is-active) .splide__list {display: flex;}.splide.is-initialized.desktop-destroy--grid:not(.is-active) .splide__list {display: grid;}.splide:not(.splide--destroy-desktop) .splide__slide__container--card {padding-top: max(var(--focus-outline-padding), var(--shadow-padding-top));padding-bottom: max( var(--focus-outline-padding), var(--shadow-padding-bottom) );}}@media screen and (max-width: 749px) {.splide--precalc-width-mobile .splide__slide {width: calc( ((100% + var(--gap-mobile)) / var(--columns-mobile)) - var(--gap-mobile) );margin-right: var(--gap-mobile);}.splide--rtl.splide--precalc-width-mobile .splide__slide {margin-left: var(--gap-mobile);margin-right: 0;}.splide--precalc-padding-mobile .splide__track {padding-left: var(--side-padding-mobile);padding-right: var(--side-padding-mobile);}.splide.splide--destroy-mobile .splide__list {display: grid;grid-template-columns: repeat(var(--columns-mobile), 1fr);column-gap: var(--grid-mobile-horizontal-spacing);row-gap: var(--grid-mobile-vertical-spacing);overflow: visible;}.splide.splide--destroy-mobile .splide__track {overflow: visible;}.splide--mobile-dots-under .splide__pagination {position: relative;bottom: 0;padding-top: 1rem;}.splide--mobile-dots-hidden .splide__pagination, .splide--mobile-arrows-hidden .splide__arrows {display: none;}.splide.is-initialized[data-mobile-adaptive-height="true"] .splide__track {height: var(--active-slide-height);transition: height 0.2s ease-in-out;}.splide__arrows {font-size: 2.8rem;}.splide--mobile-arrows-under:not(.splide--mobile-dots-under) .splide__arrows {display: flex;justify-content: center;column-gap: 1.5rem;padding-top: 0.8rem;}.splide--mobile-arrows-under:not(.splide--mobile-dots-under) .splide__arrow {position: relative;top: 0;left: 0;right: 0;transform: none;}.splide--mobile-dots-over.splide--mobile-arrows-under .splide__pagination {bottom: 4.3rem;}.splide--mobile-dots-under.splide--mobile-arrows-under .splide__dots-and-arrows {width: fit-content;margin-left: auto;margin-right: auto;position: relative;}.splide--mobile-dots-under.splide--mobile-arrows-under .splide__arrow--prev {left: -1em;margin-top: 0.6rem;}.splide--mobile-dots-under.splide--mobile-arrows-under .splide__arrow--next {right: -1em;margin-top: 0.6rem;}.splide--mobile-dots-under.splide--mobile-arrows-under .splide__arrows--rtl .splide__arrow--prev {left: auto;right: -1em;margin-top: 1rem;}.splide--mobile-dots-under.splide--mobile-arrows-under .splide__arrows--rtl .splide__arrow--next {right: auto;left: -1em;margin-top: 1rem;}.splide--mobile-arrows-under .splide__dots-and-arrows {margin-top: 0.75rem;}.splide--small-pagination.splide--mobile-arrows-under .splide__dots-and-arrows {margin-top: -0.3rem;}.splide--small-pagination.splide--mobile-arrows-hidden.splide--mobile-dots-under .splide__pagination {padding-top: 0.4rem;}.splide.is-initialized.mobile-destroy--flex:not(.is-active) .splide__list {display: flex;}.splide.is-initialized.mobile-destroy--grid:not(.is-active) .splide__list {display: grid;}.splide:not(.splide--destroy-mobile) .splide__slide__container--card {padding-top: max(var(--focus-outline-padding), var(--shadow-padding-top));padding-bottom: max( var(--focus-outline-padding), var(--shadow-padding-bottom) );}}.splide.is-active .splide__slide__container--card {scroll-snap-align: unset;}.logo-list__heading .title {text-align: center;margin-top: 0;}.logo-list__item {display: flex;justify-content: center;align-items: center;margin: 2rem;}.logo-list__item--gray img {filter: grayscale(1);opacity: 0.8;}@media screen and (min-width: 750px) {.logo-list-container-horizontal {display: flex;justify-content: center;align-items: center;flex-wrap: wrap;row-gap: 2rem;column-gap: 3rem;}.logo-list-container-horizontal .title {margin-bottom: 0;}.logo-list-container .splide__list {flex-wrap: wrap;justify-content: center;}}@media screen and (max-width: 749px) {.logo-list-container .mobile-logo-list--grid {display: grid;grid-template-columns: repeat(var(--columns-mobile), 1fr);}.logo-list__item img {max-width: 100%;object-fit: contain;margin: 0 auto;}}.hero {position: relative;z-index: 0;overflow: hidden;--additional-padding: 0px;}.hero-slide {--content-bg-opacity: 0;position: relative;z-index: 0;display: flex;}.hero-slide__background {z-index: -2;}.hero-slide__background, .hero-slide__overlay {position: absolute;top: 0;left: 0;width: 100%;height: 100%;}.hero-slide__overlay {background: var(--overlay-color);opacity: var(--overlay-opacity);}.hero-slide__content-container--full-width {max-width: none;}.hero-slide__content-container {width: 100%;display: flex;align-items: var(--content-vertical-position);justify-content: var(--content-horizontal-position);}.hero-slide__content {position: relative;z-index: 5;background: rgba(var(--color-background), var(--content-bg-opacity));text-align: var(--content-text-alignment);}.hero-slide__content--limited {max-width: var(--content-max-width);}.hero__heading-prefix, .hero__heading-suffix, .hero__heading {margin: 0;}.hero__text:not(:first-child) {margin-top: 2rem;}.hero__buttons:not(:first-child) {margin-top: 2rem;}.hero__buttons .button + .button {margin-left: 1rem;}@media screen and (min-width: 750px) {.hero-slide__background__mobile-media {display: none;}.parallax-hero .hero__heading {font-size: var(--heading-size);}.hero-slide .internal-video__sound-btn {bottom: 2rem;right: 2rem;}}@media screen and (max-width: 749px) {.hero-slide__background--has-mobile .hero-slide__background__desktop-media {display: none;}.hero-slide__content-container {align-items: var(--mobile-content-vertical-position);justify-content: var(--mobile-content-text-alignment);}.slideshow-hero-slide .hero-slide__overlay {--overlay-color: var(--mobile-overlay-color);--overlay-opacity: var(--mobile-overlay-opacity);}.hero-slide__content {text-align: var(--mobile-content-text-alignment);max-width: 100%;}.parallax-hero .hero__heading {font-size: var(--mobile-heading-size);}}parallax-hero {display: block;}.parallax-hero .parallax-hero__layer {z-index: var(--z-index);transition: transform 0.05s ease-in-out;}.extension-container .extension-content, a[href*="pipiads"], [id$="wr"], [id*="uuspy"], #shopify-raise-container, .ixspy-tools, #unispy-app, #ci-extension-div, #simplytrends-main-extension-root, a[href*="salesource.com"] {display: none !important;opacity: 0 !important;visibility: hidden !important;pointer-events: none !important;}.deferred-media__poster {background-color: transparent;border: none;cursor: pointer;margin: 0;padding: 0;height: 100%;width: 100%;overflow: hidden;}.media > .deferred-media__poster {display: flex;align-items: center;justify-content: center;}.deferred-media__poster img {width: auto;max-width: 100%;height: 100%;}.deferred-media {overflow: hidden;}.deferred-media:not([loaded]) template {z-index: -1;}.deferred-media[loaded] > .deferred-media__poster {display: none;}.deferred-media__poster:focus-visible {outline: none;box-shadow: 0 0 0 var(--media-border-width) rgba(var(--color-foreground), var(--media-border-opacity)), 0 0 0 calc(var(--media-border-width) + 0.3rem) rgb(var(--color-background)), 0 0 0 calc(var(--media-border-width) + 0.5rem) rgba(var(--color-foreground), 0.5);border-radius: calc(var(--media-radius) - var(--media-border-width));}.deferred-media__poster:focus {outline: none;box-shadow: 0 0 0 var(--media-border-width) rgba(var(--color-foreground), var(--media-border-opacity)), 0 0 0 calc(var(--media-border-width) + 0.3rem) rgb(var(--color-background)), 0 0 0 calc(var(--media-border-width) + 0.5rem) rgba(var(--color-foreground), 0.5);border-radius: calc(var(--media-radius) - var(--media-border-width));}@media (forced-colors: active) {.deferred-media__poster:focus {outline: transparent solid 1px;}}.deferred-media__poster:focus:not(:focus-visible) {outline: 0;box-shadow: none;}.deferred-media__poster-button {background-color: rgb(var(--color-background));border: 0.1rem solid rgba(var(--color-foreground), 0.1);border-radius: 50%;color: rgb(var(--color-foreground));display: flex;align-items: center;justify-content: center;height: 6.2rem;width: 6.2rem;position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%) scale(1);transition: transform var(--duration-short) ease, color var(--duration-short) ease;z-index: 1;}.deferred-media__poster-button:hover {transform: translate(-50%, -50%) scale(1.1);}.deferred-media__poster-button .icon {width: 2rem;height: 2rem;}.deferred-media__poster-button .icon-play {margin-left: 0.2rem;}.list-payment {display: flex;flex-wrap: wrap;justify-content: center;margin: -0.5rem 0;padding-top: 1rem;padding-left: 0;}@media screen and (min-width: 750px) {.list-payment {justify-content: flex-end;margin: -0.5rem;padding-top: 0;}}.list-payment__item {align-items: center;display: flex;padding: 0.5rem;}.list-payment__item svg {visibility: visible;}.price {font-size: 1.6rem;letter-spacing: 0.1rem;line-height: calc(1 + 0.5 / var(--font-body-scale));color: rgb(var(--color-foreground));display: flex;align-items: center;flex-wrap: wrap;column-gap: 1rem;}.price.price--unavailable {visibility: hidden;}.price--end {text-align: right;}.price .price-item {display: inline-block;margin: 0;color: rgb(var(--accent-color));font-weight: 700;}.price--large {font-size: 2rem;line-height: calc(1 + 0.5 / var(--font-body-scale));letter-spacing: 0.13rem;}.price--sold-out .price__availability, .price__regular {display: block;}.price__sale, .price__availability, .price .price__badge-sale, .price .price__badge-sold-out, .price--on-sale .price__regular, .price--on-sale .price__availability {display: none;}.price--sold-out .price__badge-sold-out, .price--on-sale .price__badge-sale {display: inline-flex;}.price--on-sale .price__sale {display: initial;flex-direction: row;flex-wrap: wrap;}.price--center {display: initial;justify-content: center;}.price--on-sale .price-item--regular {text-decoration: line-through;color: rgba(var(--color-foreground), 0.9);font-size: 1.3rem;}.unit-price {display: block;font-size: 1.1rem;letter-spacing: 0.04rem;line-height: calc(1 + 0.2 / var(--font-body-scale));margin-top: 0.2rem;text-transform: uppercase;color: rgba(var(--color-foreground), 0.7);}.price--large .price-item--regular {font-size: 1.6rem;}.product-page-price .price--on-sale .price__sale--price-second {display: flex;flex-direction: row;flex-wrap: wrap;}.product-page-price .price--on-sale .price__sale--price-second .compare-price-label {order: 1;}.product-page-price .price--on-sale .price__sale--price-second .price__compare-price {order: 2;margin-right: 0.55rem;}.product-page-price .price--on-sale .price__sale--price-second .price__compare-price .price-item--regular {font-size: 2rem;}.product-page-price .price--on-sale .price__sale--price-second .regular-price-label {order: 3;}.product-page-price .price--on-sale .price__sale--price-second .price-item--sale {order: 4;}.product-page-price .price--on-sale .price__sale--price-second .price-item--sale span.money {font-size: 1.7rem;}.price .main-price {color: rgb(var(--accent-color));}.price .main-comapre-price {color: rgba(var(--accent-color), 0.9);}.loading-overlay {position: absolute;z-index: 1;width: 1.8rem;}@media screen and (max-width: 749px) {.loading-overlay {top: 0;right: 0;}}@media screen and (min-width: 750px) {.loading-overlay {left: 0;}}.loading-overlay__spinner {width: 1.8rem;display: inline-block;}.spinner {animation: rotator 1.4s linear infinite;}@keyframes rotator {0% {transform: rotate(0deg);}100% {transform: rotate(270deg);}}.path {stroke-dasharray: 280;stroke-dashoffset: 0;transform-origin: center;stroke: rgb(var(--color-foreground));animation: dash 1.4s ease-in-out infinite;}@media screen and (forced-colors: active) {.path {stroke: CanvasText;}}@keyframes dash {0% {stroke-dashoffset: 280;}50% {stroke-dashoffset: 75;transform: rotate(135deg);}100% {stroke-dashoffset: 280;transform: rotate(450deg);}}.loading-overlay:not(.hidden) + .cart-item__price-wrapper, .loading-overlay:not(.hidden) ~ cart-remove-button {opacity: 50%;}.loading-overlay:not(.hidden) ~ cart-remove-button {pointer-events: none;cursor: default;}.list-menu--right {right: 0;}.list-menu--disclosure {position: absolute;min-width: 100%;width: 20rem;border: 1px solid rgba(var(--color-foreground), 0.2);}.list-menu--disclosure:focus {outline: none;}.list-menu__item--active {color: rgb(var(--accent-color));}.list-menu__item--active:hover {text-decoration: underline;}.list-menu--disclosure.localization-selector {max-height: 18rem;overflow: auto;width: 10rem;padding: 0.5rem;}.button-show-more {padding-left: 0;justify-content: flex-start;padding-bottom: 1.1rem;}.button-show-more, .button-show-less {margin-top: 1.5rem;}.page-title {margin-top: 0;}.main-page-title {margin-bottom: 3rem;}@media screen and (min-width: 750px) {.main-page-title {margin-bottom: 4rem;}}.page-placeholder-wrapper {display: flex;justify-content: center;}.page-placeholder {width: 52.5rem;height: 52.5rem;}slider-component {--desktop-margin-left-first-item: max( 5rem, calc( ( 100vw - var(--page-width) + 10rem - var(--grid-desktop-horizontal-spacing) ) / 2 ) );position: relative;display: block;}slider-component.slider-component-full-width {--desktop-margin-left-first-item: 1.5rem;}@media screen and (max-width: 749px) {slider-component.page-width {padding: 0 1.5rem;}}@media screen and (min-width: 749px) and (max-width: 990px) {slider-component.page-width {padding: 0 5rem;}}@media screen and (max-width: 989px) {.no-js slider-component .slider {padding-bottom: 3rem;}}.slider__slide {--focus-outline-padding: 0.5rem;--shadow-padding-top: calc( (var(--shadow-vertical-offset) * -1 + var(--shadow-blur-radius)) * var(--shadow-visible) );--shadow-padding-bottom: calc( (var(--shadow-vertical-offset) + var(--shadow-blur-radius)) * var(--shadow-visible) );scroll-snap-align: start;flex-shrink: 0;padding-bottom: 0;}@media screen and (max-width: 749px) {.slider.slider--mobile {position: relative;flex-wrap: inherit;overflow-x: auto;scroll-snap-type: x mandatory;scroll-behavior: smooth;scroll-padding-left: 1.5rem;-webkit-overflow-scrolling: touch;margin-bottom: 0rem;}.slider--mobile:after {content: "";width: 0;padding-left: 1.5rem;}.slider.slider--mobile .slider__slide {margin-bottom: 0;padding-top: 0.1rem;padding-bottom: 0.1rem;}.slider.slider--mobile.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom: var(--focus-outline-padding);}.slider.slider--mobile.contains-content-container .slider__slide {--focus-outline-padding: 0rem;}}@media screen and (min-width: 750px) {.slider.slider--tablet-up {position: relative;flex-wrap: inherit;overflow-x: auto;scroll-snap-type: x mandatory;scroll-behavior: smooth;scroll-padding-left: 1rem;-webkit-overflow-scrolling: touch;}.slider.slider--tablet-up .slider__slide {margin-bottom: 0;}}@media screen and (max-width: 989px) {.slider.slider--tablet {position: relative;flex-wrap: inherit;overflow-x: auto;scroll-snap-type: x mandatory;scroll-behavior: smooth;scroll-padding-left: 1.5rem;-webkit-overflow-scrolling: touch;margin-bottom: 1rem;}.slider--tablet:after {content: "";width: 0;padding-left: 1.5rem;margin-left: calc(-1 * var(--grid-desktop-horizontal-spacing));}.slider.slider--tablet .slider__slide {margin-bottom: 0;padding-top: max(var(--focus-outline-padding), var(--shadow-padding-top));padding-bottom: max( var(--focus-outline-padding), var(--shadow-padding-bottom) );}.slider.slider--tablet.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom: var(--focus-outline-padding);}.slider.slider--tablet.contains-content-container .slider__slide {--focus-outline-padding: 0rem;}}.slider--everywhere {position: relative;flex-wrap: inherit;overflow-x: auto;scroll-snap-type: x mandatory;scroll-behavior: smooth;-webkit-overflow-scrolling: touch;margin-bottom: 1rem;}.slider.slider--everywhere .slider__slide {margin-bottom: 0;scroll-snap-align: center;}@media screen and (min-width: 990px) {.slider-component-desktop.page-width {max-width: none;}.slider--desktop {position: relative;flex-wrap: inherit;overflow-x: auto;scroll-snap-type: x mandatory;scroll-behavior: smooth;-webkit-overflow-scrolling: touch;margin-bottom: 1rem;scroll-padding-left: var(--desktop-margin-left-first-item);}.slider--desktop:after {content: "";width: 0;padding-left: 5rem;margin-left: calc(-1 * var(--grid-desktop-horizontal-spacing));}.slider.slider--desktop .slider__slide {margin-bottom: 0;padding-top: max(var(--focus-outline-padding), var(--shadow-padding-top));padding-bottom: max( var(--focus-outline-padding), var(--shadow-padding-bottom) );}.slider--desktop .slider__slide:first-child {margin-left: var(--desktop-margin-left-first-item);scroll-margin-left: var(--desktop-margin-left-first-item);}.slider-component-full-width .slider--desktop {scroll-padding-left: 1.5rem;}.slider-component-full-width .slider--desktop .slider__slide:first-child {margin-left: 1.5rem;scroll-margin-left: 1.5rem;}.slider-component-full-width .slider--desktop:after {padding-left: 1.5rem;}.slider--desktop.grid--5-col-desktop .grid__item {width: calc( (100% - var(--desktop-margin-left-first-item)) / 5 - var(--grid-desktop-horizontal-spacing) * 2 );}.slider--desktop.grid--4-col-desktop .grid__item {width: calc( (100% - var(--desktop-margin-left-first-item)) / 4 - var(--grid-desktop-horizontal-spacing) * 3 );}.slider--desktop.grid--3-col-desktop .grid__item {width: calc( (100% - var(--desktop-margin-left-first-item)) / 3 - var(--grid-desktop-horizontal-spacing) * 4 );}.slider--desktop.grid--2-col-desktop .grid__item {width: calc( (100% - var(--desktop-margin-left-first-item)) / 2 - var(--grid-desktop-horizontal-spacing) * 5 );}.slider--desktop.grid--1-col-desktop .grid__item {width: calc( (100% - var(--desktop-margin-left-first-item)) - var(--grid-desktop-horizontal-spacing) * 9 );}.slider.slider--desktop.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom: var(--focus-outline-padding);}.slider.slider--desktop.contains-content-container .slider__slide {--focus-outline-padding: 0rem;}}@media (prefers-reduced-motion) {.slider {scroll-behavior: auto;}}.slider {scrollbar-color: rgb(var(--color-foreground)) rgba(var(--color-foreground), 0.04);-ms-overflow-style: none;scrollbar-width: none;}.slider::-webkit-scrollbar {height: 0.4rem;width: 0.4rem;display: none;}.no-js .slider {-ms-overflow-style: auto;scrollbar-width: auto;}.no-js .slider::-webkit-scrollbar {display: initial;}.slider::-webkit-scrollbar-thumb {background-color: rgb(var(--color-foreground));border-radius: 0.4rem;border: 0;}.slider::-webkit-scrollbar-track {background: rgba(var(--color-foreground), 0.04);border-radius: 0.4rem;}.slider-counter {display: flex;justify-content: center;min-width: 4.4rem;}@media screen and (min-width: 750px) {.slider-counter--dots {margin: 0 1.2rem;}}.slider-counter__link {padding: 1rem;}@media screen and (max-width: 749px) {.slider-counter__link {padding: 0.7rem;}}.slider-counter__link--dots .dot {width: 1rem;height: 1rem;border-radius: 50%;border: 0.1rem solid rgba(var(--color-foreground), 0.5);padding: 0;display: block;}.slider-counter__link--active.slider-counter__link--dots .dot {background-color: rgb(var(--color-foreground));}@media screen and (forced-colors: active) {.slider-counter__link--active.slider-counter__link--dots .dot {background-color: CanvasText;}}.slider-counter__link--dots:not(.slider-counter__link--active):hover .dot {border-color: rgb(var(--color-foreground));}.slider-counter__link--dots .dot, .slider-counter__link--numbers {transition: transform 0.2s ease-in-out;}.slider-counter__link--active.slider-counter__link--numbers, .slider-counter__link--dots:not(.slider-counter__link--active):hover .dot, .slider-counter__link--numbers:hover {transform: scale(1.1);}.slider-counter__link--numbers {color: rgba(var(--color-foreground), 0.5);text-decoration: none;}.slider-counter__link--numbers:hover {color: rgb(var(--color-foreground));}.slider-counter__link--active.slider-counter__link--numbers {text-decoration: underline;color: rgb(var(--color-foreground));}.slider-buttons {display: flex;align-items: center;justify-content: center;}@media screen and (min-width: 990px) {.slider:not(.slider--everywhere):not(.slider--desktop):not( .product__media-list ) + .slider-buttons {display: none;}}@media screen and (max-width: 989px) {.slider--desktop:not(.slider--tablet) + .slider-buttons {display: none;}}@media screen and (min-width: 750px) {.slider--mobile + .slider-buttons {display: none;}}.slider-button {color: rgba(var(--color-foreground), 0.9);background: transparent;border: none;cursor: pointer;width: 44px;height: 44px;display: flex;align-items: center;justify-content: center;}.slider-button:not([disabled]):hover {color: rgb(var(--color-foreground));}.slider-button .icon {height: 0.6rem;}.slider-button[disabled] .icon {color: rgba(var(--color-foreground), 0.3);cursor: not-allowed;}.slider-button--next .icon {transform: rotate(-90deg);}.slider-button--prev .icon {transform: rotate(90deg);}.slider-button--next:not([disabled]):hover .icon {transform: rotate(-90deg) scale(1.1);}.slider-button--prev:not([disabled]):hover .icon {transform: rotate(90deg) scale(1.1);}@media screen and (min-width: 750px) {.slider.gallery-slider--desktop {position: relative;flex-wrap: inherit;overflow-x: auto;scroll-snap-type: x mandatory;scroll-behavior: smooth;scroll-padding-left: 0;-webkit-overflow-scrolling: touch;margin-bottom: 0rem;}.slider.gallery-slider--desktop .product__media-item:not(.is-active), .product--thumbnail_slider .product__media-item:not(.is-active) {display: block;}.slider.gallery-slider--desktop .grid__item {width: 100%;}}.cart {position: relative;display: block;}.cart__empty-text, .is-empty .cart__contents, cart-items.is-empty .title-wrapper-with-link, .is-empty .cart__footer {display: none;}.is-empty .cart__empty-text, .is-empty .cart__warnings {display: block;}.cart__warnings {display: none;text-align: center;padding: 3rem 0 1rem;}.cart__empty-text {margin: 4.5rem 0 2rem;}.cart__contents > * + * {margin-top: 2.5rem;}.cart__login-title {margin: 5.5rem 0 0.5rem;}.cart__login-paragraph {margin-top: 0.8rem;}.cart__login-paragraph a {font-size: inherit;}@media screen and (min-width: 990px) {.cart__warnings {padding: 7rem 0 1rem;}.cart__empty-text {margin: 0 0 3rem;}}cart-items {display: block;}.cart__items {position: relative;padding-bottom: 3rem;border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);}.cart__items--disabled {pointer-events: none;}.cart__footer {padding: 4rem 0 0;}.cart__footer-wrapper:last-child .cart__footer {padding-bottom: 5rem;}.cart__footer > div:only-child {margin-left: auto;}.cart__footer > * + * {margin-top: 4rem;}.cart__footer .discounts {margin-top: 1rem;}.cart__note {height: fit-content;}.cart__note label {display: flex;align-items: flex-end;position: absolute;line-height: 1;height: 1.8rem;top: -3rem;color: rgba(var(--color-foreground), 0.9);}.cart__note .field__input {height: 100%;position: relative;border-radius: var(--inputs-radius);padding: 1rem 2rem;}.cart__note .text-area {resize: vertical;}.cart__note:after, .cart__note:hover.cart__note:after, .cart__note:before, .cart__note:hover.cart__note:before, .cart__note .field__input:focus, .cart__note .field__input {border-bottom-right-radius: 0;}@media screen and (min-width: 750px) {.cart__items {grid-column-start: 1;grid-column-end: 3;padding-bottom: 4rem;}.cart__contents > * + * {margin-top: 0;}.cart__items + .cart__footer {grid-column: 2;}.cart__footer {display: flex;justify-content: space-between;border: 0;}.cart__footer-wrapper:last-child {padding-top: 0;}.cart__footer > * {width: 35rem;}.cart__footer > * + * {margin-left: 4rem;margin-top: 0;}}.cart__ctas button {width: 100%;}.cart__ctas > *:not(noscript:first-child, .tnc-checkbox-warning--above-button) + * {margin-top: 1rem;}.cart__update-button {margin-bottom: 1rem;}.cart__dynamic-checkout-buttons {max-width: 36rem;margin: 0 auto;max-width: none;}.cart__blocks > * + * {margin-top: 1.25rem;}.cart__blocks > link:first-child ~ .cart-discount {margin-top: 0;}.cart__dynamic-checkout-buttons div[role="button"] {border-radius: var(--buttons-radius-outset) !important;}.cart-note__label {display: inline-block;margin-bottom: 1rem;line-height: calc(1 + 1 / var(--font-body-scale));}.tax-note {margin: 2.2rem 0 1.6rem auto;text-align: center;display: block;}.cart__checkout-button {max-width: 36rem;max-width: none;}.cart__ctas {text-align: center;}.cart__footer .cart-discount-form {margin-top: 0;}@media screen and (min-width: 750px) {.cart-note {max-width: 36rem;}.cart__update-button {margin-bottom: 0;margin-right: 0.8rem;}.tax-note {margin-bottom: 2.2rem;text-align: right;}[data-shopify-buttoncontainer] {justify-content: flex-end;}}.cart-items td, .cart-items th {padding: 0;border: none;}.cart-items th {text-align: left;padding-bottom: 1.8rem;opacity: 0.85;font-weight: normal;}.cart-item__quantity-wrapper {display: flex;}.cart-item__totals {position: relative;}.cart-items *.right {text-align: right;}.cart-item__image-container {display: inline-flex;align-items: flex-start;}.cart-item__image-container:after {content: none;}.cart-item__image {height: auto;max-width: calc(10rem / var(--font-body-scale));}@media screen and (min-width: 750px) {.cart-item__image {max-width: 100%;}}.cart-item__details {font-size: 1.6rem;line-height: calc(1 + 0.4 / var(--font-body-scale));}.cart-item__details > * {margin: 0;max-width: 30rem;}.cart-item__details > * + * {margin-top: 0.6rem;}.cart-item__media {position: relative;}.cart-item__link {display: block;bottom: 0;left: 0;position: absolute;right: 0;top: 0;width: 100%;height: 100%;}.cart-item__name {text-decoration: none;display: block;font-size: calc(var(--font-heading-scale) * 1.4rem);}a.cart-item__name:hover {text-decoration: underline;text-underline-offset: 0.3rem;text-decoration-thickness: 0.2rem;}.cart-item__price-wrapper > * {display: block;margin: 0;padding: 0;}.cart-item__discounted-prices dd {margin: 0;}.cart-item__discounted-prices .cart-item__old-price {font-size: 1.4rem;}.cart-item__old-price {opacity: 0.7;}.cart-item__final-price {font-weight: 400;}.product-option {font-size: 1.4rem;word-break: break-all;line-height: calc(1 + 0.5 / var(--font-body-scale));}.cart-item cart-remove-button {display: flex;margin-left: 1rem;}@media screen and (min-width: 750px) and (max-width: 989px) {.cart-item cart-remove-button {width: 4.5rem;height: 4.5rem;}}cart-remove-button .button {min-width: calc(4.5rem / var(--font-body-scale));min-height: 4.5rem;padding: 0;margin: 0 0.1rem 0.1rem 0;}cart-remove-button .button:before, cart-remove-button .button:after {content: none;}cart-remove-button .button:not([disabled]):hover {color: rgb(var(--color-foreground));}@media screen and (min-width: 750px) {cart-remove-button .button {min-width: 3.5rem;min-height: 3.5rem;}}cart-remove-button .icon-remove {height: 1.5rem;width: 1.5rem;}.cart-item .loading-overlay {top: 0;left: auto;right: auto;bottom: 0;padding: 0;}@media screen and (min-width: 750px) {.cart-item .loading-overlay {right: 0;padding-top: 4.5rem;bottom: auto;}}.cart-item .loading-overlay:not(.hidden) ~ * {visibility: hidden;}.cart-item__error {display: flex;align-items: flex-start;margin-top: 1rem;}.cart-item__error-text {font-size: 1.2rem;order: 1;}.cart-item__error-text + svg {flex-shrink: 0;width: 1.2rem;margin-right: 0.7rem;}.cart-item__error-text:empty + svg {display: none;}.product-option + .product-option {margin-top: 0.4rem;}.product-option * {display: inline;margin: 0;}.cart-items thead th {text-transform: uppercase;}@media screen and (max-width: 749px) {.cart-items, .cart-items thead, .cart-items tbody {display: block;width: 100%;}.cart-items thead tr {display: flex;justify-content: space-between;border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);margin-bottom: 4rem;}.cart-item {display: grid;grid-template: repeat(2, auto) / repeat(4, 1fr);gap: 1.5rem;margin-bottom: 3.5rem;}.cart-item:last-child {margin-bottom: 0;}.cart-item__media {grid-row: 1 / 3;}.cart-item__details {grid-column: 2 / 4;}.cart-item__quantity {grid-column: 2 / 5;}.cart-item__quantity-wrapper {flex-wrap: wrap;}.cart-item__totals {display: flex;align-items: flex-start;justify-content: flex-end;}}.cart-item__error-text + svg {margin-top: 0.4rem;}@media screen and (min-width: 750px) {.cart-items {border-spacing: 0;border-collapse: separate;box-shadow: none;width: 100%;display: table;}.cart-items th {border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);}.cart-items thead th:first-child {width: 50%;}.cart-items th + th {padding-left: 4rem;}.cart__items .cart-items td {vertical-align: top;padding-top: 4rem;}.cart-item {display: table-row;}.cart-item > td + td {padding-left: 4rem;}.cart-item__details {width: 35rem;}.cart-item__media {width: 10rem;}.cart-item cart-remove-button {margin: 0.5rem 0 0 1.5rem;}.cart-item__price-wrapper > *:only-child:not(.cart-item__discounted-prices) {margin-top: 1rem;}}@media screen and (min-width: 990px) {.cart-item .cart-item__quantity, .cart-items .cart-items__heading--wide {padding-left: 6rem;}.cart-item__details {width: 50rem;}.cart-items thead th:first-child {width: 60%;}}@media screen and (max-width: 500px) {.title-wrapper-with-link {flex-direction: column;align-items: center;}}.discounts {font-size: 1.2rem;}.discounts__discount {display: flex;align-items: center;line-height: calc(1 + 0.5 / var(--font-body-scale));}.discounts__discount svg {color: rgba(var(--color-button), var(--alpha-button-background));}.discounts__discount--position {justify-content: center;}@media screen and (min-width: 750px) {.discounts__discount--position {justify-content: flex-end;}}.discounts__discount > .icon {color: rgb(var(--color-foreground));width: 1.2rem;height: 1.2rem;margin-right: 0.7rem;}.drawer {position: fixed;z-index: 1000;left: 0;top: 0;width: 100vw;height: 100%;display: flex;justify-content: flex-end;transition: visibility var(--duration-default) ease, background-color var(--duration-default) ease;background-color: transparent;}.drawer.active {visibility: visible;background-color: rgba(var(--color-foreground), 0.5);}.drawer__inner {height: 100%;width: 40rem;max-width: calc(100vw - 3rem);border: 0.1rem solid rgba(var(--color-foreground), 0.2);border-right: 0;border-left: 0;background-color: rgb(var(--color-background));overflow: hidden;display: flex;flex-direction: column;transform: translateX(100%);transition: transform var(--duration-default) ease;}.drawer__inner-empty {height: 100%;padding: 0 1.5rem;background-color: rgb(var(--color-background));overflow: hidden;display: flex;flex-direction: column;}.cart-drawer--desktop-width-large .drawer__inner {width: 50rem;}@media screen and (max-width: 500px) {.cart-drawer--mobile-width-full .cart-drawer {width: 100%;}.cart-drawer--mobile-width-full .drawer__inner {max-width: 100%;width: 100%;}}.cart-drawer__warnings {display: flex;flex-direction: column;flex: 1;justify-content: center;}cart-drawer.is-empty .drawer__inner {display: grid;grid-template-rows: 1fr;align-items: center;}cart-drawer.is-empty .drawer__header, cart-drawer.is-empty .cart-drawer__body, cart-drawer.is-empty .drawer__footer, cart-drawer:not(.is-empty) .cart-drawer__warnings, cart-drawer:not(.is-empty) .cart-drawer__collection {height: 0;padding: 0;overflow: hidden;}.cart-drawer__warnings--has-collection .cart__login-title {margin-top: 2.5rem;}.drawer.active .drawer__inner {transform: translateX(0);}.drawer__header {position: relative;background-color: rgb(var(--color-background));padding: 1.5rem;display: flex;justify-content: var(--alignment);align-items: center;border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);}.drawer__heading {margin: 0;}.drawer__close {display: flex;align-items: center;justify-content: flex-start;padding: 0;min-width: 4.4rem;min-height: 4.4rem;box-shadow: 0 0 0 0.2rem rgba(var(--color-button), 0);position: absolute;top: 50%;right: -10px;transform: translateY(-50%);color: rgb(var(--color-foreground));background-color: transparent;border: none;cursor: pointer;}.cart-drawer__warnings .drawer__close {right: 5px;top: 10px;transform: none;}.drawer__close svg {height: 2.4rem;width: 2.4rem;}.cart-timer {margin-left: -1.5rem;margin-right: -1.5rem;padding: 1em 1.5rem;text-align: center;line-height: 1.3;color: rgb(var(--color-foreground));font-size: var(--font-size);}.cart-progress {padding-bottom: 1.5rem;}.cart-progress__bar {height: 0.9rem;background: rgba(var(--color-foreground), 0.15);position: relative;border-radius: 5rem;text-align: center;margin: 0 0.5rem;}.cart-progress__bar__progress {position: absolute;top: 0;left: 0;height: 100%;border-radius: 5rem;background: rgb(var(--accent-color));background-image: linear-gradient( 315deg, rgba(255, 255, 255, 0.4) 25%, transparent 0, transparent 50%, rgba(255, 255, 255, 0.4) 0, rgba(255, 255, 255, 0.4) 75%, transparent 0, transparent );background-size: 1rem 1rem;animation: slideBar 4s linear infinite;}.cart-progress__bar__badge {font-size: 3rem;width: 1em;height: 1em;position: absolute;right: 0;top: 50%;transform: translate(50%, -50%);background: rgb(var(--color-background));border-radius: 50%;border: solid 0.2rem rgb(var(--accent-color));display: flex !important;justify-content: center;align-items: center;}.cart-progress__bar__badge .material-symbols-outlined {color: rgb(var(--accent-color));font-size: 0.6em;}.cart-progress__bar__badge img {width: 0.6em;height: 0.6em;object-fit: contain;}.cart-progress__text {margin: 0 0 1em;text-align: center;font-size: 1.4rem;font-weight: 600;line-height: 1.3;}@keyframes slideBar {from {background-position-x: 0;}to {background-position-x: 10rem;}}.cart-checkpoints {padding-bottom: calc(1.5rem + var(--desktop-text-size));}.cart-checkpoints .cart-progress__text {margin-bottom: 0.7em;}.cart-checkpoints .cart-progress__bar {margin: 0 2rem;height: 0.7rem;}.cart-checkpoints .cart-progress__bar__badge {font-size: 2.4rem;}.cart-checkpoints__icon {right: auto;left: var(--position);transform: translate(-50%, -50%);}.cart-checkpoints__label {line-height: 1.2;position: absolute;top: calc(100% + 0.5rem);text-align: center;white-space: nowrap;display: block;}.cart-checkpoints .cart-progress__bar .cart-progress__bar__badge:last-of-type .cart-checkpoints__label {width: 6.6rem;white-space: normal;}.drawer__contents {flex-grow: 1;display: flex;flex-direction: column;}cart-drawer {position: fixed;top: 0;left: 0;width: 100vw;height: 100%;}.cart-drawer__overlay {position: fixed;top: 0;right: 0;bottom: 0;left: 0;}.cart-drawer__overlay:empty {display: block;}.cart-drawer__form {flex-grow: 1;display: flex;flex-wrap: wrap;}.cart-drawer__collection {margin: 0 2.5rem 1.5rem;}.cart-drawer .drawer__cart-items-wrapper {flex-grow: 1;}.cart-drawer .cart-items {width: 100%;display: flex;flex-direction: column;row-gap: 2.5rem;}.cart-drawer__body {overflow: auto;flex: 1;padding: 0 1.5rem;}.cart-drawer__body > * {margin-top: var(--margin-top);margin-bottom: var(--margin-bottom);}@media screen and (max-height: 650px) {.cart-drawer__body {overflow: visible;}.drawer__inner {overflow: scroll;}}cart-drawer-items {display: block;}.cart-drawer .cart-item .loading-overlay {top: 50%;left: 50%;bottom: auto;padding: 0;width: 100%;height: 100%;transform: translate(-50%, -50%);display: flex;align-items: center;justify-content: center;}.cart-drawer .cart-item__totals {pointer-events: none;display: flex;align-items: flex-start;justify-content: flex-end;}.cart-drawer.cart-drawer .cart-item__price-wrapper > *:only-child {margin-top: 0;}.cart-drawer .cart-item__price-wrapper .cart-item__discounted-prices span, .cart-drawer .cart-item__price-wrapper .cart-item__discounted-prices s {line-height: 1;}.cart-drawer .cart-item .loading-overlay:not(.hidden) ~ * {visibility: visible;opacity: 0.4;}.cart-drawer .unit-price {margin-top: 0.6rem;}.cart-drawer-item {display: flex;align-items: flex-start;gap: 0;margin-bottom: 0;position: relative;}.cart-drawer-item .cart-item__media {width: var(--image-size);flex-shrink: 0;}.cart-drawer-item .cart-item__image {box-shadow: 0.3rem 0.3rem 1rem rgba(0, 0, 0, 0.05);max-width: 100%;width: 100%;}.cart-drawer-item__right {flex-grow: 1;padding-left: 1.25rem;}.cart-drawer-item .cart-item__name {font-size: var(--title-size);}.cart-drawer-item .product-option {margin-top: 0;}.cart-drawer-item .cart-item__final-price {font-weight: 700;}.cart-drawer-item__single-item-prices {margin-top: 0.3rem;line-height: 1;display: flex;align-items: center;flex-wrap: wrap;column-gap: 0.5rem;}.cart-drawer-item__single-item-prices .cart-drawer__discounts {margin-top: 0;}.cart-drawer-item__single-item-prices .cart-drawer__discounts .badge {font-size: 0.8rem;}.cart-drawer-item__single-item-prices + dl {margin-top: 0.3rem;}.cart-drawer-item__details-and-delete-btn {display: flex;align-items: flex-start;}.cart-drawer-item .cart-item__details {flex-grow: 1;width: auto;padding-right: 1.5rem;}.cart-drawer-item .cart-item__totals {pointer-events: auto;}.cart-drawer-item cart-remove-button, .cart-drawer-item cart-remove-button .button {width: 2rem;height: 2rem;min-width: auto;min-height: auto;margin: 0;color: rgb(var(--color-foreground));transition: color 0.25s;}.cart-drawer-item cart-remove-button .button:hover {color: rgb(var(--accent-color));}.cart-drawer-item__quantity-and-prices {display: flex;align-items: center;justify-content: space-between;margin-top: 1rem;}.cart-drawer-item__quantity-and-prices--has-props {margin-top: 0.5rem;}.cart-drawer-item .quantity {align-items: center;width: auto;min-height: auto;font-size: var(--font-size);border-radius: var(--corner-radius);padding: var(--container-padding);border: solid var(--border-width) var(--border-color);}.cart-drawer-item .quantity::before, .cart-drawer-item .quantity::after {display: none;}.cart-drawer-item .quantity__button, .cart-drawer-item .quantity__input {font-size: inherit;box-sizing: content-box;width: 1em;height: 1em;line-height: 1;font-weight: 700;}.cart-drawer-item .quantity__input {width: 1.5em;padding: var(--padding) var(--input-padding);border-left: solid 0.1rem rgba(var(--color-foreground), var(--separator-opacity));border-right: solid 0.1rem rgba(var(--color-foreground), var(--separator-opacity));border-radius: 0;}.cart-drawer-item .quantity__input:focus-visible {outline: none;box-shadow: none;}.cart-drawer-item .quantity .quantity__button, .cart-drawer-item .quantity .quantity__input {background-color: rgb(var(--color-background));}.cart-drawer-item .quantity__button {display: flex;align-items: center;justify-content: center;padding: var(--padding);}.cart-drawer-item .quantity__button--minus {border-radius: var(--corner-radius) 0 0 var(--corner-radius);}.cart-drawer-item .quantity__button--plus {border-radius: 0 var(--corner-radius) var(--corner-radius) 0;}.cart-drawer-item .quantity__button svg {width: var(--icon-size);color: currentColor;}.cart-drawer-item .cart-quantity--round-btns .quantity__button {border-radius: var(--corner-radius);}.cart-drawer-item .cart-quantity--outline-btns .quantity__button {color: rgb(var(--color-background));background-color: rgb(var(--color-foreground));border: solid calc((var(--font-size) + (var(--padding) * 2)) / 15) rgb(var(--color-background));}.cart-drawer-item__quantity-and-prices--reverse {flex-direction: row-reverse;}.cart-drawer-item .cart-item__quantity {padding-left: 0;}.cart-drawer-item .cart-item__error {margin-top: 0;align-items: center;}.cart-drawer-item .cart-item__error-text:not(:empty), .cart-drawer-item .cart-item__error-text:not(:empty) + svg {margin-top: 0.5rem;}.cart-drawer-item .cart-item__price-wrapper {line-height: 1.1;}.cart-drawer-item__saving {font-size: 1.4rem;margin-top: 0.3rem;}@media screen and (max-width: 400px) {.cart-drawer-item .product-option {font-size: 1.2rem;}}@media screen and (min-width: 401px) {.cart-drawer-item .cart-item__name {font-size: calc(var(--title-size) * 1.15);}}@media screen and (max-width: 749px) {.cart-checkpoints {padding-bottom: calc(1.5rem + var(--mobile-text-size));}}.drawer__footer {background-color: rgb(var(--color-background));border-top: 0.1rem solid rgba(var(--color-foreground), 0.2);padding: 0 1.5rem;}.drawer__footer > details {border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);}.drawer__footer > details[open] {padding-bottom: 1.5rem;}.drawer__footer summary {display: flex;position: relative;line-height: 1;padding: 1.5rem 0;}.drawer__footer > .cart-drawer__footer {padding: 0;}.cart-drawer__totals {display: flex;flex-direction: column;row-gap: var(--spacing);}.cart-drawer__totals--reverse {flex-direction: column-reverse;}.cart-drawer__totals__row {margin: 0;line-height: 1.1;font-size: var(--text-size);}.cart-drawer__totals__row--center {text-align: center;}.cart-drawer__totals__row--spaced {display: flex;justify-content: space-between;align-items: center;}.cart-drawer__totals__row__money {font-size: 0.9em;}.cart-drawer .price {line-height: 1;}.cart-drawer .product-option dd {word-break: break-word;}.cart-drawer details[open] > summary .icon-caret {transform: rotate(180deg);}.cart-drawer__discounts {margin-top: 0.5rem;font-size: 1.2rem;line-height: 1;display: flex;align-items: center;flex-wrap: wrap;gap: 0.3rem;}.cart-drawer__discounts .badge {font-size: 1rem;box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.05);background: rgba(var(--color-foreground), 0.08);--alpha-badge-border: 0;}.cart-drawer__footer__discounts {justify-content: var(--alignment);}.cart-drawer .cart__checkout-button {max-width: none;font-size: 1.8rem;}.drawer__footer .cart__dynamic-checkout-buttons {max-width: 100%;}.drawer__footer #dynamic-checkout-cart ul {flex-wrap: wrap !important;flex-direction: row !important;margin: 0.5rem -0.5rem 0 0 !important;gap: 0.5rem;}.drawer__footer [data-shopify-buttoncontainer] {justify-content: flex-start;}.drawer__footer #dynamic-checkout-cart ul > li {flex-basis: calc(50% - 0.5rem) !important;margin: 0 !important;}.drawer__footer #dynamic-checkout-cart ul > li:only-child {flex-basis: 100% !important;margin-right: 0.5rem !important;}@media screen and (min-width: 750px) {.cart-drawer__totals__row {font-size: calc(var(--text-size) * 1.15);}.drawer__footer #dynamic-checkout-cart ul > li {flex-basis: calc(100% / 3 - 0.5rem) !important;margin: 0 !important;}.drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(2), .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(2) ~ li, .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(4), .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(4) ~ li {flex-basis: calc(50% - 0.5rem) !important;}}.cart-drawer__body::-webkit-scrollbar {width: 3px;}.cart-drawer__body::-webkit-scrollbar-thumb {background-color: rgba(var(--color-foreground), 0.7);border-radius: 100px;}.cart-drawer__body::-webkit-scrollbar-track-piece {margin-top: 31px;}.cart-drawer .cart-discount-form {margin: 0;}.cart-drawer .cart-discount--bottom-separator .cart-discount-form {margin-bottom: 1.5rem;}.cart-drawer .cart-discount:not(.cart-discount--bottom-separator) {border-bottom: none;}.cart-drawer .drawer__footer > * {margin-top: var(--margin-top);margin-bottom: var(--margin-bottom);}.side-margins-negative {margin-left: -1.5rem;margin-right: -1.5rem;}.totals {display: flex;justify-content: space-between;align-items: flex-end;}.totals > * {font-size: 1.6rem;margin: 0;}.totals > h2 {font-size: calc(var(--font-heading-scale) * 1.6rem);}.totals * {line-height: 1;}.totals > * + * {margin-left: 2rem;}.totals__subtotal-value {font-size: 1.8rem;font-weight: var(--font-heading-weight);}.cart__ctas + .totals {margin-top: 2rem;}.tnc-chekcbox {line-height: 1.3;cursor: pointer;}.tnc-chekcbox svg {height: 1em;width: 1em;margin-right: 0.4em;}.tnc-chekcbox[data-checked="true"] .checkmark-unchecked, .tnc-chekcbox[data-checked="false"] .checkmark-checked {display: none;}.tnc-checkbox-warning {line-height: 1.3;}.upsell {--padding: 0;--active-padding: 1rem;border-radius: var(--border-radius);position: relative;display: block;}.upsell__outside-title {margin: 0 0 0.9rem;}.upsell__container {display: flex;align-items: center;padding: var(--padding);}.upsell ~ .upsell {margin-top: 1.5rem;}.upsell:not(.color-background-1) {--padding: var(--active-padding);}.upsell--box-shadow {--padding: var(--active-padding);box-shadow: 0.25rem 0.25rem 0.75rem rgba(0, 0, 0, 0.12);}.upsell--custom-bg {--padding: var(--active-padding);background: var(--regular-bg-color);}.upsell--custom-bg[data-selected="true"] {background: var(--selected-bg-color);}.upsell--border {--padding: var(--active-padding);border: solid var(--border-width) var(--regular-border-color);}.upsell--border[data-selected="true"] {border-color: var(--selected-border-color);}.upsell-toggle-btn:not([disabled]) {cursor: pointer;}.upsell__image {flex-shrink: 0;margin-right: 1rem;align-self: flex-start;position: relative;z-index: 0;}.upsell__image img {width: auto;height: var(--image-size);border-radius: var(--image-border-radius);}.upsell__content {flex-grow: 1;}.upsell__title {display: flex;align-items: flex-start;justify-content: space-between;font-size: var(--title-font-size);}.upsell__title h3 {margin: 0;font-size: inherit;line-height: 1.2;}.upsell__title a {color: rgb(var(--color-foreground));text-decoration: none;}.upsell__title:hover a {text-decoration: underline;}.upsell__price {line-height: 1.1;font-size: var(--price-font-size);}.upsell__title .upsell__price {padding-left: 1rem;text-align: right;}.upsell__price--separate {margin-top: 0.3rem;}.upsell__desc {margin: 0;font-size: var(--desc-font-size);line-height: 1.3;margin-top: 0.3rem;}.upsell__variant-picker {display: flex;align-items: center;flex-wrap: wrap;row-gap: 0.4rem;column-gap: 0.5rem;margin-top: 0.5rem;}.upsell__variant-picker .select {width: fit-content;}.upsell__variant-picker .select__select {height: 1.8rem;font-size: 1rem;padding: 0 calc(var(--inputs-border-width) + 2rem) 0 0.7rem;}.upsell__variant-picker .select .icon-caret {right: calc(var(--inputs-border-width) + 0.6rem);}.upsell__desc + .upsell__variant-picker {margin-top: 0.8rem;}.hide-compare-price .compare-price {display: none;}.toggle-switch {font-size: 2rem;width: 2em;height: 1em;border-radius: 1em;position: relative;z-index: 0;background: rgba(var(--color-foreground), 0.2);margin-left: 1.5rem;flex-shrink: 0;transition: 0.2s background ease-in-out;}.toggle-switch__slider {display: block;width: 0.86em;height: 0.86em;border-radius: 1em;position: absolute;left: 0.5em;top: 0.5em;transform: translate(-50%, -50%);transition: left 0.3s ease-in-out;background: rgb(var(--color-background));}[data-selected="true"] .toggle-switch {background: rgb(var(--accent-color));}[data-selected="true"] .toggle-switch__slider {left: 1.5em;}.upsell__checkbox {padding: 0.5rem;margin-left: 1rem;font-size: 2rem;}.upsell__checkbox svg {width: 0.9em;height: 0.9em;color: rgb(var(--accent-color));}.upsell__checkbox .material-icon {font-size: 1em;color: rgb(var(--accent-color));}[data-selected="false"] .checkmark-checked, [data-selected="true"] .checkmark-unchecked {display: none;}.upsell--btn-left .toggle-switch, .upsell--btn-left .upsell__checkbox, .upsell--btn-left .upsell__plus-btn, .upsell--btn-left .upsell__add-btn {order: -1;margin-left: 0;margin-right: 1rem;}.upsell--btn-left .toggle-switch, .upsell--btn-left .upsell__checkbox, .upsell--btn-left .upsell__plus-btn {padding: 0.5rem 0.25rem;}.upsell__plus-btn {font-size: 2.6rem;width: 1em;height: 1em;display: flex;align-items: center;justify-content: center;border-radius: 0.1em;border: solid 0.1rem rgba(var(--color-foreground), 0.75);flex-shrink: 0;margin-left: 1.5rem;transition: 0.1s all ease-in-out;}[data-selected="true"] .upsell__plus-btn {background: rgb(var(--accent-color));border-color: rgb(var(--accent-color));}.upsell__plus-btn .material-icon {font-size: 0.65em;color: rgba(var(--color-foreground), 0.75);}[data-selected="true"] .upsell__plus-btn .material-icon {color: rgb(var(--color-background));}.upsell__add-btn {font-size: 1.5rem;line-height: 1.9;padding: 0 1em;margin-left: 1.5rem;border-radius: calc(var(--buttons-radius-outset) / 2);position: relative;min-height: auto;min-width: auto;}.upsells-container--stacked-columns {display: grid;grid-template-columns: repeat(var(--item-count), 1fr);align-items: flex-start;column-gap: 1rem;}.upsells-container--stacked-columns .upsell {margin: 0;}.upsells-container--stacked-columns .upsell__container {flex-direction: column;}.upsells-container--stacked-columns .upsell__toggle-switch, .upsells-container--stacked-columns .upsell__checkbox, .upsells-container--stacked-columns .upsell__plus-btn {margin: 0;position: absolute;top: var(--padding);right: var(--padding);}.upsells-container--stacked-columns .upsell__checkbox {padding: 0;}.upsells-container--stacked-columns .upsell__image {align-self: auto;margin: 0;margin-bottom: 1rem;width: calc(100% * var(--image-size-number) / 6.5);}.upsells-container--stacked-columns .upsell__image img {height: auto;width: 100%;}.upsells-container--stacked-columns .upsell__title {flex-direction: column;align-items: center;text-align: center;}.upsells-container--stacked-columns .upsell__title .upsell__price {margin: 0;margin-top: 1rem;padding: 0;text-align: center;}.upsells-container--stacked-columns[data-count="3"] .upsell {--padding: 0.8rem;}@media screen and (max-width: 749px) {.upsells-container--stacked-columns[data-count="3"] .upsell {--padding: 0.6rem;}.upsells-container--stacked-columns[data-count="3"] .upsell__title {font-size: calc(var(--title-font-size) * 0.9);}.upsells-container--stacked-columns[data-count="3"] .upsell__desc {font-size: calc(var(--desc-font-size) * 0.9);}.upsells-container--stacked-columns[data-count="3"] .upsell__checkbox {font-size: 1.8rem;}.upsells-container--stacked-columns[data-count="2"] .upsell__toggle-switch, .upsells-container--stacked-columns[data-count="3"] .upsell__plus-btn {font-size: 1.6rem;}.upsells-container--stacked-columns[data-count="3"] .upsell__toggle-switch {font-size: 1.4rem;}.upsells-container--stacked-columns[data-count="2"] .upsell__plus-btn {font-size: 2rem;}}@media screen and (min-width: 1000px) {.product__info-wrapper :not(.upsells-container--stacked-columns) .upsell, .cart-drawer--desktop-width-large :not(.upsells-container--stacked-columns) .upsell {--active-padding: 1.2rem;border-radius: calc(var(--border-radius) * 1.2);}.product__info-wrapper .upsell__image, .cart-drawer--desktop-width-large .upsell__image {margin-right: 1.2rem;}.product__info-wrapper .upsell__image img, .cart-drawer--desktop-width-large .upsell__image img {height: calc(var(--image-size) * 1.2);border-radius: calc(var(--image-border-radius) * 1.2);}.product__info-wrapper .upsell__title, .cart-drawer--desktop-width-large .upsell__title {font-size: calc(var(--title-font-size) * 1.2);}.product__info-wrapper .upsell__desc, .cart-drawer--desktop-width-large .upsell__desc {font-size: calc(var(--desc-font-size) * 1.2);}.product__info-wrapper .upsell__variant-picker .select__select, .cart-drawer--desktop-width-large .upsell__variant-picker .select__select {height: 2rem;font-size: 1.1rem;}.product__info-wrapper .upsell__price, .cart-drawer--desktop-width-large .upsell__price {font-size: calc(var(--price-font-size) * 1.2);}.product__info-wrapper .upsell__checkbox, .product__info-wrapper .upsell__toggle-switch, .cart-drawer--desktop-width-large .upsell__checkbox, .cart-drawer--desktop-width-large .upsell__toggle-switch {font-size: 2.2rem;}.product__info-wrapper .upsell__plus-btn, .cart-drawer--desktop-width-large .upsell__plus-btn {font-size: 3rem;}.product__info-wrapper .upsells-container--stacked-columns[data-count="3"] .upsell__toggle-switch, .cart-drawer--desktop-width-large .upsells-container--stacked-columns[data-count="3"] .upsell__toggle-switch {font-size: 1.8rem;}.product__info-wrapper .upsells-container--stacked-columns[data-count="2"] .upsell__plus-btn, .cart-drawer--desktop-width-large .upsells-container--stacked-columns[data-count="2"] .upsell__plus-btn {font-size: 2.6rem;}.product__info-wrapper .upsells-container--stacked-columns[data-count="3"] .upsell__plus-btn, .cart-drawer--desktop-width-large .upsells-container--stacked-columns[data-count="3"] .upsell__plus-btn {font-size: 2.2rem;}}.product-info-upsells-container .splide__pagination, .cart-drawer-upsells-container .splide__pagination {padding-top: 0.5rem;}.cart-drawer-gift {max-width: 40rem;margin-left: auto;margin-right: auto;}.cart-drawer-gift__progress {line-height: 1.2;margin: 0;padding: 1rem var(--padding);}.upsell--border .cart-drawer-gift__progress {border-bottom: solid 0.1rem var(--regular-border-color);}.upsell--border[data-selected="true"] .cart-drawer-gift__progress {border-color: var(--selected-border-color);}.cart-drawer-gift__image__locked {position: absolute;z-index: 1;bottom: 0;left: 0;width: 100%;height: 100%;border-radius: var(--image-border-radius);display: flex;align-items: center;justify-content: center;background: rgba(var(--color-background), 0.75);border: solid 0.1rem rgba(var(--color-foreground), 0.01);}.cart-drawer-gift__image__locked .lock {--lock-color: rgb(var(--color-foreground));transform: scale(0.65);}.bundle-offer {--padding: 1rem;}.bundle-offer h3 {margin-bottom: 0.4em;}.bundle-offer .upsell {margin: 0;}.bundle-offer__divider {padding: 0 var(--padding);}.bundle-offer__divider svg {display: block;font-size: 3rem;width: 1em;height: 1em;padding: 0.2em;border-radius: 50%;color: rgb(var(--border-color));border: solid 1px rgba(var(--border-color), var(--plus-border-opacity));}.bundle-offer__divider span {display: block;height: 1px;flex-grow: 1;background: rgba(var(--border-color), var(--divider-opacity));}.bundle-offer__footer {border-top: solid var(--border-thickness) rgba(var(--border-color), var(--border-opacity));padding: var(--padding);}.bundle-offer__footer__total-text {font-size: 1.8rem;}.bundle-offer__footer p {margin: 0;line-height: 1.25;}.bundle-offer__footer__benefit {font-size: 1.2rem;}.bundle-offer__footer__prices {font-size: 1.7rem;}.bundle-offer-container + product-form {margin-top: 1rem;}@media screen and (min-width: 1000px) {.bundle-offer {--padding: 1.2rem;}.bundle-offer__footer__total-text {font-size: 2rem;}.bundle-offer__footer__benefit {font-size: 1.3rem;}.bundle-offer__footer__prices {font-size: 1.8rem;}.bundle-offer-container + product-form {margin-top: 1.2rem;}}.pt-0 {padding-top: 0;}.pb-0 {padding-bottom: 0;}.mt-0 {margin-top: 0;}.mb-0 {margin-bottom: 0;}.accordion {display: block;border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);}.accordion--top-border {border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);}.accordion__summary {display: flex;align-items: center;position: relative;line-height: 1;padding: 1.25rem 0;cursor: pointer;}.accordion .summary__title {display: flex;align-items: center;flex: 1;}.accordion .summary__title + .icon-caret {height: calc(var(--font-heading-scale) * 0.6rem);transition: transform 0.2s ease-in-out;}.accordion .summary__title + .icon-plus {width: calc(var(--font-heading-scale) * 1.2rem);transition: transform 0.2s ease-in-out;}.accordion + .accordion {border-top: none;margin-top: 0;}.accordion__title {display: inline-block;max-width: calc(100% - 6rem);font-size: calc(var(--font-heading-scale) * 1.2rem);margin: 0;word-break: break-word;}.accordion .summary__title img {height: calc(var(--font-heading-scale) * 1.8rem);width: auto;margin-right: calc(var(--font-heading-scale) * 0.75rem);}.accordion .material-symbols-outlined {align-self: center;color: rgb(var(--color-foreground));font-size: calc(var(--font-heading-scale) * 1.8rem);margin-right: calc(var(--font-heading-scale) * 0.75rem);}.accordion__details[open] .accordion__summary .icon-caret {transform: rotate(180deg);}.accordion__details[open] .accordion__summary .icon-plus {transform: rotate(135deg);}.accordion__content-wrapper {display: grid;grid-template-rows: 0fr;overflow: hidden;transition: grid-template-rows 0.2s ease-in-out;}.accordion__details[open] + .accordion__content-wrapper {grid-template-rows: 1fr;}.accordion__content {word-break: break-word;overflow: visible;padding: 0 0.6rem;min-height: 0;transition: padding-bottom 0s, opacity 0.2s;overflow-y: visible;opacity: 0;}.accordion__details[open] + .accordion__content-wrapper .accordion__content {padding-bottom: 1.5rem;opacity: 1;}.accordion--small .material-symbols-outlined {font-size: calc(var(--font-heading-scale) * 1.4rem);margin-right: calc(var(--font-heading-scale) * 0.4rem);}.accordion--small .summary__title img {height: calc(var(--font-heading-scale) * 1.4rem);margin-right: calc(var(--font-heading-scale) * 0.4rem);}.accordion--small .accordion__title {max-width: calc(100% - 5rem);font-size: calc(var(--font-heading-scale) * 1.1rem);}.accordion--small .summary__title + .icon-caret {height: calc(var(--font-heading-scale) * 0.5rem);}.accordion--small .summary__title + .icon-plus {width: calc(var(--font-heading-scale) * 1rem);}.accordion--large .material-symbols-outlined {font-size: calc(var(--font-heading-scale) * 2.2rem);}.accordion--large .summary__title img {height: calc(var(--font-heading-scale) * 2.2rem);}.accordion--large .accordion__title {max-width: calc(100% - 5rem);font-size: calc(var(--font-heading-scale) * 1.4rem);}.accordion--large .summary__title + .icon-caret {height: calc(var(--font-heading-scale) * 0.7rem);}.accordion--large .summary__title + .icon-plus {width: calc(var(--font-heading-scale) * 1.4rem);}.rating {display: inline-block;margin: 0;}.product .rating-star {--letter-spacing: 0.8;--font-size: 1.7;}.card-wrapper .rating-star {--letter-spacing: 0.7;--font-size: 1.4;}.rating-star {--percent: calc( ( var(--rating) / var(--rating-max) + var(--rating-decimal) * var(--font-size) / (var(--rating-max) * (var(--letter-spacing) + var(--font-size))) ) * 100% );letter-spacing: calc(var(--letter-spacing) * 1rem);font-size: calc(var(--font-size) * 1rem);line-height: 1;display: inline-block;font-family: Times;margin: 0;}.rating-star::before {content: "★★★★★";background: linear-gradient( 90deg, var(--color-icon) var(--percent), rgba(var(--color-foreground), 0.15) var(--percent) );-webkit-background-clip: text;-webkit-text-fill-color: transparent;}.rating-text {display: none;}.rating-count {display: inline-block;margin: 0;}@media (forced-colors: active) {.rating {display: none;}.rating-text {display: block;}}.header__icon--menu {position: initial;}.js menu-drawer > details > summary::before, .js menu-drawer > details[open]:not(.menu-opening) > summary::before {content: "";position: absolute;cursor: default;width: 100%;height: calc(100vh - 100%);height: calc( var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%)) );top: 100%;left: 0;background: rgba(var(--color-foreground), 0.5);opacity: 0;visibility: hidden;z-index: 2;transition: opacity 0s, visibility 0s;top: 0;}menu-drawer > details[open] > summary::before {visibility: visible;opacity: 1;transition: opacity var(--duration-default) ease, visibility var(--duration-default) ease;}.menu-drawer {position: absolute;transform: translateX(-100%);visibility: hidden;z-index: 10;left: 0;top: 100%;width: calc(100vw - 4rem);padding: 0;border-width: 0 var(--drawer-border-width) 0 0;background-color: rgb(var(--color-background));overflow-x: hidden;border-style: solid;border-color: rgba(var(--color-foreground), var(--drawer-border-opacity));filter: drop-shadow( var(--drawer-shadow-horizontal-offset) var(--drawer-shadow-vertical-offset) var(--drawer-shadow-blur-radius) rgba(var(--color-shadow), var(--drawer-shadow-opacity)) );top: 0;position: fixed;}.js .menu-drawer {height: calc(100vh - 100%);height: calc( var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%)) );height: 100vh;}.js details[open] > .menu-drawer, .js details[open] > .menu-drawer__submenu {transition: transform var(--duration-default) ease, visibility var(--duration-default) ease;}.no-js details[open] > .menu-drawer, .js details[open].menu-opening > .menu-drawer, details[open].menu-opening > .menu-drawer__submenu {transform: translateX(0);visibility: visible;}.js .menu-drawer__navigation .submenu-open {visibility: hidden;}.menu-drawer__title-and-close-btn {display: flex;align-items: center;justify-content: space-between;padding: 1rem 0.5rem 1rem 2rem;border-bottom: solid 1px rgba(var(--color-foreground), 0.1);}.menu-drawer__title {margin: 0;}.menu-drawer__close-btn {display: flex;align-items: center;justify-content: center;}@media screen and (min-width: 750px) {.menu-drawer {width: 40rem;}}.menu-drawer__inner-container {position: relative;height: 100%;}.menu-drawer__navigation-container {height: 100%;}.menu-drawer__navigation {padding: 5.6rem 0;}.menu-drawer__inner-submenu {height: 100%;overflow-x: hidden;overflow-y: auto;}.no-js .menu-drawer__navigation {padding: 0;}.no-js .menu-drawer__navigation > ul > li {border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.04);}.no-js .menu-drawer__submenu ul > li {border-top: 0.1rem solid rgba(var(--color-foreground), 0.04);}.js .menu-drawer__menu li {margin-bottom: 0.2rem;}.menu-drawer__menu-item {padding: 1.1rem 3.2rem;text-decoration: none;font-size: 1.8rem;}.no-js .menu-drawer__menu-item {font-size: 1.6rem;}.no-js .menu-drawer__submenu .menu-drawer__menu-item {padding: 1.2rem 5.2rem 1.2rem 6rem;}.no-js .menu-drawer__submenu .menu-drawer__submenu .menu-drawer__menu-item {padding-left: 9rem;}.menu-drawer summary.menu-drawer__menu-item {padding-right: 5.2rem;}.no-js .menu-drawer__menu-item .icon-caret {right: 3rem;}.menu-drawer__menu-item--active, .menu-drawer__menu-item:focus, .menu-drawer__close-button:focus, .menu-drawer__menu-item:hover, .menu-drawer__close-button:hover {color: rgb(var(--color-foreground));background-color: rgba(var(--color-foreground), 0.04);}.menu-drawer__menu-item--active:hover {background-color: rgba(var(--color-foreground), 0.08);}.js .menu-drawer__menu-item .icon-caret, .no-js .menu-drawer .icon-arrow {display: none;}.menu-drawer__menu-item > .icon-arrow {position: absolute;right: 2.5rem;top: 50%;transform: translateY(-50%);}.js .menu-drawer__submenu {position: absolute;top: 0;width: 100%;bottom: 0;left: 0;background-color: rgb(var(--color-background));border-left: 0.1rem solid rgba(var(--color-foreground), 0.2);z-index: 1;transform: translateX(100%);visibility: hidden;}.js .menu-drawer__submenu .menu-drawer__submenu {overflow-y: auto;}header-drawer .js .menu-drawer__submenu .menu-drawer__submenu {overflow-y: auto;}.menu-drawer__close-button {margin-top: 1.5rem;padding: 1.2rem 2.6rem;text-decoration: none;display: flex;align-items: center;font-size: 1.4rem;width: 100%;background-color: transparent;font-family: var(--font-body-family);font-style: var(--font-body-style);text-align: left;}.no-js .menu-drawer__close-button {display: none;}.menu-drawer__close-button .icon-arrow {transform: rotate(180deg);margin-right: 1rem;}.menu-drawer__utility-links {padding: 2rem;background-color: rgba(var(--color-foreground), 0.03);}.menu-drawer__account {display: inline-flex;align-items: center;text-decoration: none;padding: 1.2rem;margin-left: -1.2rem;font-size: 1.4rem;color: rgb(var(--color-foreground));}.menu-drawer__account .icon-account {height: 2rem;width: 2rem;margin-right: 1rem;}.menu-drawer__account:hover .icon-account {transform: scale(1.07);}.menu-drawer .list-social {justify-content: flex-start;margin-left: -1.25rem;margin-top: 2rem;}.menu-drawer .list-social:empty {display: none;}.menu-drawer .list-social__link {padding: 1.3rem 1.25rem;}.menu-drawer__menu-item {padding: 0.9rem 2rem;color: rgb(var(--color-foreground));}.menu-drawer__inner-container {height: auto;}header-drawer .menu-drawer__inner-container {height: calc(100% - 6.5rem);}.menu-drawer__navigation {padding: 1rem 0;}.menu-drawer__utility-links {background: transparent;padding: 1rem 2rem;border-top: solid 1px rgba(var(--color-foreground), 0.1);}.menu-drawer .list-social {justify-content: center;margin: 0;}.header__icon--menu[aria-expanded="true"]:before {z-index: 1;position: fixed;height: 100vh;top: 0;}.mega-menu {position: static;}.mega-menu__content {background-color: rgb(var(--color-background));border-left: 0;border-radius: 0;border-right: 0;left: 0;overflow-y: auto;padding-bottom: 2.4rem;padding-top: 2.4rem;position: absolute;right: 0;top: 100%;}.shopify-section-header-sticky .mega-menu__content {max-height: calc(100vh - var(--header-bottom-position-desktop, 20rem) - 4rem);}.header-wrapper--border-bottom .mega-menu__content {border-top: 0;}.js .mega-menu__content {opacity: 0;transform: translateY(-1.5rem);}.mega-menu[open] .mega-menu__content {opacity: 1;transform: translateY(0);}.mega-menu__list {display: grid;gap: 2.4rem 4rem;grid-template-columns: repeat(6, minmax(0, 1fr));list-style: none;}.mega-menu__link {color: rgba(var(--color-foreground), 0.9);display: block;font-size: 1.3rem;line-height: calc(1 + 0.3 / var(--font-body-scale));padding-bottom: 0.6rem;padding-top: 0.6rem;text-decoration: none;transition: text-decoration var(--duration-short) ease;word-wrap: break-word;}.mega-menu__link--level-2 {font-size: 1.4rem;}.mega-menu__link--level-2:not(:only-child) {margin-bottom: 0.8rem;}.header--top-center .mega-menu__list {display: flex;justify-content: center;flex-wrap: wrap;column-gap: 0;}.header--top-center .mega-menu__list > li {width: 16%;padding-right: 2.4rem;}.mega-menu__link:hover {text-decoration: underline;}.mega-menu__link--active {color: rgb(var(--accent-color));}.mega-menu .mega-menu__list--condensed {display: block;}.mega-menu__list--condensed .mega-menu__link {font-weight: normal;}.products-mega-menu__body {position: absolute;top: 100%;left: 0;z-index: 5;width: 100%;border-top: solid 0.1rem rgba(var(--color-foreground), var(--popup-border-opacity));border-bottom: solid 0.1rem rgba(var(--color-foreground), var(--popup-border-opacity));}.products-mega-menu__body > .page-width {padding-top: 1.5rem;padding-bottom: 1.5rem;overflow: hidden;}.products-mega-menu__overlay {position: absolute;top: 100%;left: 0;width: 100%;height: 100vh;z-index: -1;background: rgba(0, 0, 0, 0.3);}.products-mega-menu__list {list-style: none;padding-left: 0;max-width: 25rem;}.products-mega-menu__container {position: relative;}.products-mega-menu__content {position: absolute;top: 0;left: 25rem;max-width: calc(100% - 25rem);width: 100%;}.products-mega-menu__item .header__menu-item {border-bottom: solid 0.1rem rgba(var(--color-foreground), 0.15);display: flex;align-items: center;transition: all 0.2s;}.products-mega-menu__item .header__menu-item svg {margin-left: auto;width: 1.1rem;height: 1.1rem;transform: rotate(-90deg);}.header__menu-item__preview-image {width: 4rem;height: 4rem;margin-right: 1rem;flex-shrink: 0;}.header__menu-item__preview-image img {width: 100%;height: 100%;object-fit: cover;object-position: center center;}.products-mega-menu__item--active .header__menu-item {background: rgba(var(--color-foreground), 0.04);}.products-mega-menu__cards {padding-left: 0;list-style: none;display: grid;grid-template-columns: repeat(5, 1fr);justify-items: center;align-items: start;column-gap: 2rem;row-gap: 1.5rem;transition: all 0.2s;overflow: hidden;visibility: hidden;opacity: 0;max-height: 0;padding-left: 2rem;}.products-mega-menu__item--active .products-mega-menu__cards {visibility: visible;opacity: 1;max-height: none;}.products-mega-menu__cards li {width: 100%;}.products-mega-menu__cards .card__information {padding: 0;}.products-mega-menu__cards .card__information .price {display: none;}.products-mega-menu__cards .card__heading {font-size: calc(var(--font-heading-scale) * 1.1rem);}.products-mega-menu__cards .card__heading svg {transform: translateY(0.2rem);}.products-mega-menu__cards .card--card .card__content .card__information .card-information .loox-rating {margin-top: 0.3rem;}.products-mega-menu__cards svg.loox-icon {width: 1.3rem;height: 1.3rem;}.products-mega-menu__cards .loox-rating-label {font-size: 1.3rem;}.products-mega-menu__list, .products-mega-menu__content {min-height: 300px;max-height: 450px;overflow-y: auto;}@media screen and (max-width: 1250px) {.products-mega-menu__cards {grid-template-columns: repeat(4, 1fr);}}.products-mega-menu__cards--mobile {grid-template-columns: repeat(2, 1fr);visibility: visible;opacity: 1;max-height: none;padding: 1.5rem;}.products-mega-menu__cards .card__badge {display: none;}.header__menu-item__preview-image--mobile {width: 3rem;height: 3rem;margin-right: 0.75rem;}.menu-drawer__submenu {overflow-y: visible;}.newsletter-form {display: flex;flex-direction: column;justify-content: center;align-items: center;width: 100%;position: relative;}@media screen and (min-width: 750px) {.newsletter-form {align-items: flex-start;margin: 0 auto;max-width: 36rem;}}.newsletter-form__field-wrapper {width: 100%;}.newsletter-form__field-wrapper .field__input {padding-right: 5rem;}.newsletter-form__field-wrapper .field {z-index: 0;}.newsletter-form__message {justify-content: center;margin-bottom: 0;}.newsletter-form__message--success {margin-top: 2rem;}@media screen and (min-width: 750px) {.newsletter-form__message {justify-content: flex-start;}}.newsletter-form__button {width: 4.4rem;margin: 0;right: var(--inputs-border-width);top: 0;height: 100%;z-index: 2;}.newsletter-form__button:focus-visible {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.4rem rgba(var(--color-foreground));background-color: rgb(var(--color-background));}.newsletter-form__button:focus {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.4rem rgba(var(--color-foreground));background-color: rgb(var(--color-background));}.newsletter-form__button:not(:focus-visible):not(.focused) {box-shadow: inherit;background-color: inherit;}.newsletter-form__button .icon {width: 1.5rem;}.newsletter__solid-btn {margin-top: 1rem;}.search__input.field__input {padding-right: 9.8rem;}.search__button {right: var(--inputs-border-width);top: var(--inputs-border-width);}.reset__button {right: calc(var(--inputs-border-width) + 4.4rem);top: var(--inputs-border-width);}.reset__button:not(:focus-visible)::after {border-right: 0.1rem solid rgba(var(--color-foreground), 0.08);display: block;height: calc(100% - 1.6rem);content: "";position: absolute;right: 0;}.reset__button:not(:focus)::after {border-right: 0.1rem solid rgba(var(--color-foreground), 0.08);display: block;height: calc(100% - 1.8rem);content: "";position: absolute;right: 0;}.search__button:focus-visible, .reset__button:focus-visible {background-color: rgb(var(--color-background));z-index: 4;}.search__button:focus, .reset__button:focus {background-color: rgb(var(--color-background));z-index: 4;}.search__button:not(:focus-visible):not(.focused), .reset__button:not(:focus-visible):not(.focused) {box-shadow: inherit;background-color: inherit;}.search__button:hover .icon, .reset__button:hover .icon {transform: scale(1.07);}.search__button .icon {height: 1.8rem;width: 1.8rem;}.reset__button .icon.icon-close {height: 1.8rem;width: 1.8rem;stroke-width: 0.1rem;}input::-webkit-search-decoration {-webkit-appearance: none;}.disclosure {position: relative;}.disclosure__button {align-items: center;cursor: pointer;display: flex;height: 4rem;padding: 0 1.5rem 0 1.5rem;font-size: 1.3rem;background-color: transparent;}.disclosure__list-wrapper {border-width: var(--popup-border-width);border-style: solid;border-color: rgba(var(--color-foreground), var(--popup-border-opacity));overflow: hidden;position: absolute;bottom: 100%;transform: translateY(-1rem);z-index: 2;background-color: rgb(var(--color-background));border-radius: var(--popup-corner-radius);box-shadow: var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius) rgba(var(--color-shadow), var(--popup-shadow-opacity));}.disclosure__list {position: relative;overflow-y: auto;font-size: 1.4rem;padding-bottom: 0.5rem;padding-top: 0.5rem;scroll-padding: 0.5rem 0;min-height: 8.2rem;max-height: 19rem;max-width: 22rem;min-width: 12rem;width: max-content;}.disclosure__item {position: relative;}.disclosure__link {display: block;padding: 0.5rem 2.2rem;text-decoration: none;line-height: calc(1 + 0.8 / var(--font-body-scale));}.header__icon--menu {position: initial;}.js menu-drawer > details > summary::before, .js menu-drawer > details[open]:not(.menu-opening) > summary::before {content: "";position: absolute;cursor: default;width: 100%;height: calc(100vh - 100%);height: calc( var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%)) );top: 100%;left: 0;background: rgba(var(--color-foreground), 0.5);opacity: 0;visibility: hidden;z-index: 2;transition: opacity 0s, visibility 0s;top: 0;}menu-drawer > details[open] > summary::before {visibility: visible;opacity: 1;transition: opacity var(--duration-default) ease, visibility var(--duration-default) ease;}.menu-drawer {position: absolute;transform: translateX(-100%);visibility: hidden;z-index: 10;left: 0;top: 100%;width: calc(100vw - 4rem);padding: 0;border-width: 0 var(--drawer-border-width) 0 0;background-color: rgb(var(--color-background));overflow-x: hidden;border-style: solid;border-color: rgba(var(--color-foreground), var(--drawer-border-opacity));filter: drop-shadow( var(--drawer-shadow-horizontal-offset) var(--drawer-shadow-vertical-offset) var(--drawer-shadow-blur-radius) rgba(var(--color-shadow), var(--drawer-shadow-opacity)) );top: 0;position: fixed;}.js .menu-drawer {height: calc(100vh - 100%);height: calc( var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%)) );height: 100vh;}.js details[open] > .menu-drawer, .js details[open] > .menu-drawer__submenu {transition: transform var(--duration-default) ease, visibility var(--duration-default) ease;}.no-js details[open] > .menu-drawer, .js details[open].menu-opening > .menu-drawer, details[open].menu-opening > .menu-drawer__submenu {transform: translateX(0);visibility: visible;}.js .menu-drawer__navigation .submenu-open {visibility: hidden;}.menu-drawer__title-and-close-btn {display: flex;align-items: center;justify-content: space-between;padding: 1rem 0.5rem 1rem 2rem;border-bottom: solid 1px rgba(var(--color-foreground), 0.1);}.menu-drawer__title {margin: 0;}.menu-drawer__close-btn {display: flex;align-items: center;justify-content: center;}@media screen and (min-width: 750px) {.menu-drawer {width: 40rem;}}.menu-drawer__inner-container {position: relative;height: 100%;}.menu-drawer__navigation-container {height: 100%;}.menu-drawer__navigation {padding: 5.6rem 0;}.menu-drawer__inner-submenu {height: 100%;overflow-x: hidden;overflow-y: auto;}.no-js .menu-drawer__navigation {padding: 0;}.no-js .menu-drawer__navigation > ul > li {border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.04);}.no-js .menu-drawer__submenu ul > li {border-top: 0.1rem solid rgba(var(--color-foreground), 0.04);}.js .menu-drawer__menu li {margin-bottom: 0.2rem;}.menu-drawer__menu-item {padding: 1.1rem 3.2rem;text-decoration: none;font-size: 1.8rem;}.no-js .menu-drawer__menu-item {font-size: 1.6rem;}.no-js .menu-drawer__submenu .menu-drawer__menu-item {padding: 1.2rem 5.2rem 1.2rem 6rem;}.no-js .menu-drawer__submenu .menu-drawer__submenu .menu-drawer__menu-item {padding-left: 9rem;}.menu-drawer summary.menu-drawer__menu-item {padding-right: 5.2rem;}.no-js .menu-drawer__menu-item .icon-caret {right: 3rem;}.menu-drawer__menu-item--active, .menu-drawer__menu-item:focus, .menu-drawer__close-button:focus, .menu-drawer__menu-item:hover, .menu-drawer__close-button:hover {color: rgb(var(--color-foreground));background-color: rgba(var(--color-foreground), 0.04);}.menu-drawer__menu-item--active:hover {background-color: rgba(var(--color-foreground), 0.08);}.js .menu-drawer__menu-item .icon-caret, .no-js .menu-drawer .icon-arrow {display: none;}.menu-drawer__menu-item > .icon-arrow {position: absolute;right: 2.5rem;top: 50%;transform: translateY(-50%);}.js .menu-drawer__submenu {position: absolute;top: 0;width: 100%;bottom: 0;left: 0;background-color: rgb(var(--color-background));border-left: 0.1rem solid rgba(var(--color-foreground), 0.2);z-index: 1;transform: translateX(100%);visibility: hidden;}.js .menu-drawer__submenu .menu-drawer__submenu {overflow-y: auto;}header-drawer .js .menu-drawer__submenu .menu-drawer__submenu {overflow-y: auto;}.menu-drawer__close-button {margin-top: 1.5rem;padding: 1.2rem 2.6rem;text-decoration: none;display: flex;align-items: center;font-size: 1.4rem;width: 100%;background-color: transparent;font-family: var(--font-body-family);font-style: var(--font-body-style);text-align: left;}.no-js .menu-drawer__close-button {display: none;}.menu-drawer__close-button .icon-arrow {transform: rotate(180deg);margin-right: 1rem;}.menu-drawer__utility-links {padding: 2rem;background-color: rgba(var(--color-foreground), 0.03);}.menu-drawer__account {display: inline-flex;align-items: center;text-decoration: none;padding: 1.2rem;margin-left: -1.2rem;font-size: 1.4rem;color: rgb(var(--color-foreground));}.menu-drawer__account .icon-account {height: 2rem;width: 2rem;margin-right: 1rem;}.menu-drawer__account:hover .icon-account {transform: scale(1.07);}.menu-drawer .list-social {justify-content: flex-start;margin-left: -1.25rem;margin-top: 2rem;}.menu-drawer .list-social:empty {display: none;}.menu-drawer .list-social__link {padding: 1.3rem 1.25rem;}.menu-drawer__menu-item {padding: 0.9rem 2rem;color: rgb(var(--color-foreground));}.menu-drawer__inner-container {height: auto;}header-drawer .menu-drawer__inner-container {height: calc(100% - 6.5rem);}.menu-drawer__navigation {padding: 1rem 0;}.menu-drawer__utility-links {background: transparent;padding: 1rem 2rem;border-top: solid 1px rgba(var(--color-foreground), 0.1);}.menu-drawer .list-social {justify-content: center;margin: 0;}.header__icon--menu[aria-expanded="true"]:before {z-index: 1;position: fixed;height: 100vh;top: 0;}.product {margin: 0;}.product.grid {gap: 0;}.product--no-media {max-width: 57rem;margin: 0 auto;}.product__media-wrapper {padding-left: 0;}dynamic-dates, quantity-gifts, quantity-breaks, bundle-deals {display: block;}.product-media__trust-badge {position: absolute;z-index: 1;}.product-media__trust-badge--small {width: 15%;}.product-media__trust-badge--medium {width: 20%;}.product-media__trust-badge--large {width: 25%;}.product-media__trust-badge img {max-width: 100%;width: 100%;}.product-media__trust-badge--top-right {top: 1rem;right: 1rem;}.product-media__trust-badge--top-left {top: 1rem;left: 1rem;}.product-media__trust-badge--bottom-right {bottom: 1rem;right: 1rem;}.product-media__trust-badge--bottom-left {bottom: 1rem;left: 1rem;}.product-title--uppercase {text-transform: uppercase;}.product-page-price .price__regular .price-item--regular {font-size: 2.5rem;}.color-swatches-container {display: flex;column-gap: 0.5rem;row-gap: 0.5rem;flex-wrap: wrap;--swatch-size: 4.75rem;--transparent-border-size: 0.3rem;--selected-border-size: 0.2rem;}.color-swatches-container--size-small {--swatch-size: 3.5rem;--transparent-border-size: 0.2rem;--selected-border-size: 0.1rem;}.color-swatches-container--size-large {--swatch-size: 5.75rem;column-gap: 0.8rem;row-gap: 0.8rem;}.color-swatches-container--size-extra-large {--swatch-size: 7rem;column-gap: 1rem;row-gap: 1rem;padding-top: 0.2rem;}.color-swatch {position: relative;border-radius: var(--swatches-radius);}.color-swatch__image {width: var(--swatch-size);height: var(--swatch-size);position: relative;overflow: hidden;border-radius: var(--swatches-radius);border: solid var(--transparent-border-size) rgb(var(--color-background));box-shadow: 0 0 0 var(--selected-border-size) rgba(var(--color-foreground), var(--swatches-border-opacity));}.color-swatch input:checked {color: red;}.color-swatch input:checked + .color-swatch__image {box-shadow: 0 0 0 var(--selected-border-size) rgba(var(--color-foreground), var(--swatches-selected-border-opacity));}.color-swatch input.disabled + .color-swatch__image {opacity: 0.3;}.color-swatch__image img {position: absolute;top: 0;left: 0;width: 100%;height: 100%;object-fit: cover;object-position: center center;}.color-swatch__custom-color {width: 95%;height: 95%;background: var(--bg-color);position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);border-radius: var(--swatches-radius);box-shadow: inset 0 0 0 1px rgba(var(--color-foreground), 0.3);}.color-swatch_hidden-label {position: absolute;top: 0;left: 0;width: 100%;height: 100%;cursor: pointer;}.color-swatch__label {position: absolute;left: 50%;bottom: calc(100% + 0.5em);text-align: center;background: rgb(var(--color-foreground));color: rgb(var(--color-background));transform: translate(-50%, 50%);margin: 0;opacity: 0;visibility: hidden;font-size: 1.5rem;line-height: 2em;padding: 0.25em 0.65em;border-radius: 0.5em;transition: all 0.25s ease-in-out;white-space: nowrap;display: none;}@media (hover: hover) {.color-swatch:hover .color-swatch__label {opacity: 1;visibility: visible;transform: translate(-50%, 0);}}.color-swatch__label::before {font-size: 1em;content: "";display: block;position: absolute;top: 100%;left: 50%;transform: translateX(-50%);width: 0.75em;height: 0.5em;background: rgb(var(--color-foreground));clip-path: polygon(0 0, 50% 100%, 100% 0);}.estimated-shipping {display: flex;align-items: flex-start;}.estimated-shipping--align-center {align-items: center;}.estimated-shipping__icon:not(:empty) {margin-right: 1.25rem;min-height: 2.75rem;display: flex;align-items: center;--icon-size: 2.75rem;}.estimated-shipping__icon--small {--icon-size: 2.25rem;}.estimated-shipping__icon--large {--icon-size: 3.5rem;}.estimated-shipping__icon span {font-size: var(--icon-size);}.estimated-shipping__icon img {display: block;width: var(--icon-size);}.estimated-shipping__text p {margin-top: 0;margin-bottom: 0.5rem;font-size: 1.6rem;line-height: 2.75rem;}.estimated-shipping__text p:last-child {margin-bottom: 0;}@media screen and (min-width: 750px) {.product__column-sticky {display: block;position: sticky;top: 3rem;z-index: 2;}.product--thumbnail .thumbnail-list {padding-right: var(--media-shadow-horizontal-offset);}.product__info-wrapper {padding: 0 0 0 5rem;}.product__info-wrapper--extra-padding {padding: 0 0 0 8rem;}.product--right .product__info-wrapper {padding: 0 5rem 0 0;}.product--right .product__info-wrapper--extra-padding {padding: 0 8rem 0 0;}.product--right .product__media-list {margin-bottom: 2rem;}.product__media-container .slider-buttons {display: none;}.color-swatch__label {display: block;}.color-swatches-container {--swatch-size: 5rem;}.color-swatches-container--size-small {--swatch-size: 4.25rem;--transparent-border-size: 0.2rem;--selected-border-size: 0.2rem;}.color-swatches-container--size-large {--swatch-size: 6.5rem;}.color-swatches-container--size-extra-large {--swatch-size: 8rem;}}.payment-badges-block .list-payment {padding-top: 0;}.payment-badges {padding: 0;display: flex;justify-content: center;align-items: center;flex-wrap: wrap;margin: 0;}.emoji-benefits-container p {font-size: 1.4rem;line-height: 1;margin-top: 0;}.emoji-benefits-container p:last-child {margin-bottom: 0;}@media screen and (min-width: 990px) {.product--large:not(.product--no-media) .product__media-wrapper {max-width: 65%;width: calc(65% - var(--grid-desktop-horizontal-spacing) / 2);}.product--large:not(.product--no-media) .product__info-wrapper {padding: 0 0 0 4rem;max-width: 35%;width: calc(35% - var(--grid-desktop-horizontal-spacing) / 2);}.product--large:not(.product--no-media).product--right .product__info-wrapper {padding: 0 4rem 0 0;}.product--medium:not(.product--no-media) .product__media-wrapper, .product--small:not(.product--no-media) .product__info-wrapper {max-width: 55%;width: calc(55% - var(--grid-desktop-horizontal-spacing) / 2);}.product--medium:not(.product--no-media) .product__info-wrapper, .product--small:not(.product--no-media) .product__media-wrapper {max-width: 45%;width: calc(45% - var(--grid-desktop-horizontal-spacing) / 2);}}.shopify-payment-button__button {font-family: inherit;min-height: 4.6rem;}.shopify-payment-button__button [role="button"].focused, .no-js .shopify-payment-button__button [role="button"]:focus {outline: 0.2rem solid rgba(var(--color-foreground), 0.5) !important;outline-offset: 0.3rem;box-shadow: 0 0 0 0.1rem rgba(var(--color-button), var(--alpha-button-border)), 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3) !important;}.shopify-payment-button__button [role="button"]:focus:not(:focus-visible) {outline: 0;box-shadow: none !important;}.shopify-payment-button__button [role="button"]:focus-visible {outline: 0.2rem solid rgba(var(--color-foreground), 0.5) !important;box-shadow: 0 0 0 0.1rem rgba(var(--color-button), var(--alpha-button-border)), 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3) !important;}.shopify-payment-button__button--unbranded {--color-button: var(--color-base-outline-button-labels);--color-button-text: var(--color-base-outline-button-labels);--color-button: var(--color-background);--alpha-button-background: 1;}.color-background-2 .shopify-payment-button__button--unbranded, .color-accent-1 .shopify-payment-button__button--unbranded, .color-accent-2 .shopify-payment-button__button--unbranded {--color-button: var(--color-background);--color-button-text: var(--color-foreground);}.color-inverse .shopify-payment-button__button--unbranded {--color-button: var(--color-background);--color-button-text: var(--color-foreground);}.shopify-payment-button__button--unbranded::before, .shopify-payment-button__button--unbranded::after {--border-opacity: var(--buttons-border-opacity);}.shopify-payment-button__button--unbranded {background-color: rgba(var(--color-button), var(--alpha-button-background));color: rgb(var(--color-button-text));font-size: 1.4rem;line-height: calc(1 + 0.2 / var(--font-body-scale));letter-spacing: 0.07rem;}.shopify-payment-button__button--unbranded::selection {background-color: rgba(var(--color-button-text), 0.3);}.shopify-payment-button__button--unbranded:hover, .shopify-payment-button__button--unbranded:hover:not([disabled]) {background-color: rgba(var(--color-button), var(--alpha-button-background));}.shopify-payment-button__more-options {margin: 1.6rem 0 1rem;font-size: 1.2rem;line-height: calc(1 + 0.5 / var(--font-body-scale));letter-spacing: 0.05rem;text-decoration: underline;text-underline-offset: 0.3rem;}.shopify-payment-button__button + .shopify-payment-button__button--hidden {display: none;}.product-form {display: block;}.product-form__error-message-wrapper:not([hidden]) {display: flex;align-items: flex-start;font-size: 1.2rem;margin-bottom: 1.5rem;}.product-form__error-message-wrapper svg {flex-shrink: 0;width: 1.2rem;height: 1.2rem;margin-right: 0.7rem;margin-top: 0.5rem;}.product-form__input {flex: 0 0 100%;padding: 0;margin: 0 0 1.2rem 0;max-width: 37rem;min-width: fit-content;border: none;}.product-form__quantity {max-width: none;}.product-form__input--quantity-breaks {max-width: none;}variant-selects {display: block;}.product-form__input--dropdown {margin-bottom: 1.6rem;}.product-form__input:last-of-type {margin-bottom: 0;}.product-form__input .form__label {padding-left: 0;font-weight: 600;}fieldset.product-form__input .form__label {margin-bottom: 0.2rem;}.product-form__input input[type="radio"] {clip: rect(0, 0, 0, 0);overflow: hidden;position: absolute;height: 1px;width: 1px;}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"] + label {--color-foreground: var(--accent-color);border: var(--variant-pills-border-width) solid rgba(var(--color-foreground), var(--variant-pills-border-opacity));background-color: rgba( var(--color-foreground), var(--variant-pills-inactive-overlay-opacity) );border-radius: var(--variant-pills-radius);color: rgb(var(--color-foreground));display: inline-block;margin: 0.7rem 0.5rem 0.2rem 0;padding: var(--variant-pills-padding-y) var(--variant-pills-padding-x);font-size: var(--variant-pills-text-size);letter-spacing: 0.1rem;line-height: 1;text-align: center;transition: border var(--duration-short) ease;cursor: pointer;position: relative;}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"] + label.variant-pills--bold {font-weight: 700;}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"] + label:before {content: "";position: absolute;top: calc(var(--variant-pills-border-width) * -1);right: calc(var(--variant-pills-border-width) * -1);bottom: calc(var(--variant-pills-border-width) * -1);left: calc(var(--variant-pills-border-width) * -1);z-index: -1;border-radius: var(--variant-pills-radius);box-shadow: var(--variant-pills-shadow-horizontal-offset) var(--variant-pills-shadow-vertical-offset) var(--variant-pills-shadow-blur-radius) rgba(var(--color-shadow), var(--variant-pills-shadow-opacity));}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"] + label:hover {border-color: rgb(var(--color-foreground));}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:checked + label {background-color: rgb(var(--color-foreground));color: rgb(var(--color-background));}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:not(.disabled) + label > .visually-hidden {display: none;}@media screen and (forced-colors: active) {.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:checked + label {text-decoration: underline;}}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:checked + label::selection {background-color: rgba(var(--color-background), 0.3);}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:disabled + label, .product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"].disabled + label {border-color: rgba(var(--color-foreground), 0.1);color: rgba(var(--color-foreground), 0.6);text-decoration: line-through;}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"].disabled:checked + label, .product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:disabled:checked + label {color: rgba(var(--color-background), 0.6);}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:focus-visible + label {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.5rem rgba(var(--color-foreground), 0.55);}.product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"].focused + label, .no-js .shopify-payment-button__button [role="button"]:focus + label {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.5rem rgba(var(--color-foreground), 0.55);}.no-js .product-form__input:not(.product-form__input--quantity-breaks) input[type="radio"]:focus:not(:focus-visible) + label {box-shadow: none;}.product-form__input .select {max-width: 25rem;}.no-js .product-form__submit.button--secondary {--color-button: var(--color-base-accent-1);--color-button-text: var(--color-base-solid-button-labels);--alpha-button-background: 1;font-family: sans-serif !important;font-weight: 400 !important;}.product-form__submit[aria-disabled="true"] + .shopify-payment-button .shopify-payment-button__button[disabled], .product-form__submit[disabled] + .shopify-payment-button .shopify-payment-button__button[disabled] {cursor: not-allowed;opacity: 0.5;}@media screen and (forced-colors: active) {.product-form__submit[aria-disabled="true"] {color: Window;}}.shopify-payment-button__more-options {color: rgb(var(--color-foreground));}.product-form__submit {font-size: 1.9rem;}.product-form__buttons > * + * {margin-top: 1rem;}.product-form__buttons--uppercase .product-form__submit, .product-form__buttons--uppercase .shopify-payment-button__button {text-transform: uppercase;font-family: sans-serif !important;font-weight: 400 !important;}.shopify-payment-button__button {letter-spacing: 0.1rem;font-size: 1.9rem;padding-top: 0;padding-bottom: 0;}.product__info-container > * {margin-top: var(--margin-top);margin-bottom: var(--margin-bottom);}.product-form__quantity-and-btn {display: flex;align-items: center;column-gap: 1rem;}.product-form__quantity-and-btn--right {flex-direction: row-reverse;}.product-form__quantity-and-btn .product-form__quantity {flex-basis: auto;}.product-form__quantity-and-btn .quantity__label {display: none;}.product-form__quantity-and-btn--stretch-quantity .quantity {min-height: calc(4.5rem + var(--buttons-border-width) * 2);}.product-form__quantity-and-btn--shrink-atc .product-form__quantity + .product-form__submit {min-height: calc((var(--inputs-border-width) * 2) + 4.5rem);}@media screen and (max-width: 749px) {.product-form__quantity-and-btn--shrink-atc .product-form__quantity + .product-form__submit {min-height: calc((var(--inputs-border-width) * 2) + 4rem);}}.product__info-container > *:first-child {margin-top: 0;}.product__info-container > *:last-child, .main-product__info-container > *:nth-last-child(2) {margin-bottom: 0;}.product__info-container iframe {max-width: 100%;}.product__text-container {--font-size: var(--mobile-text-size);--icon-scale: 1.2;font-size: var(--font-size);border: solid var(--border-size) var(--border-color);width: 100%;}.product__text-container--fit-content {width: fit-content;}.product__text-container.side-margins-negative {width: auto;}.product__text-container--fit-content.product__text-container--center {margin-left: auto;margin-right: auto;}.product__text-container--fit-content.product__text-container--right {margin-left: auto;}.product__text-container--background {background: var(--bg-color);border-radius: var(--corner-radius);padding: var(--padding) calc(var(--padding) * 2);}.product__text-container--multiple {display: grid;grid-template-columns: repeat(var(--item-count), 1fr);column-gap: var(--column-gap);row-gap: var(--row-gap);}.product__text-container--multiple.product__text-container--fit-content {display: flex;}.product__text-container--multiple.product__text-container--vertical {display: flex;flex-direction: column;align-items: stretch;}a.product__text {display: block;text-decoration: none;color: rgba(var(--color-foreground), 0.9);}.product__text {display: flex;align-items: center;text-align: left;justify-content: flex-start;color: var(--text-color);font-size: inherit;line-height: 1.3;margin: 0;}.product__text-center {text-align: center;justify-content: center;}.product__text-right {text-align: right;justify-content: flex-end;}.product__text img {height: 1em;width: auto;}.product__text .material-icon, .product__text img {font-size: calc(1em * var(--icon-scale));margin-right: 0.25em;color: var(--icon-color);flex-shrink: 0;}.product__title {word-break: break-word;}.product__title > * {margin: 0;}.product__title > a {display: none;}.product__accordion .accordion__content {padding-left: 1rem;padding-right: 1rem;}.product .price dl {margin-top: 0.5rem;margin-bottom: 0.5rem;}.product .price--sold-out .price__badge-sold-out {background: transparent;color: rgb(var(--color-base-text));border-color: transparent;}.product .price--sold-out .price__badge-sale, .product .price--sold-out .price__badge-custom {display: none;}.price__badge-custom .material-icon {font-size: var(--icon-size);}.price__badge-custom img {height: var(--icon-size);width: auto;}@media screen and (min-width: 750px) {.product__info-container {max-width: 60rem;}.product__info-container .price--on-sale .price-item--regular {font-size: 1.6rem;}.product__text-container {--font-size: var(--desktop-text-size);}}@media screen and (max-width: 749px) {.product__text-container--fit-content.product__text-container--mobile-left {margin-left: 0;}.product__text-container--fit-content.product__text-container--mobile-center {margin-left: auto;margin-right: auto;}.product__text-container--fit-content.product__text-container--mobile-right {margin-left: auto;}.product__text-mobile-left {text-align: left;justify-content: flex-start;}.product__text-mobile-center {text-align: center;justify-content: center;}.product__text-mobile-right {text-align: right;justify-content: flex-end;}}.product__description-title {font-weight: 600;}.product--no-media .product__title, .product--no-media .product__text, .product--no-media noscript .product-form__input, .product--no-media .product__sku, .product--no-media shopify-payment-terms {text-align: center;}.product--no-media .product__media-wrapper, .product--no-media .product__info-wrapper {padding: 0;}.product--no-media noscript .product-form__input, .product--no-media .share-button {max-width: 100%;}.product--no-media fieldset.product-form__input, .product--no-media .product-form__quantity, .product--no-media .product-form__input--dropdown, .product--no-media .share-button, .product--no-media .product__view-details, .product--no-media .product__pickup-availabilities, .product--no-media .product-form {display: flex;align-items: center;justify-content: center;text-align: center;}.product--no-media .product-form {flex-direction: column;}.product--no-media .product-form > .form {max-width: 30rem;width: 100%;}.product--no-media .product-form__quantity, .product--no-media .product-form__input--dropdown {flex-direction: column;max-width: 100%;}.product-form__quantity .form__label {margin-bottom: 0.6rem;}.product-form__quantity-top .form__label {margin-bottom: 1.2rem;}.product--no-media fieldset.product-form__input {flex-wrap: wrap;margin: 0 auto 1.2rem auto;}.product--no-media .product__info-container > modal-opener {display: block;text-align: center;}.product--no-media .product-popup-modal__button {padding-right: 0;}.product--no-media .price {text-align: center;}.urgency-text {text-align: center;padding: 0 0.5em;border: solid 0.2rem rgb(var(--color-background));color: rgb(var(--color-background));background: none;width: fit-content;margin-left: auto;margin-right: auto;}.product-info__image-block {display: flex;justify-content: var(--image-alignment);}.product-info__image-block .media {width: var(--image-width);}.product-info__image-block img, .product-info__image-block video {border-radius: var(--border-radius);}@media screen and (max-width: 749px) {.product-info__image-block--mobile-alignment {justify-content: var(--mobile-image-alignment);}.product-info__image-block--mobile-full {margin-left: -1.5rem;margin-right: -1.5rem;}.product__media-list {margin-left: -2.5rem;margin-bottom: 3rem;width: calc(100% + 4rem);}.product__media-wrapper slider-component:not(.thumbnail-slider--no-slide) {margin-left: -1.5rem;margin-right: -1.5rem;}.slider.product__media-list::-webkit-scrollbar {height: 0.2rem;width: 0.2rem;}.product__media-list::-webkit-scrollbar-thumb {background-color: rgb(var(--color-foreground));}.product__media-list::-webkit-scrollbar-track {background-color: rgba(var(--color-foreground), 0.2);}.product__media-list .product__media-item {width: calc(100% - 3rem - var(--grid-mobile-horizontal-spacing));}.product--mobile-columns .product__media-item {width: calc(50% - 1.5rem - var(--grid-mobile-horizontal-spacing));}}@media screen and (min-width: 750px) {.product--thumbnail .product__media-list, .product--thumbnail_slider .product__media-list {padding-bottom: calc( var(--media-shadow-vertical-offset) * var(--media-shadow-visible) );}.product__media-list {padding-right: calc( var(--media-shadow-horizontal-offset) * var(--media-shadow-visible) );}.product--thumbnail .product__media-item:not(.is-active), .product--thumbnail_slider .product__media-item:not(.is-active) {display: none;}.product-media-modal__content > .product__media-item--variant.product__media-item--variant {display: none;}.product-media-modal__content > .product__media-item--variant:first-child {display: block;}}.product__media-item.product__media-item--variant {display: none;}.product__media-item--variant:first-child {display: block;}@media screen and (min-width: 750px) and (max-width: 989px) {.product__media-list .product__media-item:first-child {padding-left: 0;}.product--thumbnail_slider .product__media-list {margin-left: 0;}.product__media-list .product__media-item {width: 100%;}}.product__media-icon .icon {width: 1.2rem;height: 1.4rem;}.product__media-icon, .thumbnail__badge {background-color: rgb(var(--color-background));border-radius: 50%;border: 0.1rem solid rgba(var(--color-foreground), 0.1);color: rgb(var(--color-foreground));display: flex;align-items: center;justify-content: center;height: 3rem;width: 3rem;position: absolute;left: 1rem;top: 1rem;z-index: 1;transition: color var(--duration-short) ease, opacity var(--duration-short) ease;}.product__media-video .product__media-icon {opacity: 1;}.product__modal-opener--image .product__media-toggle:hover {cursor: zoom-in;}.product__modal-opener:hover .product__media-icon {border: 0.1rem solid rgba(var(--color-foreground), 0.1);}@media screen and (min-width: 750px) {.grid__item.product__media-item--full {width: 100%;}.product--columns .product__media-item:not(.product__media-item--single):not(:only-child) {max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}.product--large.product--columns .product__media-item--full .deferred-media__poster-button {height: 5rem;width: 5rem;}.product--medium.product--columns .product__media-item--full .deferred-media__poster-button {height: 4.2rem;width: 4.2rem;}.product--medium.product--columns .product__media-item--full .deferred-media__poster-button .icon {width: 1.8rem;height: 1.8rem;}.product--small.product--columns .product__media-item--full .deferred-media__poster-button {height: 3.6rem;width: 3.6rem;}.product--small.product--columns .product__media-item--full .deferred-media__poster-button .icon {width: 1.6rem;height: 1.6rem;}}@media screen and (min-width: 990px) {.product--stacked .product__media-item {max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}.product:not(.product--columns) .product__media-list .product__media-item:first-child, .product:not(.product--columns) .product__media-list .product__media-item--full {width: 100%;max-width: 100%;}.product__modal-opener .product__media-icon {opacity: 0;}.product__modal-opener:hover .product__media-icon, .product__modal-opener:focus .product__media-icon {opacity: 1;}}.product__media-item > * {display: block;position: relative;}.product__media-toggle {display: flex;border: none;background-color: transparent;color: currentColor;padding: 0;}.product__media-toggle::after {content: "";cursor: pointer;display: block;margin: 0;padding: 0;position: absolute;top: calc(var(--border-width) * -1);right: calc(var(--border-width) * -1);bottom: calc(var(--border-width) * -1);left: calc(var(--border-width) * -1);z-index: 2;}.product__media-toggle:focus-visible {outline: 0;box-shadow: none;}@media (forced-colors: active) {.product__media-toggle:focus-visible, .product__media-toggle:focus-visible:after, .product-form__input input[type="radio"]:focus-visible + label {outline: transparent solid 1px;outline-offset: 2px;}}.product__media-toggle.focused {outline: 0;box-shadow: none;}.product__media-toggle:focus-visible:after {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);border-radius: var(--media-radius);}.product__media-toggle.focused:after {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);border-radius: var(--media-radius);}.product-media-modal {background-color: rgb(var(--color-background));height: 100%;position: fixed;top: 0;left: 0;width: 100%;visibility: hidden;opacity: 0;z-index: -1;}.product-media-modal[open] {visibility: visible;opacity: 1;z-index: 101;}.product-media-modal__dialog {display: flex;align-items: center;height: 100vh;}.product-media-modal__content {max-height: 100vh;width: 100%;overflow: auto;}.product-media-modal__content > *:not(.active), .product__media-list .deferred-media {display: none;}@media screen and (min-width: 750px) {.product-media-modal__content {padding-bottom: 2rem;}.product-media-modal__content > *:not(.active) {display: block;}.product__modal-opener:not(.product__modal-opener--image) {display: none;}.product__media-list .deferred-media {display: block;}}@media screen and (max-width: 749px) {.product--thumbnail .is-active .product__modal-opener:not(.product__modal-opener--image), .product--thumbnail_slider .is-active .product__modal-opener:not(.product__modal-opener--image) {display: none;}.product--thumbnail .is-active .deferred-media, .product--thumbnail_slider .is-active .deferred-media {display: block;width: 100%;}}.product-media-modal__content > * {display: block;height: auto;margin: auto;}.product-media-modal__content .media {background: none;}.product-media-modal__model {width: 100%;}.product-media-modal__toggle {background-color: rgb(var(--color-background));border: 0.1rem solid rgba(var(--color-foreground), 0.1);border-radius: 50%;color: rgba(var(--color-foreground), 0.55);display: flex;align-items: center;justify-content: center;cursor: pointer;right: 2rem;padding: 1.2rem;position: fixed;z-index: 2;top: 2rem;width: 4rem;}.product-media-modal__content .deferred-media {width: 100%;}@media screen and (min-width: 750px) {.product-media-modal__content {padding: 2rem 11rem;}.product-media-modal__content > * {width: 100%;}.product-media-modal__content > * + * {margin-top: 2rem;}.product-media-modal__toggle {right: 5rem;top: 2.2rem;}}@media screen and (min-width: 990px) {.product-media-modal__content {padding: 2rem 11rem;}.product-media-modal__content > * + * {margin-top: 1.5rem;}.product-media-modal__content {padding-bottom: 1.5rem;}.product-media-modal__toggle {right: 5rem;}}.product-media-modal__toggle:hover {color: rgba(var(--color-foreground), 0.75);}.product-media-modal__toggle .icon {height: auto;margin: 0;width: 2.2rem;}.product-popup-modal {box-sizing: border-box;opacity: 0;position: fixed;visibility: hidden;z-index: -1;margin: 0 auto;top: 0;left: 0;overflow: auto;width: 100%;background: rgba(var(--color-foreground), 0.2);height: 100%;transition: all 0.15s;}.product-popup-modal[open] {opacity: 1;visibility: visible;z-index: 101;}.product-popup-modal__content {border-radius: var(--popup-corner-radius);background-color: rgb(var(--color-background));overflow: auto;height: 80%;margin: 0 auto;left: 50%;transform: translateX(-50%);margin-top: 5rem;width: 92%;position: absolute;top: 0;padding: 0 1.5rem 0 3rem;border-color: rgba(var(--color-foreground), var(--popup-border-opacity));border-style: solid;border-width: var(--popup-border-width);box-shadow: var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius) rgba(var(--color-shadow), var(--popup-shadow-opacity));}.product-popup-modal__content--centered {top: 50%;margin-top: 0;transform: translate(-50%, -50%);height: auto;max-height: 90vh;padding-bottom: 2rem;}.product-popup-modal__content--centered .product-popup-modal__content-info {margin-top: -2rem;}.product-popup-modal__content.focused {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3), var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius) rgba(var(--color-shadow), var(--popup-shadow-opacity));}.product-popup-modal__content:focus-visible {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3), var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius) rgba(var(--color-shadow), var(--popup-shadow-opacity));}@media screen and (min-width: 750px) {.product-popup-modal__content {padding-right: 1.5rem;margin-top: 10rem;width: 70%;padding: 0 3rem;}.product-media-modal__dialog .global-media-settings--no-shadow {overflow: visible !important;}}@media screen and (max-width: 749px) {.product-popup-modal__content {border-radius: calc(var(--popup-corner-radius) * 0.8);}.product-popup-modal__content:not(.product-popup-modal__content) table {display: block;max-width: fit-content;overflow-x: auto;white-space: nowrap;margin: 0;}.product-media-modal__dialog .global-media-settings, .product-media-modal__dialog .global-media-settings video, .product-media-modal__dialog .global-media-settings model-viewer, .product-media-modal__dialog .global-media-settings iframe, .product-media-modal__dialog .global-media-settings img {border: none;border-radius: 0;}.product-popup-modal__content--centered {padding-bottom: 1.5rem;}}.product-popup-modal__opener {display: block;}.product-popup-modal__button {font-size: 1.6rem;padding-right: 1.3rem;padding-left: 0;min-height: 2.5rem;text-underline-offset: 0.3rem;text-decoration-thickness: 0.1rem;transition: text-decoration-thickness var(--duration-short) ease;}.product-popup-modal__button:hover {text-decoration-thickness: 0.2rem;}.product-popup-modal__content-info {padding-right: 4.4rem;}.product-popup-modal__content-info > * {height: auto;margin: 0 auto;max-width: 100%;width: 100%;}@media screen and (max-width: 749px) {.product-popup-modal__content-info > * {max-height: 100%;}}.product-popup-modal__toggle {background-color: rgb(var(--color-background));border: 0.1rem solid rgba(var(--color-foreground), 0.1);border-radius: 50%;color: rgba(var(--color-foreground), 0.55);display: flex;align-items: center;justify-content: center;cursor: pointer;position: sticky;padding: 1.2rem;z-index: 2;top: 1.5rem;width: 4rem;margin: 0 0 0 auto;}.product-popup-modal__toggle:hover {color: rgba(var(--color-foreground), 0.75);}.product-popup-modal__toggle .icon {height: auto;margin: 0;width: 2.2rem;}.product-popup-modal__toggle--minimalistic {border: none;transform: translate(1.2rem, -1.2rem);color: rgba(var(--color-foreground), 0.75);}.product-popup-modal__toggle--minimalistic:hover {color: rgba(var(--color-foreground), 0.95);}.product__media-list .media > * {overflow: hidden;}.thumbnail-list {flex-wrap: wrap;grid-gap: 1rem;}.slider--mobile.thumbnail-list:after {content: none;}@media screen and (min-width: 750px) {.product--stacked .thumbnail-list {display: none;}.thumbnail-list {display: grid;grid-template-columns: repeat(4, 1fr);}}.thumbnail-list_item--variant:not(:first-child) {display: none;}@media screen and (min-width: 990px) {.thumbnail-list {grid-template-columns: repeat(4, 1fr);}.product--medium .thumbnail-list {grid-template-columns: repeat(5, 1fr);}.product--large .thumbnail-list {grid-template-columns: repeat(6, 1fr);}}@media screen and (max-width: 749px) {.product__media-item {display: flex;align-items: center;}.product__modal-opener {width: 100%;}.thumbnail-slider {display: flex;align-items: center;}.thumbnail-slider .thumbnail-list.slider {display: flex;padding: 0.75rem 0.1rem;column-gap: 0.75rem;flex: 1;scroll-padding-left: 0.5rem;}.thumbnail-list__item.slider__slide {width: calc((100% / var(--mobile-thumbnails)) - 0.5rem);}.thumbnail-slider .slider-button {width: 2.5rem;}}@media screen and (min-width: 750px) {.product--thumbnail_slider .thumbnail-slider {display: flex;align-items: center;padding-top: 0.5rem;}.thumbnail-slider .thumbnail-list.slider--tablet-up {display: flex;padding: 0.5rem;flex: 1;scroll-padding-left: 0.5rem;}.product__media-wrapper .slider-mobile-gutter .slider-button {display: none;}.thumbnail-list.slider--tablet-up .thumbnail-list__item.slider__slide {width: calc((100% / (var(--desktop-thumbnails) - 1)) - 0.8rem);}.product--thumbnail_slider .slider-mobile-gutter .slider-button {display: flex;}}@media screen and (min-width: 900px) {.thumbnail-list.slider--tablet-up .thumbnail-list__item.slider__slide {width: calc((100% / var(--desktop-thumbnails)) - 0.8rem);}}.thumbnail {position: absolute;top: 0;left: 0;display: block;height: 100%;width: 100%;padding: 0;color: rgb(var(--color-base-text));cursor: pointer;background-color: transparent;}.thumbnail:hover {opacity: 0.7;}.thumbnail.global-media-settings img {border-radius: 0;}.thumbnail[aria-current] {box-shadow: 0 0 0rem 0.1rem rgb(var(--color-foreground));border-color: rgb(var(--color-foreground));}.image-magnify-full-size {cursor: zoom-out;z-index: 1;margin: 0;border-radius: calc(var(--media-radius) - var(--media-border-width));}.image-magnify-hover {cursor: zoom-in;}.product__modal-opener--image .product__media-zoom-none, .product__media-icon--none {display: none;}@media (hover: hover) {.product__media-zoom-hover, .product__media-icon--hover {display: none;}}@media screen and (max-width: 749px) {.product__media-zoom-hover, .product__media-icon--hover {display: flex;}}.js .product__media {overflow: hidden !important;}.thumbnail[aria-current]:focus-visible {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);}.thumbnail[aria-current]:focus, .thumbnail.focused {outline: 0;box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);}@media (forced-colors: active) {.thumbnail[aria-current]:focus, .thumbnail.focused {outline: transparent solid 1px;}}.thumbnail[aria-current]:focus:not(:focus-visible) {outline: 0;box-shadow: 0 0 0 0.1rem rgb(var(--color-foreground));}.thumbnail img {object-fit: cover;width: 100%;height: 100%;pointer-events: none;}.thumbnail__badge .icon {width: 1rem;height: 1rem;}.thumbnail__badge .icon-3d-model {width: 1.2rem;height: 1.2rem;}.thumbnail__badge {color: rgb(var(--color-foreground), 0.6);height: 2rem;width: 2rem;left: auto;right: calc(0.4rem + var(--media-border-width));top: calc(0.4rem + var(--media-border-width));}@media screen and (min-width: 750px) {.product:not(.product--small) .thumbnail__badge {height: 3rem;width: 3rem;}.product:not(.product--small) .thumbnail__badge .icon {width: 1.2rem;height: 1.2rem;}.product:not(.product--small) .thumbnail__badge .icon-3d-model {width: 1.4rem;height: 1.4rem;}}.thumbnail-list__item {position: relative;}.thumbnail-list__item::before {content: "";display: block;padding-bottom: 100%;}.product:not(.featured-product) .product__view-details {display: none;}.product__view-details {display: block;text-decoration: none;}.product__view-details:hover {text-decoration: underline;text-underline-offset: 0.3rem;}.product__view-details .icon {width: 1.2rem;margin-left: 1.2rem;flex-shrink: 0;}.product__inventory {display: flex;align-items: center;gap: 0.5rem;}.product--no-media .product__inventory {justify-content: center;}.product__inventory.visibility-hidden:empty {display: block;}.product__inventory.visibility-hidden:empty::after {content: "#";}.icon-with-text {--icon-size: var(--desktop-icon-size);--icon-spacing: var(--desktop-spacing);--text-size: var(--desktop-text-size);}.icon-with-text--horizontal {display: flex;justify-content: center;column-gap: 3rem;flex-direction: row;}.icon-with-text--horizontal .material-icon, .icon-with-text--horizontal img {margin-bottom: var(--icon-spacing);}.icon-with-text--vertical .material-icon, .icon-with-text--vertical img {margin-right: var(--icon-spacing);}.icon-with-text img {height: var(--icon-size);width: auto;}.icon-with-text .material-icon {font-size: var(--icon-size);}.icon-with-text .h4 {text-align: center;font-size: var(--text-size);}.icon-with-text__item {display: flex;align-items: center;}.icon-with-text--horizontal .icon-with-text__item {flex-direction: column;width: 33%;}.icon-with-text--vertical .icon-with-text__item {margin-bottom: calc(var(--icon-size) / 3);}@media screen and (max-width: 750px) {.icon-with-text {--icon-size: var(--mobile-icon-size);--icon-spacing: var(--mobile-spacing);--text-size: var(--mobile-text-size);}}@media screen and (max-width: 500px) {.icon-with-text--horizontal {column-gap: 1.5rem;}}.product__sku.visibility-hidden::after {content: "#";}.product-media-container {--aspect-ratio: var(--preview-ratio);--ratio-percent: calc(1 / var(--aspect-ratio) * 100%);position: relative;width: 100%;max-width: calc(100% - calc(var(--media-border-width) * 2));}.product-media-container .media {padding-top: var(--ratio-percent);}@media screen and (min-width: 750px) {.product-media-container.constrain-height {--viewport-offset: 400px;--constrained-min-height: 300px;--constrained-height: max( var(--constrained-min-height), calc(100vh - var(--viewport-offset)) );margin-right: auto;margin-left: auto;}.product-media-container.constrain-height.media-fit-contain {--contained-width: calc(var(--constrained-height) * var(--aspect-ratio));width: min(var(--contained-width), 100%);}.product-media-container.constrain-height .media {padding-top: min(var(--constrained-height), var(--ratio-percent));}}@media screen and (max-width: 749px) {.product-media-container.media-fit-cover {display: flex;align-self: stretch;}.product-media-container.media-fit-cover .media {position: initial;}}@media screen and (min-width: 750px) {.product-media-container {max-width: 100%;}.product-media-container:not(.media-type-image) {--aspect-ratio: var(--ratio);}.product-media-container.constrain-height {--viewport-offset: 170px;--constrained-min-height: 500px;}.product-media-container.media-fit-cover, .product-media-container.media-fit-cover .product__modal-opener, .product-media-container.media-fit-cover .media {height: 100%;}.product-media-container.media-fit-cover .deferred-media__poster img {object-fit: cover;width: 100%;}}.product-media-container .product__modal-opener {display: block;position: relative;}media-gallery .slider-buttons {width: 100%;}.slider__dots {height: auto;min-height: 3rem;flex-wrap: wrap;max-width: 100%;padding: 0 0.3rem;}media-gallery .slider-counter__link {padding: calc(var(--pagination-dot-spacing) / 2);}media-gallery .slider-counter__link--dots .dot {width: var(--pagination-dot-width);height: var(--pagination-dot-height);border-radius: var(--pagination-dot-radius);border: none;padding: 0;display: block;opacity: 0.5;margin: 0.1rem;}media-gallery .slider-counter__link--active.slider-counter__link--dots .dot {width: calc(var(--pagination-dot-width) * var(--pagination-dot-active-scale));height: calc( var(--pagination-dot-height) * var(--pagination-dot-active-scale) );border-radius: calc( var(--pagination-dot-radius) * var(--pagination-dot-active-scale) );margin: 0;opacity: 1;background: rgb(var(--color-background));}@media screen and (max-width: 749px) {.product__info-wrapper--top-padding {padding-top: 1.5rem;}media-gallery .slider.slider--mobile .slider__slide {padding-top: 0;padding-bottom: 0;}media-gallery .slider-mobile-buttons-overlay {position: absolute;left: 50%;transform: translateX(-50%);bottom: 0;}media-gallery .slider-mobile-buttons-overlay.slider__dots {width: 100%;}media-gallery .slider-buttons {column-gap: 1rem;padding: 0.5rem;}media-gallery .slider-buttons.splide--transparent-arrows {column-gap: 0.5rem;}media-gallery .slider-buttons.slider--mobile-arrows-pagination:not( .splide--transparent-arrows, .slider-mobile-buttons-overlay ) {padding: 1rem 0;}media-gallery .slider-mobile-buttons-overlay-container, media-gallery .slider-buttons--no-pagination {padding: 0;}media-gallery .slider-buttons.slider-mobile-buttons-overlay.slider--mobile-arrows-pagination.splide--transparent-arrows {padding: 0.2rem 0;}media-gallery .slider--mobile-arrows-pagination .splide__arrow {position: static;transform: none;}media-gallery .slider.slider--mobile {scroll-padding-left: var(--mobile-scroll-padding);}media-gallery .slider.slider--mobile .grid__item {width: calc( var(--slide-container-percentage-width) - (var(--mobile-scroll-padding) * 2) - (var(--grid-mobile-horizontal-spacing) * 2) );}media-gallery .slider.slider--mobile .grid__item .product-media-container {width: var(--slide-inner-percentage-width);margin-left: auto;margin-right: auto;}.slider--mobile.product__media-list--outer-spacing .grid__item:first-of-type {margin-left: var(--mobile-scroll-padding);}.slider--mobile.product__media-list--outer-spacing::after {margin-left: var(--mobile-scroll-padding);}.slider--mobile.product__media-list--no-outer-spacing .grid__item:first-of-type {margin-left: 0;}.slider--mobile.product__media-list--no-outer-spacing::after {display: none;}}@media screen and (min-width: 750px) {.product-media-container .product__modal-opener:not(.product__modal-opener--image) {display: none;}media-gallery .product__media-list + .slider-buttons {display: flex;}}.custom-product-field {display: block;}.product__info-container .form__label {margin-bottom: 0;}.product-form__label-container {margin-bottom: 0.3rem;display: flex;align-items: center;column-gap: 1.5rem;}.product-form__input--pills .product-form__label-container {margin-bottom: -0.3rem;}.product-form__label-container .product-popup-modal__opener {flex-grow: 1;}.product-form__label-container .sizing-chart__button {min-height: auto;line-height: calc(1 + 0.5 / var(--font-body-scale));}.small-field__field {max-width: 25rem;}.input--small {outline: none;background-color: rgb(var(--color-background));color: rgb(var(--color-foreground));padding: 0.5rem 1rem;height: 3.8rem;font-size: 16px;width: 100%;border: solid var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-border-opacity));}.input--small:focus-visible {box-shadow: none;outline: none;}.input--small:focus {border-color: rgb(var(--color-foreground));border-width: 2px;}.input--error, .input--error:focus {border-color: red;border-width: 2px;}.input-error-msg {font-size: 1.5rem;color: red;display: none;line-height: 1;margin-top: 0.3rem;}.input--error + .input-error-msg {display: block;}.product-form__input .select__select {height: 3.5rem;padding: 0 calc(var(--inputs-border-width) + 1.5rem) 0 1.25rem;}textarea {resize: none;}.textarea--small {height: 7rem;}.textarea--medium {height: 9rem;}.textarea--large {height: 11rem;}@media screen and (max-width: 749px) {.input--small, .product-form__input .select::after, .product-form__input .select:hover::after, .product-form__input .select, .product-form__input .select__select, .product__info-container .quantity::after, .product__info-container .quantity:hover::after, .product__info-container .quantity {border-radius: calc((var(--inputs-radius) / 5) * 4);}.main-quantity {--inputs-radius-inner: calc((var(--inputs-radius) / 5) * 4);}.product-form__input .select::before, .product__info-container .quantity::before {border-radius: calc( ((var(--inputs-radius) / 5) * 4) + var(--inputs-border-width) );}}@media screen and (min-width: 750px) {.input--small, .product-form__input .select__select {height: 4rem;padding: 0.5rem 1.2rem;}.input--small {border-radius: var(--inputs-radius);}.textarea--small {height: 8rem;}.textarea--medium {height: 10rem;}.textarea--large {height: 12rem;}}.product-form__input--full, .product-form__input--full .select {max-width: none;}.shipping-checkpoints {position: relative;display: grid;grid-template-columns: repeat(var(--item-count), 1fr);justify-items: center;align-items: flex-start;column-gap: 1rem;--icon-size: 4rem;z-index: 0;}.shipping-checkpoints__bar {position: absolute;top: calc(var(--icon-size) / 2);left: 50%;transform: translate(-50%, -50%);width: calc(100% / var(--item-count) * var(--item-count-sub));height: 0.2rem;z-index: -1;display: block;}.shipping-checkpoint {display: flex;flex-direction: column;align-items: center;text-align: center;}.shipping-checkpoint__icon {width: var(--icon-size);height: var(--icon-size);border-radius: 50%;margin-bottom: 1rem;}.shipping-checkpoint__icon span {font-size: calc(var(--icon-size) / 2);}.shipping-checkpoint__icon img {width: calc(var(--icon-size) / 2);height: calc(var(--icon-size) / 2);object-fit: contain;}.shipping-checkpoint p {margin: 0;font-size: 1rem;line-height: 1.2;letter-spacing: 0;}.shipping-checkpoint p + p {margin-top: 0.5rem;}@media screen and (max-width: 500px) {.shipping-checkpoints--has-4 {--icon-size: 3.4rem;column-gap: 1rem;}.shipping-checkpoints--has-4 .shipping-checkpoint p {font-size: 0.8rem;}}@media screen and (min-width: 1000px) {.shipping-checkpoints {--icon-size: 5rem;}.shipping-checkpoint p {font-size: 1.5rem;}.shipping-checkpoints__bar {height: 0.3rem;}}.quantity-gifts {display: grid;grid-template-columns: repeat(var(--item-count), 1fr);justify-items: center;column-gap: 0.75rem;}.quantity-gift {max-width: 12rem;width: 100%;position: relative;}.quantity-gift__container {display: flex;align-items: center;justify-content: center;background: repeating-linear-gradient( -45deg, rgba(var(--color-base-text), 0.1) 0px, rgba(var(--color-base-text), 0.1) 6px, rgba(var(--color-base-text), 0.15) 6px, rgba(var(--color-base-text), 0.15) 8px );border: solid 2px rgba(var(--color-base-text), 0.75);border-radius: 1rem;padding: 10% 0;aspect-ratio: 1 / 1;position: relative;transition: all 0.3s;z-index: 0;}.quantity-gift--unlocked .quantity-gift__container {background: var(--unlocked-bg-color);border-color: var(--unlocked-border-color);transition: all 0.3s 0.5s;}.quantity-gift__title {text-align: center;font-size: 1.1rem;margin-bottom: 0;margin-top: 0.75rem;opacity: 0.5;}.quantity-gift--unlocked .quantity-gift__title {opacity: 1;transition: all 0.3s 0.3s;}.quantity-gift__lock {display: flex;align-items: center;justify-content: space-evenly;flex-direction: column;position: absolute;bottom: 0;left: 0;width: 100%;height: 100%;z-index: 5;background: none;}.quantity-gift--unlocked .quantity-gift__lock {opacity: 0;transition: all 0.3s 0.2s;}.quantity-gift__lock__top, .quantity-gift__lock__bottom {margin: 0;font-size: 1.2rem;text-align: center;display: block;line-height: 1;color: rgb(var(--color-foreground));min-height: 1.2rem;}.lock {--lock-color: rgba(var(--color-foreground), 0.75);width: 2.4rem;height: 2.1rem;border: 0.3rem solid var(--lock-color);border-radius: 0.5rem;transition: all 0.1s ease-in-out;position: relative;margin-top: 0.6rem;}.lock:after {content: "";display: block;background: var(--lock-color);width: 0.3rem;height: 0.7rem;position: absolute;top: 50%;left: 50%;margin: -0.35rem 0 0 -0.2rem;transition: all 0.1s ease-in-out;}.lock:before {content: "";display: block;width: 1.4rem;height: 1.2rem;bottom: 100%;position: absolute;left: 50%;transform: translateX(-50%);border: 0.3rem solid var(--lock-color);border-top-right-radius: 50%;border-top-left-radius: 50%;border-bottom: 0;transition: all 0.1s ease-in-out;}.quantity-gift--unlocked .lock {transform: rotate(10deg);}.quantity-gift--unlocked .lock:before {bottom: 130%;left: 31%;margin-left: -1.15rem;transform: rotate(-45deg);}.quantity-gift__image {width: 50%;height: 50%;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -25%);z-index: 1;opacity: 0;}.quantity-gift__image img {width: 100%;height: 100%;object-fit: contain;object-position: center center;vertical-align: bottom;border-radius: 0.35rem;}.quantity-gift .gift-box {position: relative;width: 50%;z-index: 2;transform: translateY(25%);transform-origin: center bottom;opacity: 0;}.quantity-gift .gift-box-body {position: relative;z-index: 0;width: 100%;aspect-ratio: 1 / 1;background: rgb(var(--color-background));border-bottom-left-radius: 5%;border-bottom-right-radius: 5%;box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.3);}.quantity-gift--unlocked .quantity-gift__image {animation: image-1 2s 1.2s forwards ease-in-out;opacity: 1;transition: all 0.5s 0.95s;}.quantity-gift--unlocked .gift-box {animation: box-disappear 1.5s 1.6s forwards ease-in-out;opacity: 1;transition: all 0.5s 0.95s;}.quantity-gift--unlocked .gift-box-body {animation: box-body 1s 1.2s forwards ease-in-out;}.quantity-gift--unlocked .gift-box-body .gift-box-lid {animation: box-lid 1s 1.2s forwards ease-in-out;}.quantity-gift--unlocked .gift-box-body .gift-box-bowtie::before {animation: box-bowtie-left 1.1s 1.2s forwards ease-in-out;}.quantity-gift--unlocked .gift-box-body .gift-box-bowtie::after {animation: box-bowtie-right 1.1s 1.2s forwards ease-in-out;}.quantity-gift .gift-box-body::before {content: "";position: absolute;top: 0;left: 0;width: 100%;height: 100%;background: linear-gradient( 0deg, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.1) 100% );z-index: 2;}.quantity-gift .gift-box-body::after {content: "";position: absolute;top: 0;bottom: 0;left: 50%;transform: translateX(-50%);width: 20%;background: var(--gift-box-tie-color);z-index: 1;}.quantity-gift .gift-box-lid {position: absolute;z-index: 3;left: 50%;transform: translateX(-50%);bottom: 90%;background: rgb(var(--color-background));width: 110%;aspect-ratio: 11 / 2;border-radius: 5%;box-shadow: 0 8px 4px -4px rgba(0, 0, 0, 0.3);}.quantity-gift .gift-box-lid::after {content: "";position: absolute;top: 0;bottom: 0;left: 50%;transform: translateX(-50%);width: 20%;background: var(--gift-box-tie-color);}.quantity-gift .gift-box-bowtie {z-index: 1;height: 100%;display: block;}.quantity-gift .gift-box-bowtie::before, .quantity-gift .gift-box-bowtie::after {content: "";width: 40%;aspect-ratio: 1 / 1;border: 0.6rem solid var(--gift-box-tie-color);border-radius: 50% 50% 0 50%;position: absolute;bottom: 99%;z-index: -1;}.quantity-gift .gift-box-bowtie::before {left: 50%;transform: translateX(-100%) skew(10deg, 10deg);}.quantity-gift .gift-box-bowtie::after {left: 50%;transform: translateX(0%) rotate(90deg) skew(10deg, 10deg);}@keyframes box-disappear {0% {transform: translateY(25%);opacity: 1;}20% {transform: translateY(48%);opacity: 1;}80% {transform: translateY(48%);opacity: 1;}100% {transform: translateY(48%) scale(0.2);opacity: 0;}}@keyframes box-lid {0%, 42% {transform: translate3d(-50%, 0%, 0) rotate(0deg);}60% {transform: translate3d(-85%, -230%, 0) rotate(-25deg);}90%, 100% {transform: translate3d(-119%, 225%, 0) rotate(-70deg);}}@keyframes box-body {0% {transform: translate3d(0%, 0%, 0) rotate(0deg);}25% {transform: translate3d(0%, 25%, 0) rotate(20deg);}50% {transform: translate3d(0%, -15%, 0) rotate(0deg);}70% {transform: translate3d(0%, 0%, 0) rotate(0deg);}}@keyframes image-1 {0% {transform: translate(-50%, -25%) translate3d(0%, 0%, 0) rotate(0deg);}12.5% {transform: translate(-50%, -25%) translate3d(0%, 25%, 0) rotate(20deg);}25% {transform: translate(-50%, -25%) translate3d(0%, -15%, 0) rotate(0deg);}35% {transform: translate(-50%, -25%) translate3d(0%, 0%, 0) rotate(0deg);}45% {transform: translate(-50%, -100%);}85% {transform: translate(-50%, -100%);}92% {transform: translate(-50%, -50%) scale(1.75);}100% {transform: translate(-50%, -50%) scale(1.75);}}@keyframes box-bowtie-right {0%, 50%, 75% {transform: translateX(0%) rotate(90deg) skew(10deg, 10deg);}90%, 100% {transform: translate(-50%, -15%) rotate(45deg) skew(10deg, 10deg);box-shadow: 0px 4px 8px -4px rgba(0, 0, 0, 0.3);}}@keyframes box-bowtie-left {0% {transform: translateX(-100%) rotate(0deg) skew(10deg, 10deg);}50%, 75% {transform: translate(-50%, -15%) rotate(45deg) skew(10deg, 10deg);}90%, 100% {transform: translateX(-100%) rotate(0deg) skew(10deg, 10deg);}}@media screen and (max-width: 500px) {.quantity-gifts-4 {column-gap: 0.6rem;}.quantity-gifts-4 .quantity-gift__lock .lock {transform: scale(0.7);}.quantity-gifts-4 .quantity-gift__lock__top {font-size: 1rem;margin-top: 0.4rem;}.quantity-gifts-4 .quantity-gift__lock__bottom {font-size: 1rem;margin-bottom: 0.4rem;}.quantity-gifts-4 .quantity-gift__title {font-size: 0.9rem;margin-top: 0.5rem;}}.sizing-chart.product-popup-modal__opener {display: flex;}.sizing-chart__button {column-gap: 0.3em;padding: 0;font-size: var(--font-size);}.sizing-chart__button--underline .sizing-chart__button__text {text-decoration: underline;}.sizing-chart__button img {width: 1.1em;height: 1.1em;object-fit: contain;object-position: center center;}.sizing-chart__button .material-icon {font-size: 1.3em;}.sizing-chart-table {width: 100%;border-spacing: 0;border: solid 1px rgb(var(--color-foreground));}.sizing-chart-table th, .sizing-chart-table td {padding: 0.4em;}.sizing-chart-table thead th:not(:last-child) {border-right: solid 2px rgba(var(--color-background), 1);}.sizing-chart-table tbody tr:nth-child(2n) {background: rgba(var(--color-foreground), 0.03);}.sizing-chart-table tbody tr:nth-child(2n + 1) td:not(:last-child) {border-right: solid 2px rgba(var(--color-foreground), 0.03);}.sizing-chart__caption {font-size: var(--text-size);color: var(--text-color);line-height: 1.3;}.sizing-chart-table + .sizing-chart__caption {margin-top: 0.5em;}.sizing-chart__modal img {width: 100%;}.sizing-chart__modal {padding: 0;}.sizing-chart__modal > * + * {margin-top: 2rem;}.sizing-chart__modal-container {padding: 0.5rem 2rem 2rem;}@media screen and (max-width: 749px) {.sizing-chart-table {font-size: 1.2rem;}.sizing-chart__caption {font-size: calc(var(--text-size) * 0.75);}.sizing-chart__modal > * + * {margin-top: 1.5rem;}.sizing-chart__modal-container {padding: 0 1.2rem 1.5rem;}.sizing-chart__modal-containe .product-popup-modal__toggle {top: 1.2rem;}}.rating-stars-and-text {justify-content: var(--alignment);}.rating-stars__container {display: flex;}.rating-stars__container svg {width: 1em;height: 1em;margin-left: 0.125em;flex-shrink: 0;}.rating-stars__container .trustpilot-stars-svg {width: auto;height: 1.25em;}.rating-stars__container svg:first-of-type {margin-left: 0;}.rating-stars__container--underlay {position: relative;}.rating-stars__container--underlay svg {color: var(--bg-star-color);}.rating-stars__container--overlay {position: absolute;top: 0;left: 0;overflow: hidden;width: calc(100% / 5 * var(--rating));}.rating-stars__container--overlay svg {color: var(--star-color);}.rating-stars a {color: rgb(var(--color-foreground));text-decoration: none;}.rating-stars__label {margin-left: 0.125em;}clickable-discount {display: flex;justify-content: var(--alignment);}.clickable-discount__btn {width: fit-content;padding: 0;}clickable-discount[data-applied="true"] .clickable-discount__btn__text-1, clickable-discount[data-applied="false"] .clickable-discount__btn__text-2, clickable-discount[data-loading="false"] .spinner, clickable-discount[data-error="false"] + .clickable-discount__error {display: none;}.clickable-discount__btn .spinner {width: 1em;margin-left: 0.4em;}.clickable-discount__error {margin: 0;font-size: 1.2rem;line-height: 1.5;}.quantity-breaks {--badge-font-size: 1.5rem;--badge-line-height: 1.6;--badge-border-radius: 0.2em;--label-font-size: 1.6rem;--benefit-font-size: 1rem;--benefit-border-radius: 0.6em;--caption-font-size: 1.4rem;--price-font-size: 1.6rem;--compare-price-font-size: 1.4rem;--border-width: 0.2rem;--border-radius: 1rem;--column-gap: 0.75rem;--row-gap: 0.85rem;}.quantity-breaks-container {position: relative;display: grid;align-items: flex-start;grid-template-columns: repeat(1, 1fr);column-gap: var(--column-gap);row-gap: var(--row-gap);}.quantity-breaks__title {margin: 0;font-size: 1.4rem;line-height: 1;column-gap: 10px;margin-bottom: 1.3rem;}.quantity-breaks__title span:nth-of-type(1), .quantity-breaks__title span:nth-of-type(3) {display: block;height: 2px;flex-grow: 1;background: rgb(var(--accent-color));}.quantity-breaks-container input {opacity: 0;width: 0;height: 0;cursor: pointer;position: absolute;}.quantity-break {padding: 1.75rem;cursor: pointer;border-radius: var(--border-radius);background: rgba(var(--accent-color), 0.02);border: solid var(--border-width) rgba(var(--accent-color), 0.3);position: relative;}.quantity-breaks--normal.quantity-breaks--show-indicator .quantity-break {padding-left: 4rem;}.quantity-breaks-container input:checked + .quantity-break {background: rgba(var(--accent-color), 0.1);border: solid var(--border-width) rgb(var(--accent-color));}@media (hover: hover) {.quantity-break:hover {background: rgba(var(--accent-color), 0.15);border: solid 2px rgb(var(--accent-color));}}.quantity-breaks--show-indicator .quantity-break::before {content: "";display: block;width: 1.5rem;height: 1.5rem;position: absolute;left: 2rem;top: 50%;transform: translate(-50%, -50%);border-radius: 50%;border: solid 0.2rem rgba(var(--accent-color), 0.3);}.quantity-breaks--show-indicator .quantity-breaks-container input:checked + .quantity-break::before {border: solid 0.2rem rgb(var(--color-background));background: rgb(var(--accent-color));box-shadow: 0 0 0 0.1rem rgb(var(--accent-color));}.quantity-breaks-container input.disabled + .quantity-break {opacity: 0.3;}.quantity-break__badge {position: absolute;font-weight: var(--font-body-weight-bold);font-size: var(--badge-font-size);line-height: var(--badge-line-height);top: -1rem;right: -1rem;transform: rotate(3deg);margin: 0;padding: 0 0.7em;border-radius: var(--badge-border-radius);}.quantity-breaks--normal .quantity-break--badge-style-2 .quantity-break__badge {transform: translateY(calc(-50% - (var(--border-width) / 2)));top: 0;right: 1.5rem;}.quantity-breaks--normal .quantity-break--badge-style-3 .quantity-break__badge {transform: translateY(calc(-100% - var(--border-width)));top: 0;right: 1.5rem;border-radius: var(--badge-border-radius) var(--badge-border-radius) 0 0;--badge-font-size: 1.4rem;}.quantity-breaks--normal .quantity-break--badge-style-3.quantity-break--badge {margin-top: 1.6rem;}.quantity-break__image-and-content {display: flex;align-items: center;column-gap: 1rem;}.quantity-breaks--normal .quantity-break__image {width: calc(var(--image-width) / 3.5);max-width: 6rem;}.quantity-break__content {display: flex;justify-content: space-between;align-items: center;flex-grow: 1;}.quantity-break__left > span, .quantity-break__right > span {display: block;}.quantity-break__left {display: flex;flex-direction: column;align-items: flex-start;row-gap: 0.5rem;text-align: left;}.quantity-break__left .quantity-break__label {font-size: var(--label-font-size);font-weight: 700;line-height: 1;display: flex;align-items: center;column-gap: 0.5rem;row-gap: 0.5rem;}.quantity-breaks--normal .quantity-break--benefit-bottom .quantity-break__label, .quantity-breaks--compact .quantity-break--benefit-top .quantity-break__label {flex-direction: column;align-items: flex-start;}.quantity-break__label-text {line-height: 1;}.quantity-break__benefit {display: inline-block;font-size: var(--benefit-font-size);background: rgb(var(--color-background));color: rgb(var(--accent-color));line-height: 1.9;padding: 0 0.75em;font-weight: 700;border: solid 1px rgb(var(--accent-color));border-radius: var(--benefit-border-radius);white-space: nowrap;}.quantity-break__benefit--solid {background: rgb(var(--accent-color));color: rgb(var(--color-background));}.quantity-break__caption {font-size: var(--caption-font-size);line-height: 1;}.quantity-break__right {text-align: right;display: flex;flex-direction: column;align-items: flex-end;row-gap: 0.5rem;column-gap: 0.5rem;}.quantity-break__price {line-height: 1;font-size: var(--price-font-size);font-weight: 700;color: rgb(var(--accent-color));}.quantity-break__compare-price {font-size: var(--compare-price-font-size);text-decoration: line-through;line-height: 1;}.quantity-break__caption span {display: inline;}.quantity-break__variants {height: 0;overflow: hidden;padding-top: 0;}.quantity-breaks-container input:checked + .quantity-break .quantity-break__variants {height: auto;padding-top: 1rem;}.quantity-break__variants__label {font-size: 1.3rem;line-height: 1.3;display: block;margin-bottom: 0.3rem;}.quantity-break__selector-item__number {font-weight: 700;line-height: 1.5rem;}.quantity-break__selector-item {display: flex;align-items: center;flex-wrap: wrap;row-gap: 0.5rem;column-gap: 0.5rem;}.quantity-break__selector-item:not(:last-of-type) {margin-bottom: 0.75rem;}.quantity-break__selector-item .select {width: fit-content;}.quantity-breaks-full-width-pickers .select {flex-grow: 1;}.quantity-break__selector-item .select__select {height: 2.1rem;}.variant-breaks-fieldset {margin: 0;padding: 0;border: none;}.variant-breaks label {display: block;}.quantity-breaks--compact {--badge-font-size: 1.1rem;--benefit-font-size: 1.1rem;--price-font-size: 1.2rem;--compare-price-font-size: 0.9rem;}.quantity-breaks--compact .quantity-breaks__title {margin-bottom: 0.9rem;}.quantity-breaks--compact .quantity-breaks-container {grid-template-columns: repeat(2, 1fr);}.quantity-breaks--compact .quantity-break {padding: 1.25rem 1.5rem;}.quantity-breaks--compact.quantity-breaks--show-indicator .quantity-break {padding-left: 3.6rem;}.quantity-breaks--compact.quantity-breaks--show-indicator .quantity-break::before {left: 1.8rem;width: 1.4rem;height: 1.4rem;}.quantity-breaks--compact .quantity-break__image {display: none;}.quantity-breaks--compact .quantity-break__content {flex-direction: column;align-items: flex-start;row-gap: 0.2rem;}.quantity-breaks--compact span, .quantity-breaks--vertical span {word-break: break-word;}.quantity-breaks--compact .quantity-break__left {align-items: center;text-align: center;margin-bottom: 0.3rem;margin-bottom: 0;}.quantity-breaks--compact .quantity-break__right {display: block;text-align: center;line-height: 1.1;}.quantity-breaks--compact .quantity-break__caption {display: none;}.quantity-breaks--compact .quantity-break__right span {display: inline;}.quantity-breaks--compact .quantity-break__badge, .quantity-breaks--vertical .quantity-break--badge-style-2 .quantity-break__badge, .quantity-breaks--compact .quantity-break--benefit-bottom .quantity-break__benefit, .quantity-breaks--vertical .quantity-break--benefit-bottom .quantity-break__benefit {top: calc(var(--border-width) / -2);left: 50%;right: auto;transform: translate(-50%, -50%);text-align: center;white-space: nowrap;}.quantity-breaks--vertical .quantity-break--benefit-bottom .quantity-break__benefit, .quantity-breaks--compact .quantity-break--benefit-bottom .quantity-break__benefit {top: calc(100% + (var(--border-width) / 2));position: absolute;}.quantity-breaks--vertical {--badge-font-size: 1rem;--badge-line-height: 2;--label-font-size: 1.6rem;--benefit-font-size: 1rem;--caption-font-size: 1rem;--price-font-size: 1.4rem;--compare-price-font-size: 1.1rem;--side-padding: 1rem;--y-padding: 1.5rem;--row-gap: 1rem;}.quantity-breaks--vertical .quantity-breaks-container {grid-template-columns: repeat(min(var(--items-count), 4), 1fr);align-items: flex-start;}.quantity-breaks--vertical .quantity-break {padding: 0 var(--side-padding) var(--y-padding);text-align: center;margin-bottom: 0;display: flex;}.quantity-breaks--vertical .quantity-break, .quantity-breaks--vertical .quantity-break__image-and-content, .quantity-breaks--vertical .quantity-break__content, .quantity-breaks--vertical .quantity-break__label {flex-direction: column;justify-content: center;}.quantity-breaks--vertical .quantity-break__label {row-gap: 0.7rem;}.quantity-breaks--vertical.quantity-breaks--vertical-image-bottom .quantity-break__image-and-content {flex-direction: column-reverse;}.quantity-breaks--vertical.quantity-breaks--has-badge .quantity-break--no-badge.quantity-break--badge-style-1 {margin-top: calc(var(--badge-font-size) * var(--badge-line-height));}.quantity-breaks--vertical .quantity-break--badge-style-1 .quantity-break__badge {position: relative;top: 0;left: 0;margin-top: calc(var(--border-width) * -1);margin-left: calc((var(--border-width) + var(--side-padding)) * -1);margin-right: calc((var(--border-width) + var(--side-padding)) * -1);transform: none;border-radius: var(--border-radius) var(--border-radius) 0 0;}.quantity-breaks--vertical .quantity-break--badge-style-3 .quantity-break__badge {right: -0.7rem;}.quantity-breaks--vertical .quantity-break__image {margin-left: calc(var(--side-padding) * -1);margin-right: calc(var(--side-padding) * -1);}.quantity-breaks--vertical .quantity-break__image img {width: var(--image-width);margin: 0 auto;}.quantity-breaks--vertical .quantity-break::before {display: none;}.quantity-breaks--vertical .quantity-break__content {row-gap: 1rem;margin-top: var(--y-padding);}.quantity-breaks--space-images .quantity-break__image {margin-top: var(--y-padding);}.quantity-breaks--vertical.quantity-breaks--vertical-image-bottom .quantity-break__image {margin-top: 1rem;}.quantity-breaks--vertical .quantity-break__left, .quantity-breaks--vertical .quantity-break__right {text-align: center;align-items: center;row-gap: 0.3rem;}.quantity-breaks--vertical.quantity-breaks--vertical-prices-horizontal .quantity-break__right {flex-direction: row;}.quantity-breaks--vertical .quantity-break__left {row-gap: 0.7rem;}.quantity-breaks--vertical[data-items="2"], .quantity-breaks--vertical[data-items="4"], .quantity-breaks--vertical[data-items="5"] {--badge-font-size: 1.3rem;--badge-line-height: 1.9;}@media screen and (max-width: 999px) {.quantity-breaks--vertical .quantity-breaks__title {margin-bottom: 1rem;}.quantity-breaks--vertical[data-items="4"] .quantity-breaks-container, .quantity-breaks--vertical[data-items="5"] .quantity-breaks-container {grid-template-columns: repeat(2, 1fr);}.quantity-breaks--vertical[data-items="6"] .quantity-breaks-container {grid-template-columns: repeat(3, 1fr);}.quantity-breaks--vertical[data-items="3"], .quantity-breaks--vertical[data-items="6"] {--label-font-size: 1.3rem;--benefit-font-size: 0.8rem;--caption-font-size: 0.9rem;--price-font-size: 1.3rem;--compare-price-font-size: 1rem;}.quantity-breaks--vertical[data-items="2"] .quantity-break--badge-style-2, .quantity-breaks--vertical[data-items="2"] .quantity-break--badge-style-3, .quantity-breaks--vertical[data-items="4"] .quantity-break--badge-style-2, .quantity-breaks--vertical[data-items="4"] .quantity-break--badge-style-3 {--badge-font-size: 1.1rem;}.quantity-breaks--vertical[data-items="3"] .quantity-break--badge-style-2, .quantity-breaks--vertical[data-items="3"] .quantity-break--badge-style-3 {--badge-font-size: 0.8rem;}}@media screen and (min-width: 1000px) {.quantity-breaks--vertical[data-items="2"] {--badge-font-size: 1.8rem;--badge-line-height: 2;--label-font-size: 2rem;--benefit-font-size: 1.2rem;--caption-font-size: 1.6rem;--price-font-size: 1.8rem;--compare-price-font-size: 1.4rem;--side-padding: 1.2rem;--y-padding: 1.8rem;}.quantity-breaks--vertical[data-items="3"] {--badge-font-size: 1.4rem;--badge-line-height: 2;--label-font-size: 1.8rem;--benefit-font-size: 1rem;--caption-font-size: 1.3rem;--price-font-size: 1.4rem;--compare-price-font-size: 1.2rem;--y-padding: 1.7rem;}.quantity-breaks--vertical[data-items="4"] {--badge-font-size: 1.2rem;--badge-line-height: 1.9;--label-font-size: 1.4rem;--benefit-font-size: 0.8rem;--caption-font-size: 1rem;--price-font-size: 1.3rem;--compare-price-font-size: 1rem;}.quantity-breaks--vertical[data-items="4"] .quantity-break--badge-style-2, .quantity-breaks--vertical[data-items="4"] .quantity-break--badge-style-3 {--badge-font-size: 1rem;}.quantity-breaks--vertical[data-items="3"] .quantity-break--badge-style-2, .quantity-breaks--vertical[data-items="3"] .quantity-break--badge-style-3 {--badge-font-size: 1.1rem;}.quantity-breaks--vertical[data-items="2"] .quantity-break--badge-style-2, .quantity-breaks--vertical[data-items="2"] .quantity-break--badge-style-3 {--badge-font-size: 1.3rem;}}.sticky-atc {position: fixed;bottom: 0;left: 0;width: 100%;margin: 0;padding: 1rem 0 1.5rem;z-index: 2;box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.2);}.sticky-atc-container {display: flex;justify-content: space-between;align-items: center;column-gap: 1rem;}.sticky-atc__button {flex-shrink: 0;background: rgb(var(--color-background));border-radius: var(--buttons-radius-outset);}.sticky-atc .button {padding: 0 1rem;min-height: auto;min-width: auto;line-height: 2.75em;font-size: 1.5rem;}.sticky-atc__title {margin: 0;font-size: 1.5rem;}.sticky-atc__price .price {column-gap: 0.5rem;margin: 0;}.sticky-atc__price .price, .sticky-atc__price .price-item--regular {font-size: 1.3rem;}.sticky-atc__price .badge {font-size: 0.8rem;}.sticky-atc.color-accent-1 .badge, .sticky-atc.color-accent-2 .badge {background: rgb(var(--color-foreground));color: rgb(var(--color-background));}.sticky-atc .price__regular .price-item--regular {font-size: 2rem;line-height: 1.1;}.sticky-atc__left {display: flex;justify-content: space-between;align-items: center;flex-grow: 1;}.sticky-atc__image {flex-shrink: 0;}.sticky-atc__image img {width: auto;height: 4.2rem;}.sticky-atc__left__content > * {margin: 0;}secondary-variant-select {display: block;}.sticky-atc__picker--separate {display: flex;column-gap: 1rem;row-gap: 0.4rem;}.sticky-atc__picker--separate .product-form__input {flex: 1 1 auto;max-width: calc((100% / var(--options-count)) - 0.5rem);}.sticky-atc .product-form__input {margin: 0;}@media screen and (min-width: 400px) {.sticky-atc__price .price, .sticky-atc__price .price-item--regular {font-size: 1.6rem;}.sticky-atc__title {font-size: 1.7rem;}.sticky-atc__price .badge {font-size: 1rem;}.sticky-atc .button {font-size: 1.7rem;padding: 0 1.1em;}}@media screen and (min-width: 500px) {.sticky-atc__price .badge {font-size: 1.2rem;}}@media screen and (min-width: 750px) {.sticky-atc {padding: 1rem 0;}.sticky-atc-container {column-gap: 1.5rem;}.sticky-atc__left {padding-right: 1rem;}.sticky-atc .select {width: 20rem;}.sticky-atc__image img {height: 5.2rem;}.sticky-atc__price .price, .sticky-atc__price .price-item--regular {font-size: 1.8rem;}.sticky-atc .price__regular .price-item--regular {font-size: 2.2rem;}.sticky-atc__title {font-size: 1.9rem;}.sticky-atc__price .badge {font-size: 1.3rem;}.sticky-atc .button {font-size: 1.9rem;padding: 0 1.5em;}.sticky-atc--desktop-no-badge .badge.price__badge-sale {display: none;}.sticky-atc--desktop-btn-full {flex-direction: column;align-items: stretch;}.sticky-atc--desktop-btn-full .button, .sticky-atc--desktop-btn-full .sticky-atc__button, .sticky-atc--desktop-transparent .button, .sticky-atc--desktop-transparent .sticky-atc__button {width: 100%;}.sticky-atc--desktop-btn-full .sticky-atc__left {margin-bottom: 1rem;}.sticky-atc--desktop-transparent {background: none;box-shadow: none;padding-top: 0;}.sticky-atc--desktop-transparent .sticky-atc__left {display: none;}.sticky-atc.sticky-atc--mobile-transparent .button {box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.3);}}@media screen and (max-width: 749px) {.sticky-atc--mobile-no-badge .badge.price__badge-sale {display: none;}.sticky-atc--mobile-btn-full {flex-direction: column;align-items: stretch;}.sticky-atc--mobile-btn-full .sticky-atc__button, .sticky-atc--mobile-transparent .sticky-atc__button {width: 100%;}.sticky-atc--mobile-btn-full .button, .sticky-atc--mobile-transparent .button {font-size: 1.9rem;padding: 0 1.5em;width: 100%;line-height: calc(1 + 0.2 / var(--font-body-scale));min-height: calc(4.5rem + var(--buttons-border-width) * 2);}.sticky-atc--mobile-btn-full .sticky-atc__left {margin-bottom: 0.7rem;}.sticky-atc--mobile-transparent {background: none;box-shadow: none;padding-top: 0;}.sticky-atc--mobile-transparent .sticky-atc__left {display: none;}.sticky-atc.sticky-atc--mobile-transparent .button {box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.3);}.sticky-atc__left {flex-direction: column;align-items: flex-start;}.sticky-atc--small-mobile-select .select {--inputs-radius: var(--pickers-small-radius);}.sticky-atc secondary-variant-select {width: 100%;padding-right: 0.5rem;}.sticky-atc--small-mobile-select secondary-variant-select {padding-top: 0.5rem;max-width: 15rem;}.sticky-atc--small-mobile-select .select__select {font-size: 1.2rem;height: 2.1rem;padding: 0 2.5rem 0 1rem;}.sticky-atc--small-mobile-select .select__select .icon-caret {right: calc(var(--inputs-border-width) + 1rem);}.sticky-atc:not(.sticky-atc--small-mobile-select) .select__select {height: 4rem;}}.sticky-atc[data-after-scroll="true"] {transform: translateY(100%);transition: transform 0.15s ease-in-out;}.review-item {display: flex;}.review-item--padding {padding: 0.8rem 1.2rem 1rem;}.review-item--middle {align-items: center;}.review-item__image {width: 5rem;height: 5rem;flex-shrink: 0;margin-right: 1.5rem;}.review-item__right {flex-grow: 1;}.review-item__text p {margin-top: 0;line-height: 1.3;font-size: 1.4rem;color: rgb(var(--color-foreground));}.review-item__text p:last-of-type {margin-bottom: 0;}.review-item__author {display: flex;align-items: center;flex-wrap: wrap;column-gap: 0.3em;margin: 0.5rem 0 0;padding-top: 0.6rem;line-height: 1.3;color: rgba(var(--color-foreground), 0.5);font-size: 1.3rem;border-top: solid 1px rgba(var(--color-foreground), 0.06);}.review-item__author .verified-icon {width: 1em;height: 1em;color: var(--checkmark-color);}.review-items-container .slideshow__slide {flex-basis: 100%;}.review-items-container .slideshow__controls {border: none;}.review-items-container .slider-button {width: 2rem;height: 2rem;}.review-items-container .slider-button svg {height: 0.7rem;}.review-items-container .slider-counter__link--dots .dot {width: 0.7rem;height: 0.7rem;}.review-items-container .slider-counter__link {padding: 0.5rem;}.review-items-container .slider-counter--dots {margin: 0 0.2rem;}.featured-product .product__media-list {width: 100%;margin: 0;padding-bottom: 0;}.featured-product .product-media-container {margin-bottom: var(--media-shadow-vertical-offset);max-width: 100%;}.featured-product .product__media-item {padding-left: 0;width: 100%;}.featured-product .product__media-item:not(:first-child) {display: none;}.background-secondary .featured-product {padding: 2.5rem;}.featured-product .share-button:nth-last-child(2) {display: inline-flex;}.share-button + .product__view-details {display: inline-flex;float: right;align-items: center;}.share-button + .product__view-details::after {content: "";clear: both;display: table;}@media screen and (min-width: 750px) {.featured-product .product__media-item {padding-bottom: 0;}.background-secondary .featured-product {padding: 5rem;}}@media screen and (min-width: 990px) {.background-secondary .featured-product:not(.product--no-media) > .product__info-wrapper {padding: 0 0 0 5rem;}.background-secondary .featured-product:not(.product--no-media).product--right > .product__info-wrapper {padding: 0 5rem 0 0;}.featured-product:not(.product--no-media) > .product__info-wrapper {padding: 0 7rem;}.background-secondary .featured-product {padding: 6rem 7rem;position: relative;z-index: 1;}}@media screen and (max-width: 749px) {.featured-product .product__media-list--full-mobile-width {width: calc(100% + 3rem);margin-left: -1.5rem;margin-right: -1.5rem;}}.related-products {display: block;}.related-products__heading {margin: 0 0 3rem;}.vertical-ticker__inner {overflow: hidden;-webkit-mask-image: linear-gradient( 0deg, transparent 0, #000 20%, #000 70%, transparent );mask-image: linear-gradient( 180deg, transparent 0, #000 20%, #000 70%, transparent );}.vertical-ticker__container {display: flex;flex-direction: column;justify-content: flex-end;align-items: center;text-align: center;}.vertical-ticker__item {margin: 0;color: rgb(var(--color-foreground));}.vertical-ticker__item--italic {font-style: italic;}.vertical-ticker__item--uppercase {text-transform: uppercase;}.vertical-ticker__item--bold {font-weight: 700;}@keyframes vertTicker {to {transform: translateY(-50%);}}.vertical-ticker-and-content {display: grid;grid-template-columns: repeat(1, 1fr);}.vertical-ticker-content {display: grid;place-items: center;position: relative;}.vertical-ticker-content__bg, .vertical-ticker-content__bg img {position: absolute;top: 0;left: 0;width: 100%;height: 100%;z-index: 0;}.vertical-ticker-content__bg img {object-fit: cover;object-position: center center;z-index: -2;}.vertical-ticker-content .page-width {position: relative;z-index: 1;}@media screen and (min-width: 900px) {.vertical-ticker-and-content--ticker-first .vertical-ticker {order: -1;}.vertical-ticker-and-content {grid-template-columns: repeat(2, 1fr);}.vertical-ticker-content {padding-top: 36px;}}@media screen and (max-width: 900px) {.vertical-ticker-and-content--content-desktop-only .vertical-ticker-content {display: none;}}.horizontal-ticker {overflow-x: hidden;max-width: 100%;}.horizontal-ticker__inner {overflow: hidden;}.horizontal-ticker__container {display: flex;align-items: center;width: fit-content;}.horizontal-ticker__item {margin: 0;color: rgb(var(--color-foreground));line-height: 1;white-space: nowrap;}.horizontal-ticker__item--italic {font-style: italic;}.horizontal-ticker__item--uppercase {text-transform: uppercase;}.horizontal-ticker__item--bold {font-weight: 700;}@keyframes horTicker {to {transform: translateX(-50%);}}.image-with-text .grid {margin-bottom: 0;}.image-with-text__grid {align-items: stretch;}.image-with-text .grid__item {position: relative;}@media screen and (min-width: 750px) {.image-with-text__grid--reverse {flex-direction: row-reverse;}}.image-with-text__media {min-height: 100%;overflow: visible;}.image-with-text__media--small {height: 19.4rem;}.image-with-text__media--medium {height: 29.6rem;}.image-with-text__media--large {height: 43.5rem;}@media screen and (min-width: 750px) {.image-with-text__media--small {height: 31.4rem;}.image-with-text__media--medium {height: 46rem;}.image-with-text__media--large {height: 69.5rem;}}.image-with-text__media--placeholder {position: relative;overflow: hidden;}.image-with-text__media--placeholder:after {content: "";position: absolute;width: 100%;height: 100%;background: rgba(var(--color-foreground), 0.04);}.image-with-text__media--placeholder.image-with-text__media--adapt {height: 20rem;}@media screen and (min-width: 750px) {.image-with-text__media--placeholder.image-with-text__media--adapt {height: 30rem;}}.image-with-text__media--placeholder > svg {position: absolute;left: 50%;max-width: 80rem;top: 50%;transform: translate(-50%, -50%);width: 100%;fill: currentColor;}.image-with-text__content {align-items: flex-start;display: flex;justify-content: center;align-self: center;flex-direction: column;height: 100%;padding: 3rem calc(3rem / var(--font-body-scale)) 3rem;position: relative;z-index: 1;}.image-with-text .grid__item::after {content: "";position: absolute;top: 0;left: 0;width: 100%;height: 100%;z-index: -1;}.image-with-text:not(.image-with-text--overlap) .image-with-text__media-item:after {border-radius: var(--media-radius);}.image-with-text:not(.image-with-text--overlap) .image-with-text__text-item:after {border-radius: var(--text-boxes-radius);box-shadow: var(--text-boxes-shadow-horizontal-offset) var(--text-boxes-shadow-vertical-offset) var(--text-boxes-shadow-blur-radius) rgba(var(--color-shadow), var(--text-boxes-shadow-opacity));}.image-with-text .image-with-text__media-item > * {border-radius: var(--media-radius);overflow: hidden;}.image-with-text .global-media-settings {overflow: hidden !important;}.image-with-text .image-with-text__text-item > * {border-radius: var(--text-boxes-radius);overflow: hidden;box-shadow: var(--text-boxes-shadow-horizontal-offset) var(--text-boxes-shadow-vertical-offset) var(--text-boxes-shadow-blur-radius) rgba(var(--color-shadow), var(--text-boxes-shadow-opacity));}.image-with-text:not(.image-with-text--overlap) .image-with-text__media-item > *, .image-with-text:not(.image-with-text--overlap) .image-with-text__text-item > * {box-shadow: none;}@media screen and (max-width: 749px) {.image-with-text--mobile-reverse .image-with-text__grid {flex-direction: column-reverse;}.image-with-text__media-item.image-with-text__media--mobile-full {margin: 0 -1.5rem;max-width: calc(100% + 3rem);width: calc(100% + 3rem);--media-radius: 0;}.image-with-text.image-with-text--mobile-normal.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__media-item:after, .image-with-text.image-with-text--mobile-normal.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid__item .image-with-text__media, .image-with-text.image-with-text--mobile-normal.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__media img, .image-with-text.image-with-text--mobile-normal.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__media video, .image-with-text.image-with-text--mobile-reverse.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__text-item:after, .image-with-text.image-with-text--mobile-reverse.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid__item .image-with-text__content {border-bottom-right-radius: 0;border-bottom-left-radius: 0;}.image-with-text.image-with-text--mobile-reverse.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__media-item:after, .image-with-text.image-with-text--mobile-reverse.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid__item .image-with-text__media, .image-with-text.image-with-text--mobile-reverse.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__media img, .image-with-text.image-with-text--mobile-reverse.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__media video, .image-with-text.image-with-text--mobile-normal.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__text-item:after, .image-with-text.image-with-text--mobile-normal.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid__item .image-with-text__content {border-top-left-radius: 0;border-top-right-radius: 0;}.image-with-text.collapse-borders:not(.image-with-text--overlap) .image-with-text__content {border-top: 0;}}.image-with-text__content--mobile-right > * {align-self: flex-end;text-align: right;}.image-with-text__content--mobile-center > * {align-self: center;text-align: center;}.image-with-text--overlap .image-with-text__content {width: 90%;margin: -3rem auto 0;}@media screen and (min-width: 750px) {.image-with-text__grid--reverse .image-with-text__content {margin-left: auto;}.image-with-text__content--bottom {justify-content: flex-end;align-self: flex-end;}.image-with-text__content--top {justify-content: flex-start;align-self: flex-start;}.image-with-text__content--desktop-right > * {align-self: flex-end;text-align: right;}.image-with-text__content--desktop-left > * {align-self: flex-start;text-align: left;}.image-with-text__content--desktop-center > * {align-self: center;text-align: center;}.image-with-text--overlap .image-with-text__text-item {display: flex;padding: 3rem 0;}.image-with-text--overlap .image-with-text__content {height: auto;width: calc(100% + 4rem);min-width: calc(100% + 4rem);margin-top: 0;margin-left: -4rem;}.image-with-text--overlap .image-with-text__grid--reverse .image-with-text__content {margin-left: 0;margin-right: -4rem;}.image-with-text--overlap .image-with-text__grid--reverse .image-with-text__text-item {justify-content: flex-end;}.image-with-text__media-item--top {align-self: flex-start;}.image-with-text__media-item--middle {align-self: center;}.image-with-text__media-item--bottom {align-self: flex-end;}.image-with-text__media-item--small, .image-with-text__media-item--large + .image-with-text__text-item {flex-grow: 0;}.image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid:not(.image-with-text__grid--reverse) .image-with-text__media-item:after, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid:not(.image-with-text__grid--reverse) .image-with-text__media, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid:not(.image-with-text__grid--reverse) .image-with-text__media img, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid:not(.image-with-text__grid--reverse) .image-with-text__media video, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__grid--reverse .image-with-text__text-item:after, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__grid--reverse .image-with-text__content, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__grid--reverse .image-with-text__content:after {border-top-right-radius: 0;border-bottom-right-radius: 0;}.image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid:not(.image-with-text__grid--reverse) .image-with-text__text-item:after, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid:not(.image-with-text__grid--reverse) .image-with-text__content, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .grid:not(.image-with-text__grid--reverse) .image-with-text__content:after, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__grid--reverse .image-with-text__media-item:after, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__grid--reverse .image-with-text__media, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__grid--reverse .image-with-text__media img, .image-with-text.different-colors.collapse-corners:not( .image-with-text--overlap ) .image-with-text__grid--reverse .image-with-text__media video {border-top-left-radius: 0;border-bottom-left-radius: 0;}.image-with-text.collapse-borders:not(.image-with-text--overlap) .grid:not(.image-with-text__grid--reverse) .image-with-text__content {border-left: 0;}.image-with-text.collapse-borders:not(.image-with-text--overlap) .image-with-text__grid--reverse .image-with-text__content {border-right: 0;}}.image-with-text:not(.collapse-corners, .image-with-text--overlap) .image-with-text__media-item {z-index: 2;}.image-with-text__content {border-radius: var(--text-boxes-radius);box-shadow: var(--text-boxes-shadow-horizontal-offset) var(--text-boxes-shadow-vertical-offset) var(--text-boxes-shadow-blur-radius) rgba(var(--color-shadow), var(--text-boxes-shadow-opacity));word-break: break-word;}@media screen and (min-width: 990px) {.image-with-text__content {padding: 6rem 7rem 7rem;}}.image-with-text__content > * + * {margin-top: 2rem;}.image-with-text__content > .image-with-text__text:empty ~ a {margin-top: 2rem;}.image-with-text__content > :first-child:is(.image-with-text__heading), .image-with-text__text--caption + .image-with-text__heading, .image-with-text__text--caption:first-child {margin-top: 0;}.image-with-text__content :last-child:is(.image-with-text__heading), .image-with-text__text--caption {margin-bottom: 0;}.image-with-text__content .button + .image-with-text__text {margin-top: 2rem;}.image-with-text__content .image-with-text__text + .button {margin-top: 3rem;}.image-with-text__heading {margin-bottom: 0;}.image-with-text__text p {margin-top: 0;margin-bottom: 1rem;}.image-with-text .product__text-container--multiple.product__text-container--vertical + .product__text-container--multiple.product__text-container--vertical {margin-top: var(--row-gap);}@media screen and (max-width: 749px) {.collapse-padding .image-with-text__grid .image-with-text__content {padding: 2rem 0;}}@media screen and (min-width: 750px) {.collapse-padding:not(.image-with-text--desktop-full-width) .image-with-text__grid:not(.image-with-text__grid--reverse) .image-with-text__content:not(.image-with-text__content--desktop-center) {padding-right: 0;}.collapse-padding:not(.image-with-text--desktop-full-width) .image-with-text__grid--reverse .image-with-text__content:not(.image-with-text__content--desktop-center) {padding-left: 0;}}@supports not (inset: 10px) {.image-with-text .grid {margin-left: 0;}}@media screen and (min-width: 750px) {.image-with-text--desktop-full-width.page-width {max-width: none;padding-left: 0;padding-right: 0;--media-radius: 0px;--text-boxes-radius: 0px;}.image-with-text--desktop-full-width .image-with-text__content {padding-left: 3rem;padding-right: 3rem;}}@media screen and (min-width: 990px) {.image-with-text--desktop-full-width .image-with-text__content {padding-left: 7rem;padding-right: 7rem;}}.multirow__inner {display: flex;flex-direction: column;row-gap: var(--grid-mobile-vertical-spacing);}@media screen and (min-width: 750px) {.multirow__inner {row-gap: var(--grid-desktop-vertical-spacing);}}.banner {display: flex;position: relative;flex-direction: column;z-index: 0;}.banner__box {text-align: center;}@media only screen and (max-width: 749px) {.banner--content-align-mobile-left .banner__box {text-align: left;}.banner--content-align-mobile-right .banner__box {text-align: right;}.banner--content-align-mobile-center .banner__box .rating-stars-and-text {justify-content: center;}.banner--content-align-mobile-right .banner__box .rating-stars-and-text {justify-content: flex-end;}}@media only screen and (min-width: 750px) {.banner--content-align-left .banner__box {text-align: left;}.banner--content-align-right .banner__box {text-align: right;}.banner--content-align-center .banner__box .rating-stars-and-text {justify-content: center;}.banner--content-align-right .banner__box .rating-stars-and-text {justify-content: flex-end;}.banner--content-align-left.banner--desktop-transparent .banner__box, .banner--content-align-right.banner--desktop-transparent .banner__box, .banner--medium.banner--desktop-transparent .banner__box {max-width: 68rem;}}@media screen and (max-width: 749px) {.banner--small.banner--mobile-bottom:not(.banner--adapt) .banner__media, .banner--small.banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt) > .banner__media {height: 28rem;}.banner--medium.banner--mobile-bottom:not(.banner--adapt) .banner__media, .banner--medium.banner--stacked:not(.banner--mobile-bottom):not( .banner--adapt ) > .banner__media {height: 34rem;}.banner--large.banner--mobile-bottom:not(.banner--adapt) .banner__media, .banner--large.banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt) > .banner__media {height: 39rem;}.banner--small:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {min-height: 28rem;}.banner--medium:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {min-height: 34rem;}.banner--large:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {min-height: 39rem;}}@media screen and (min-width: 750px) {.banner {flex-direction: row;}.banner--small:not(.banner--adapt) {min-height: 42rem;}.banner--medium:not(.banner--adapt) {min-height: 56rem;}.banner--large:not(.banner--adapt) {min-height: 72rem;}.banner__content.banner__content--top-left {align-items: flex-start;justify-content: flex-start;}.banner__content.banner__content--top-center {align-items: flex-start;justify-content: center;}.banner__content.banner__content--top-right {align-items: flex-start;justify-content: flex-end;}.banner__content.banner__content--middle-left {align-items: center;justify-content: flex-start;}.banner__content.banner__content--middle-center {align-items: center;justify-content: center;}.banner__content.banner__content--middle-right {align-items: center;justify-content: flex-end;}.banner__content.banner__content--bottom-left {align-items: flex-end;justify-content: flex-start;}.banner__content.banner__content--bottom-center {align-items: flex-end;justify-content: center;}.banner__content.banner__content--bottom-right {align-items: flex-end;justify-content: flex-end;}}@media screen and (max-width: 749px) {.banner:not(.banner--stacked) {flex-direction: row;flex-wrap: wrap;}.banner--stacked {height: auto;}.banner--stacked .banner__media {flex-direction: column;}}.banner__media {height: 100%;position: absolute;left: 0;top: 0;width: 100%;}.banner__media-half {width: 50%;}.banner__media-half + .banner__media-half {right: 0;left: auto;}@media screen and (max-width: 749px) {.banner--stacked .banner__media-half {width: 100%;}.banner--stacked .banner__media-half + .banner__media-half {order: 1;}}@media screen and (min-width: 750px) {.banner__media {height: 100%;}}.banner--adapt, .banner--adapt_image.banner--mobile-bottom .banner__media:not(.placeholder) {height: auto;}@media screen and (max-width: 749px) {.banner--mobile-bottom .banner__media, .banner--stacked:not(.banner--mobile-bottom) .banner__media {position: relative;}.banner--stacked.banner--adapt .banner__content {height: auto;}.banner:not(.banner--mobile-bottom):not(.email-signup-banner) .banner__box {background: transparent;--color-foreground: 255, 255, 255;--color-button: 255, 255, 255;--color-button-text: 0, 0, 0;}.banner:not(.banner--mobile-bottom):not(.email-signup-banner) .banner__box.banner--transparent-black {background: transparent;--color-foreground: 0, 0, 0;--color-button: 0, 0, 0;--color-button-text: 255, 255, 255;}.banner:not(.banner--mobile-bottom) .banner__box {border: none;border-radius: 0;box-shadow: none;}.banner:not(.banner--mobile-bottom) .button--secondary {--color-button: 255, 255, 255;--color-button-text: 255, 255, 255;--alpha-button-background: 0;}.banner:not(.banner--mobile-bottom) .banner__box.banner--transparent-black .button--secondary {--color-button: 0, 0, 0;--color-button-text: 0, 0, 0;}.banner--stacked:not(.banner--mobile-bottom):not(.banner--adapt) .banner__content {position: absolute;height: auto;}.banner--stacked.banner--adapt:not(.banner--mobile-bottom) .banner__content {max-height: 100%;overflow: hidden;position: absolute;}.banner--stacked:not(.banner--adapt) .banner__media {position: relative;}.banner::before {display: none !important;}.banner--stacked .banner__media-image-half {width: 100%;}}.banner__content {padding: 0;display: flex;position: relative;width: 100%;align-items: center;justify-content: center;z-index: 1;}@media screen and (min-width: 750px) {.banner__content {padding: 5rem;}.banner__content--top-left {align-items: flex-start;justify-content: flex-start;}.banner__content--top-center {align-items: flex-start;justify-content: center;}.banner__content--top-right {align-items: flex-start;justify-content: flex-end;}.banner__content--middle-left {align-items: center;justify-content: flex-start;}.banner__content--middle-center {align-items: center;justify-content: center;}.banner__content--middle-right {align-items: center;justify-content: flex-end;}.banner__content--bottom-left {align-items: flex-end;justify-content: flex-start;}.banner__content--bottom-center {align-items: flex-end;justify-content: center;}.banner__content--bottom-right {align-items: flex-end;justify-content: flex-end;}}@media screen and (max-width: 749px) {.banner--mobile-bottom:not(.banner--stacked) .banner__content {order: 2;}.banner:not(.banner--mobile-bottom) .field__input {background-color: transparent;}}.banner__box {padding: 4rem 3.5rem;position: relative;height: fit-content;align-items: center;text-align: center;width: 100%;word-wrap: break-word;z-index: 0;}@media screen and (min-width: 750px) {.banner--desktop-transparent .banner__box {background: transparent;--color-foreground: 255, 255, 255;--color-button: 255, 255, 255;--color-button-text: 0, 0, 0;max-width: 89rem;border: none;border-radius: 0;box-shadow: none;}.banner--desktop-transparent .banner__box.banner--transparent-black {background: transparent;--color-foreground: 0, 0, 0;--color-button: 0, 0, 0;--color-button-text: 255, 255, 255;}.banner--desktop-transparent .button--secondary {--color-button: 255, 255, 255;--color-button-text: 255, 255, 255;--alpha-button-background: 0;}.banner--desktop-transparent .banner__box.banner--transparent-black .button--secondary {--color-button: 0, 0, 0;--color-button-text: 0, 0, 0;}.banner--desktop-transparent .content-container:after {display: none;}}@media screen and (max-width: 749px) {.banner--mobile-bottom::after, .banner--mobile-bottom .banner__media::after {display: none;}}.banner::after, .banner__media::after {content: "";position: absolute;top: 0;background: #000000;opacity: 0;z-index: 0;width: 100%;height: 100%;}.banner__box > * + .banner__text {margin-top: 1.5rem;}@media screen and (min-width: 750px) {.banner__box > * + .banner__text {margin-top: 2rem;}}.banner__box > * + * {margin-top: 1rem;}.banner__box > *:first-child {margin-top: 0;}@media screen and (max-width: 749px) {.banner--stacked .banner__box {width: 100%;}}@media screen and (min-width: 750px) {.banner__box {width: auto;max-width: 71rem;min-width: 45rem;}}@media screen and (min-width: 1400px) {.banner__box {max-width: 90rem;}}.banner__heading {margin-bottom: 0;}.banner__box .banner__heading + *:not(.rating-stars, .trustpilot-stars) {margin-top: 1rem;}.banner__buttons {display: inline-flex;flex-wrap: wrap;gap: 1rem;max-width: 45rem;word-break: break-word;}@media screen and (max-width: 749px) {.banner--content-align-mobile-right .banner__buttons--multiple {justify-content: flex-end;}.banner--content-align-mobile-center .banner__buttons--multiple > * {flex-grow: 1;min-width: 22rem;}}@media screen and (min-width: 750px) {.banner--content-align-center .banner__buttons--multiple > * {flex-grow: 1;min-width: 22rem;}.banner--content-align-right .banner__buttons--multiple {justify-content: flex-end;}}.banner__box > * + .banner__buttons {margin-top: 2rem;}slideshow-component {position: relative;display: flex;flex-direction: column;}@media screen and (max-width: 989px) {.no-js slideshow-component .slider {padding-bottom: 3rem;}}slideshow-component .slideshow.banner {flex-direction: row;flex-wrap: nowrap;margin: 0;gap: 0;}.slideshow__slide {padding: 0;position: relative;display: flex;flex-direction: column;}@media screen and (max-width: 749px) {.slideshow--placeholder.banner--mobile-bottom.banner--adapt_image .slideshow__media, .slideshow--placeholder.banner--adapt_image:not(.banner--mobile-bottom) {height: 28rem;}}@media screen and (min-width: 750px) {.slideshow--placeholder.banner--adapt_image {height: 56rem;}}.slideshow__text.banner__box {display: flex;flex-direction: column;justify-content: center;max-width: 54.5rem;}.slideshow__text > * {max-width: 100%;}@media screen and (max-width: 749px) {slideshow-component.page-width .slideshow__text {border-right: var(--text-boxes-border-width) solid rgba(var(--color-foreground), var(--text-boxes-border-opacity));border-left: var(--text-boxes-border-width) solid rgba(var(--color-foreground), var(--text-boxes-border-opacity));}.banner--mobile-bottom .slideshow__text.banner__box {max-width: 100%;}.banner--mobile-bottom .slideshow__text-wrapper {flex-grow: 1;}.banner--mobile-bottom .slideshow__text.banner__box {height: 100%;}.banner--mobile-bottom .slideshow__text .button {flex-grow: 0;}.slideshow__text.slideshow__text-mobile--left {align-items: flex-start;text-align: left;}.slideshow__text.slideshow__text-mobile--right {align-items: flex-end;text-align: right;}}@media screen and (min-width: 750px) {.slideshow__text.slideshow__text--left {align-items: flex-start;text-align: left;}.slideshow__text.slideshow__text--right {align-items: flex-end;text-align: right;}}.slideshow:not(.banner--mobile-bottom) .slideshow__text-wrapper {height: 100%;}@media screen and (min-width: 750px) {.slideshow__text-wrapper.banner__content {height: 100%;padding: 5rem;}}.slideshow__controls {border: 0.1rem solid rgba(var(--color-foreground), 0.08);}.slideshow__controls--top {order: 2;z-index: 1;}@media screen and (max-width: 749px) {.slideshow__controls--border-radius-mobile {border-bottom-right-radius: var(--text-boxes-radius);border-bottom-left-radius: var(--text-boxes-radius);}}.spaced-section--full-width:last-child slideshow-component:not(.page-width) .slideshow__controls {border-bottom: none;}@media screen and (min-width: 750px) {.slideshow__controls {position: relative;}}slideshow-component:not(.page-width) .slider-buttons {border-right: 0;border-left: 0;}.slideshow__control-wrapper {display: flex;}.slideshow__autoplay {position: absolute;right: 0;border-left: none;display: flex;justify-content: center;align-items: center;}@media screen and (max-width: 749px) {slideshow-component.page-width .slideshow__autoplay {right: 1.5rem;}.slideshow__controls--mobile-over {position: absolute;left: 0;bottom: 0;width: 100%;border: none;}}@media screen and (min-width: 750px) {.slideshow__autoplay.slider-button {position: inherit;margin-left: 0.6rem;padding: 0 0 0 0.6rem;border-left: 0.1rem solid rgba(var(--color-foreground), 0.08);}.slideshow__controls--desktop-over {position: absolute;left: 0;bottom: 0;width: 100%;border: none;}}.slideshow__autoplay .icon.icon-play, .slideshow__autoplay .icon.icon-pause {display: block;position: absolute;opacity: 1;transform: scale(1);transition: transform 150ms ease, opacity 150ms ease;width: 0.8rem;height: 1.2rem;}.slideshow__autoplay .icon.icon-play {height: 1rem;}.slideshow__autoplay path {fill: rgba(var(--color-foreground), 0.75);}.slideshow__autoplay:hover path {fill: rgb(var(--color-foreground));}@media screen and (forced-colors: active) {.slideshow__autoplay path, .slideshow__autoplay:hover path {fill: CanvasText;}}.slideshow__autoplay:hover svg {transform: scale(1.1);}.slideshow__autoplay--paused .icon-pause, .slideshow__autoplay:not(.slideshow__autoplay--paused) .icon-play {visibility: hidden;opacity: 0;transform: scale(0.8);}.slideshow.banner {align-items: stretch;}.video-section__media {position: relative;padding-bottom: 56.25%;}.video-section__media.deferred-media {box-shadow: var(--media-shadow-horizontal-offset) var(--media-shadow-vertical-offset) var(--media-shadow-blur-radius) rgba(var(--color-shadow), var(--media-shadow-opacity));}.video-section__media.deferred-media:after {content: none;}.video-section__poster.deferred-media__poster:focus {outline-offset: 0.3rem;}.video-section__media iframe {background-color: rgba(var(--color-foreground), 0.03);border: 0;}.video-section__poster, .video-section__media iframe {position: absolute;width: 100%;height: 100%;}.newsletter__wrapper {padding-right: calc(3rem / var(--font-body-scale));padding-left: calc(3rem / var(--font-body-scale));}@media screen and (min-width: 750px) {.newsletter__wrapper {padding-right: 9rem;padding-left: 9rem;}}.newsletter__wrapper > * {margin-top: 0;margin-bottom: 0;}.newsletter__wrapper > * + * {margin-top: 2rem;}.newsletter__wrapper > * + .newsletter-form {margin-top: 3rem;}.newsletter__subheading {max-width: 70rem;margin-left: auto;margin-right: auto;}.newsletter__wrapper .newsletter-form__field-wrapper {max-width: 36rem;}.newsletter-form__field-wrapper .newsletter-form__message {margin-top: 1.5rem;}.newsletter__button {margin-top: 3rem;width: fit-content;}@media screen and (min-width: 750px) {.newsletter__button {flex-shrink: 0;margin: 0 0 0 1rem;}}.contact img {max-width: 100%;}.contact .form__message {align-items: flex-start;margin-top: 0.5rem;}.contact .icon-success {margin-top: 0.2rem;}.contact .field-wrapper {margin-bottom: 1.5rem;}.contact__button {display: flex;justify-content: center;}.contact-from__message-container {padding: 0.75rem;column-gap: 0.75rem;margin-bottom: 2rem;}.contact-from__message-container .material-icon {font-size: 4.5rem;}.contact-from__message-container img {height: 4.5rem;width: auto;}.contact-from__message-container ul {padding-left: 1.5rem;margin: 0.3rem 0 0;}@media screen and (min-width: 750px) {.contact .field-wrapper {margin-bottom: 2rem;}.contact__fields {display: grid;grid-template-columns: repeat(2, 1fr);grid-column-gap: 2rem;}}.collapsible-content {position: relative;z-index: 0;}.collapsible-section-layout {padding-top: 3rem;padding-bottom: 3rem;}@media screen and (min-width: 750px) {.collapsible-content__grid {align-items: center;}.collapsible-section-layout {padding-bottom: 5rem;padding-top: 5rem;}}.collapsible-content__media--small {height: 19.4rem;}.collapsible-content__media--large {height: 43.5rem;}@media screen and (min-width: 750px) {.collapsible-content__media--small {height: 31.4rem;}.collapsible-content__media--large {height: 69.5rem;}}@media screen and (min-width: 750px) {.collapsible-content__grid--reverse {flex-direction: row-reverse;}}.collapsible-content-wrapper-narrow {margin: 0 auto;padding-right: 2rem;padding-left: 2rem;max-width: 73.4rem;}.collapsible-content__header {word-break: break-word;}.collapsible-content__heading {margin-bottom: 2rem;margin-top: 0;}@media screen and (min-width: 750px) {.collapsible-content__heading {margin-bottom: 3rem;}}.collapsible-none-layout .accordion + .accordion {border-top: 0;}.collapsible-row-layout .accordion:not(:first-child):not(.color-background-1) {margin-top: 1rem;}.caption-with-letter-spacing + h2 {margin-top: 1rem;}.collapsible-row-layout .accordion {border: var(--text-boxes-border-width) solid rgba(var(--color-foreground), var(--text-boxes-border-opacity));margin-bottom: 1.5rem;}.collapsible-row-layout .accordion summary, .collapsible-row-layout .accordion .accordion__content {padding: 1.5rem;}.collapsible-row-layout .accordion .accordion__content {padding-top: 0;padding-bottom: 0;}.collapsible-row-layout .accordion__details[open] + .accordion__content-wrapper .accordion__content {padding-bottom: 1.5rem;}.collapsible-content summary:hover {background: rgba(var(--color-foreground), 0.04);}.collapsible-content summary:hover .accordion__title {text-decoration: underline;text-underline-offset: 0.3rem;}@supports not (inset: 10px) {@media screen and (min-width: 750px) {.collapsible-content__grid:not(.collapsible-content__grid--reverse) .grid__item:last-child, .collapsible-content__grid--reverse .collapsible-content__grid-item {padding-left: 5rem;padding-right: 0;}}@media screen and (min-width: 990px) {.collapsible-content__grid:not(.collapsible-content__grid--reverse) .grid__item:last-child, .collapsible-content__grid--reverse .collapsible-content__grid-item {padding-left: 7rem;}}}.rich-text {z-index: 1;}.rich-text__wrapper {display: flex;justify-content: center;}.rich-text:not(.rich-text--full-width) .rich-text__wrapper {margin: auto;}.rich-text__blocks {width: 100%;}@media screen and (min-width: 750px) {.rich-text__wrapper {width: 100%;}.rich-text__wrapper--left {justify-content: flex-start;}.rich-text__wrapper--right {justify-content: flex-end;}.rich-text:not(.rich-text--full-width) .rich-text__blocks {max-width: 50rem;}}@media screen and (min-width: 990px) {.rich-text:not(.rich-text--full-width) .rich-text__blocks {max-width: 78rem;}}.rich-text__blocks * {overflow-wrap: break-word;}.rich-text__blocks > * {margin-top: 0;margin-bottom: 0;}.rich-text__blocks > * + * {margin-top: 2rem;}.rich-text__blocks > * + a {margin-top: 3rem;}h2 + .rating-stars, h2 + .trustpilot-stars {margin-top: 0.3rem;}.rating-stars + h2, .trustpilot-stars + h2 {margin-top: 0;}.rich-text__buttons {display: inline-flex;justify-content: center;flex-wrap: wrap;gap: 1rem;width: 100%;max-width: 45rem;word-break: break-word;}.rich-text__buttons--multiple > * {flex-grow: 1;min-width: 22rem;}.rich-text__buttons + .rich-text__buttons {margin-top: 1rem;}.rich-text__blocks.left .rich-text__buttons {justify-content: flex-start;}.rich-text__blocks.right .rich-text__buttons {justify-content: flex-end;}.email-signup-banner .newsletter-form, .email-signup-banner .newsletter-form__field-wrapper {display: inline-block;}@media only screen and (min-width: 750px) {.email-signup-banner:not(.banner--desktop-transparent) .email-signup-banner__box {width: 100%;}}.email-signup-banner__box .email-signup-banner__heading {margin-bottom: 0;}.email-signup-banner__box > * + .newsletter__subheading {margin-top: 2rem;}.email-signup-banner__box .newsletter__subheading p {margin: 0;}.email-signup-banner-background {width: 100%;height: 100%;position: relative;left: 50%;transform: translateX(-50%);}@media screen and (max-width: 749px) {.email-signup-banner:not(.banner--mobile-bottom) .banner__box:not(.email-signup-banner__box--no-image) {background-color: transparent;--color-foreground: 255, 255, 255;--color-button: 255, 255, 255;--color-button-text: 0, 0, 0;}}@media only screen and (min-width: 750px) {.banner--desktop-transparent .email-signup-banner__box--no-image * {color: rgb(var(--color-base-text));}.banner--desktop-transparent .email-signup-banner__box .field__input {background-color: transparent;}.banner--desktop-transparent .email-signup-banner__box--no-image .field__input {box-shadow: 0 0 0 0.1rem rgba(var(--color-base-text), 0.55);}.banner--desktop-transparent .email-signup-banner__box--no-image .field__input:focus {box-shadow: 0 0 0 0.2rem rgba(var(--color-base-text), 0.75);}.banner--desktop-transparent .email-signup-banner__box--no-image .field__button:focus-visible {outline: 0.2rem solid rgba(var(--color-base-text), 0.5);box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-base-text), 0.3);}}@media only screen and (min-width: 750px) {.email-signup-banner-background-mobile {display: none;}}@media only screen and (max-width: 749px) {.email-signup-banner-background:not(.email-signup-banner-background-mobile) {display: none;}}.email-signup-banner .banner__media {overflow: hidden;}@media screen and (max-width: 749px) {.banner--mobile-content-align-left .newsletter-form__message {justify-content: flex-start;}.banner--mobile-content-align-right .newsletter-form__message {justify-content: right;}}@media screen and (min-width: 750px) {.banner--content-align-center .newsletter-form__message {justify-content: center;}.banner--content-align-right .newsletter-form__message {justify-content: right;}}.collection-list {margin-top: 0;margin-bottom: 0;}.collection-list-title {margin: 0;}@media screen and (max-width: 749px) {.collection-list:not(.slider) {padding-left: 0;padding-right: 0;}.section-collection-list .page-width {padding-left: 0;padding-right: 0;}}.collection-list__item:only-child {max-width: 100%;width: 100%;}@media screen and (max-width: 749px) {.slider.collection-list--1-items {padding-bottom: 0;}}@media screen and (min-width: 750px) and (max-width: 989px) {.slider.collection-list--1-items, .slider.collection-list--2-items, .slider.collection-list--3-items, .slider.collection-list--4-items {padding-bottom: 0;}}@media screen and (min-width: 750px) {.collection-list__item a:hover {box-shadow: none;}}@media screen and (max-width: 989px) {.collection-list.slider .collection-list__item {max-width: 100%;}}.collection-list-view-all {margin-top: 2rem;}.multicolumn .title {margin: 0;}.multicolumn.no-heading .title {display: none;}.multicolumn .title-wrapper-with-link {margin-top: 0;}@media screen and (max-width: 749px) {.multicolumn .title-wrapper-with-link {margin-bottom: 3rem;}}.multicolumn-card__image-wrapper--third-width {width: 33%;}.multicolumn-card__image-wrapper--half-width {width: 50%;}.multicolumn-card {max-width: 72rem;position: relative;box-sizing: border-box;display: flex;flex-direction: column;}.multicolumn-card--media-bottom {flex-direction: column-reverse;}.splide--align-stretch .splide__slide__container, .splide--align-stretch .multicolumn-card {height: 100%;}.splide--align-stretch .multicolumn-card__info {display: flex;flex-direction: column;flex-grow: 1;justify-content: var(--stretched-cards-alignment);}.multicolumn-card, .multicolumn-card.center .multicolumn-card__image-wrapper:not( .multicolumn-card__image-wrapper--full-width ) {margin-left: auto;margin-right: auto;}.multicolumn .button {margin-top: 1.5rem;}@media screen and (min-width: 750px) {.multicolumn .button {margin-top: 4rem;}}.multicolumn-card.color-bg-overlay {background: rgb(var(--color-background)) linear-gradient( rgba(var(--color-foreground), 0.04), rgba(var(--color-foreground), 0.04) );}.multicolumn-card h3 {line-height: calc(1 + 0.5 / max(1, var(--font-heading-scale)));}.multicolumn-card h3, .multicolumn-card p {margin: 0;}.multicolumn-card-spacing {padding-top: 2.5rem;margin-left: 2.5rem;margin-right: 2.5rem;}.multicolumn-card__info > :nth-child(2) {margin-top: 1rem;}.multicolumn-card.center .media--adapt, .multicolumn-card .media--adapt .multicolumn-card__image {width: auto;}.multicolumn-card.center .media--adapt img {left: 50%;transform: translateX(-50%);}.multicolumn--same-bgs .multicolumn-card-spacing {padding: 0;margin: 0;}.multicolumn-card__info {padding: 2.5rem;}.multicolumn--same-bgs .multicolumn-card__info {padding-top: 0;padding-left: 1.5rem;padding-right: 1.5rem;}.is-active .multicolumn--same-bgs .multicolumn-card__info {padding-bottom: 0;}.multicolumn--same-bgs .multicolumn-card__image-wrapper + .multicolumn-card__info {padding-top: 2.5rem;}.multicolumn--same-bgs .multicolumn-card__image-wrapper .media {border-radius: calc( var(--text-boxes-radius) - var(--text-boxes-border-width) );overflow: hidden;}@media screen and (max-width: 749px) {}@media screen and (min-width: 750px) {.multicolumn--same-bgs .multicolumn-card__image-wrapper {margin-left: 1.5rem;margin-right: 1.5rem;}.multicolumn .multicolumn--same-bgs .multicolumn-card__info, .splide:not(.is-active) .multicolumn--same-bgs.center .multicolumn-card__info {padding-left: 1.5rem;padding-right: 1.5rem;}}.multicolumn-card:not(.multicolumn-card--media-bottom) > .multicolumn-card__image-wrapper--full-width:not( .multicolumn-card-spacing ) {border-top-left-radius: calc( var(--text-boxes-radius) - var(--text-boxes-border-width) );border-top-right-radius: calc( var(--text-boxes-radius) - var(--text-boxes-border-width) );overflow: hidden;}.multicolumn-card--media-bottom > .multicolumn-card__image-wrapper--full-width:not( .multicolumn-card-spacing ) {border-bottom-left-radius: calc( var(--text-boxes-radius) - var(--text-boxes-border-width) );border-bottom-right-radius: calc( var(--text-boxes-radius) - var(--text-boxes-border-width) );overflow: hidden;}.multicolumn--same-bgs.multicolumn-card {border-radius: 0;}.multicolumn-card__info .link {text-decoration: none;font-size: inherit;margin-top: 1.5rem;}.multicolumn-card__info .icon-wrap {margin-left: 0.8rem;white-space: nowrap;}.icon-bar-card__icon--small {--icon-size: 3rem;}.icon-bar-card__icon--medium {--icon-size: 5rem;}.icon-bar-card__icon--large {--icon-size: 7rem;}.multicolumn-card.multicolumn--diff-bgs .icon-bar-card__icon {padding-top: 2.5rem;}.icon-bar-card__icon img {height: var(--icon-size);}.icon-bar-card__icon .material-symbols-outlined {font-size: var(--icon-size);}.multicolumn .multicolumn-title-with-text {margin-bottom: 0.5rem;}.multicolumn-text {text-align: center;margin-bottom: 3rem;}.multicolumn-text p:first-child {margin-top: 0;}.icon-bar-card .multicolumn-card__info {padding-top: 1rem;}@media screen and (max-width: 749px) {.multicolumn[data-mobile-columns="2"] .multicolumn-card.multicolumn--diff-bgs .icon-bar-card__icon {padding-top: 1.5rem;}.icon-bar[data-mobile-columns="2"] .multicolumn-card__info {padding-top: 1rem;}.icon-bar[data-mobile-columns="2"] .material-icon {font-size: calc(var(--icon-size) * 0.8);}.icon-bar.background-none .multicolumn-list:not(.slider) .center .multicolumn-card__info {padding: 0.7rem 0.5rem 1.5rem;}}.icon-bar .icon-bar-card--horizontal {display: flex;flex-direction: row;align-items: center;text-align: left;}.icon-bar.background-primary .icon-bar-card--horizontal .icon-bar-card__icon {padding-top: 0;}.icon-bar.background-primary .icon-bar-card--horizontal {padding: 1rem;}.icon-bar .icon-bar-card--horizontal .multicolumn-card__info {padding: 0 0 0 1rem !important;}.icon-bar .icon-bar-card--horizontal .multicolumn-card__info h3 {font-size: calc(var(--font-heading-scale) * 1.3rem);}.icon-bar .icon-bar-card--horizontal .multicolumn-card__info > :nth-child(2) {margin-top: 0.5rem;font-size: 1.3rem;line-height: 1.6;}.testimonial-card__stars {font-size: 2.2rem;color: var(--stars-color);margin: 0;line-height: 1.2;}.testimonial-card__author-container {display: flex;align-items: center;padding-top: 1rem;margin-top: 0.75rem;border-top: solid 1px rgba(var(--color-foreground), 0.06);}.testimonial-card.center .testimonial-card__author-container {justify-content: center;}.testimonial-card__avatar {width: 3rem;height: 3rem;margin-right: 1rem;border-radius: 50%;overflow: hidden;}.testimonial-card__avatar img {width: 100%;height: 100%;object-fit: cover;object-position: center center;}.testimonial-card__author {font-size: 1.4rem;line-height: 1.1;}.testimonial-card .multicolumn-card__info {padding: 1.25rem 2rem 2rem;position: relative;}.testimonial-card__quotes {font-size: 5rem;position: absolute;top: 0;right: 2rem;width: 1em;height: 1em;border-radius: 50%;transform: translateY(-50%);margin: 0 !important;}.testimonial-card__quotes svg {width: 55%;}.testimonial-card__quotes--image-blank {font-size: 3.5rem;right: 5px;top: 5px;transform: none;}.testimonial-card__info--no-image-no-stars h3 {padding: 0 2.5rem;}.testimonial-card .testimonial-card__info--image-no-stars {padding-top: 1.25rem;}.testimonial-card__info--image-no-stars .testimonial-card__quotes {font-size: 4rem;}.multicolumn .testimonial-card .multicolumn-card__info h3 {margin-top: 0.5rem;font-size: calc(var(--font-heading-scale) * 1.45rem);margin-bottom: 1rem;}.testimonial-card--has-author .multicolumn-card__info {padding-bottom: 1.25rem;}.section-divider {position: relative;}.section-divider::before {content: "";display: block;position: absolute;left: 0;bottom: calc(100% - 1px);top: -1px;z-index: -1;width: 100%;background: rgb(var(--color-background));}.section-divider__svg {--max-height: 150px;background: none;position: relative;overflow: hidden;transform: translateY(1px);}.section-divider__svg svg {color: rgb(var(--color-background));display: block;width: 100%;height: auto;overflow: hidden;max-height: var(--max-height);}.waves-animated-1 {--max-height: 3rem;margin: 0;}.parallax1 > use {animation: move-forever1 10s linear infinite;}.parallax2 > use {animation: move-forever2 8s linear infinite;opacity: 0.4;}.parallax3 > use {animation: move-forever3 6s linear infinite;opacity: 0.3;}.parallax4 > use {animation: move-forever4 4s linear infinite;opacity: 0.2;}@keyframes move-forever1 {0% {transform: translate(85px, 0%);}100% {transform: translate(-90px, 0%);}}@keyframes move-forever2 {0% {transform: translate(-90px, 0%);}100% {transform: translate(85px, 0%);}}@keyframes move-forever3 {0% {transform: translate(85px, 0%);}100% {transform: translate(-90px, 0%);}}@keyframes move-forever4 {0% {transform: translate(-90px, 0%);}100% {transform: translate(85px, 0%);}}@media screen and (min-width: 1000px) {.waves-animated-1 {--max-height: 6rem;}}.content-and-comparison-table {display: grid;grid-template-columns: repeat(2, 1fr);place-items: center;column-gap: 5rem;row-gap: 2rem;}.comparison-table-container {width: 100%;max-width: 50rem;}.comparison-table {--border-radius: 2rem;flex-grow: 1;border-spacing: 0;}.comparison-table th {padding: 0.5rem 0.75rem;font-size: var(--font-size);line-height: 1.3;}.comparison-table thead {z-index: 1;}.comparison-table tbody {border-radius: var(--border-radius);}.comparison-table td {--cell-bottom-separator-thickness: -0.1rem;--cell-separator-opacity: 0;--cell-overlay-opacity: 0;--border-shadow-offset: -0.1rem 0 0 0;--outer-box-shadow: 0.3rem 0.3rem 0.8rem rgba(0, 0, 0, 0.15), var(--border-shadow-offset) rgba(0, 0, 0, 0.08);padding: 1rem 1.5rem;text-align: center;box-shadow: inset 0 var(--cell-bottom-separator-thickness) 0 0 rgba(var(--color-foreground), var(--cell-separator-opacity)), inset 0 0 0 6rem rgba(var(--color-foreground), var(--cell-overlay-opacity)), var(--outer-box-shadow);position: relative;}.comparison-table td:last-of-type {--border-shadow-offset: 0.1rem 0 0 0;}.comparison-table--classic tbody tr:first-of-type td {--border-shadow-offset: 0 -0.1rem 0 0;}.comparison-table--classic tbody tr:last-of-type td {--border-shadow-offset: 0 0.1rem 0 0;}.comparison-table tbody tr:first-of-type td:first-child {border-radius: var(--border-radius) 0 0 0;--border-shadow-offset: -0.1rem -0.1rem 0 0;}.comparison-table tbody tr:last-of-type td:first-child {border-radius: 0 0 0 var(--border-radius);--border-shadow-offset: -0.1rem 0.1rem 0 0;}.comparison-table tbody tr:first-of-type td:last-child {border-radius: 0 var(--border-radius) 0 0;--border-shadow-offset: 0.1rem -0.1rem 0 0;}.comparison-table tbody tr:last-of-type td:last-child {border-radius: 0 0 var(--border-radius) 0;--border-shadow-offset: 0.1rem 0.1rem 0 0;}.comparison-table tbody td:not(.comparison-table__row-name) {padding: 1rem 2rem;}.comparison-table__icon {font-size: 2rem;width: 1em;height: 1em;margin: auto;border-radius: 50%;box-sizing: content-box;}.comparison-table__icon--solid {background: var(--bg-color);padding: 0.4rem;font-size: 1.4rem;}.comparison-table__icon svg {color: var(--icon-color);width: 100%;height: 100%;box-sizing: border-box;}.comparison-table .comparison-table__row-name {--font-size: 1.8rem;font-size: var(--font-size);margin: 0;}.content-and-comparison-table--no-content {grid-template-columns: repeat(1, 1fr);}@media screen and (max-width: 899px) {.content-and-comparison-table {grid-template-columns: repeat(1, 1fr);}}@media screen and (min-width: 900px) {.content-and-comparison-table--table-first .comparison-table-container {order: -1;}}@media screen and (max-width: 750px) {.comparison-table .comparison-table__row-name, .comparison-table th {font-size: calc(var(--font-size) * 0.85);}.comparison-table__checkmark, .comparison-table__x {width: 1.8rem;height: 1.8rem;}}@media screen and (max-width: 500px) {.comparison-table {--border-radius: 1.5rem;}.comparison-table thead img {max-width: var(--mobile-logo-width);}}.comparison-table--centered {--bottom-piece-height: 3rem;padding-bottom: var(--bottom-piece-height);}.comparison-table--centered .comparison-table__logo {border-radius: var(--border-radius) var(--border-radius) 0 0;padding: 0.75rem 1rem;}.comparison-table--centered tbody tr:last-of-type td:nth-child(2)::after {content: "";display: block;width: 100%;height: var(--bottom-piece-height);position: absolute;top: 100%;left: 0;background: rgb(var(--color-background));border-radius: 0 0 var(--border-radius) var(--border-radius);}.comparison-table--minimal tbody, .comparison-table--minimal td {box-shadow: none;border-radius: 0;}.comparison-table--minimal tr {display: flex;justify-content: space-between;align-items: center;}.comparison-table--minimal tbody tr:not(:last-of-type) {border-bottom: solid 0.1rem rgba(var(--color-foreground), var(--border-opacity));}.comparison-table--minimal th:nth-child(2), .comparison-table--minimal td:nth-child(2) {order: -1;text-align: left;}.comparison-table--minimal th, .comparison-table--minimal tbody td:not(.comparison-table__row-name) {padding-left: 0.2rem;padding-right: 0.2rem;}.comparison-table--minimal .comparison-table__row-name {flex-grow: 1;padding: 1rem 1.5rem;}.comparison-table--minimal .comparison-table__icon--solid {font-size: 2rem;padding: 0.75rem;}@media screen and (min-width: 999px) {.comparison-table--minimal .comparison-table__icon--solid {font-size: 2.2rem;padding: 0.9rem;}}.content-and-results {display: grid;grid-template-columns: repeat(2, 1fr);place-items: center;column-gap: 5rem;row-gap: 2rem;}.content-and-results--no-content {grid-template-columns: repeat(1, 1fr);}@media screen and (max-width: 899px) {.content-and-results {grid-template-columns: repeat(1, 1fr);}}@media screen and (min-width: 900px) {.content-and-results--results-first .results-container {order: -1;}}.results-container .title, .results-container .rte {margin-top: 0;margin-bottom: 1.5rem;}.content-and-results .content-container {width: 100%;}.content-and-results__image {max-width: 60rem;width: 100%;margin-left: auto;margin-right: auto;}.results__rows-container {margin-bottom: 1rem;}.results__percentage {width: 7rem;height: 7rem;display: flex;align-items: center;justify-content: center;text-align: center;border-radius: 50%;position: relative;--border-width: 0.7rem;flex-shrink: 0;margin-right: 2rem;z-index: 0;}.results__percentage::before {content: "";display: block;position: absolute;top: 0;left: 0;bottom: 0;right: 0;border-radius: 50%;z-index: -2;background: conic-gradient( from 0deg at 50% 50%, rgb(var(--accent-color)) 0%, rgb(var(--accent-color)) var(--percentage), rgb(var(--color-background)) var(--percentage), rgb(var(--color-background)) 100% );}.results__percentage::after {content: "";display: block;position: absolute;top: var(--border-width);left: var(--border-width);bottom: var(--border-width);right: var(--border-width);border-radius: 50%;z-index: -1;background: rgb(var(--color-background));}.results__percentage p {margin: 0;color: rgb(var(--accent-color));font-size: 1.8rem;font-weight: 700;}.results__row {padding: 1rem 0;display: flex;align-items: center;border-bottom: 2px solid rgba(var(--color-foreground), 0.05);}.results__row:first-of-type {border-top: 2px solid rgba(var(--color-foreground), 0.05);}.results__text p {margin: 0;}.results__caption p {margin: 0;font-size: 1.2rem;}.content-and-comparison-slider {display: grid;grid-template-columns: repeat(2, 1fr);place-items: center;column-gap: 5rem;position: relative;z-index: 0;row-gap: 2rem;}.content-and-comparison-slider--no-content {grid-template-columns: repeat(1, 1fr);}.comparison-slider {display: block;position: relative;max-width: 45rem;width: 100%;margin: 0 auto;overflow: hidden;}.comparison-slider__overlay {position: absolute;z-index: 2;top: 0;left: 0;width: 50%;height: 100%;overflow: hidden;max-width: 100%;}.comparison-slider__overlay img {width: 100%;height: 100%;object-fit: cover;object-position: top left;}.comparison-slider__underlay img {width: 100%;}.comparison-slider__before-text, .comparison-slider__after-text {position: absolute;top: 0;left: 0;z-index: 4;}.comparison-slider__before-text h3, .comparison-slider__after-text h3 {text-align: center;width: fit-content;font-size: 1.2rem;letter-spacing: 0.1rem;line-height: 1;padding: 0.5rem 1.3rem 0.6rem;margin: 0;white-space: nowrap;}.comparison-slider__after-text {left: auto;right: 0;}.comparison-slider__input {position: absolute;top: 0;left: 0;z-index: 5;height: 100%;width: 100%;opacity: 0;-webkit-appearance: none;overflow: hidden;}.comparison-slider__input::-webkit-slider-thumb {-webkit-appearance: none;height: 50rem;max-height: 100%;width: 5rem;}.comparison-slider__line {--line-width: 0.5rem;--arrow-x-offset: 1.75rem;--arrow-size: 0.7rem;display: block;position: absolute;z-index: 3;top: 0;left: 50%;height: 100%;width: var(--line-width);transform: translateX(-50%);}.comparison-slider__arrow-left, .comparison-slider__arrow-right {font-size: var(--arrow-size);width: 1em;height: 1.7em;position: absolute;top: 50%;background: rgb(var(--color-background));}.comparison-slider__arrow-left {left: 0;transform: translate(calc(var(--arrow-x-offset) * -1), -50%);clip-path: polygon(100% 0%, 0 50%, 100% 100%);}.comparison-slider__arrow-right {right: 0;transform: translate(var(--arrow-x-offset), -50%);clip-path: polygon(100% 50%, 0 0, 0 100%);}.comparison-slider__line--circle {--circle-size: 4.25rem;--border-size: 0.3rem;--line-width: 0.3rem;top: 50%;transform: translate(-50%, -50%);width: var(--circle-size);height: var(--circle-size);border-radius: 50%;background: rgba(var(--color-background), 0.15);border: solid var(--border-size) rgb(var(--color-background));backdrop-filter: blur(2px);box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);}.comparison-slider__line--circle::before, .comparison-slider__line--circle::after {content: "";display: block;height: 50rem;width: var(--line-width);background: rgb(var(--color-background));position: absolute;left: 50%;z-index: 1;transform: translateX(-50%);box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);}.comparison-slider__line--circle::before {bottom: 100%;}.comparison-slider__line--circle::after {top: 100%;}.comparison-slider__line--circle .comparison-slider__arrow-left {left: 25%;transform: translate(-50%, -50%);}.comparison-slider__line--circle .comparison-slider__arrow-right {right: 25%;transform: translate(50%, -50%);}@media screen and (max-width: 899px) {.content-and-comparison-slider {grid-template-columns: repeat(1, 1fr);}}@media screen and (min-width: 900px) {.content-and-comparison-slider--slider-first .comparison-slider {order: -1;}.comparison-slider__line--classic {--line-width: 0.6rem;--arrow-size: 0.9rem;}.comparison-slider__line--circle {--circle-size: 5rem;--arrow-size: 0.75rem;}}.icon-with-content__grid {display: grid;grid-template-columns: repeat(2, 1fr);column-gap: 8rem;align-items: center;row-gap: 3rem;}.icons-with-text__icon-item {--line-height: 1.5;display: flex;align-items: center;margin-bottom: 3rem;}.icons-with-text__icon-item:last-child {margin-bottom: 0;}.icons-with-text__icon-item--center {text-align: center;}.icons-with-text__icon-item--center.icons-with-text__icon-item--next-to-title .icons-with-text__icon__title {justify-content: center;}.icons-with-text__icon-item--right {text-align: right;}.icons-with-text__icon-item--right.icons-with-text__icon-item--next-to-title .icons-with-text__icon__title {justify-content: flex-end;}.icons-with-text__icon__icon--xs {--icon-size: 1.6rem;--margin: 1rem;}.icons-with-text__icon__icon--s {--icon-size: 2.3rem;--margin: 1.25rem;}.icons-with-text__icon__icon--m {--icon-size: 2.5rem;--margin: 1.4rem;}.icons-with-text__icon__icon--l {--icon-size: 3.5rem;--margin: 1.5rem;}.icons-with-text__icon__icon--xl {--icon-size: 5rem;}.icons-with-text__icon__icon--xxl {--icon-size: 7rem;}.icons-with-text__icon__icon--xl, .icons-with-text__icon__icon--xxl {--margin: calc((var(--icon-size)) * 0.35);}.icons-with-text__icon__icon {flex-shrink: 0;width: var(--icon-size);height: var(--icon-size);display: flex;align-items: center;justify-content: center;margin-right: var(--margin);background: none;}.icons-with-text__icon__icon .material-icon {color: rgb(var(--color-background));font-size: var(--icon-size);}.icons-with-text__icon__icon img {width: 100%;height: 100%;object-fit: contain;object-position: center center;}.icons-with-text__icon__title {margin: 0;line-height: 1.3;margin-bottom: 0.4em;}.icons-with-text__icon-item--next-to-title .icons-with-text__icon__title {display: flex;align-items: center;}.icons-with-text__icon__title.h5 {font-size: 1.75rem;}.icons-with-text__icon__title.h4 {font-size: 1.9remem;}.icons-with-text__icon__title.h3 {font-size: 2.25rem;}.icons-with-text__icon__title.h2 {font-size: 2.65rem;}.icons-with-text__icon__text p {line-height: var(--line-height);}.icon-with-text__icon__text--small {font-size: 1.2rem;}.icons-with-text__icon-item--above {flex-direction: column;text-align: center;}.icons-with-text__icon-item--above .icons-with-text__icon__icon {margin-right: 0;margin-bottom: var(--margin);}.icon-with-content__content > * {margin-top: 2rem;margin-bottom: 0;}.icon-with-content__content > *:first-child {margin-top: 0;}.icon-with-content__content .icon-with-content__text--caption + .icon-with-content__heading {margin-top: 0;}@media screen and (max-width: 749px) {.icon-with-content__grid {grid-template-columns: repeat(1, 1fr);}.icon-with-content__mobile-grid--reverse .icon-with-content__content {order: -1;}.icon-with-content__content--hide-on-mobile {display: none;}.icon-with-content__content {text-align: center;}.icons-with-text__icon__icon--xs {--icon-size: 1.5rem;--margin: 0.75rem;}.icons-with-text__icon__icon--s {--icon-size: 1.75rem;--margin: 0.85rem;}.icons-with-text__icon__icon--m {--icon-size: 2.1rem;--margin: 1rem;}.icons-with-text__icon__icon--l {--icon-size: 3rem;--margin: 1.3rem;}.icons-with-text__icon__icon--xl {--icon-size: 4.25rem;}.icons-with-text__icon__icon--xxl {--icon-size: 5.5rem;}.icons-with-text__icon__title.h5 {font-size: 1.4rem;}.icons-with-text__icon__title.h4 {font-size: 1.6rem;}.icons-with-text__icon__title.h3 {font-size: 1.8rem;}.icons-with-text__icon__title.h2 {font-size: 2.25rem;}.icon-with-content__icons--mobile-2-columns {display: grid;grid-template-columns: repeat(2, 1fr);align-items: flex-start;column-gap: 1.75rem;row-gap: 2rem;}.icon-with-content__icons--mobile-2-columns ..icons-with-text__icon-item {margin-bottom: 0;}.icon-with-content__icons--mobile-2-columns .icons-with-text__icon__title {font-size: 1.4rem;}.icon-with-content__icons--mobile-2-columns .rte {font-size: 1.2rem;}.icon-with-content__icons--mobile-2-columns .icons-with-text__icon__icon {width: calc(var(--icon-size) * 0.7);height: calc(var(--icon-size) * 0.7);margin-bottom: 1rem;}}@media screen and (min-width: 750px) {.icon-with-content__grid--desktop-reverse {column-gap: 9rem;}.icon-with-content__grid--desktop-reverse .icon-with-content__content {order: -1;}.icon-with-content__icons--desktop-2-columns {display: grid;grid-template-columns: repeat(2, 1fr);align-items: flex-start;column-gap: 3rem;row-gap: 3rem;}.icon-with-content__icons--desktop-2-columns ..icons-with-text__icon-item {margin-bottom: 0;}}.popup-overlay {position: fixed;top: 0;left: 0;z-index: 1090;transition: width 0s, height 0s, background-color 0.3s;width: 0;height: 0;background-color: rgba(0, 0, 0, 0);}.popup-overlay--active {width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.3);}.popup-modal {position: fixed;top: 50%;left: 50%;transform: translate(-50%, -50%) scale(0);transform-origin: center center;border-radius: 1.25rem;overflow: hidden;visibility: hidden;opacity: 0;z-index: 1100;transition: all 0.3s ease-in-out;max-width: calc(100% - 1rem);width: 38rem;}.popup-modal--image {width: 76rem;}.popup-modal--image-second .popup-modal__content {order: -1;}.popup-modal__container {display: flex;}.popup-modal--active {transform: translate(-50%, -50%);visibility: visible;opacity: 1;}.popup-modal__image {width: 100%;max-width: 38rem;position: relative;}.popup-modal__image img {object-fit: cover;object-position: center center;width: 100%;height: 100%;}.popup-modal__content {text-align: center;display: flex;flex-direction: column;align-items: center;padding: 5rem 2rem 2.5rem;background: rgb(var(--color-background));max-width: 38rem;width: 100%;}.popup-modal__title {margin: 2rem 0;z-index: 0;}.popup-modal__title span {display: block;}.popup-modal__title .title {margin: 0;position: relative;z-index: 0;}.popup-modal__title .title::before {content: "";display: block;width: 25rem;height: 25rem;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -35%);border-radius: 50%;z-index: -1;background: linear-gradient( 180deg, rgba(var(--color-base-accent-1), 0.1) 20%, rgba(0, 0, 0, 0) 60% );}.popup-modal__title__prefix, .popup-modal__title__suffix {font-size: calc(var(--font-heading-scale) * 1.6rem);}.popup-modal__title .h2 {font-size: calc(var(--font-heading-scale) * 2.5rem);}.popup-modal__title .h1 {font-size: calc(var(--font-heading-scale) * 3.5rem);}.popup-modal__title .h0 {font-size: calc(var(--font-heading-scale) * 4.5rem);}.popup-modal__title .title {line-height: calc(1 + 0.2 / max (1, var(--font-heading-scale)));}.popup-modal__text p {margin-top: 0;line-height: 1.4;margin-bottom: 0.5em;}.popup-modal__timer p {margin: 0;font-weight: var(--font-heading-weight);line-height: 1;margin-bottom: 1.5rem;font-weight: var(--font-heading-weight);}.popup-modal__timer__minutes, .popup-modal__timer__seconds {font-size: 4rem;}.popup-modal__timer__colon {font-size: 3rem;}.popup-modal__email-form {width: 100%;}.popup-modal .newsletter-form__message {justify-content: center;width: 100%;margin-top: 1rem;}.popup-modal .button {width: 100%;margin-top: 0.75rem;margin-bottom: 1.5rem;}.popup-modal__close {font-size: 3.5rem;cursor: pointer;outline: none;width: 1em;height: 1em;padding: 0;position: absolute;top: 0.5rem;right: 0.5rem;border-radius: 50%;display: flex;justify-content: center;align-items: center;border: solid 0.2rem rgba(var(--color-foreground), 0.5);background: rgb(var(--color-background));z-index: 2;}.popup-modal__close span {display: block;width: 0.5em;height: 0.2rem;background: rgba(var(--color-foreground), 0.5);transform: rotate(45deg);flex-shrink: 0;}.popup-modal__close span:nth-child(2) {position: absolute;transform: rotate(-45deg);}.popup-modal__close--borderless {border: none;font-size: 3rem;}.popup-modal__dismiss-btn {border: none;outline: none;background: none;font-size: 1.4rem;color: rgba(var(--color-foreground), 0.75);cursor: pointer;position: relative;}.popup-modal__dismiss-btn:hover {text-decoration: underline;}@media screen and (max-width: 50rem) {.popup-modal__image {display: none;}.popup-modal--image {width: 38rem;}}.success-popup-modal__text {margin-bottom: 1rem;}.popup-modal__discount-code {margin-bottom: 1.5rem;position: relative;}.field:hover.field::after {box-shadow: 0 0 0 var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-border-opacity));}.popup-modal__discount-code .field {align-items: center;}.popup-modal__discount-code .field__input {padding-left: 1.2rem;box-shadow: none;cursor: default;}.popup-modal__discount-code .field__input:not(:placeholder-shown) ~ .field__label {left: calc(var(--inputs-border-width) + 1.2rem);}.popup-modal .popup-modal__copy-btn {margin: 0;display: flex;align-items: center;min-width: auto;width: fit-content;min-height: 3.9rem;line-height: 1;margin-right: 0.4rem;font-size: 1.6rem;padding: 0 2.5rem;}.popup-modal__copy-btn svg {width: 1.6rem;height: 1.6rem;margin-left: 0.5rem;}.popup-modal__success-msg {font-size: 1.3rem;margin: 0;margin-top: 0.2rem;color: #00a100;display: none;}insta-stories {display: block;--pfp-size: 7rem;--ring-width: 0.3rem;--ring-offset: 0.3rem;}.content-rte + insta-stories {margin-top: 2rem;}.insta-stories__open-buttons-and-arrows-container {position: relative;z-index: 0;}.insta-stories__open-buttons-container {overflow: hidden;}.insta-stories__open-buttons-and-arrows-container .insta-stories__open-btns-prev {left: 0;transform: translateY(-50%);display: none;}.insta-stories__open-buttons-and-arrows-container .insta-stories__open-btns-next {right: 0;transform: translateY(-50%);display: none;}.insta-stories__open-buttons {display: flex;column-gap: 1rem;align-items: flex-start;width: fit-content;margin: 0 auto;}.insta-story-open-btn {width: var(--pfp-size);padding: 0;}.insta-story-open-btn:focus-visible {outline: none;box-shadow: none;}.insta-story-open-btn__pfp-ring {width: var(--pfp-size);height: var(--pfp-size);border-radius: 50%;padding: var(--ring-width);background: purple;background: linear-gradient( 45deg, rgba(255, 200, 0, 1) 15%, rgba(255, 21, 88, 1) 50%, rgba(211, 0, 197, 1) 85% );}.insta-story-open-btn__pfp-ring--close-friends {background: rgb(68, 189, 114);background: linear-gradient( 45deg, rgba(68, 189, 114, 1) 0%, rgba(87, 194, 124, 1) 50%, rgba(74, 197, 113, 1) 100% );}.insta-story-open-btn__pfp-ring-offset {width: 100%;height: 100%;border-radius: 50%;padding: var(--ring-offset);background: rgb(var(--color-background));}.insta-story-open-btn__pfp {width: 100%;height: 100%;border-radius: 50%;overflow: hidden;background: #c2c2c2;display: flex;align-items: flex-end;justify-content: center;}.insta-story-open-btn__pfp .material-icon {font-size: calc(var(--pfp-size) * 0.9);color: #fff;transform: translateY(18%);}.insta-story-open-btn__pfp img {width: 100%;height: 100%;object-fit: cover;object-position: center center;}.insta-story-open-btn__username {max-width: 100%;white-space: nowrap;text-overflow: ellipsis;display: block;overflow: hidden;text-align: center;margin-top: 0.4em;font-size: calc(var(--pfp-size) / 6);}.insta-stories__modal {position: fixed;top: 0;left: 0;z-index: 130;height: 100vh;width: 100%;overflow: hidden;display: none;place-items: center;background: rgba(0, 0, 0, 0.9);backdrop-filter: blur(5px);--story-height: calc(100vh - 4rem);--story-width: min(calc(var(--story-height) * 9 / 16), 100vw);--slide-transition-duration: 0.3s;}.insta-stories__modal[data-open="true"] {display: grid;}.insta-stories__modal__close {font-size: 4rem;width: 1em;height: 1em;display: grid;place-items: center;position: absolute;top: 2rem;right: 2rem;z-index: 2;}.insta-stories__modal__close .material-icon {font-size: 1em;color: #ececec;}.insta-stories__slider-container {width: calc(var(--story-width) * 3);overflow: hidden;}.insta-stories__slider {display: flex;width: fit-content;transition: transform var(--slide-transition-duration);position: relative;left: var(--story-width);}.insta-story {height: var(--story-height);width: var(--story-width);position: relative;z-index: 0;box-shadow: 0.3rem 0.3rem 1rem rgba(0, 0, 0, 0.3);transition: transform var(--slide-transition-duration);transform: scale(0.6);}.insta-story--active {transform: scale(1);z-index: 1;}.insta-story__top {position: absolute;top: 0;left: 0;padding: 2rem;z-index: 2;width: 100%;}.insta-story__progress {display: flex;justify-content: space-between;column-gap: 0.3rem;}.insta-story__progress-item {display: block;height: 0.3rem;border-radius: 1rem;flex: 1 1 auto;background: rgba(255, 255, 255, 0.35);position: relative;}.insta-story__progress-bar {content: "";display: block;position: absolute;top: 0;left: 0;width: 0;height: 100%;background: #fff;}.insta-story__progress-item--completed .insta-story__progress-bar {width: 100%;}.insta-story__progress-item--active .insta-story__progress-bar {animation: fillProgress var(--duration) linear forwards;}@keyframes fillProgress {from {width: 0;}to {width: 100%;}}.insta-story__info {display: flex;align-items: center;padding-top: 1rem;--pfp-size: 4rem;}.insta-story__pfp {width: var(--pfp-size);height: var(--pfp-size);}.insta-story__info__text, .insta-story__info__text .material-icon {font-size: 1.6rem;line-height: var(--pfp-size);margin: 0;}.insta-story__info__text {margin-right: auto;margin-left: 0.6em;}.insta-story__time-posted {margin-left: 0.25em;opacity: 0.8;}.insta-story__info__btn {color: #ececec;padding: 0.5rem;}.insta-story__info__volume-btn[data-muted="true"] svg:first-child {display: none;}.insta-story__info__volume-btn[data-muted="false"] svg:last-child {display: none;}.insta-story__pause-resume-btn[data-paused="true"] .material-icon:first-child {display: none;}.insta-story__pause-resume-btn[data-paused="false"] .material-icon:last-child {display: none;}.insta-story__info__close-btn {padding: 0;margin-left: 0.4rem;display: none;}.insta-story__info__close-btn .material-icon {font-size: 3.25rem;}.insta-story__invisible-btn {position: absolute;top: 0;left: 0;height: 100%;width: 15%;background: none;border: none;outline: none;opacity: 0;z-index: 1;}.insta-story__invisible-btn--next {left: auto;right: 0;}.insta-story__media {position: absolute;top: 0;left: 0;width: 100%;height: 100%;z-index: 0;background: var(--background);display: none;border-radius: 0.5rem;overflow: hidden;}.insta-story__media img, .insta-story__media video {width: 100%;height: 100%;object-fit: var(--object-fit);object-position: center center;pointer-events: none;}.active-content, .inactive-content {visibility: visible;opacity: 1;transition: opacity 0.15s;}.insta-story:not(.insta-story--active) .active-content, .insta-story--active .inactive-content {visibility: hidden;opacity: 0;}.insta-story__inactive-overlay {--pfp-size: calc(var(--story-width) * 0.3);position: absolute;top: 0;left: 0;width: 100%;height: 100%;z-index: 5;display: flex;flex-direction: column;align-items: center;justify-content: center;background: rgba(0, 0, 0, 0.75);}.insta-story__inactive-overlay .insta-story-open-btn__pfp-ring {margin-bottom: calc(var(--pfp-size) / 10);}.insta-story__inactive-overlay .insta-story__username {color: #fff;font-size: calc(var(--story-width) * 0.06);}.insta-story__arrow {width: 3rem;height: 3rem;display: grid;place-items: center;background: #fff;border-radius: 50%;position: absolute;top: 50%;transform: translate(-50%, -50%);padding: 0;box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.3);}.insta-story .insta-story__arrow {opacity: 0.8;}.insta-story__arrow .material-icon {font-size: 2.6rem;color: #000;}.insta-story__arrow[disabled] {opacity: 0.2;}.insta-story .insta-story__arrow--next {left: calc(100% + 3rem);}.insta-story__arrow--next .material-icon {transform: translateX(3%);}.insta-story .insta-story__arrow--prev {right: calc(100% + 3rem);transform: translate(50%, -50%);}.insta-story__arrow--prev .material-icon {transform: translateX(-3%);}@media screen and (max-width: 999px) {.insta-stories__slider-container {width: calc(var(--story-width) + 9rem);padding: 0 4.5rem;}.insta-stories__slider {left: 0;}}@media screen and (max-width: 749px) {.insta-stories__open-buttons-container {margin: 0 -1.5rem;}.insta-stories__open-buttons {padding: 0 1.5rem;}.insta-stories__slider-container {width: var(--story-width);padding: 0;}.insta-stories__modal {--story-height: 100vh;--story-width: 100vw;}.insta-story {transform: scale(1);}.insta-story__inactive-overlay {pointer-events: none;}.insta-story__info__close-btn {display: block;}.insta-stories__open-buttons-and-arrows-container .insta-stories__open-btns-prev {left: -1rem;}.insta-stories__open-buttons-and-arrows-container .insta-stories__open-btns-next {right: -1rem;}}.insta-stories__slider--paused .insta-story__top {opacity: 0;}.content-tabs__header {display: flex;flex-direction: column;align-items: center;}.content-tabs__heading-content {margin-bottom: 2rem;text-align: center;}.content-tabs__heading + .rte {margin-top: 1rem;}.content-tabs__buttons {flex-wrap: wrap;gap: 0.5em;flex-shrink: 0;background: none;position: relative;z-index: 0;}.content-tabs__buttons--outlined {--padding: 0.4rem;padding: var(--padding);border: solid 0.1rem rgb(var(--color-background));}.content-tabs__buttons--shadow {box-shadow: 0 0.3em 1em rgba(0, 0, 0, 0.15);}.content-tab-button {font-size: inherit;padding: 0 1.25em;line-height: 2.5em;flex-shrink: 0;color: rgb(var(--color-background));background: none;transition: background 0.3s, color 0.3s;cursor: pointer;z-index: 1;}.content-tab-button--active {color: rgb(var(--color-foreground));}.content-tabs__buttons--animation-opacity .content-tab-button--active {background: rgb(var(--color-background));transition-delay: 0.1s;}.content-tab-button .material-icon + span {margin-left: 0.25em;}.content-tab-buttom__active-bg {position: absolute;z-index: 0;width: 0;background: rgb(var(--color-background));transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);}.content-tabs__buttons--animation-moving .content-tab-button {transition-duration: 0.4s;transition-delay: 0s;}.content-tabs__tabs {position: relative;z-index: 0;overflow: hidden;transition: height 0.3s ease-in-out;margin-top: 2rem;border-radius: 0.5rem;}.content-tab {position: absolute;top: 0;left: 0;opacity: 0;visibility: hidden;transition: opacity 0.3s ease-in-out;z-index: -1;width: 100%;padding: 2rem;display: flex;flex-direction: column;align-items: center;row-gap: 3rem;}.content-tab--active {position: relative;opacity: 1;visibility: visible;z-index: 0;transition-delay: 0.2s;}.content-tab__media, .content-tab__content {width: 100%;}@media screen and (min-width: 750px) {.content-tabs__header--horizontal {flex-direction: row;justify-content: space-between;flex-wrap: wrap;}.content-tabs__heading-content {margin-bottom: 3rem;}.content-tabs__header--horizontal .content-tabs__heading-content {margin-bottom: 0;text-align: left;}.content-tabs__header--horizontal .content-tabs__heading {line-height: 1.3;}.content-tab-buttons--desktop-expand .content-tabs__buttons {width: 100%;}.content-tab-buttons--desktop-expand .content-tab-button {flex-grow: 1;}.content-tab--2-column {display: grid;grid-template-columns: repeat(2, 1fr);place-items: center;column-gap: 3rem;}}@media screen and (max-width: 749px) {.content-tabs__buttons--outlined {--padding: 0.3rem;}.content-tab-buttons--mobile-expand .content-tabs__buttons {width: 100%;}.content-tab-buttons--mobile-expand .content-tab-button {flex-grow: 1;}.content-tabs__tabs {margin-top: 0.5rem;}.content-tab {padding: 1.5rem;row-gap: 2rem;}}.fb-post {background-color: rgb(var(--color-background));color: rgb(var(--color-foreground));padding: 1rem 1.5rem;border-radius: 1rem;box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.15);}.facebook-testimonials .splide__slide__container {padding: 4px 8px 8px 8px;}.fb-post p {margin: 0;line-height: 1.25;}.fb-post__profile-picture {width: 4rem;height: 4rem;}.fb-post__top, .fb-post__content__text, .fb-post__content .media {margin-bottom: 1rem;}.fb-post__top__info {padding: 0 1rem;}.fb-post__author {font-size: 1.5rem;}.fb-post__time {font-size: 1.3rem;}.fb-post__content__text p {line-height: 1.33;}.fb-post__content .media {margin-left: -1.5rem;margin-right: -1.5rem;}.fb-post__reactions__icons svg {width: 17px;height: 17px;}.fb-post__reactions__text, .fb-post__reactions__comments-text {padding-left: 0.3em;}.fb-post__ctas {display: grid;grid-template-columns: repeat(3, 1fr);place-items: center;border-top: solid 1px var(--separator-color);padding-top: 0.5rem;margin-top: 1rem;}.fb-post__ctas svg {margin-right: 0.4rem;width: 1.8rem;}.fb-post__ctas + .fb-post__comment {margin-top: 0.5rem;border-top: solid 1px var(--separator-color);}.fb-post__comment {padding-top: 1.5rem;}.fb-post__comment__avatar {width: 3.4rem;height: 3.4rem;margin-right: 0.8rem;}.fb-post__comment__box {background: var(--comment-bg-color);border-radius: 1rem;padding: 0.5rem 0.8rem;}.fb-post__comment__ctas {font-size: 1rem;margin-top: 0.3rem;overflow: hidden;}.fb-post__comment__ctas span + span {margin-left: 0.6rem;}.fb-post .verified-icon {width: 0.9em;height: 0.9em;margin-left: 0.2em;}@media screen and (min-width: 1000px) {.fb-post__author {font-size: 1.6rem;}}.trustpilot-reviews__subheading {column-gap: 0.3em;flex-wrap: wrap;}.trustpilot-reviews__subheading--body {font-family: var(--font-body-family);font-style: var(--font-body-style);font-weight: var(--font-body-weight);}.trustpilot-reviews__subheading .trustpilot-stars-svg {height: 1em;}.title-with-highlight + .trustpilot-reviews__subheading {margin-top: 1rem;}.trustpilot-review .rating-stars__container--underlay {width: fit-content;}.trustpilot-review.center .testimonial-card__author-container {justify-content: center;}.trustpilot-review.center .rating-stars__container--underlay {margin-left: auto;margin-right: auto;}.trustpilot-review {padding: 2rem;max-width: 45rem;margin-left: auto;margin-right: auto;}.trustpilot-review .trustpilot-stars-svg {height: 2.3rem;}.trustpilot-reviews__content-container + .trustpilot__reviews-container {margin-top: 2.5rem;}@media screen and (max-width: 749px) {}@media screen and (min-width: 750px) {.desktop-left .trustpilot-reviews__subheading {justify-content: flex-start;}.desktop-right .trustpilot-reviews__subheading {justify-content: flex-end;}}@media screen and (min-width: 1200px) {.trustpilot-reviews--desktop-content-left, .trustpilot-reviews--desktop-content-right {display: flex;align-items: center;column-gap: 5rem;}.trustpilot-reviews--desktop-content-right {flex-direction: row-reverse;}.trustpilot-reviews--desktop-content-left .trustpilot__reviews-container, .trustpilot-reviews--desktop-content-right .trustpilot__reviews-container {flex-grow: 1;}.trustpilot-reviews--desktop-content-left .trustpilot-reviews__content-container, .trustpilot-reviews--desktop-content-right .trustpilot-reviews__content-container {flex-shrink: 0;max-width: 50rem;}.trustpilot-reviews__content-container + .trustpilot__reviews-container {margin-top: 0;}.trustpilot-reviews--desktop-content-above .trustpilot-reviews__content-container + .trustpilot__reviews-container {margin-top: 3.5rem;}}.hotspots-image {position: relative;}.hotspots-image-container {display: flex;justify-content: center;margin: 0 auto;position: relative;z-index: 1;}.hotspots-image-container--limited .hotspots-image {max-width: var(--max-width);width: 100%;}.hotspots-image__image .media {width: 100%;}.hotspots-image .placeholder {vertical-align: bottom;}.hotspot {--btn-size: 4rem;--carret-height: 0.5rem;}.hotspot-btn {display: block;position: absolute;top: var(--offset-y);left: var(--offset-x);transform: translate(-50%, -50%);border-radius: 50%;display: flex;align-items: center;justify-content: center;width: var(--btn-size);height: var(--btn-size);z-index: 0;}.hotspot-btn .material-icon {font-size: calc(var(--btn-size) * 0.75);pointer-events: none;transition: transform 0.1s ease-in-out;flex-shrink: 0;}.hotspot[data-open="true"] .material-icon {transform: rotate(135deg);}.hotspot__content {position: absolute;top: var(--offset-y);transform: translate( -50%, calc(-100% - (var(--btn-size) / 2) - var(--carret-height)) );box-shadow: 4px 4px 15px rgba(0, 0, 0, 0.3);opacity: 0;pointer-events: none;transition: opacity 0.1s ease-in-out, transform 0s;z-index: 1;border-radius: 0.3rem;overflow: hidden;}.hotspot[data-open="true"] .hotspot__content {opacity: 1;pointer-events: auto;}.hotspot__content img {width: 100%;}.hotspot__content__title-and_text {padding: 1.5rem 1.5rem;word-break: break-word;}.hotspot__content__title {margin: 0;font-size: 2rem;line-height: 1.3;}.hotspot__content__text {line-height: 1.3;}.hotspot__content__title-and_text > * + * {margin-top: 1rem;}.hotspot[data-direction="bottom"] .hotspot__content {transform: translate( -50%, calc((var(--btn-size) / 2) + var(--carret-height)) );}.hotspot__content__image-and-text--small-image {display: flex;align-items: flex-start;}.hotspot__content__image-and-text--small-image .hotspot__content__image {max-width: 25%;padding: 1.25rem;padding-right: 0.25rem;flex-shrink: 0;}.hotspot__content__image-and-text--small-image .hotspot__content__title-and_text {padding: 1rem 1.25rem 1rem 1.25rem;flex-grow: 1;}.hotspot__content__image-and-text--small-image .hotspot__content__title-and_text > * + * {margin-top: 0.5rem;}.hotspot__content__btn {padding: 1.5rem;border-top: solid 0.2rem rgba(var(--color-foreground), 0.1);}.hotspot__content__btn .button {font-size: 1.7rem;}.hotspot-overlay {display: none;}.hotspot__content .price {margin-bottom: 0;margin-top: 0.3rem;}.hotspot__content .price--on-sale.price--show-badge {margin-top: 0.75rem;}.hotspot__content .price-item--regular {font-size: 2rem;}@media screen and (min-width: 1000px) {.hotspots-image .placeholder {height: calc(var(--max-width) * 9 / 16);width: 100%;}}@media screen and (min-width: 750px) {.image--corner-radius img {border-radius: 3rem;}.hotspots-image-container--content-top.hotspots-image--different-bg .image--corner-radius img {border-radius: 0 0 3rem 3rem;}.hotspots-image-container--content-right.hotspots-image--different-bg .image--corner-radius img {border-radius: 3rem 0 0 3rem;}.hotspots-image-container--content-bottom.hotspots-image--different-bg .image--corner-radius img {border-radius: 3rem 3rem 0 0;}.hotspots-image-container--content-left.hotspots-image--different-bg .image--corner-radius img {border-radius: 0 3rem 3rem 0;}.hotspots-image-container.hotspots-image-container--content-empty .image--corner-radius img {border-radius: 3rem;}.hotspots-image-container--content-top {flex-direction: column;align-items: center;}.hotspots-image-container--content-top.hotspots-image--different-bg .hotspots-image__content.content--border-radius {border-radius: 3rem 3rem 0 0;}.hotspots-image-container--content-right {flex-direction: row-reverse;justify-content: center;}.hotspots-image-container--content-right.hotspots-image--different-bg .hotspots-image__content.content--border-radius {border-radius: 0 3rem 3rem 0;}.hotspots-image-container--content-bottom {flex-direction: column-reverse;align-items: center;}.hotspots-image-container--content-bottom.hotspots-image--different-bg .hotspots-image__content.content--border-radius {border-radius: 0 0 3rem 3rem;}.hotspots-image-container--content-left {flex-direction: row;justify-content: center;}.hotspots-image-container--content-left.hotspots-image--different-bg .hotspots-image__content.content--border-radius {border-radius: 3rem 0 0 3rem;}.hotspots-image__content {padding: 3rem;}.hotspots-image-container--content-right .hotspots-image__content, .hotspots-image-container--content-left .hotspots-image__content {display: flex;flex-direction: column;justify-content: center;min-width: 35rem;flex: 1;max-width: 60rem;}.hotspots-image--same-bg.hotspots-image-container--content-top .hotspots-image__content {padding-top: 0;padding-bottom: 4rem;}.hotspots-image--same-bg.hotspots-image-container--content-bottom .hotspots-image__content {padding-bottom: 0;padding-top: 4rem;}.hotspots-image-container--limited.hotspots-image-container--content-bottom .hotspots-image__content, .hotspots-image-container--limited.hotspots-image-container--content-top .hotspots-image__content {max-width: var(--max-width);width: 100%;}}@media screen and (max-width: 749px) {.hotspot__content__title {font-size: 1.6rem;}.hotspot__content__title-and_text, .hotspot__content__btn {padding: 1rem;}.hotspot__content__text {font-size: 1.3rem;}.hotspot__content .price {margin-top: 0;}.hotspot__content .price--on-sale.price--show-badge {margin-top: 0.5rem;}.hotspot__content .price-item--regular {font-size: 1.8rem;}.hotspot__content__btn .button {font-size: 1.5rem;min-height: 3.8rem;padding: 0 1.25rem;}.hotspot__content__title-and_text > * + * {margin-top: 0.5rem;}.image--corner-radius img {border-radius: 2rem;}.hotspots-image-container--mobile-content-top.hotspots-image--different-bg .image--corner-radius img {border-radius: 0 0 2rem 2rem;}.hotspots-image-container--mobile-content-bottom.hotspots-image--different-bg .image--corner-radius img {border-radius: 2em 2rem 0 0;}.hotspots-image-container.hotspots-image-container--content-empty .image--corner-radius img {border-radius: 2rem;}.hotspot {--btn-size: 3rem;}.hotspot--mobile-position {--offset-x: var(--mobile-offset-x) !important;--offset-y: var(--mobile-offset-y) !important;}.hotspots-image--different-bg .hotspots-image__content {padding: 2rem;}.hotspots-image-container--mobile-content-top {flex-direction: column;}.hotspots-image-container--mobile-content-top.hotspots-image--different-bg .hotspots-image__content.content--border-radius {border-radius: 2rem 2rem 0 0;}.hotspots-image-container--mobile-content-bottom {flex-direction: column-reverse;}.hotspots-image-container--mobile-content-bottom.hotspots-image--different-bg .hotspots-image__content.content--border-radius {border-radius: 0 0 2rem 2rem;}.hotspots-image--same-bg.hotspots-image-container--mobile-content-top .hotspots-image__content {padding: 0 0 2.5rem;}.hotspots-image--same-bg.hotspots-image-container--mobile-content-bottom .hotspots-image__content {padding: 2.5rem 0 0;}.hotspot--fixed .hotspot-overlay {display: block;visibility: hidden;opacity: 0;width: 0;height: 0;transition: opacity 0.1s ease-in-out, width 0s, height 0s;}.hotspot--fixed[data-open="true"] .hotspot-overlay {z-index: 5;position: fixed;top: 0;left: 0;width: 100vw;height: 100vh;background: rgba(0, 0, 0, 0.15);visibility: visible;opacity: 1;}.hotspot--fixed[data-open="true"] .hotspot__content {z-index: 6;}}.hotspots-image-container--content-empty .hotspots-image__content {display: none;}.bullet-point {--bullet-size: 1.5rem;--line-width: 0.3rem;--line-square-size: 4rem;--line-square-diagonal: calc(var(--line-square-size) * 1.41);--text-padding: 1rem;background: none;color: rgb(var(--color-background));position: absolute;top: var(--offset-y);background: rgb(var(--color-background));height: var(--line-width);transform: translate(var(--line-square-size), -50%);left: var(--offset-x);text-align: right;max-width: calc(var(--desktop-width) - var(--line-square-size));width: 100%;}.bullet-point__line-end {content: "";display: block;background: rgb(var(--color-background));height: var(--line-width);width: var(--line-square-size);transform-origin: top right;position: absolute;right: 100%;top: 0;}.bullet-point__line-end::after {content: "";display: block;background: rgb(var(--color-background));height: var(--bullet-size);width: var(--bullet-size);position: absolute;left: 0;top: calc(var(--line-width) / 2);transform: translate(-50%, -50%);border-radius: 50%;}.bullet-point[data-line-bend="bottom"] {top: calc(var(--offset-y) - var(--line-square-size));}.bullet-point[data-line-bend="top"] {top: calc(var(--offset-y) + var(--line-square-size));}.bullet-point[data-line-bend="bottom"] .bullet-point__line-end {width: var(--line-square-diagonal);transform: rotate(315deg);}.bullet-point[data-line-bend="top"] .bullet-point__line-end {width: var(--line-square-diagonal);transform: rotate(45deg);}.bullet-point[data-direction="left"] {transform: translate( calc(-100% - var(--line-square-size) - (var(--line-width) / 2)), -50% );text-align: left;}.bullet-point[data-direction="left"] .bullet-point__line-end {left: 100%;transform-origin: top left;}.bullet-point[data-direction="left"] .bullet-point__line-end::after {left: 100%;}.bullet-point[data-line-bend="bottom"][data-direction="left"] .bullet-point__line-end {transform: rotate(45deg);}.bullet-point[data-line-bend="top"][data-direction="left"] .bullet-point__line-end {transform: rotate(-45deg);}.bullet-point__title {margin: 0;font-size: 2rem;line-height: 1.3;position: absolute;top: -0.2em;left: 0;width: 100%;max-width: 100%;transform: translateY(-100%);color: rgb(var(--color-background));}.bullet-point__text {position: absolute;top: 1rem;left: 0;width: 100%;max-width: 100%;line-height: 1.3;}.bullet-point[data-direction="left"] .bullet-point__title, .bullet-point[data-direction="left"] .bullet-point__text {padding-right: var(--text-padding);}.bullet-point[data-direction="right"] .bullet-point__title, .bullet-point[data-direction="right"] .bullet-point__text {padding-left: var(--text-padding);}.bullet-point[data-contain="true"][data-direction="right"] {max-width: min( var(--desktop-width), calc(100% - var(--offset-x) - var(--line-square-size) - 1rem) );}.bullet-point[data-contain="true"][data-direction="left"] {max-width: min( var(--desktop-width), calc(var(--offset-x) - var(--line-square-size) - 1rem) );}@media screen and (max-width: 749px) {.bullet-point__title {font-size: 1.5rem;}.bullet-point__text {font-size: 1.2rem;}.bullet-point--mobile-position {--offset-x: var(--mobile-offset-x) !important;--offset-y: var(--mobile-offset-y) !important;}.bullet-point {--bullet-size: 1.25rem;--line-width: 0.3rem;--line-square-size: 3rem;--text-padding: 0.5rem;max-width: min( var(--mobile-width), calc(100% - var(--offset-x) - var(--line-square-size) - 1rem) );}.bullet-point[data-direction="left"] {max-width: min( var(--mobile-width), calc(var(--offset-x) - var(--line-square-size) - 1rem) );}.bullet-point--mobile-position[data-mobile-line-bend="none"] {top: var(--offset-y);}.bullet-point--mobile-position[data-mobile-line-bend="bottom"] {top: calc(var(--offset-y) - var(--line-square-size));}.bullet-point--mobile-position[data-mobile-line-bend="top"] {top: calc(var(--offset-y) + var(--line-square-size));}.bullet-point--mobile-position[data-mobile-direction="right"] {transform: translate(var(--line-square-size), -50%);text-align: right;max-width: min( var(--mobile-width), calc(100% - var(--offset-x) - var(--line-square-size) -1rem) );}.bullet-point--mobile-position[data-mobile-direction="left"] {transform: translate( calc(-100% - var(--line-square-size) - (var(--line-width) / 2)), -50% );text-align: left;max-width: min( var(--mobile-width), calc(var(--offset-x) - var(--line-square-size) - 1rem) );}.bullet-point--mobile-position[data-mobile-direction="right"] .bullet-point__line-end {transform-origin: top right;left: auto;right: 100%;}.bullet-point--mobile-position[data-mobile-direction="right"] .bullet-point__line-end::after {left: 0;right: auto;}.bullet-point--mobile-position[data-mobile-line-bend="none"] .bullet-point__line-end {transform: none;}.bullet-point--mobile-position[data-mobile-line-bend="bottom"][data-mobile-direction="right"] .bullet-point__line-end {transform: rotate(315deg);}.bullet-point--mobile-position[data-mobile-line-bend="top"][data-mobile-direction="right"] .bullet-point__line-end {transform: rotate(45deg);}.bullet-point--mobile-position[data-mobile-direction="left"] .bullet-point__line-end {left: 100%;right: auto;transform-origin: top left;}.bullet-point--mobile-position[data-mobile-direction="left"] .bullet-point__line-end::after {left: 100%;right: auto;}.bullet-point--mobile-position[data-mobile-line-bend="bottom"][data-mobile-direction="left"] .bullet-point__line-end {transform: rotate(45deg);}.bullet-point--mobile-position[data-mobile-line-bend="top"][data-mobile-direction="left"] .bullet-point__line-end {transform: rotate(-45deg);}.bullet-point--mobile-position[data-mobile-direction="left"] .bullet-point__title, .bullet-point--mobile-position[data-mobile-direction="left"] .bullet-point__text {padding-right: var(--text-padding);padding-left: 0;}.bullet-point--mobile-position[data-mobile-direction="right"] .bullet-point__title, .bullet-point--mobile-position[data-mobile-direction="right"] .bullet-point__text {padding-left: var(--text-padding);padding-right: 0;}}.collection-hero__inner {display: flex;flex-direction: column;}.collection-hero--with-image .collection-hero__inner {margin-bottom: 0;padding-bottom: 2rem;}@media screen and (min-width: 750px) {.collection-hero.collection-hero--with-image {padding: calc(4rem + var(--page-width-margin)) 0 calc(4rem + var(--page-width-margin));overflow: hidden;}.collection-hero--with-image .collection-hero__inner {padding-bottom: 0;}}.collection-hero__text-wrapper {flex-basis: 100%;}@media screen and (min-width: 750px) {.collection-hero {padding: 0;}.collection-hero__inner {align-items: center;flex-direction: row;padding-bottom: 0;}}.collection-hero__title {margin: 2.5rem 0;}.collection-hero__title + .collection-hero__description {margin-top: 1.5rem;margin-bottom: 1.5rem;font-size: 1.6rem;line-height: calc(1 + 0.5 / var(--font-body-scale));}@media screen and (min-width: 750px) {.collection-hero__title + .collection-hero__description {font-size: 1.8rem;margin-top: 2rem;margin-bottom: 2rem;}.collection-hero__description {max-width: 66.67%;margin-left: auto;margin-right: auto;}.collection-hero--with-image .collection-hero__description {max-width: 100%;}}.collection-hero--with-image .collection-hero__title {margin: 0;}.collection-hero--with-image .collection-hero__text-wrapper {padding: 5rem 0 4rem;}.collection-hero__image-container {border: var(--media-border-width) solid rgba(var(--color-foreground), var(--media-border-opacity));border-radius: var(--media-radius);box-shadow: var(--media-shadow-horizontal-offset) var(--media-shadow-vertical-offset) var(--media-shadow-blur-radius) rgba(var(--color-shadow), var(--media-shadow-opacity));}@media screen and (max-width: 749px) {.collection-hero__image-container {height: 20rem;}}@media screen and (min-width: 750px) {.collection-hero--with-image .collection-hero__text-wrapper {padding: 4rem 2rem 4rem 0;flex-basis: 50%;}.collection-hero__image-container {align-self: stretch;flex: 1 0 50%;margin-left: 3rem;min-height: 20rem;}}.card-wrapper {color: inherit;height: 100%;position: relative;text-decoration: none;}.card {text-decoration: none;text-align: var(--text-alignment);}.card:not(.ratio) {display: flex;flex-direction: column;height: 100%;}.card.card--horizontal {--text-alignment: left;--image-padding: 0rem;flex-direction: row;align-items: flex-start;gap: 1.5rem;}.card--horizontal.ratio:before {padding-bottom: 0;}.card--card.card--horizontal {padding: 1.2rem;}.card--card.card--horizontal.card--text {column-gap: 0;}.card--card {height: 100%;}.card--card, .card--standard .card__inner {position: relative;box-sizing: border-box;border-radius: var(--border-radius);border: var(--border-width) solid rgba(var(--color-foreground), var(--border-opacity));}.card--card:after, .card--standard .card__inner:after {content: "";position: absolute;z-index: -1;width: calc(var(--border-width) * 2 + 100%);height: calc(var(--border-width) * 2 + 100%);top: calc(var(--border-width) * -1);left: calc(var(--border-width) * -1);border-radius: var(--border-radius);box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius) rgba(var(--color-shadow), var(--shadow-opacity));}.card .card__inner .card__media {overflow: hidden;z-index: 0;border-radius: calc( var(--border-radius) - var(--border-width) - var(--image-padding) );}.card--card .card__inner .card__media {border-bottom-right-radius: 0;border-bottom-left-radius: 0;}.card--standard.card--text {background-color: transparent;}.card-information {text-align: var(--text-alignment);}.card-information .price {display: block;}.card__media, .card .media {bottom: 0;position: absolute;top: 0;}.card .media {width: 100%;}.card__media {margin: var(--image-padding);width: calc(100% - 2 * var(--image-padding));}.card--standard .card__media {margin: var(--image-padding);}.card__inner {width: 100%;}.card--media .card__inner .card__content {position: relative;padding: calc(var(--image-padding) + 1rem);}.card__content {display: grid;grid-template-rows: minmax(0, 1fr) max-content minmax(0, 1fr);padding: 1rem;width: 100%;flex-grow: 1;}.card__content--auto-margins {grid-template-rows: minmax(0, auto) max-content minmax(0, auto);}.card__information {grid-row-start: 2;padding: 1rem;}.card:not(.ratio) > .card__content {grid-template-rows: max-content minmax(0, 1fr) max-content auto;}@media screen and (min-width: 750px) {.card__information {padding-bottom: 1rem;padding-top: 1rem;}}.card__badge {align-self: flex-end;grid-row-start: 3;justify-self: flex-start;}.card__badge.top {align-self: flex-start;grid-row-start: 1;}.card__badge.right {justify-self: flex-end;}.card:not(.card--horizontal) > .card__content > .card__badge {margin: 1.3rem;}.card__media .media img {height: 100%;object-fit: cover;object-position: center center;width: 100%;}.card__inner:not(.ratio) > .card__content {height: 100%;}.card__heading {margin-top: 0;margin-bottom: 0;}.card__heading:last-child {margin-bottom: 0;}.card--horizontal .card__heading, .card--horizontal .price__container .price-item, .card--horizontal__quick-add {font-size: calc(var(--font-heading-scale) * 1.2rem);}.card--horizontal .card-information > *:not(.visually-hidden:first-child) + *:not(.rating) {margin-top: 0;}.card--horizontal__quick-add:before {box-shadow: none;}@media only screen and (min-width: 750px) {.card--horizontal .card__heading, .card--horizontal .price__container .price-item, .card--horizontal__quick-add {font-size: calc(var(--font-heading-scale) * 1.3rem);}}.card--card.card--media > .card__content {margin-top: calc(0rem - var(--image-padding));}.card--standard.card--text a::after, .card--card .card__heading a::after {bottom: calc(var(--border-width) * -1);left: calc(var(--border-width) * -1);right: calc(var(--border-width) * -1);top: calc(var(--border-width) * -1);}.card__heading a::after {bottom: 0;content: "";left: 0;position: absolute;right: 0;top: 0;z-index: 1;}.card__heading a:after {outline-offset: 0.3rem;}.card__heading a:focus:after {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);outline: 0.2rem solid rgba(var(--color-foreground), 0.5);}.card__heading a:focus-visible:after {box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);outline: 0.2rem solid rgba(var(--color-foreground), 0.5);}.card__heading a:focus:not(:focus-visible):after {box-shadow: none;outline: 0;}.card__heading a:focus {box-shadow: none;outline: 0;}@media screen and (min-width: 990px) {.card .media.media--hover-effect > img:only-child, .card-wrapper .media.media--hover-effect > img:only-child {transition: transform var(--duration-long) ease;}.card:hover .media.media--hover-effect > img:first-child:only-child, .card-wrapper:hover .media.media--hover-effect > img:first-child:only-child {transform: scale(1.03);}.card-wrapper:hover .media.media--hover-effect > img:first-child:not(:only-child) {opacity: 0;}.card-wrapper:hover .media.media--hover-effect > img + img {opacity: 1;transition: transform var(--duration-long) ease;transform: scale(1.03);}.underline-links-hover:hover a {text-decoration: underline;text-underline-offset: 0.3rem;}}.card--standard.card--media .card__inner .card__information, .card--standard.card--text:not(.card--horizontal) > .card__content .card__heading, .card--standard:not(.card--horizontal) > .card__content .card__badge, .card--standard.card--text.article-card > .card__content .card__information, .card--standard > .card__content .card__caption {display: none;}.card--standard > .card__content {padding: 0;}.card--standard > .card__content .card__information {padding-left: 0;padding-right: 0;}.card--card.card--media .card__inner .card__information, .card--card.card--text .card__inner, .card--card.card--media > .card__content .card__badge {display: none;}.card--horizontal .card__badge, .card--horizontal.card--text .card__inner {display: none;}.card--extend-height {height: 100%;}.card--extend-height.card--standard.card--text, .card--extend-height.card--media {display: flex;flex-direction: column;}.card--extend-height.card--standard.card--text .card__inner, .card--extend-height.card--media .card__inner {flex-grow: 1;}.card .icon-wrap {margin-left: 0.8rem;white-space: nowrap;transition: transform var(--duration-short) ease;overflow: hidden;}.card-information > * + * {margin-top: 0.5rem;}.card-information {width: 100%;}.card-information > * {line-height: calc(1 + 0.4 / var(--font-body-scale));color: rgb(var(--color-foreground));}.card-information > .price {color: rgb(var(--color-foreground));}.card--horizontal .card-information > .price {color: rgba(var(--color-foreground), 0.9);}.card-information > .rating {margin-top: 0.4rem;}.card-information > *:not(.visually-hidden:first-child) + *:not(.rating) {margin-top: 0.7rem;}.card-information .caption {letter-spacing: 0.07rem;}.card-article-info {margin-top: 1rem;}.card__content .product-form__input--pills {display: none;}.card__content .product-form__input {margin-bottom: 0;}.card--has-swatches.card--swatches-bottom .card__information {grid-row-start: 1;}.card--has-swatches.card--swatches-bottom .quick-add {margin-top: 1.5rem;}@media screen and (max-width: 749px) {.grid--2-col-tablet-down .card-wrapper .card__information, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .card__information {padding: 0.5rem 0 1rem;}.grid--2-col-tablet-down .card-wrapper .badge, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .badge {font-size: 1rem;}.grid--2-col-tablet-down .card-wrapper .card, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .card {--border-radius: calc(var(--product-card-corner-radius) / 2);}.grid--2-col-tablet-down .card-wrapper .card__heading, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .card__heading {font-size: 1.7rem;}.grid--2-col-tablet-down .card-wrapper .card__heading.h5, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .card__heading.h5, .grid--2-col-tablet-down .card-wrapper .price--on-sale, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .price--on-sale {font-size: 1.3rem;}.grid--2-col-tablet-down .card-wrapper .price--on-sale .price-item--regular, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .price--on-sale .price-item--regular {font-size: 1rem;}.grid--2-col-tablet-down .card-wrapper .color-swatches-container, .splide__slide__container[data-mobile-columns="2"] .card-wrapper .color-swatches-container {--transparent-border-size: 0.1rem;}.grid--2-col-tablet-down .card--has-swatches.card--swatches-bottom .quick-add, .splide__slide__container[data-mobile-columns="2"] .card--has-swatches.card--swatches-bottom .quick-add {margin-top: 1.2rem;}.grid--2-col-tablet-down .card--has-swatches.card--swatches-top .card__information, .splide__slide__container[data-mobile-columns="2"] .card--has-swatches.card--swatches-top .card__information {padding-top: 0.8rem;}}@media screen and (min-width: 750px) {.card--has-swatches.card--swatches-top .card__content, .card--has-swatches.card--swatches-top .card__information {padding-top: 1.3rem;}}.product-card-wrapper .card__badge .badge {line-height: 1.9;padding: 0 0.75em;}.card .card__badge.top.center {margin: 0;}.card__badge.top.center .badge {position: absolute;top: 0;left: 50%;transform: translate(-50%, -50%);white-space: nowrap;}.card.card--badge-sided:not(.card--horizontal) .card__badge.left {margin-left: -1rem;}.card.card--badge-sided:not(.card--horizontal) .card__badge.left .badge {border-top-left-radius: 0;border-bottom-left-radius: 0;}.card.card--badge-sided:not(.card--horizontal) .card__badge.right {margin-right: -1rem;}.card.card--badge-sided:not(.card--horizontal) .card__badge.right .badge {border-top-right-radius: 0;border-bottom-right-radius: 0;}.card--limited-title .card__heading a {display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: var(--title-lines);overflow: hidden;text-overflow: ellipsis;white-space: normal;}.pagination-wrapper {margin-top: 4rem;}@media screen and (min-width: 990px) {.pagination-wrapper {margin-top: 5rem;}}.pagination__list {display: flex;flex-wrap: wrap;justify-content: center;}.pagination__list > li {flex: 1 0 4.4rem;max-width: 4.4rem;}.pagination__list > li:not(:last-child) {margin-right: 1rem;}.pagination__item {color: rgb(var(--color-foreground));display: inline-flex;justify-content: center;align-items: center;position: relative;height: 4.4rem;width: 100%;padding: 0;text-decoration: none;}a.pagination__item:hover::after {height: 0.1rem;}.pagination__item .icon-caret {height: 0.6rem;}.pagination__item--current::after {height: 0.1rem;}.pagination__item--current::after, .pagination__item:hover::after {content: "";display: block;width: 2rem;position: absolute;bottom: 8px;left: 50%;transform: translateX(-50%);background-color: currentColor;}.pagination__item--next .icon {margin-left: -0.2rem;transform: rotate(90deg);}.pagination__item--next:hover .icon {transform: rotate(90deg) scale(1.07);}.pagination__item--prev .icon {margin-right: -0.2rem;transform: rotate(-90deg);}.pagination__item--prev:hover .icon {transform: rotate(-90deg) scale(1.07);}.pagination__item-arrow:hover::after {display: none;}.footer {border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);}.footer:not(.color-background-1) {border-top: none;}.footer__content-top {padding-bottom: 5rem;display: block;}@media screen and (max-width: 749px) {.footer .grid {display: block;}.footer-block.grid__item {padding: 0;margin: 4rem 0;width: 100%;}.footer-block.grid__item:first-child {margin-top: 0;}.footer__content-top {padding-bottom: 3rem;}}@media screen and (min-width: 750px) {.footer__content-top .grid {row-gap: 6rem;margin-bottom: 0;}}.footer__content-bottom {border-top: solid 0.1rem rgba(var(--color-foreground), 0.08);padding-top: 3rem;}.footer__content-bottom:only-child {border-top: 0;}.footer__content-bottom-wrapper {display: flex;width: 100%;justify-content: center;}@media screen and (max-width: 749px) {.footer__content-bottom {flex-wrap: wrap;padding-top: 0;padding-left: 0;padding-right: 0;row-gap: 1.5rem;}.footer__content-bottom-wrapper {flex-wrap: wrap;row-gap: 1.5rem;justify-content: center;}}.footer__localization:empty + .footer__column--info {align-items: center;}@media screen and (max-width: 749px) {.footer__localization:empty + .footer__column {padding-top: 1.5rem;}}.footer__column {width: 100%;align-items: flex-end;}.footer__column--info {display: flex;flex-direction: column;justify-content: center;align-items: center;padding-left: 2rem;padding-right: 2rem;}@media screen and (min-width: 750px) {.footer__column--info {padding-left: 0;padding-right: 0;align-items: flex-end;}}.footer-block:only-child:last-child {text-align: center;max-width: 76rem;margin: 0 auto;}@media screen and (min-width: 750px) {.footer-block {display: block;margin-top: 0;}}.footer-block:empty {display: none;}.footer-block--newsletter {display: flex;align-items: flex-end;margin-top: 3rem;gap: 1rem;}.footer-block--newsletter:only-child {margin-top: 0;}@media screen and (max-width: 749px) {.footer-block.footer-block--menu:only-child {text-align: left;}}@media screen and (min-width: 750px) {.footer-block--newsletter {flex-wrap: nowrap;justify-content: center;}}.footer-block__heading {margin-bottom: 2rem;margin-top: 0;font-size: calc(var(--font-heading-scale) * 1.6rem);}@media screen and (min-width: 990px) {.footer-block__heading {font-size: calc(var(--font-heading-scale) * 1.8rem);}}.footer__list-social:empty, .footer-block--newsletter:empty {display: none;}.footer__follow-on-shop {display: flex;text-align: center;}.footer__list-social.list-social:only-child {justify-content: center;}.footer-block__newsletter {text-align: center;flex-grow: 1;}@media screen and (min-width: 750px) {.footer-block__newsletter:not(:only-child) {text-align: left;margin-right: auto;}.footer-block__newsletter:not(:only-child) .footer__newsletter {justify-content: flex-start;margin: 0;}.footer-block__newsletter:not(:only-child) .newsletter-form__message--success {left: auto;}.footer__follow-on-shop {margin-bottom: 0.4rem;}.footer__follow-on-shop:first-child:not(:last-child) {justify-content: flex-start;margin-right: auto;text-align: left;}.footer__follow-on-shop:not(:first-child):not(:last-child) {justify-content: flex-end;text-align: right;}}@media screen and (max-width: 749px) {.footer-block--newsletter {display: flex;flex-direction: column;flex: 1 1 100%;align-items: center;gap: 3rem;}.footer__list-social.list-social, .footer__follow-on-shop, .footer-block__newsletter {display: flex;justify-content: center;}.footer-block__newsletter {flex-direction: column;}}@media screen and (min-width: 750px) {.footer-block__newsletter + .footer__list-social {margin-top: 0;}}.footer__localization {display: flex;flex-direction: row;justify-content: center;align-content: center;flex-wrap: wrap;padding: 1rem 1rem 0;}.footer__localization:empty {display: none;}.localization-form {display: flex;flex-direction: column;flex: auto 1 0;padding: 1rem;margin: 0 auto;}.localization-form:only-child {display: inline-flex;flex-wrap: wrap;flex: initial;padding: 1rem 0;}.localization-form:only-child .button, .localization-form:only-child .localization-form__select {margin: 1rem 1rem 0.5rem;flex-grow: 1;}.footer__localization h2 {margin: 1rem 1rem 0.5rem;color: rgba(var(--color-foreground), 0.9);}@media screen and (min-width: 750px) {.footer__localization {padding: 0.4rem 0;justify-content: flex-start;}.localization-form {padding: 1rem 2rem 1rem 0;}.localization-form:first-of-type {padding-left: 0;}.localization-form:only-child {justify-content: start;width: auto;margin: 0 1rem 0 0;}.localization-form:only-child .button, .localization-form:only-child .localization-form__select {margin: 1rem 0;}.footer__localization h2 {margin: 1rem 0 0;}}@media screen and (max-width: 989px) {noscript .localization-form:only-child, .footer__localization noscript {width: 100%;}}.localization-form .button {padding: 1rem;}.localization-form__currency {display: inline-block;}@media screen and (max-width: 749px) {.localization-form .button {word-break: break-all;}}.localization-form__select {border-radius: var(--inputs-radius-outset);position: relative;margin-bottom: 1.5rem;padding-left: 1rem;text-align: left;min-height: calc(4rem + var(--inputs-border-width) * 2);min-width: calc(7rem + var(--inputs-border-width) * 2);}.disclosure__button.localization-form__select {padding: calc(2rem + var(--inputs-border-width));background: rgb(var(--color-background));}noscript .localization-form__select {padding-left: 0rem;}@media screen and (min-width: 750px) {noscript .localization-form__select {min-width: 20rem;}}.localization-form__select .icon-caret {position: absolute;content: "";height: 0.6rem;right: calc(var(--inputs-border-width) + 1.5rem);top: calc(50% - 0.2rem);}.localization-selector.link {text-decoration: none;appearance: none;-webkit-appearance: none;-moz-appearance: none;color: rgb(var(--color-foreground));width: 100%;padding-right: 4rem;padding-bottom: 1.5rem;}noscript .localization-selector.link {padding-top: 1.5rem;padding-left: 1.5rem;}.disclosure .localization-form__select {padding-top: 1.5rem;}.localization-selector option {color: #000000;}.localization-selector + .disclosure__list-wrapper {margin-left: 1rem;opacity: 1;animation: animateLocalization var(--duration-default) ease;}@media screen and (min-width: 750px) {.footer__payment {margin-top: 1.5rem;}}.footer__content-bottom-wrapper--center {justify-content: center;}.footer__copyright {text-align: center;margin-top: 1.5rem;}@media screen and (min-width: 750px) {.footer__content-bottom-wrapper:not(.footer__content-bottom-wrapper--center) .footer__copyright {text-align: right;}}@keyframes appear-down {0% {opacity: 0;margin-top: -1rem;}100% {opacity: 1;margin-top: 0;}}.footer-block__details-content {margin-bottom: 4rem;}@media screen and (min-width: 750px) {.footer-block__details-content {margin-bottom: 0;}.footer-block__details-content > p, .footer-block__details-content > li {padding: 0;}.footer-block:only-child li {display: inline;}.footer-block__details-content > li:not(:last-child) {margin-right: 1.5rem;}}.footer-block__details-content .list-menu__item--link, .copyright__content a {color: rgba(var(--color-foreground), 0.9);}.footer-block__details-content .list-menu__item--active {transition: text-decoration-thickness var(--duration-short) ease;color: rgb(var(--color-foreground));}@media screen and (min-width: 750px) {.footer-block__details-content .list-menu__item--link:hover, .copyright__content a:hover {color: rgb(var(--color-foreground));text-decoration: underline;text-underline-offset: 0.3rem;}.footer-block__details-content .list-menu__item--active:hover {text-decoration-thickness: 0.2rem;}}@media screen and (max-width: 989px) {.footer-block__details-content .list-menu__item--link {padding-top: 1rem;padding-bottom: 1rem;}}@media screen and (min-width: 750px) {.footer-block__details-content .list-menu__item--link {display: inline-block;font-size: 1.4rem;}.footer-block__details-content > :first-child .list-menu__item--link {padding-top: 0;}}.footer-block-image {display: flex;}.footer-block-image.left {justify-content: flex-start;}.footer-block-image.center {justify-content: center;}.footer-block-image.right {justify-content: flex-end;}@media screen and (max-width: 749px) {.footer-block-image, .footer-block-image.left, .footer-block-image.center, .footer-block-image.right {justify-content: center;}}.footer-block__image-wrapper {margin-bottom: max( calc( 2rem + var(--media-shadow-vertical-offset) * var(--media-shadow-visible) ), 2rem );overflow: hidden !important;background: none;}.footer-block__image-wrapper img {display: block;}.footer-block__brand-info {text-align: left;}.footer-block:only-child .footer-block__brand-info {text-align: center;}.footer-block:only-child > .footer-block__brand-info > .footer-block__image-wrapper {margin-left: auto;margin-right: auto;}.footer-block-image > img, .footer-block__brand-info > img {height: auto;}.footer-block:only-child .footer-block__brand-info .footer__list-social.list-social {justify-content: center;}.footer-block__brand-info .footer__list-social.list-social {justify-content: flex-start;margin-left: -1.3rem;margin-right: -1.3rem;}.footer-block__details-content .placeholder-svg {max-width: 20rem;}.copyright__content {font-size: 1.1rem;}.copyright__content a {color: currentColor;text-decoration: none;}.policies {display: inline;}.policies li {display: inline-flex;justify-content: center;align-items: center;}.policies li::before {content: "\00B7";padding: 0 0.8rem;}.policies li a {padding: 0.6rem 0;display: block;}@media screen and (min-width: 750px) {.policies li a {padding: 0;}}@keyframes animateLocalization {0% {opacity: 0;transform: translateY(0);}100% {opacity: 1;transform: translateY(-1rem);}}.footer .disclosure__link {padding: 0.95rem 3.5rem 0.95rem 2rem;color: rgba(var(--color-foreground), 0.9);}.footer .disclosure__link:hover {color: rgb(var(--color-foreground));}.footer .disclosure__link--active {text-decoration: underline;}@supports not (inset: 10px) {@media screen and (max-width: 749px) {.footer .grid {margin-left: 0;}}@media screen and (min-width: 750px) {.footer__content-top .grid {margin-left: -3rem;}.footer__content-top .grid__item {padding-left: 3rem;}}}.footer .footer-block__details-content.footer-block__details-content-mb p {margin-bottom: 1em;}.footer .footer__newsletter, .footer .newsletter-form__field-wrapper {max-width: none;}@media screen and (min-width: 750px) {.footer__content-top .grid {column-gap: 0;margin-left: calc((var(--grid-desktop-horizontal-spacing) / 2) * -1);margin-right: calc((var(--grid-desktop-horizontal-spacing) / 2) * -1);}.footer-block.grid__item {margin: 0 calc(var(--grid-desktop-horizontal-spacing) / 2);flex-shrink: 0;flex-grow: 0;max-width: none;}.footer-block.footer-block--desktop-12 {width: calc(100% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-11 {width: calc(91.63% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-10 {width: calc(83.33% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-9 {width: calc(75% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-6 {width: calc(50% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-5 {width: calc(41.6% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-4 {width: calc(33.3% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-3 {width: calc(25% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-2 {width: calc(16.66% - var(--grid-desktop-horizontal-spacing));}.footer-block.footer-block--desktop-1 {width: calc(8.33% - var(--grid-desktop-horizontal-spacing));}}@media screen and (max-width: 749px) {.footer__content-top .grid {column-gap: var(--grid-mobile-horizontal-spacing);row-gap: 0;display: flex;margin: 0;}.footer-block.grid__item {margin: 0;flex-shrink: 0;flex-grow: 0;max-width: none;margin-bottom: 4rem;}.footer-block.grid__item:last-child {margin-bottom: 0;}.footer-block__details-content {margin-bottom: 0;}.footer-block.footer-block--mobile-1 {width: calc(50% - (var(--grid-mobile-horizontal-spacing) / 2));}.footer-block.footer-block--mobile-2 {width: 100%;}.footer-block__details-content .list-menu__item--link {padding-top: 0.75rem;padding-bottom: 0.75rem;}.footer_card--half-width .card__information, .footer-block--mobile-1 .card__information {padding: 0;}.footer_card--half-width {max-width: 50%;}}
}