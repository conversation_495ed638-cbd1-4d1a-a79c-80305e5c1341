{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width section-{{ section.id }}-padding">
    <div class="content-and-comparison-slider section-group__container__child-grid{% if section.settings.layout == 'slider_first' %} content-and-comparison-slider--slider-first{% endif %}{% if section.settings.title == blank and section.settings.text == blank %} content-and-comparison-slider--no-content{% endif %}">
      {% assign content_index = 0 %}
      <div class="content-container content-rte center animate-item animate-item--child index-0{% if section.settings.layout == 'slider_first' %} desktop-index-1{% endif %}">
        {%- unless section.settings.title == blank -%}
          {% assign content_index = 1 %}
          <h2 class="{{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
            {{ section.settings.title }}
          </h2>
        {%- endunless -%}
        {%- unless section.settings.text == blank -%}
          {% assign content_index = 1 %}
          <div class='rte'>
            {{ section.settings.text }}
          </div>
        {%- endunless -%}
        {%- if section.settings.button_label != blank -%}
          <a
            {% if section.settings.link %}
              href="{{ section.settings.link }}"
            {% else %}
              role="link" aria-disabled="true"
            {% endif %}
            class="button mb-1em {% if section.settings.button_style_secondary %}button--secondary{% else %}button--primary{% endif %}"
          >
            {{- section.settings.button_label | escape -}}
          </a>
        {%- endif -%}
        {%- if section.settings.atc_button_label != blank -%}
          {% if section.settings.atc_product == blank %}
            <button
              id="SectionAtcBtn-{{ section.id }}"
              type="button"
              class="button mb-1em main-product-atc button--has-spinner"
              {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                disabled
              {% endif %}
            >
              {{ section.settings.atc_button_label | escape }}
              <div class="loading-overlay__spinner">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </button>
          {% else %}
            {% assign product_form_id = 'section-product-form-'
              | append: section.id
            %}
            {% render 'separate-atc-btn',
              product: section.settings.atc_product,
              product_form_id: product_form_id,
              label: section.settings.atc_button_label,
              skip_cart: section.settings.atc_skip_cart
            %}
          {% endif %}
        {%- endif -%}
      </div>
      <comparison-slider class="comparison-slider animate-item animate-item--child index-{{ content_index }}{% if section.settings.layout == 'slider_first' %} desktop-index-0{% endif %}">
        {% unless section.settings.before_label == blank %}
          <div class="comparison-slider__before-text color-{{ section.settings.labels_color_scheme }}">
            <h3>{{ section.settings.before_label }}</h3>
          </div>
        {% endunless %}
        {% unless section.settings.after_label == blank %}
          <div class="comparison-slider__after-text color-{{ section.settings.labels_color_scheme }}">
            <h3>{{ section.settings.after_label }}</h3>
          </div>
        {% endunless %}
        <div class="comparison-slider__overlay{% if section.settings.before_image != blank %} color-{{ section.settings.color_scheme }}{% endif %}">
          {%- if section.settings.before_image != blank -%}
            {{ section.settings.before_image | image_url: width: 1000 | image_tag: loading: 'lazy', width: 450 }}
          {%- endif -%}
        </div>
        <div class="comparison-slider__line comparison-slider__line--{{ section.settings.arrows_style }} color-{{ section.settings.line_color }}">
          <div class="comparison-slider__arrow-left">&nbsp</div>
          <div class="comparison-slider__arrow-right">&nbsp</div>
        </div>
        <input type="range" class="comparison-slider__input" min="0" max="100" value="50">
        <div class="comparison-slider__underlay color-{{ section.settings.color_scheme }}">
          {%- if section.settings.after_image != blank -%}
            {{ section.settings.after_image | image_url: width: 1000 | image_tag: loading: 'lazy', width: 450 }}
          {%- else -%}
            {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}
        </div>
      </comparison-slider>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Before & After slider",
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Before & After slider",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "text",
      "default": "<p>Highlight key differences between before and after using your product.</p>",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.slideshow.blocks.slide.settings.button_label.label",
      "info": "t:sections.slideshow.blocks.slide.settings.button_label.info"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:sections.slideshow.blocks.slide.settings.link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
      "default": false
    },
    {
      "type": "text",
      "id": "atc_button_label",
      "label": "Add to Cart button label",
      "info": "Leave the label blank to hide the Add to Cart button."
    },
    {
      "type": "product",
      "id": "atc_product",
      "label": "ATC Custom product",
      "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
    },
    {
      "type": "checkbox",
      "id": "atc_skip_cart",
      "label": "ATC Custom product skip cart"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Slider"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "slider_first",
          "label": "Slider first"
        },
        {
          "value": "slider_second",
          "label": "Slider second"
        }
      ],
      "default": "slider_second",
      "label": "Desktop slider placement"
    },
    {
      "type": "text",
      "id": "before_label",
      "label": "Before label",
      "default": "Before"
    },
    {
      "type": "text",
      "id": "after_label",
      "label": "After label",
      "default": "After"
    },
    {
      "type": "select",
      "id": "labels_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "accent-1",
      "label": "Labels color scheme"
    },
    {
      "type": "select",
      "id": "line_color",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "background-1",
      "label": "Separator line color"
    },
    {
      "type": "select",
      "id": "arrows_style",
      "options": [
        {
          "value": "classic",
          "label": "Classic"
        },
        {
          "value": "circle",
          "label": "Circle around"
        }
      ],
      "default": "circle",
      "label": "Arrows style"
    },
    {
      "type": "image_picker",
      "id": "before_image",
      "label": "Before image"
    },
    {
      "type": "image_picker",
      "id": "after_image",
      "label": "After image"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    }
  ],
  "presets": [
    {
      "name": "Before & After slider"
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
