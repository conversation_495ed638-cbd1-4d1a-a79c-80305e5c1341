/**
 * UpCart Bundle Synchronization System
 * Integrates with UpCart cart drawer for bundle removal synchronization
 */

class UpCartBundleSync {
  constructor() {
    this.isProcessing = false;
    this.previousCartItems = [];
    this.bundlesToHide = new Set(); // Track bundles that should be hidden
    this.init();
  }

  init() {
    this.initializeCartState();

    this.waitForUpCart(() => {
      this.setupUpCartIntegration();
    });
  }

  waitForUpCart(callback, attempts = 0) {
    const maxAttempts = 20; // Wait up to 10 seconds

    // Check multiple ways UpCart might be available
    const upCartDetected = window.UpCart ||
                          window.upcart ||
                          window.upcartSubscribeItemRemoved ||
                          window.upcartSubscribeAddedToCart ||
                          document.querySelector('[data-upcart]') ||
                          document.querySelector('.upcart') ||
                          document.querySelector('#upcart') ||
                          document.querySelector('[class*="upcart"]') ||
                          document.querySelector('[id*="upcart"]');

    if (upCartDetected) {
      callback();
    } else if (attempts < maxAttempts) {
      setTimeout(() => this.waitForUpCart(callback, attempts + 1), 500);
    } else {
      this.setupFallbackIntegration();
    }
  }

  setupUpCartIntegration() {
    if (window.upcartSubscribeItemRemoved) {
      window.upcartSubscribeItemRemoved(({ item }) => {
        this.handleUpCartItemRemoved(item);
      });
    }

    if (window.upcartSubscribeAddedToCart) {
      window.upcartSubscribeAddedToCart(() => {
        // Optional: Handle bundle additions if needed
      });
    }

    document.addEventListener('aftersell-upcart:public-events:item-removed', (event) => {
      if (event.detail && event.detail.item) {
        this.handleUpCartItemRemoved(event.detail.item);
      }
    });

    document.addEventListener('cart:updated', () => {
      this.checkBundleIntegrity();
    });

    this.setupEnhancedEventDetection();
  }

  async initializeCartState() {
    try {
      const cart = await this.getCart();
      this.previousCartItems = [...cart.items];
    } catch (error) {
      // Silent error handling
    }
  }

  setupEnhancedEventDetection() {
    const upCartEvents = [
      'upcart:item:removed',
      'upcart:item:added',
      'upcart:cart:updated',
      'upcart:quantity:changed',
      'upcart:remove',
      'upcart:add'
    ];

    upCartEvents.forEach(eventName => {
      document.addEventListener(eventName, (event) => {
        if (eventName.includes('removed') || eventName.includes('remove')) {
          const item = event.detail?.item || event.detail;
          if (item) {
            this.handleUpCartItemRemoved(item);
          }
        }
      });
    });

    this.setupManualCartMonitoring();
    this.setupDirectButtonDetection();
    this.setupUpCartModification();
  }

  setupFallbackIntegration() {
    document.addEventListener('cart:updated', () => {
      this.checkBundleIntegrity();
    });

    document.addEventListener('aftersell-upcart:public-events:item-removed', (event) => {
      if (event.detail && event.detail.item) {
        this.handleUpCartItemRemoved(event.detail.item);
      }
    });

    this.setupManualCartMonitoring();
  }

  setupManualCartMonitoring() {
    const checkCartChanges = async () => {
      try {
        const cart = await this.getCart();
        const currentItems = cart.items;

        const removedItems = this.previousCartItems.filter(prevItem =>
          !currentItems.find(currItem => currItem.key === prevItem.key)
        );

        // Process removed items - group by bundle ID to avoid duplicates
        const bundleIdsToRemove = new Set();

        for (const removedItem of removedItems) {
          const bundleId = removedItem.properties['Bundle ID'] ||
                          removedItem.properties._bundle_id ||
                          removedItem.properties['_bundle_id'] ||
                          removedItem.properties['Bundle Name'];

          if (bundleId && !bundleIdsToRemove.has(bundleId)) {
            bundleIdsToRemove.add(bundleId);
            await this.removeRemainingBundleItems(bundleId);
          }
        }

        this.previousCartItems = [...currentItems];

      } catch (error) {
        // Silent error handling
      }
    };

    setInterval(checkCartChanges, 1000);

    // Initial cart state
    this.getCart().then(cart => {
      this.previousCartItems = [...cart.items];
    });
  }

  setupDirectButtonDetection() {
    document.addEventListener('click', async (event) => {
      const button = event.target.closest('button');
      if (!button) return;

      // Check if button is in cart area
      const cartArea = button.closest('.cart-drawer, #cart-drawer, [class*="cart"], [id*="cart"]');
      if (!cartArea) return;

      // Check if it looks like a remove button
      const buttonText = button.textContent?.toLowerCase() || '';
      const buttonClass = button.className?.toLowerCase() || '';
      const isRemoveButton = buttonText.includes('remove') ||
                            buttonText.includes('delete') ||
                            buttonClass.includes('remove') ||
                            buttonClass.includes('delete') ||
                            buttonClass.includes('trash') ||
                            button.querySelector('svg[class*="trash"]') ||
                            button.querySelector('[class*="trash"]');

      if (isRemoveButton) {
        const cartItem = button.closest('[data-line-item], .cart-item, [class*="cart-item"], [id*="cart-item"]');
        if (cartItem) {
          setTimeout(async () => {
            await this.checkCartForRemovals();
          }, 500);
        }
      }
    });
  }

  async checkCartForRemovals() {
    try {
      const cart = await this.getCart();

      if (this.previousCartItems) {
        const removedItems = this.previousCartItems.filter(prevItem =>
          !cart.items.find(currItem => currItem.key === prevItem.key)
        );

        for (const removedItem of removedItems) {
          const bundleId = removedItem.properties['Bundle ID'] ||
                          removedItem.properties._bundle_id ||
                          removedItem.properties['_bundle_id'] ||
                          removedItem.properties['Bundle Name'];

          if (bundleId) {
            await this.removeRemainingBundleItems(bundleId);
          }
        }

        this.previousCartItems = [...cart.items];
      }
    } catch (error) {
      // Silent error handling
    }
  }

  async handleUpCartItemRemoved(item) {
    if (this.isProcessing) return;

    const variantId = item.variant_id || item.id || item.key;

    if (!variantId) {
      return;
    }

    const bundleId = await this.getBundleIdFromUpCartItem(item);

    if (bundleId) {
      await this.removeRemainingBundleItems(bundleId);
    }
  }

  // Removed: handleRemoveButtonClick - no longer needed with UpCart events

  async getBundleIdFromUpCartItem(item) {
    // Check if bundle ID is directly in the item properties
    if (item.properties) {
      const bundleId = item.properties['Bundle ID'] ||
                      item.properties._bundle_id ||
                      item.properties['_bundle_id'] ||
                      item.properties['Bundle Name'];
      if (bundleId) return bundleId;
    }

    // Fallback: get from cart using variant ID
    const variantId = item.variant_id || item.id || item.key;
    return await this.getBundleIdFromVariant(variantId);
  }

  async removeRemainingBundleItems(bundleId) {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      this.bundlesToHide.add(bundleId);
      this.refreshUpCart();

      // Get current cart to remove items from actual Shopify cart
      const cart = await this.getCart();

      // Find ALL bundle items with this bundle ID
      const allBundleItems = cart.items.filter(item => {
        const itemBundleId = item.properties['Bundle ID'] ||
                           item.properties._bundle_id ||
                           item.properties['_bundle_id'] ||
                           item.properties['Bundle Name'];

        return itemBundleId === bundleId;
      });

      if (allBundleItems.length === 0) {
        return;
      }

      // Remove ALL bundle items from actual Shopify cart
      for (const item of allBundleItems) {
        await fetch('/cart/change.js', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            id: item.key || item.variant_id,
            quantity: 0
          })
        });
      }

      this.previousCartItems = this.previousCartItems.filter(item => {
        const itemBundleId = item.properties['Bundle ID'] ||
                           item.properties._bundle_id ||
                           item.properties['_bundle_id'] ||
                           item.properties['Bundle Name'];
        return itemBundleId !== bundleId;
      });

      setTimeout(() => {
        this.bundlesToHide.delete(bundleId);
        this.refreshUpCart();
      }, 1000);

    } catch (error) {
      // Silent error handling
      // Remove from hide list on error
      this.bundlesToHide.delete(bundleId);
    } finally {
      this.isProcessing = false;
    }
  }

  setupUpCartModification() {
    const originalModifyCart = window.upcartModifyCart;

    window.upcartModifyCart = (cart) => {
      if (originalModifyCart && typeof originalModifyCart === 'function') {
        cart = originalModifyCart(cart);
      }

      if (this.bundlesToHide.size > 0) {

        let hiddenItemCount = 0;
        let hiddenTotalPrice = 0;

        // Filter out items from bundles that should be hidden
        cart.items = cart.items.filter(item => {
          const bundleId = item.properties['Bundle ID'] ||
                          item.properties._bundle_id ||
                          item.properties['_bundle_id'] ||
                          item.properties['Bundle Name'];

          if (bundleId && this.bundlesToHide.has(bundleId)) {
            hiddenItemCount += item.quantity;
            hiddenTotalPrice += (item.price * item.quantity);
            return false;
          }

          return true;
        });

        cart.item_count -= hiddenItemCount;
        cart.total_price -= hiddenTotalPrice;
      }

      return cart;
    };
  }

  async getBundleIdFromVariant(variantId) {
    if (!variantId) return null;

    try {
      // Get current cart to find the item
      const cart = await this.getCart();
      const item = cart.items.find(item => 
        item.variant_id == variantId || 
        item.id == variantId ||
        item.key == variantId
      );

      if (!item) return null;

      // Check for bundle ID in properties
      return item.properties['Bundle ID'] ||
             item.properties._bundle_id ||
             item.properties['_bundle_id'] ||
             item.properties['Bundle Name'];

    } catch (error) {
      console.error('Error getting bundle ID:', error);
      return null;
    }
  }

  // Removed: removeBundleFromUpCart - replaced with removeRemainingBundleItems

  async checkBundleIntegrity() {
    if (this.isProcessing) return;

    try {
      const cart = await this.getCart();
      const bundleGroups = new Map();

      // Group items by bundle ID
      cart.items.forEach(item => {
        const bundleId = item.properties['Bundle ID'] ||
                        item.properties._bundle_id ||
                        item.properties['_bundle_id'] ||
                        item.properties['Bundle Name'];

        if (bundleId) {
          if (!bundleGroups.has(bundleId)) {
            bundleGroups.set(bundleId, []);
          }
          bundleGroups.get(bundleId).push(item);
        }
      });

      for (const [, items] of bundleGroups) {
        if (items.length < 2) {
          // Optionally remove incomplete bundles
        }
      }

    } catch (error) {
      console.error('Error checking bundle integrity:', error);
    }
  }

  refreshUpCart() {
    if (window.UpCart && window.UpCart.refresh) {
      window.UpCart.refresh();
    }

    if (window.UpCart && window.UpCart.updateCart) {
      window.UpCart.updateCart();
    }

    if (window.UpCart && window.UpCart.render) {
      window.UpCart.render();
    }

    document.dispatchEvent(new CustomEvent('cart:updated'));
    document.dispatchEvent(new CustomEvent('upcart:refresh'));
    document.dispatchEvent(new CustomEvent('upcart:cart:updated'));
    document.dispatchEvent(new CustomEvent('aftersell-upcart:cart:updated'));
  }

  async getCart() {
    const response = await fetch('/cart.js');
    return await response.json();
  }
}

// Initialize when UpCart is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.upCartBundleSync = new UpCartBundleSync();
  });
} else {
  window.upCartBundleSync = new UpCartBundleSync();
}

// Export for debugging
window.UpCartBundleSync = UpCartBundleSync;


