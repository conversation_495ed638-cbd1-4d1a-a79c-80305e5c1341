{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width section-{{ section.id }}-padding">
    <div class="content-rte center animate-item animate-item--child index-0">
      {% assign content_index = 0 %}
      {% if section.settings.title != blank %}
        {% assign content_index = 1 %}
        <h2 class="{{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
          {{ section.settings.title }}
        </h2>
      {% endif %}
      {% if section.settings.text != blank %}
        {% assign content_index = 1 %}
        <div class="rte">
          {{ section.settings.text }}
        </div>
      {% endif %}
      {%- if section.settings.button_label != blank -%}
        {% assign content_index = 1 %}
        <a
          {% if section.settings.link %}
            href="{{ section.settings.link }}"
          {% else %}
            role="link" aria-disabled="true"
          {% endif %}
          class="button {% if section.settings.button_style_secondary %}button--secondary{% else %}button--primary{% endif %}"
        >
          {{- section.settings.button_label | escape -}}
        </a>
      {%- endif -%}
      {%- if section.settings.atc_button_label != blank -%}
        {% assign content_index = 1 %}
        <button
          id="SectionAtcBtn-{{ section.id }}"
          type="button"
          class="button main-product-atc button--has-spinner"
          {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
            disabled
          {% endif %}
        >
          {{ section.settings.atc_button_label | escape }}
          <div class="loading-overlay__spinner">
            <svg
              aria-hidden="true"
              focusable="false"
              class="spinner"
              viewBox="0 0 66 66"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
            </svg>
          </div>
        </button>
      {%- endif -%}
    </div>
    
    <insta-stories
      class="insta-stories"
      data-last-index="{{ section.blocks.size | minus: 1 }}"
      data-section="{{ section.id }}"
    >
      <div class="insta-stories__open-buttons-and-arrows-container">
        <div class="insta-stories__open-buttons-container">
          <div class="insta-stories__open-buttons" id="insta-stories__open-buttons-{{ section.id }}">
            {% for block in section.blocks %}
              <button
                class="insta-story-open-btn animate-item animate-item--child"
                data-index="{{ forloop.index0 }}"
                style="--index:{{ forloop.index0 | plus: content_index }};"
                {{ block.shopify_attributes }}
              >
                <div class="insta-story-open-btn__pfp-ring{% if block.settings.close_friends %} insta-story-open-btn__pfp-ring--close-friends{% endif %}">
                  <div class="insta-story-open-btn__pfp-ring-offset">
                    <div class="insta-story-open-btn__pfp">
                      {% if block.settings.profile_picture != blank %}
                        <img
                          src="{{ block.settings.profile_picture | image_url: width: 400 }}"
                          alt="{{ block.settings.username }}"
                          width="auto"
                          height="auto"
                          loading="lazy"
                        >
                      {% else %}
                        {% render 'material-icon', icon: 'person', filled: true %}
                      {% endif %}
                    </div>
                  </div>
                </div>
                <span class="insta-story-open-btn__username">{{ block.settings.username }}</span>
              </button>
            {% endfor %}
          </div>
        </div>
        <button class='insta-story__arrow insta-story__arrow--prev active-content insta-stories__open-btns-prev'>
          {% render 'material-icon', icon: 'chevron_left' %}
        </button>
        <button class='insta-story__arrow insta-story__arrow--next active-content insta-stories__open-btns-next'>
          {% render 'material-icon', icon: 'chevron_right' %}
        </button>
      </div>
      <div
        class="insta-stories__modal color-inverse"
        data-open="false"
      >
        <button class='insta-stories__modal__close insta-stories__close-button mobile-hidden'>
          {% render 'material-icon', icon: 'close' %}
        </button>
        <div class='insta-stories__slider-container'>
          <div class='insta-stories__slider'>
            {% for block in section.blocks %}
              <div
                class="insta-story"
                data-index="{{ forloop.index0 }}"
                data-active-media-index="0"
                {{ block.shopify_attributes }}
              >
                <div class='insta-story__top active-content'>
                  <div class='insta-story__progress'></div>
                  <div class='insta-story__info'>
                    <div class='insta-story-open-btn__pfp insta-story__pfp'>
                      {% if block.settings.profile_picture != blank %}
                        <img
                          src="{{ block.settings.profile_picture | image_url: width: 400 }}"
                          alt="{{ block.settings.username }}"
                          width="auto"
                          height="auto"
                          loading="lazy"
                        >
                      {% else %}
                        {% render 'material-icon', icon: 'person', filled: true %}
                      {% endif %}
                    </div>
                    <p class='insta-story__info__text'>
                      <span class='insta-story__username'>
                        {{ block.settings.username }}
                      </span>
                      {% if block.settings.verified %}
                        {% render 'material-icon', icon: 'verified', filled: true %}
                      {% endif %}
                      <span class='insta-story__time-posted'></span>
                    </p>
                    <button class='insta-story__info__btn insta-story__pause-resume-btn' data-paused='false'>
                      {% render 'material-icon', icon: 'pause', filled: true %}
                      {% render 'material-icon', icon: 'play_arrow', filled: true %}
                    </button>
                    <button class='insta-story__info__btn insta-story__info__volume-btn insta-story__volume-btn flex-center' data-muted='true'>
                      <svg fill="currentColor" height="16" role="img" viewBox="0 0 24 24" width="16">
                        <path d="M16.636 7.028a1.5 1.5 0 1 0-2.395 1.807 5.365 5.365 0 0 1 1.103 3.17 5.378 5.378 0 0 1-1.105 3.176 1.5 1.5 0 1 0 2.395 1.806 8.396 8.396 0 0 0 1.71-4.981 8.39 8.39 0 0 0-1.708-4.978Zm3.73-2.332A1.5 1.5 0 1 0 18.04 6.59 8.823 8.823 0 0 1 20 12.007a8.798 8.798 0 0 1-1.96 5.415 1.5 1.5 0 0 0 2.326 1.894 11.672 11.672 0 0 0 2.635-7.31 11.682 11.682 0 0 0-2.635-7.31Zm-8.963-3.613a1.001 1.001 0 0 0-1.082.187L5.265 6H2a1 1 0 0 0-1 1v10.003a1 1 0 0 0 1 1h3.265l5.01 4.682.02.021a1 1 0 0 0 1.704-.814L12.005 2a1 1 0 0 0-.602-.917Z"></path>
                      </svg>
                      <svg fill="currentColor" height="16" role="img" viewBox="0 0 48 48" width="16">
                        <path clip-rule="evenodd" d="M1.5 13.3c-.8 0-1.5.7-1.5 1.5v18.4c0 .8.7 1.5 1.5 1.5h8.7l12.9 12.9c.9.9 2.5.3 2.5-1v-9.8c0-.4-.2-.8-.4-1.1l-22-22c-.3-.3-.7-.4-1.1-.4h-.6zm46.8 31.4-5.5-5.5C44.9 36.6 48 31.4 48 24c0-11.4-7.2-17.4-7.2-17.4-.6-.6-1.6-.6-2.2 0L37.2 8c-.6.6-.6 1.6 0 2.2 0 0 5.7 5 5.7 13.8 0 5.4-2.1 9.3-3.8 11.6L35.5 32c1.1-1.7 2.3-4.4 2.3-8 0-6.8-4.1-10.3-4.1-10.3-.6-.6-1.6-.6-2.2 0l-1.4 1.4c-.6.6-.6 1.6 0 2.2 0 0 2.6 2 2.6 6.7 0 1.8-.4 3.2-.9 4.3L25.5 22V1.4c0-1.3-1.6-1.9-2.5-1L13.5 10 3.3-.3c-.6-.6-1.5-.6-2.1 0L-.2 1.1c-.6.6-.6 1.5 0 2.1L4 7.6l26.8 26.8 13.9 13.9c.6.6 1.5.6 2.1 0l1.4-1.4c.7-.6.7-1.6.1-2.2z" fill-rule="evenodd"></path>
                      </svg>
                    </button>
                    <button class='insta-story__info__btn insta-story__info__close-btn insta-stories__close-button'>
                      {% render 'material-icon', icon: 'close' %}
                    </button>
                  </div>
                </div>
                {% if block.settings.story_1_video != blank %}
                  <div 
                    class='insta-story__media' 
                    data-type='video'
                    data-duration="{{ block.settings.story_1_video.duration | divided_by: 1000.0 }}" 
                    data-time-posted="{{ block.settings.story_1_time_posted }}"
                    style="--background:{{ block.settings.story_1_bg }};--object-fit:{{ block.settings.story_1_media_fit }}"
                  >
                    {{
                      block.settings.story_1_video
                      | video_tag:
                        image_size: '1000x',
                        loop: false,
                        muted: true,
                        playsinline: '',
                        autoplay: false
                    }}
                  </div>
                {% elsif block.settings.story_1_image != blank %}
                  <div 
                    class='insta-story__media' 
                    data-type='image' 
                    data-duration="{{ section.settings.autoplay_speed }}" 
                    data-time-posted="{{ block.settings.story_1_time_posted }}"
                    style="--background:{{ block.settings.story_1_bg }};--object-fit:{{ block.settings.story_1_media_fit }}"
                  >
                    {{
                      block.settings.story_1_image
                      | image_url: width: 1500
                      | image_tag: loading: 'lazy', widths: '165, 360, 535, 750, 1070, 1250, 1500'
                    }}
                  </div>
                {% endif %}
                {% if block.settings.story_2_video != blank %}
                  <div 
                    class='insta-story__media' 
                    data-type='video' 
                    data-duration="{{ block.settings.story_2_video.duration | divided_by: 1000.0 }}" 
                    data-time-posted="{{ block.settings.story_2_time_posted }}"
                    style="--background:{{ block.settings.story_2_bg }};--object-fit:{{ block.settings.story_2_media_fit }}"
                  >
                    {{
                      block.settings.story_2_video
                      | video_tag:
                        image_size: '1000x',
                        loop: false,
                        muted: true,
                        playsinline: '',
                        autoplay: false
                    }}
                  </div>
                {% elsif block.settings.story_2_image != blank %}
                  <div 
                    class='insta-story__media' 
                    data-type='image' 
                    data-duration="{{ section.settings.autoplay_speed }}" 
                    data-time-posted="{{ block.settings.story_2_time_posted }}"
                    style="--background:{{ block.settings.story_2_bg }};--object-fit:{{ block.settings.story_2_media_fit }}"
                  >
                    {{
                      block.settings.story_2_image
                      | image_url: width: 1500
                      | image_tag: loading: 'lazy', widths: '165, 360, 535, 750, 1070, 1250, 1500'
                    }}
                  </div>
                {% endif %}
                {% if block.settings.story_3_video != blank %}
                  <div 
                    class='insta-story__media' 
                    data-type='video' 
                    data-duration="{{ block.settings.story_3_video.duration | divided_by: 1000.0 }}" 
                    data-time-posted="{{ block.settings.story_3_time_posted }}"
                    style="--background:{{ block.settings.story_3_bg }};--object-fit:{{ block.settings.story_3_media_fit }}"
                  >
                    {{
                      block.settings.story_3_video
                      | video_tag:
                        image_size: '1000x',
                        loop: false,
                        muted: true,
                        playsinline: '',
                        autoplay: false
                    }}
                  </div>
                {% elsif block.settings.story_3_image != blank %}
                  <div 
                    class='insta-story__media' 
                    data-type='image' 
                    data-duration="{{ section.settings.autoplay_speed }}" 
                    data-time-posted="{{ block.settings.story_3_time_posted }}"
                    style="--background:{{ block.settings.story_3_bg }};--object-fit:{{ block.settings.story_3_media_fit }}"
                  >
                    {{
                      block.settings.story_3_image
                      | image_url: width: 1500
                      | image_tag: loading: 'lazy', widths: '165, 360, 535, 750, 1070, 1250, 1500'
                    }}
                  </div>
                {% endif %}
                <button class='insta-story__inactive-overlay inactive-content insta-story__slide-btn' data-index="{{ forloop.index0 }}">
                  <div class="insta-story-open-btn__pfp-ring{% if block.settings.close_friends %} insta-story-open-btn__pfp-ring--close-friends{% endif %} mobile-hidden">
                    <div class="insta-story-open-btn__pfp-ring-offset">
                      <div class="insta-story-open-btn__pfp">
                        {% if block.settings.profile_picture != blank %}
                          <img
                            src="{{ block.settings.profile_picture | image_url: width: 400 }}"
                            alt="{{ block.settings.username }}"
                            width="auto"
                            height="auto"
                            loading="lazy"
                          >
                        {% else %}
                          {% render 'material-icon', icon: 'person', filled: true %}
                        {% endif %}
                      </div>
                    </div>
                  </div>
                  <span class='insta-story__username mobile-hidden'>
                    {{ block.settings.username }}
                  </span>
                </button>
                <button class='insta-story__invisible-btn insta-story__invisible-btn--prev insta-story__prev active-content' data-index="{{ forloop.index0 }}">&nbsp</button>
                <button class='insta-story__invisible-btn insta-story__invisible-btn--next insta-story__next active-content' data-index="{{ forloop.index0 }}">&nbsp</button>
                <button class='insta-story__arrow insta-story__arrow--prev active-content insta-story__prev mobile-hidden' data-index="{{ forloop.index0 }}">
                  {% render 'material-icon', icon: 'chevron_left' %}
                </button>
                <button class='insta-story__arrow insta-story__arrow--next active-content insta-story__next mobile-hidden' data-index="{{ forloop.index0 }}">
                  {% render 'material-icon', icon: 'chevron_right' %}
                </button>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </insta-stories>
  </div>
</div>

{% schema %}
{
  "name": "Instagram stories",
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Instagram stories",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "text",
      "id": "text",
      "label": "Text",
      "default": "Display images and testimonials in a format familiar to your customers."
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Stories"
    },
    {
      "type": "paragraph",
      "content": "NOTE: On touch devices, arrows on the slider for the story opener buttons are automatically changed to SWIPE/DRAG function for the best mobile experience. Swipe function to skip stories is also included."
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 5,
      "max": 15,
      "step": 1,
      "unit": "sec",
      "label": "Image autoplay speed",
      "default": 10
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#121212",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "user",
      "name": "User post",
      "settings": [
        {
          "type": "text",
          "id": "username",
          "label": "User name",
          "default": "shrinetheme"
        },
        {
          "type": "checkbox",
          "id": "verified",
          "label": "Verified account",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "profile_picture",
          "label": "Profile picture"
        },
        {
          "type": "checkbox",
          "id": "close_friends",
          "label": "Close friends story",
          "default": false
        },
        {
          "type": "header",
          "content": "Story #1"
        },
        {
          "type": "image_picker",
          "id": "story_1_image",
          "label": "Story #1 Image"
        },
        {
          "type": "video",
          "id": "story_1_video",
          "label": "Story #1 Video"
        },
        {
          "type": "select",
          "id": "story_1_media_fit",
          "options": [
            {
              "value": "contain",
              "label": "Contain"
            },
            {
              "value": "cover",
              "label": "Cover"
            }
          ],
          "default": "contain",
          "label": "Story #1 Media fit",
          "info": "Contain preserves media's aspect ratio, cover fills out the whole story box."
        },
        {
          "type": "text",
          "id": "story_1_time_posted",
          "label": "Story #1 Time posted",
          "default": "10m"
        },
        {
          "type": "color",
          "id": "story_1_bg",
          "label": "Story #1 Background",
          "default": "#282828",
          "info": "Visible if Media fit is set to Contain"
        },
        {
          "type": "header",
          "content": "Story #2"
        },
        {
          "type": "image_picker",
          "id": "story_2_image",
          "label": "Story #2 Image"
        },
        {
          "type": "video",
          "id": "story_2_video",
          "label": "Story #2 Video"
        },
        {
          "type": "select",
          "id": "story_2_media_fit",
          "options": [
            {
              "value": "contain",
              "label": "Contain"
            },
            {
              "value": "cover",
              "label": "Cover"
            }
          ],
          "default": "contain",
          "label": "Story #2 Media fit",
          "info": "Contain preserves media's aspect ratio, cover fills out the whole story box."
        },
        {
          "type": "text",
          "id": "story_2_time_posted",
          "label": "Story #2 Time posted",
          "default": "1h"
        },
        {
          "type": "color",
          "id": "story_2_bg",
          "label": "Story #2 Background",
          "default": "#282828",
          "info": "Visible if Media fit is set to Contain"
        },
        {
          "type": "header",
          "content": "Story #3"
        },
        {
          "type": "image_picker",
          "id": "story_3_image",
          "label": "Story #3 Image"
        },
        {
          "type": "video",
          "id": "story_3_video",
          "label": "Story #3 Video"
        },
        {
          "type": "select",
          "id": "story_3_media_fit",
          "options": [
            {
              "value": "contain",
              "label": "Contain"
            },
            {
              "value": "cover",
              "label": "Cover"
            }
          ],
          "default": "contain",
          "label": "Story #3 Media fit",
          "info": "Contain preserves media's aspect ratio, cover fills out the whole story box."
        },
        {
          "type": "text",
          "id": "story_3_time_posted",
          "label": "Story #3 Time posted",
          "default": "4h"
        },
        {
          "type": "color",
          "id": "story_3_bg",
          "label": "Story #3 Background",
          "default": "#282828",
          "info": "Visible if Media fit is set to Contain"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Instagram stories",
      "blocks": [
        {
          "type": "user"
        },
        {
          "type": "user"
        },
        {
          "type": "user"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
