{% comment %}theme-check-disable UndefinedObject{% endcomment %}

{%- style -%}
  .footer {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
  }

  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .footer {
      margin-top: {{ section.settings.margin_top }}px;
    }

    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<footer class="footer color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient section-{{ section.id }}-padding animate-section animate--hidden" data-animations='{{ section.settings.display_animations }}' data-type='{{ settings.animations_type }}'> 
  {%- liquid
    assign has_social_icons = true
    if settings.social_facebook_link == blank and settings.social_instagram_link == blank and settings.social_youtube_link == blank and settings.social_tiktok_link == blank and settings.social_twitter_link == blank and settings.social_pinterest_link == blank and settings.social_snapchat_link == blank and settings.social_tumblr_link == blank and settings.social_vimeo_link == blank
      assign has_social_icons = false
    endif

    if settings.brand_image == blank and settings.brand_headline == blank and settings.brand_description == blank
      assign brand_empty = true
    endif

    if section.blocks.size == 1 and section.blocks[0].type == 'brand_information' and brand_empty and has_social_icons == false and section.settings.newsletter_enable == false and section.settings.enable_follow_on_shop == false
      assign only_empty_brand = true
    endif
  -%}
  {%- if section.blocks.size > 0
    or section.settings.newsletter_enable
    or section.settings.show_social
    or section.settings.enable_follow_on_shop
  -%}
    {%- unless only_empty_brand -%}
      <div class="footer__content-top page-width">
        {%- if section.blocks.size > 0 -%}
          <div class="footer__blocks-wrapper grid">
            {%- for block in section.blocks -%}
              <div
                class="footer-block grid__item{% if block.type == 'link_list' %} footer-block--menu{% endif %} footer-block--desktop-{{ block.settings.width_desktop }} footer-block--mobile-{{ block.settings.width_mobile }} animate-item animate-item--child"
                style="--index: {{ forloop.index0 }};" {{ block.shopify_attributes }}
              >
                {%- if block.settings.heading != blank -%}
                  <h2 class="footer-block__heading">{{- block.settings.heading | escape -}}</h2>
                {%- endif -%}

                {%- case block.type -%}
                  {%- when '@app' -%}
                    {% render block %}
                  {%- when 'text' -%}
                    <div class="footer-block__details-content rte">
                      {{ block.settings.subtext }}
                    </div>
                  {%- when 'link_list' -%}
                    {%- if block.settings.menu != blank -%}
                      <ul class="footer-block__details-content list-unstyled">
                        {%- for link in block.settings.menu.links -%}
                          <li>
                            <a
                              href="{{ link.url }}"
                              class="link link--text list-menu__item list-menu__item--link{% if link.active %} list-menu__item--active{% endif %}"
                            >
                              {{ link.title }}
                            </a>
                          </li>
                        {%- endfor -%}
                      </ul>
                    {%- endif -%}
                  {%- when 'brand_information' -%}
                    <div class="footer-block__brand-info">
                      {%- if settings.brand_image != blank -%}
                        {%- assign brand_image_height = settings.brand_image_width
                          | divided_by: settings.brand_image.aspect_ratio
                        -%}
                        <div
                          class="footer-block__image-wrapper global-media-settings"
                          style="max-width: min(100%, {{ settings.brand_image_width }}px);"
                        >
                          {{
                            settings.brand_image
                            | image_url: width: 1100
                            | image_tag:
                              loading: 'lazy',
                              widths: '50, 100, 150, 200, 300, 400, 550, 800, 1100',
                              height: brand_image_height,
                              width: settings.brand_image_width
                          }}
                        </div>
                      {%- endif -%}
                      {%- if settings.brand_headline != blank -%}
                        <h2 class="footer-block__heading rte">{{ settings.brand_headline }}</h2>
                      {%- endif -%}
                      {%- if settings.brand_description != blank -%}
                        <div class="rte">{{ settings.brand_description }}</div>
                      {%- endif -%}
                      {%- if block.settings.show_social and has_social_icons -%}
                        {%- render 'social-icons' -%}
                      {%- endif -%}
                    </div>
                  {%- when 'image' -%}
                    <div class="footer-block__details-content footer-block-image {{ block.settings.alignment }}">
                      {%- if block.settings.image != blank -%}
                        {%- assign image_size_2x = block.settings.image_width | times: 2 | at_most: 5760 -%}
                        <div
                          class="footer-block__image-wrapper global-media-settings"
                          style="max-width: min(100%, {{ block.settings.image_width }}px);"
                        >
                          {% if block.settings.url != blank %}<a href='{{ block.settings.url }}'>{% endif %}
                            <img
                              srcset="{{ block.settings.image | image_url: width: block.settings.image_width }}, {{ block.settings.image | image_url: width: image_size_2x }} 2x"
                              src="{{ block.settings.image | image_url: width: 760 }}"
                              alt="{{ block.settings.image.alt | escape }}"
                              loading="lazy"
                              width="{{ block.settings.image.width }}"
                              height="{{ block.settings.image.height }}"
                            >
                          {% if block.settings.url != blank %}</a>{% endif %}
                        </div>
                      {%- else -%}
                        {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                      {%- endif -%}
                    </div>
                  {%- when 'featured_product' -%}
                    <div class="{{ block.settings.card_width }}">
                      {% render 'card-product',
                        card_product: block.settings.product,
                        media_aspect_ratio: 'adapt',
                        show_secondary_image: block.settings.second_image,
                        extend_height: true,
                        custom_color_scheme: block.settings.color_scheme
                      %}
                    </div>
                  {%- when 'featured_collection' -%}
                    <div class="{{ block.settings.card_width }}">
                      {% render 'card-collection',
                        card_collection: block.settings.collection,
                        media_aspect_ratio: block.settings.image_ratio,
                        columns: 1,
                        custom_color_scheme: block.settings.color_scheme
                      %}
                    </div>
                  {% when 'email_signup' %}
                    <div>
                      <div class="footer-block__details-content footer-block__details-content-mb rte">
                        {{ block.settings.subtext }}
                      </div>
                      {%- form 'customer', id: 'ContactFooter', class: 'footer__newsletter newsletter-form' -%}
                        <input type="hidden" name="contact[tags]" value="newsletter">
                        <div class="newsletter-form__field-wrapper">
                          <div class="field">
                            <input
                              id="NewsletterForm--{{ section.id }}"
                              type="email"
                              name="contact[email]"
                              class="field__input"
                              value="{{ form.email }}"
                              aria-required="true"
                              autocorrect="off"
                              autocapitalize="off"
                              autocomplete="email"
                              {% if form.errors %}
                                autofocus
                                aria-invalid="true"
                                aria-describedby="ContactFooter-error"
                              {% elsif form.posted_successfully? %}
                                aria-describedby="ContactFooter-success"
                              {% endif %}
                              placeholder="{{ 'newsletter.label' | t }}"
                              required
                            >
                            <label class="field__label" for="NewsletterForm--{{ section.id }}">
                              {{ 'newsletter.label' | t }}
                            </label>
                            {% if block.settings.button_type == 'arrow' %}
                              <button
                                type="submit"
                                class="newsletter-form__button field__button"
                                name="commit"
                                id="Subscribe"
                                aria-label="{{ 'newsletter.button_label' | t }}"
                              >
                                {% render 'icon-arrow' %}
                              </button>
                            {% endif %}
                          </div>
                          {%- if form.errors -%}
                            <small class="newsletter-form__message form__message" id="ContactFooter-error">
                              {%- render 'icon-error' -%}
                              {{- form.errors.translated_fields.email | capitalize }}
                              {{ form.errors.messages.email -}}
                            </small>
                          {%- endif -%}
                        </div>
                        {% if block.settings.button_type == 'solid' %}
                          <button
                            type="submit"
                            class="button newsletter__solid-btn button--full-width{% if block.settings.button_style_secondary %} button--secondary{% endif %}"
                            name="commit"
                            id="Subscribe"
                            aria-label="{{ 'newsletter.button_label' | t }}"
                          >
                            {{ block.settings.button_label }}
                          </button>
                        {% endif %}
                        {%- if form.posted_successfully? -%}
                          <h3
                            class="newsletter-form__message newsletter-form__message--success form__message"
                            id="ContactFooter-success"
                            tabindex="-1"
                            autofocus
                          >
                            {% render 'icon-success' -%}
                            {{- 'newsletter.success' | t }}
                          </h3>
                        {%- endif -%}
                      {%- endform -%}
                    </div>
                {%- endcase -%}
              </div>
            {%- endfor -%}
          </div>
        {%- endif -%}

        <div class="footer-block--newsletter">
          {%- if shop.features.follow_on_shop? and section.settings.enable_follow_on_shop -%}
            <div class="footer__follow-on-shop animate-item animate-item--child" style="--index: {{ section.blocks.size }};">
              {% comment %} TODO: enable theme-check once `login_button` is accepted as valid filter {% endcomment %}
              {% # theme-check-disable %}
              {{ shop | login_button: action: 'follow' }}
              {% # theme-check-enable %}
            </div>
          {%- endif -%}

          {%- if section.settings.show_social and has_social_icons -%}
            {%- render 'social-icons' -%}
          {%- endif -%}
        </div>
      </div>
    {%- endunless -%}
  {%- endif -%}

  <div class="footer__content-bottom animate-item animate-item--child" style="--index: {{ section.blocks.size }};">
    <div class="footer__content-bottom-wrapper page-width">
      <div class="footer__column footer__localization isolate">
        {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
          <noscript>
            {%- form 'localization', id: 'FooterCountryFormNoScript', class: 'localization-form' -%}
              <div class="localization-form__select">
                <h2 class="visually-hidden" id="FooterCountryLabelNoScript">{{ 'localization.country_label' | t }}</h2>
                <select
                  class="localization-selector link"
                  name="country_code"
                  aria-labelledby="FooterCountryLabelNoScript"
                >
                  {%- for country in localization.available_countries -%}
                    <option
                      value="{{ country.iso_code }}"
                      {%- if country.iso_code == localization.country.iso_code %}
                        selected
                      {% endif %}
                    >
                      {{ country.name }} ({{ country.currency.iso_code }}
                      {{ country.currency.symbol }})
                    </option>
                  {%- endfor -%}
                </select>
                {% render 'icon-caret' %}
              </div>
              <button class="button button--tertiary">{{ 'localization.update_country' | t }}</button>
            {%- endform -%}
          </noscript>
          <localization-form>
            {%- form 'localization', id: 'FooterCountryForm', class: 'localization-form' -%}
              <div class="no-js-hidden">
                <h2 class="caption-large text-body" id="FooterCountryLabel">{{ 'localization.country_label' | t }}</h2>
                <div class="disclosure">
                  <button
                    type="button"
                    class="disclosure__button localization-form__select localization-selector link link--text caption-large"
                    aria-expanded="false"
                    aria-controls="FooterCountryList"
                    aria-describedby="FooterCountryLabel"
                  >
                    {{ localization.country.name }} ({{ localization.country.currency.iso_code }}
                    {{ localization.country.currency.symbol }})
                    {% render 'icon-caret' %}
                  </button>
                  <div class="disclosure__list-wrapper" hidden>
                    <ul id="FooterCountryList" role="list" class="disclosure__list list-unstyled">
                      {%- for country in localization.available_countries -%}
                        <li class="disclosure__item" tabindex="-1">
                          <a
                            class="link link--text disclosure__link caption-large{% if country.iso_code == localization.country.iso_code %} disclosure__link--active{% endif %} focus-inset"
                            href="#"
                            {% if country.iso_code == localization.country.iso_code %}
                              aria-current="true"
                            {% endif %}
                            data-value="{{ country.iso_code }}"
                          >
                            {{ country.name }}
                            <span class="localization-form__currency"
                              >({{ country.currency.iso_code }}
                              {{ country.currency.symbol }})</span
                            >
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                </div>
                <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%}

        {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
          <noscript>
            {%- form 'localization', id: 'FooterLanguageFormNoScript', class: 'localization-form' -%}
              <div class="localization-form__select">
                <h2 class="visually-hidden" id="FooterLanguageLabelNoScript">
                  {{ 'localization.language_label' | t }}
                </h2>
                <select
                  class="localization-selector link"
                  name="locale_code"
                  aria-labelledby="FooterLanguageLabelNoScript"
                >
                  {%- for language in localization.available_languages -%}
                    <option
                      value="{{ language.iso_code }}"
                      lang="{{ language.iso_code }}"
                      {%- if language.iso_code == localization.language.iso_code %}
                        selected
                      {% endif %}
                    >
                      {{ language.endonym_name | capitalize }}
                    </option>
                  {%- endfor -%}
                </select>
                {% render 'icon-caret' %}
              </div>
              <button class="button button--tertiary">{{ 'localization.update_language' | t }}</button>
            {%- endform -%}
          </noscript>

          <localization-form>
            {%- form 'localization', id: 'FooterLanguageForm', class: 'localization-form' -%}
              <div class="no-js-hidden">
                <h2 class="caption-large text-body" id="FooterLanguageLabel">
                  {{ 'localization.language_label' | t }}
                </h2>
                <div class="disclosure">
                  <button
                    type="button"
                    class="disclosure__button localization-form__select localization-selector link link--text caption-large"
                    aria-expanded="false"
                    aria-controls="FooterLanguageList"
                    aria-describedby="FooterLanguageLabel"
                  >
                    {{ localization.language.endonym_name | capitalize }}
                    {% render 'icon-caret' %}
                  </button>
                  <div class="disclosure__list-wrapper" hidden>
                    <ul id="FooterLanguageList" role="list" class="disclosure__list list-unstyled">
                      {%- for language in localization.available_languages -%}
                        <li class="disclosure__item" tabindex="-1">
                          <a
                            class="link link--text disclosure__link caption-large{% if language.iso_code == localization.language.iso_code %} disclosure__link--active{% endif %} focus-inset"
                            href="#"
                            hreflang="{{ language.iso_code }}"
                            lang="{{ language.iso_code }}"
                            {% if language.iso_code == localization.language.iso_code %}
                              aria-current="true"
                            {% endif %}
                            data-value="{{ language.iso_code }}"
                          >
                            {{ language.endonym_name | capitalize }}
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                </div>
                <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%}
      </div>
      <div class="footer__column footer__column--info">
        {%- if section.settings.payment_enable -%}
          <div class="footer__payment">
            <span class="visually-hidden">{{ 'sections.footer.payment' | t }}</span>
            <ul class="list list-payment" role="list">
              {% assign enabled_payment_types = shop.enabled_payment_types %}
              {% if section.settings.enabled_payment_types != blank %}
                {% assign enabled_payment_types = section.settings.enabled_payment_types | remove: ' ' | split: ',' %}
              {% endif %}

              {%- for type in enabled_payment_types -%}
                <li class="list-payment__item">
                  {{ type | payment_type_svg_tag: class: 'icon icon--full-color' }}
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
      </div>
    </div>
    <div class="footer__content-bottom-wrapper page-width{% if section.settings.enable_country_selector == false and section.settings.enable_language_selector == false %} footer__content-bottom-wrapper--center{% endif %}">
      <div class="footer__copyright caption">
        <small class="copyright__content">
          &copy; {{ 'now' | date: '%Y' }}, {{ shop.name | link_to: routes.root_url -}}
        </small>
        {% unless section.settings.branding_text == blank %}
          <small class="copyright__content">{{ section.settings.branding_text }}</small>
        {% endunless %}
        {%- if section.settings.show_policy -%}
          <ul class="policies list-unstyled">
            {%- for policy in shop.policies -%}
              {%- if policy != blank -%}
                <li>
                  <small class="copyright__content"
                    ><a href="{{ policy.url }}">{{ policy.title }}</a></small
                  >
                </li>
              {%- endif -%}
            {%- endfor -%}
          </ul>
        {%- endif -%}
      </div>
    </div>
  </div>
</footer>

{% javascript %}
  class LocalizationForm extends HTMLElement {
    constructor() {
      super();
      this.elements = {
        input: this.querySelector('input[name="locale_code"], input[name="country_code"]'),
        button: this.querySelector('button'),
        panel: this.querySelector('.disclosure__list-wrapper'),
      };
      this.elements.button.addEventListener('click', this.openSelector.bind(this));
      this.elements.button.addEventListener('focusout', this.closeSelector.bind(this));
      this.addEventListener('keyup', this.onContainerKeyUp.bind(this));

      this.querySelectorAll('a').forEach(item => item.addEventListener('click', this.onItemClick.bind(this)));
    }

    hidePanel() {
      this.elements.button.setAttribute('aria-expanded', 'false');
      this.elements.panel.setAttribute('hidden', true);
    }

    onContainerKeyUp(event) {
      if (event.code.toUpperCase() !== 'ESCAPE') return;

      this.hidePanel();
      this.elements.button.focus();
    }

    onItemClick(event) {
      event.preventDefault();
      const form = this.querySelector('form');
      this.elements.input.value = event.currentTarget.dataset.value;
      if (form) form.submit();
    }

    openSelector() {
      this.elements.button.focus();
      this.elements.panel.toggleAttribute('hidden');
      this.elements.button.setAttribute('aria-expanded', (this.elements.button.getAttribute('aria-expanded') === 'false').toString());
    }

    closeSelector(event) {
      const shouldClose = event.relatedTarget && event.relatedTarget.nodeName === 'BUTTON';
      if (event.relatedTarget === null || shouldClose) {
        this.hidePanel();
      }
    }
  }

  customElements.define('localization-form', LocalizationForm);
{% endjavascript %}

{% schema %}
{
  "name": "t:sections.footer.name",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "link_list",
      "name": "t:sections.footer.blocks.link_list.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Quick links",
          "label": "t:sections.footer.blocks.link_list.settings.heading.label"
        },
        {
          "type": "link_list",
          "id": "menu",
          "default": "footer",
          "label": "t:sections.footer.blocks.link_list.settings.menu.label",
          "info": "t:sections.footer.blocks.link_list.settings.menu.info"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width_desktop",
          "max": 12,
          "min": 1,
          "step": 1,
          "default": 3,
          "label": "Column desktop width"
        },
        {
          "type": "select",
          "id": "width_mobile",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            }
          ],
          "default": "2",
          "label": "Column mobile width"
        }
      ]
    },
    {
      "type": "brand_information",
      "name": "t:sections.footer.blocks.brand_information.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.footer.blocks.brand_information.settings.paragraph.content"
        },
        {
          "type": "header",
          "content": "t:sections.footer.blocks.brand_information.settings.header__1.content"
        },
        {
          "type": "checkbox",
          "id": "show_social",
          "default": true,
          "label": "t:sections.footer.blocks.brand_information.settings.show_social.label",
          "info": "t:sections.footer.blocks.brand_information.settings.show_social.info"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width_desktop",
          "max": 12,
          "min": 1,
          "step": 1,
          "default": 3,
          "label": "Column desktop width"
        },
        {
          "type": "select",
          "id": "width_mobile",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            }
          ],
          "default": "2",
          "label": "Column mobile width"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.footer.blocks.text.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Heading",
          "label": "t:sections.footer.blocks.text.settings.heading.label"
        },
        {
          "type": "richtext",
          "id": "subtext",
          "default": "<p>Share contact information, store details, and brand content with your customers.</p>",
          "label": "t:sections.footer.blocks.text.settings.subtext.label"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width_desktop",
          "max": 12,
          "min": 1,
          "step": 1,
          "default": 3,
          "label": "Column desktop width"
        },
        {
          "type": "select",
          "id": "width_mobile",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            }
          ],
          "default": "2",
          "label": "Column mobile width"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 550,
          "step": 5,
          "unit": "px",
          "label": "Image width",
          "default": 100
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Image alignment on large screen",
          "options": [
            {
              "value": "",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width_desktop",
          "max": 12,
          "min": 1,
          "step": 1,
          "default": 3,
          "label": "Column desktop width"
        },
        {
          "type": "select",
          "id": "width_mobile",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            }
          ],
          "default": "2",
          "label": "Column mobile width"
        }
      ]
    },
    {
      "type": "email_signup",
      "name": "Email signup",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.footer.settings.header__1.info"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Subscribe to our emails",
          "label": "t:sections.footer.blocks.link_list.settings.heading.label"
        },
        {
          "type": "richtext",
          "id": "subtext",
          "default": "<p>A short sentence encouraging customers to subscribe to your newsletter.</p>",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "button_type",
          "options": [
            {
              "value": "arrow",
              "label": "Arrow button"
            },
            {
              "value": "solid",
              "label": "Solid button"
            }
          ],
          "default": "solid",
          "label": "Submit button type"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Solid button label",
          "default": "Sign up"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
          "default": false
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width_desktop",
          "max": 12,
          "min": 1,
          "step": 1,
          "default": 3,
          "label": "Column desktop width"
        },
        {
          "type": "select",
          "id": "width_mobile",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            }
          ],
          "default": "2",
          "label": "Column mobile width"
        }
      ]
    },
    {
      "type": "featured_product",
      "name": "Featured product",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Featured product",
          "label": "t:sections.footer.blocks.link_list.settings.heading.label"
        },
        {
          "type": "product",
          "id": "product",
          "label": "t:sections.collage.blocks.product.settings.product.label"
        },
        {
          "type": "checkbox",
          "id": "second_image",
          "default": false,
          "label": "t:sections.collage.blocks.product.settings.second_image.label"
        },
        {
          "type": "select",
          "id": "card_width",
          "options": [
            {
              "value": "footer_card--half-width",
              "label": "Half width of the column"
            },
            {
              "value": "full",
              "label": "Full width of the column"
            }
          ],
          "default": "full",
          "label": "Mobile card width"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "background-1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width_desktop",
          "max": 12,
          "min": 1,
          "step": 1,
          "default": 3,
          "label": "Column desktop width"
        },
        {
          "type": "select",
          "id": "width_mobile",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            }
          ],
          "default": "2",
          "label": "Column mobile width"
        }
      ]
    },
    {
      "type": "featured_collection",
      "name": "Featured collection",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Featured collection",
          "label": "t:sections.footer.blocks.link_list.settings.heading.label"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collection-list.blocks.featured_collection.settings.collection.label"
        },
        {
          "type": "select",
          "id": "card_width",
          "options": [
            {
              "value": "footer_card--half-width",
              "label": "Half width of the column"
            },
            {
              "value": "full",
              "label": "Full width of the column"
            }
          ],
          "default": "full",
          "label": "Mobile card width"
        },
        {
          "type": "select",
          "id": "image_ratio",
          "options": [
            {
              "value": "adapt",
              "label": "t:sections.collection-list.settings.image_ratio.options__1.label"
            },
            {
              "value": "portrait",
              "label": "t:sections.collection-list.settings.image_ratio.options__2.label"
            },
            {
              "value": "square",
              "label": "t:sections.collection-list.settings.image_ratio.options__3.label"
            }
          ],
          "default": "square",
          "label": "t:sections.collection-list.settings.image_ratio.label",
          "info": "t:sections.collection-list.settings.image_ratio.info"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "background-1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width_desktop",
          "max": 12,
          "min": 1,
          "step": 1,
          "default": 3,
          "label": "Column desktop width"
        },
        {
          "type": "select",
          "id": "width_mobile",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            }
          ],
          "default": "2",
          "label": "Column mobile width"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "t:sections.footer.settings.header__9.content",
      "info": "t:sections.footer.settings.header__9.info"
    },
    {
      "type": "checkbox",
      "id": "enable_follow_on_shop",
      "default": false,
      "label": "t:sections.footer.settings.enable_follow_on_shop.label"
    },
    {
      "type": "header",
      "content": "t:sections.footer.settings.header__2.content",
      "info": "t:sections.footer.settings.header__2.info"
    },
    {
      "type": "checkbox",
      "id": "show_social",
      "default": true,
      "label": "t:sections.footer.settings.show_social.label"
    },
    {
      "type": "header",
      "content": "t:sections.footer.settings.header__3.content",
      "info": "t:sections.footer.settings.header__4.info"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": true,
      "label": "t:sections.footer.settings.enable_country_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.footer.settings.header__5.content",
      "info": "t:sections.footer.settings.header__6.info"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": true,
      "label": "t:sections.footer.settings.enable_language_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.footer.settings.header__7.content"
    },
    {
      "type": "checkbox",
      "id": "payment_enable",
      "default": true,
      "label": "t:sections.footer.settings.payment_enable.label"
    },
    {
      "type": "text",
      "id": "enabled_payment_types",
      "label": "Custom payment icons to show",
      "info": "List of payments you want to show, split with a comma (without spacing). Options are: afterpay, american_express, apple_pay, bitcoin, dankort, diners_club, discover, dogecoin, dwolla, facebook_pay, forbrugsforeningen, google_pay, ideal, jcb, klarna, klarna-pay-later, litecoin, maestro, master, paypal, shopify_pay, sofort, unionpay, visa"
    },
    {
      "type": "header",
      "content": "t:sections.footer.settings.header__8.content",
      "info": "t:sections.footer.settings.header__8.info"
    },
    {
      "type": "checkbox",
      "id": "show_policy",
      "default": false,
      "label": "t:sections.footer.settings.show_policy.label"
    },
    {
      "type": "header",
      "content": "Branding"
    },
    {
      "type": "inline_richtext",
      "id": "branding_text",
      "label": "Branding text",
      "default": "Powered by Shrine"
    },
    {
      "type": "header",
      "content": "t:sections.all.spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.footer.settings.margin_top.label",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#121212",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#FFFFFF",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#FFFFFF",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#121212",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#ffffff",
      "label": "Outline button"
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "link_list"
      },
      {
        "type": "text"
      }
    ]
  }
}
{% endschema %}
