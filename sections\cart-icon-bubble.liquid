{%- liquid
  assign non_upsell_count = cart.item_count
  for item in cart.items
    if item.product.tags contains 'cart-hidden'
      assign non_upsell_count = non_upsell_count | minus: 1
    endif
  endfor
   
  if non_upsell_count > 0
    render 'icon-cart-empty'
  else
    render 'icon-cart'
  endif
-%}
<span class="visually-hidden">{{ 'templates.cart.cart' | t }}</span>
{%- if non_upsell_count > 0 -%}
  <div class="cart-count-bubble">
    {%- if non_upsell_count < 100 -%}
      <span aria-hidden="true">{{ non_upsell_count }}</span>
    {%- endif -%}
    <span class="visually-hidden">{{ 'sections.header.cart_count' | t: count: non_upsell_count }}</span>
  </div>
{%- endif -%}
