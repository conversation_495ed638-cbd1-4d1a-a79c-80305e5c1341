{% schema %}
{
  "name": "Testimonial V2",
  "settings": [
    {
      "type": "inline_richtext",
      "id": "section_title",
      "label": "Section Title",
      "default": "What makes our customers so excited"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#ffffff"
    },
    {
      "type": "checkbox",
      "id": "pheromone_layout",
      "label": "Pheromone Layout",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "testimonial",
      "name": "Testimonial",
      "settings": [
        {
          "type": "image_picker",
          "id": "slide_image",
          "label": "Slide Image"
        },
        {
          "type": "textarea",
          "id": "testimonial_text",
          "label": "Testimonial Text",
          "default": "Normally, I never write reviews, but I had to make an exception for this pillow. This is the first time in the longest time that I slept like a baby, and when I woke up, my neck pain was gone."
        },
        {
          "type": "image_picker",
          "id": "reviewer_image",
          "label": "Reviewer Image"
        },
        {
          "type": "text",
          "id": "reviewer_name",
          "label": "Reviewer Name",
          "default": "<PERSON>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Testimonial V2"
    }
  ]
}
{% endschema %}

<section class="custom-testimonial-section {% if section.settings.pheromone_layout %}pheromone-layout{% endif %}" id="testimonialssection" style="background-color: {{ section.settings.background_color }};">
  <div class="container" id="testimonials">
    <h2 class="sy-46-title">{{ section.settings.section_title }}</h2>
    <div class="swiper testimonial-carousel">
      <div class="swiper-wrapper">
        {% for block in section.blocks %}
          <div class="swiper-slide">
            <div class="testimonial-card">
              <p class="testimonial-text">{{ block.settings.testimonial_text }}</p>
              <div class="testimonial-reviewer">
                {% if block.settings.reviewer_image != blank %}
                  <img class="reviewer-image" src="{{ block.settings.reviewer_image | img_url: '100x100' }}" alt="{{ block.settings.reviewer_name }}">
                {% endif %}
                <div class="reviewer-name__wrapper">
                  <span class="reviewer-name">{{ block.settings.reviewer_name }}</span>
                  <span class="verified-buyer">
                    <!-- Star / verified icon -->
                    ✅ Verified Review
                  </span>
                </div>
              </div>
            </div>
          </div>
          {% if block.settings.slide_image != blank %}
            <div class="swiper-slide">
              <div class="testimonial-card slide-image-container">
                <img src="{{ block.settings.slide_image | img_url: 'master' }}" alt="" class="slide-image">
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <div class="slider-controls">
        <div class="navigation-group">
          <div class="swiper-button-prev prev-slide1">←</div>
          <div class="swiper-pagination"></div>
          <div class="swiper-button-next next-slide1">→</div>
        </div>
      </div>
    </div>
  </div>
</section>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const swiper = new Swiper('.testimonial-carousel', {
    slidesPerView: 1.2,
    spaceBetween: 20,
    loop: false,
    watchOverflow: true,
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
    },
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
    breakpoints: {
      0: { slidesPerView: 1.25, spaceBetween: 18 },
      479: { slidesPerView: 1.75, spaceBetween: 18 },
      640: { slidesPerView: 2.5, spaceBetween: 18 },
      769: { slidesPerView: 3, spaceBetween: 18 },
      1024: { slidesPerView: 3.75, spaceBetween: 18 },
    },
  });
});
</script>

<style>
.custom-testimonial-section {
  padding: 64px 0;
  text-align: center;
}
.sy-46-title {
  padding-bottom: 52px;
  font-size: 32px;
  font-weight: 400;
}
.testimonial-card {
  background: linear-gradient(90deg, #6C4E3E 0%, #DDBEAE 100%);
  padding: 26px 24px;
  border-radius: 10px;
  min-height: 340px;
  display: flex;
  flex-direction: column;
}
.slide-image-container {
  padding: 0;
  background: #fff;
  overflow: hidden;
}
.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  max-height: 340px;
}
.testimonial-text {
  color: #FAEADC;
  font-size: 18px;
  line-height: 1.5;
  font-weight: 400;
  margin-bottom: 24px;
  flex-grow: 1;
}
.testimonial-reviewer {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 8px;
}
.reviewer-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}
.reviewer-name {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
.verified-buyer {
  font-size: 12px;
  color: #FAEADC;
  margin-top: 2px;
}
.slider-controls {
  margin-top: 64px;
}
.navigation-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  max-width: 380px;
  margin: auto;
}
.swiper-pagination {
  display: flex;
  gap: 8px;
  justify-content: center;
}
.swiper-pagination-bullet {
  width: 6px;
  height: 6px;
  background: #E3E3E3;
  opacity: 1;
}
.swiper-pagination-bullet-active {
  background: #6C4E3E;
  width: 8px;
  height: 8px;
}
.swiper-button-prev,
.swiper-button-next {
  cursor: pointer;
}
@media (max-width: 991.98px) {
  .custom-testimonial-section { padding: 45px 0; }
}
@media (max-width: 767.98px) {
  .custom-testimonial-section { padding: 36px 0; }
  .slider-controls { margin-top: 24px; }
  .sy-46-title { padding-bottom: 24px; }
}
</style>