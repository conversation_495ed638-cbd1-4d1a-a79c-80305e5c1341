<div class="sticky-cart">
  <product-form data-section="{{ section.id }}" data-type="add-to-cart-form">
    {% form 'product', product, 
      id: 'sticky-product-form', 
      class: 'product-form', 
      novalidate: 'novalidate', 
      data-type: 'add-to-cart-form' 
    %}
      <!-- Hidden input for product variant ID -->
      <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
      
      <!-- <PERSON>y Add to <PERSON><PERSON> -->
      <button type="submit" class="sticky-add-to-cart">
        Add to Cart
      </button>
    {% endform %}
  </product-form>
  <div class="vsproduct-ship-by">
    <div style="display: flex; align-items: center;">
        <span style="background-color: #137333; width: 12px; height: 12px; border-radius: 50%; margin-right: 6px; display: block;"></span>
        <span>Ships by <span class="shippingDate" style="font-weight: bold;"></span></span>
    </div>
    <div style="display: flex; align-items: center;"> 
        <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <rect y="0.0209961" width="20" height="20" fill="url(#pattern0_14414_7990)"/>
        <defs>
        <pattern id="pattern0_14414_7990" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlink:href="#image0_14414_7990" transform="scale(0.000976562)"/>
        </pattern>
        <image id="image0_14414_7990" width="1024" height="1024" xlink:href="data:image/png;base64,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"/>
        </defs>
        </svg>

        <span style="margin-left:5px;"><strong>FREE </strong>US Shipping*</span>
    </div>
</div>
</div>

<style>
/* Sticky Add to Cart Styles */
.sticky-cart {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #ffffff;
    padding: 10px 15px;
    display: flex;
    justify-content: center;
    z-index: 9999;
    flex-direction: column;
}

.sticky-add-to-cart {
  background-color: #ff6f61; /* Button color */
  color: #ffffff; /* Button text color */
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
}

.sticky-add-to-cart:hover {
  background-color: #ff4a3d; /* Hover effect */
}

  .vsproduct-ship-by {
    width: 100%;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    color: #282D3E;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin: 10px 0 0px 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function () {
  const stickyCartButton = document.querySelector('.sticky-add-to-cart');

  if (stickyCartButton) {
    stickyCartButton.addEventListener('click', function () {
      const addToCartButton = document.querySelector('[name="add"]');
      if (addToCartButton) {
        addToCartButton.click();
      }
    });
  }
});
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        function updateShippingDates() {
            var today = new Date();
            var futureDate = new Date(today.getTime() + 1 * 24 * 60 * 60 * 1000); // Add 1 day
            var options = {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
            };
            var formattedDate = futureDate.toLocaleDateString('en-US', options);

            var shippingDateElements = document.querySelectorAll('.shippingDate');
            shippingDateElements.forEach(function(element) {
                element.textContent = formattedDate;
            });
        }

        // Run once on page load
        updateShippingDates();

        // Optionally, update every minute to stay current
        setInterval(updateShippingDates, 60000);
    });
</script>

{% schema %}
{
  "name": "Sticky Add To Cart",
  "settings": [],
  "presets": [
    {
      "name": "Sticky Add To Cart",
      "category": "Custom Sections"
    }
  ]
}
{% endschema %}