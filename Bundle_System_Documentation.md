# Bundle Deals System Documentation

## Overview

The Bundle Deals system is a comprehensive solution that allows customers to purchase product bundles with automatic cart synchronization. When any item from a bundle is removed from the cart, all related bundle items are automatically removed to maintain bundle integrity.

## System Components

### 1. Bundle Deals Block
- **Location**: Product pages only
- **Purpose**: Displays bundle options with dynamic pricing and product collections
- **Activation**: Only shows on products tagged with 'bundle'

### 2. UpCart Integration
- **Purpose**: Handles visual cart updates and bundle synchronization
- **Function**: Automatically removes all bundle items when any single item is removed

## How It Works

### Bundle Structure

Each bundle consists of:
- **Main Product**: Tagged with 'main-product-bundle' (displays "Main" badge)
- **Additional Products**: Related items in the same bundle
- **Free Products**: Items with 0.00 pricing (automatically included)

### Product Setup Requirements

1. **Bundle Products Must Have**:
   - Tag: `bundle` (to show the bundle block)
   - Two variants per product:
     - `Regular` variant (for individual purchase)
     - `Bundle` variant (for bundle purchase)

2. **Main Product Must Have**:
   - Tag: `main-product-bundle` (displays the "Main" badge)

3. **Collections Setup**:
   - Create collections containing products with Bundle variants
   - Configure each bundle to use specific collections

### Bundle Configuration

#### Bundle Settings (3 bundles available):

**Bundle 1 - Starter Pack**
- Enable/disable toggle
- Custom name and description
- Collection selection
- Save text (e.g., "Save £20")

**Bundle 2 - Complete Treatment**
- Enable/disable toggle
- Custom name and description
- Collection selection
- Save text (e.g., "Save £35")

**Bundle 3 - Premium Pack**
- Enable/disable toggle
- Custom name and description
- Collection selection
- Save text (e.g., "Save £50")

### Color Customization Options

#### Bundle Styling:
- Bundle Title Color
- Tab Text Color
- Active Tab Color
- Tab Border Color
- Product Card Border Color
- Product Card Background
- Save Text Color

#### Free Gifts Section:
- Free Gifts Background
- FREE Text Color

#### Main Badge:
- Main Badge Background
- Main Badge Text Color

#### Pricing & Buttons:
- Price Text Color
- Add to Cart Button Background
- Add to Cart Button Text

#### Layout:
- Top margin (0-100px)
- Bottom margin (0-100px)

## UpCart Bundle Synchronization

### How Bundle Removal Works

1. **Detection**: System monitors cart changes every second
2. **Identification**: When an item is removed, system checks for Bundle Name property
3. **Synchronization**: All items with matching Bundle Name are automatically removed
4. **Visual Update**: UpCart cart drawer updates immediately to reflect changes

### Technical Implementation

#### Bundle Item Properties:
Each bundle item contains:
```
properties: {
  'Bundle Name': 'Starter Pack',
  '_bundle_id': 'bundle-1-template--id',
  '_bundle_type': 'collection_bundle',
  '_bundle_handle': 'product-handle',
  '_collection_bundle': 'true',
  '_variant_type': 'Bundle'
}
```

#### Removal Process:
1. Customer removes any bundle item from cart
2. System detects removal via cart monitoring
3. System identifies Bundle Name from removed item
4. All items with same Bundle Name are removed from cart
5. UpCart visual display updates automatically
6. Cart totals recalculate

### Supported Scenarios

✅ **Remove paid product** → All bundle items removed
✅ **Remove free product** → All bundle items removed  
✅ **Remove main product** → All bundle items removed
✅ **Multiple bundles in cart** → Only matching bundle removed
✅ **Mixed cart items** → Only bundle items removed, individual items remain

## User Experience

### Customer Journey

1. **Product Page**: Customer sees bundle options with pricing
2. **Bundle Selection**: Customer chooses desired bundle
3. **Add to Cart**: All bundle items added simultaneously
4. **Cart Management**: Removing any bundle item removes entire bundle
5. **Checkout**: Customer proceeds with remaining items

### Visual Indicators

- **Main Badge**: Green badge showing "Main" on primary product
- **Free Items**: Crossed-out pricing with "FREE" indicator
- **Bundle Pricing**: Individual vs bundle pricing comparison
- **Save Amount**: Clear savings display (e.g., "Save £20")

## Best Practices

### For Store Setup:
1. Ensure all bundle products have both Regular and Bundle variants
2. Set Bundle variant prices appropriately (including 0.00 for free items)
3. Tag main products with 'main-product-bundle'
4. Create organized collections for each bundle type
5. Test bundle functionality before going live

### For Customers:
1. Bundle integrity is maintained automatically
2. Removing any item removes the entire bundle
3. Individual products can still be purchased separately
4. Free items are automatically included in bundles

## Troubleshooting

### Common Issues:

**Bundle block not showing:**
- Verify product has 'bundle' tag
- Check that Bundle variants exist

**Items not removing together:**
- Verify Bundle Name property is set correctly
- Check UpCart integration is active

**Pricing issues:**
- Confirm Bundle variant pricing is set correctly
- Verify compare_at_price for crossed-out pricing

## Technical Notes

- System uses Shopify Cart API for cart management
- UpCart integration provides visual cart updates
- Bundle synchronization works with any cart drawer system
- No hardcoded product IDs - fully dynamic configuration
- Compatible with existing theme functionality

## Quick Setup Checklist

### For New Bundle Products:

1. **Create Product Variants**:
   - [ ] Add "Regular" variant for individual purchase
   - [ ] Add "Bundle" variant for bundle purchase
   - [ ] Set appropriate pricing for each variant

2. **Product Tags**:
   - [ ] Add 'bundle' tag to enable bundle block
   - [ ] Add 'main-product-bundle' tag to main product (for badge)

3. **Collections**:
   - [ ] Create collection for each bundle type
   - [ ] Add products with Bundle variants to collections

4. **Bundle Configuration**:
   - [ ] Enable desired bundles (1-3)
   - [ ] Set bundle names and descriptions
   - [ ] Select appropriate collections
   - [ ] Configure save text and pricing

5. **Testing**:
   - [ ] Verify bundle block appears on tagged products
   - [ ] Test bundle addition to cart
   - [ ] Test bundle removal synchronization
   - [ ] Confirm pricing displays correctly

## FAQ

**Q: Can customers buy individual products if they're in a bundle?**
A: Yes, customers can purchase individual products using the Regular variant. The bundle system only affects Bundle variants.

**Q: What happens if a bundle product is out of stock?**
A: The system checks variant availability. Unavailable products won't be added to bundles.

**Q: Can I have more than 3 bundles?**
A: Currently, the system supports 3 bundles per product. Additional bundles would require development work.

**Q: Do bundles work with discount codes?**
A: Yes, Shopify discount codes apply to bundle items like any other cart items.

**Q: Can I customize the bundle layout?**
A: Yes, extensive color and styling options are available in the theme customizer.

---

*This documentation covers the complete Bundle Deals system implementation. For technical support or customization requests, please contact your development team.*
