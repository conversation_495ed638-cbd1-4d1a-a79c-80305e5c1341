

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-562474574982153126.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-562474574982153126.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-562474574982153126.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-562474574982153126.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-562474574982153126.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-562474574982153126.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-562474574982153126.gps.gpsil [style*="--hvr-bgc:"]:hover{background-color:var(--hvr-bgc)}.gps-562474574982153126.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-562474574982153126.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-562474574982153126.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-562474574982153126.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-562474574982153126.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-562474574982153126.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-562474574982153126.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-562474574982153126.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-562474574982153126.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-562474574982153126.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-562474574982153126.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-562474574982153126.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-562474574982153126.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-562474574982153126.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-562474574982153126.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-562474574982153126.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-562474574982153126.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-562474574982153126.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-562474574982153126.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-562474574982153126.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-562474574982153126.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-562474574982153126.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-562474574982153126.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-562474574982153126.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-562474574982153126.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-562474574982153126.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-562474574982153126.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-562474574982153126.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-562474574982153126.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-562474574982153126.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-562474574982153126.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-562474574982153126.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-562474574982153126.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-562474574982153126.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-562474574982153126.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-562474574982153126.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-562474574982153126.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-562474574982153126.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-562474574982153126.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-562474574982153126.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-562474574982153126.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-562474574982153126.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-562474574982153126.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-562474574982153126.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-562474574982153126.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-562474574982153126.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-562474574982153126.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-562474574982153126.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-562474574982153126.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-562474574982153126.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-562474574982153126.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-562474574982153126.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-562474574982153126.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-562474574982153126.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-562474574982153126.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-562474574982153126.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-562474574982153126.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-562474574982153126.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-562474574982153126.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-562474574982153126.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-562474574982153126.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-562474574982153126.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-562474574982153126.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-562474574982153126.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-562474574982153126.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-562474574982153126.gps.gpsil [style*="--objf-tablet:"]{-o-object-fit:var(--objf-tablet);object-fit:var(--objf-tablet)}.gps-562474574982153126.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-562474574982153126.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-562474574982153126.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-562474574982153126.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-562474574982153126.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-562474574982153126.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-562474574982153126.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-562474574982153126.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-562474574982153126.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-562474574982153126.gps.gpsil [style*="--bblr-mobile:"]{border-bottom-left-radius:var(--bblr-mobile)}.gps-562474574982153126.gps.gpsil [style*="--bbrr-mobile:"]{border-bottom-right-radius:var(--bbrr-mobile)}.gps-562474574982153126.gps.gpsil [style*="--btlr-mobile:"]{border-top-left-radius:var(--btlr-mobile)}.gps-562474574982153126.gps.gpsil [style*="--btrr-mobile:"]{border-top-right-radius:var(--btrr-mobile)}.gps-562474574982153126.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-562474574982153126.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-562474574982153126.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-562474574982153126.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-562474574982153126.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-562474574982153126.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-562474574982153126.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-562474574982153126.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-562474574982153126.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-562474574982153126.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-562474574982153126.gps.gpsil [style*="--objf-mobile:"]{-o-object-fit:var(--objf-mobile);object-fit:var(--objf-mobile)}.gps-562474574982153126.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-562474574982153126.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-562474574982153126.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-562474574982153126.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-562474574982153126.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-562474574982153126.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-562474574982153126.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-562474574982153126 .-gp-translate-x-1\/2,.gps-562474574982153126 .-gp-translate-y-1\/2{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-562474574982153126 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-562474574982153126 .gp-pointer-events-none{pointer-events:none}.gps-562474574982153126 .gp-absolute{position:absolute}.gps-562474574982153126 .gp-relative{position:relative}.gps-562474574982153126 .gp-left-0{left:0}.gps-562474574982153126 .gp-left-1\/2{left:50%}.gps-562474574982153126 .gp-top-0{top:0}.gps-562474574982153126 .gp-top-1\/2{top:50%}.gps-562474574982153126 .gp-z-0{z-index:0}.gps-562474574982153126 .gp-z-1{z-index:1}.gps-562474574982153126 .gp-z-\[90\]{z-index:90}.gps-562474574982153126 .\!gp-m-0{margin:0!important}.gps-562474574982153126 .\!gp-m-auto{margin:auto!important}.gps-562474574982153126 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-562474574982153126 .gp-mb-0{margin-bottom:0}.gps-562474574982153126 .gp-block{display:block}.gps-562474574982153126 .gp-flex{display:flex}.gps-562474574982153126 .gp-inline-flex{display:inline-flex}.gps-562474574982153126 .gp-grid{display:grid}.gps-562474574982153126 .gp-contents{display:contents}.gps-562474574982153126 .\!gp-hidden{display:none!important}.gps-562474574982153126 .gp-hidden{display:none}.gps-562474574982153126 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-562474574982153126 .gp-h-0{height:0}.gps-562474574982153126 .gp-h-full{height:100%}.gps-562474574982153126 .\!gp-w-full{width:100%!important}.gps-562474574982153126 .gp-w-14{width:56px}.gps-562474574982153126 .gp-w-5{width:20px}.gps-562474574982153126 .gp-w-full{width:100%}.gps-562474574982153126 .gp-min-w-0{min-width:0}.gps-562474574982153126 .\!gp-max-w-full{max-width:100%!important}.gps-562474574982153126 .\!gp-max-w-none{max-width:none!important}.gps-562474574982153126 .gp-max-w-full{max-width:100%}.gps-562474574982153126 .gp-flex-1{flex:1 1 0%}.gps-562474574982153126 .gp-flex-none{flex:none}.gps-562474574982153126 .gp-shrink-0{flex-shrink:0}.gps-562474574982153126 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-562474574982153126 .-gp-translate-x-1\/2,.gps-562474574982153126 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-562474574982153126 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-562474574982153126 .gp-cursor-pointer{cursor:pointer}.gps-562474574982153126 .gp-grid-flow-row{grid-auto-flow:row}.gps-562474574982153126 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-562474574982153126 .gp-flex-row{flex-direction:row}.gps-562474574982153126 .gp-flex-row-reverse{flex-direction:row-reverse}.gps-562474574982153126 .gp-flex-col{flex-direction:column}.gps-562474574982153126 .gp-flex-wrap{flex-wrap:wrap}.gps-562474574982153126 .gp-items-start{align-items:flex-start}.gps-562474574982153126 .gp-items-center{align-items:center}.gps-562474574982153126 .gp-justify-start{justify-content:flex-start}.gps-562474574982153126 .gp-justify-center{justify-content:center}.gps-562474574982153126 .gp-gap-y-0{row-gap:0}.gps-562474574982153126 .gp-overflow-hidden{overflow:hidden}.gps-562474574982153126 .gp-whitespace-nowrap{white-space:nowrap}.gps-562474574982153126 .gp-break-words{overflow-wrap:break-word}.gps-562474574982153126 .\!gp-rounded-none{border-radius:0!important}.gps-562474574982153126 .gp-rounded{border-radius:4px}.gps-562474574982153126 .gp-rounded-none{border-radius:0}.gps-562474574982153126 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-562474574982153126 .gp-bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity))}.gps-562474574982153126 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-562474574982153126 .gp-p-4{padding:16px}.gps-562474574982153126 .gp-px-4{padding-left:16px;padding-right:16px}.gps-562474574982153126 .gp-py-2{padding-bottom:8px;padding-top:8px}.gps-562474574982153126 .\!gp-pb-0{padding-bottom:0!important}.gps-562474574982153126 .gp-pb-0{padding-bottom:0}.gps-562474574982153126 .gp-text-center{text-align:center}.gps-562474574982153126 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-562474574982153126 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-562474574982153126 .gp-no-underline{text-decoration-line:none}.gps-562474574982153126 .gp-opacity-0{opacity:0}.gps-562474574982153126 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-562474574982153126 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-562474574982153126 .gp-duration-200{transition-duration:.2s}.gps-562474574982153126 .gp-duration-300{transition-duration:.3s}.gps-562474574982153126 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-562474574982153126 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-562474574982153126 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-562474574982153126 .hover\:gp-bg-g-highlight:hover{background-color:var(--g-c-highlight)}}.gps-562474574982153126 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-562474574982153126 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-562474574982153126 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-562474574982153126 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-562474574982153126 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-562474574982153126 .tablet\:gp-block{display:block}.gps-562474574982153126 .tablet\:\!gp-hidden{display:none!important}.gps-562474574982153126 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-562474574982153126 .mobile\:gp-block{display:block}.gps-562474574982153126 .mobile\:\!gp-hidden{display:none!important}.gps-562474574982153126 .mobile\:gp-hidden{display:none}}.gps-562474574982153126 .\[\&\>\*\>div\:nth-child\(1\)\]\:gp-contents>*>div:first-child{display:contents}.gps-562474574982153126 .\[\&\>div\]\:gp-grid>div{display:grid}.gps-562474574982153126 .\[\&\>div\]\:gp-grid-rows-\[subgrid\]>div{grid-template-rows:subgrid}.gps-562474574982153126 .\[\&\>div\]\:gp-gap-y-0>div{row-gap:0}.gps-562474574982153126 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-562474574982153126 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-562474574982153126 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-562474574982153126 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-562474574982153126 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-562474574982153126 .\[\&_p\]\:gp-inline p{display:inline}.gps-562474574982153126 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-562474574982153126 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-562474574982153126 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="ghGuJuJEvT" data-id="ghGuJuJEvT"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:15px;--pb:var(--g-s-xl);--pr:15px;--mt-mobile:0px;--pt-mobile:var(--g-s-3xl);--pl-mobile:15px;--pb-mobile:var(--g-s-m);--pr-mobile:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="ghGuJuJEvT gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gevwdz-5St gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gFqvdP_3WT" data-id="gFqvdP_3WT"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-2xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gFqvdP_3WT gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="geBefNt6e7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gN6uNT1bjA">
    <div
      parentTag="Col"
        class="gN6uNT1bjA "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:29px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggN6uNT1bjA_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gg5bD3TX4n">
    <div
      parentTag="Col"
        class="gg5bD3TX4n "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#424242;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggg5bD3TX4n_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gdnaMXEpcm" data-id="gdnaMXEpcm"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pb:30px;--pb-mobile:0px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gdnaMXEpcm gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gp_TThOmXs gp-relative gp-flex gp-flex-col"
    >
      
    <gp-tab data-id="gYokUVVSBC" gp-data='{"setting":{"activeKey":1,"borderTab":{"active":{"border":"solid","color":"#2352E7","isCustom":true,"width":"0px 0px 2px 0px"},"hover":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#2352E7","isCustom":true,"isLink":true,"width":"0px 0px 2px 0px"},"normal":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#EEEEEE","isCustom":true,"isLink":true,"width":"0px 0px 1px 0px"}},"childItem":["<p>Tools</p>","<p>Creams/Serums</p>","<p>Clothing</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"rgba(245, 245, 245, 0)","normal":"rgba(0, 0, 0, 0)"},"labelColor":{"active":"#2352E7","normal":"#242424"},"labelTypo":{"custom":{"desktop":{"fontSize":"16px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"150%"},"mobile":{"fontSize":"14px"},"tablet":{"fontSize":"16px"}}},"labelTypoV2":{"custom":{"fontSize":{"desktop":"16px","mobile":"14px","tablet":"16px"},"fontStyle":"normal","fontWeight":"700","hasShadowText":false,"letterSpacing":"0px","lineHeight":{"desktop":"150%","mobile":"150%","tablet":"150%"},"textShadow":{"angle":37,"blur":"2px","color":"rgba(0, 0, 0, 0.6)","distance":"4px","type":"custom"}},"type":"paragraph-1"},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"center"},"panelFullWidth":{"desktop":false},"panelWidth":{"desktop":"490px"},"position":{"0":"t","1":"o","2":"p","desktop":"top"},"translate":"childItem"},"builderProps":{"uid":"gYokUVVSBC","builderData":{"advanced":{"border":{"desktop":{"normal":{"border":"none","borderType":"none","borderWidth":{},"color":"transparent","isCustom":false,"width":"0px"}}},"boxShadow":{"desktop":{"normal":{"angle":90,"blur":"4px","color":"rgba(18, 18, 18, 0.12)","distance":"2px","spread":"0px","type":"shadow-1"}}},"d":{"desktop":true,"mobile":true,"tablet":true},"hasBoxShadow":{"desktop":{"normal":false}},"op":{"desktop":"100%"},"rounded":{"desktop":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"mobile":{"normal":{"bblr":"16px","bbrr":"16px","btlr":"16px","btrr":"16px","radiusType":"custom"}}},"spacing-setting":{"desktop":{"margin":{},"padding":""},"mobile":{"link":false,"margin":{},"padding":{"top":"0px"}},"tablet":{"margin":"","padding":""}}},"childrens":["gz0q0Tpekl","gee6YzIgqs","g7zGM3WlvZ"],"label":"Tab","settings":{"activeKey":1,"borderTab":{"active":{"border":"solid","color":"#2352E7","isCustom":true,"width":"0px 0px 2px 0px"},"hover":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#2352E7","isCustom":true,"isLink":true,"width":"0px 0px 2px 0px"},"normal":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#EEEEEE","isCustom":true,"isLink":true,"width":"0px 0px 1px 0px"}},"childItem":["<p>Tools</p>","<p>Creams/Serums</p>","<p>Clothing</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"rgba(245, 245, 245, 0)","normal":"rgba(0, 0, 0, 0)"},"labelColor":{"active":"#2352E7","normal":"#242424"},"labelTypo":{"custom":{"desktop":{"fontSize":"16px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"150%"},"mobile":{"fontSize":"14px"},"tablet":{"fontSize":"16px"}}},"labelTypoV2":{"custom":{"fontSize":{"desktop":"16px","mobile":"14px","tablet":"16px"},"fontStyle":"normal","fontWeight":"700","hasShadowText":false,"letterSpacing":"0px","lineHeight":{"desktop":"150%","mobile":"150%","tablet":"150%"},"textShadow":{"angle":37,"blur":"2px","color":"rgba(0, 0, 0, 0.6)","distance":"4px","type":"custom"}},"type":"paragraph-1"},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"center"},"panelFullWidth":{"desktop":false},"panelWidth":{"desktop":"490px"},"position":{"0":"t","1":"o","2":"p","desktop":"top"},"translate":"childItem"},"styles":{},"tag":"Tabs","uid":"gYokUVVSBC","type":"component"}}}'>
      <div
        
        data-id=""
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr-mobile:16px;--bbrr-mobile:16px;--btlr-mobile:16px;--btrr-mobile:16px;--pt-mobile:0px"
        class="gp-flex gYokUVVSBC"
      >
        <style>
            .gYokUVVSBC .gp-navs-tab.left p,
            .gYokUVVSBC .gp-navs-tab.right p {
              word-wrap: break-word;
              white-space: break-spaces;
            }
            
        </style>
        <div
          class="gp-flex gp-w-full  gp-flex-col"
        >
          <div
            class="gp-flex"
            style="--jc:center"
          >
            <ul
              class="gp-tab-header-list gp-flex gp-flex-wrap  0:gp-flex-row 1:gp-flex-row 2:gp-flex-row gp-flex-row"
              style="--maxw:490px;--maxw-tablet:490px;--maxw-mobile:490px"
            >
              
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="gYokUVVSBC"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ts:none;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:0px;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggYokUVVSBC_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="gYokUVVSBC"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ts:none;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:0px;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggYokUVVSBC_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="gYokUVVSBC"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ts:none;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:0px;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggYokUVVSBC_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
            </ul>
          </div>
          <div
            class="gp-flex gp-flex-1 gp-min-w-0"
          >
            <div
              class="gp-tab-item-container gp-p-4 gp-pb-0"
              key="gYokUVVSBC"
              style="--w:100%"
            >
            
  <div
    
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-gYokUVVSBC"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:auto" class="g5pdE9X6Co ">
      

    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-same-height.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    
{%- assign gpBkProduct = product -%}
{%- assign gpBkProducts = products -%}
{%- liquid
      assign productHandles = 'firmandtone,anti-cellulite-the-original-wood-roller,celluease-daily-roller,pre-massage-brush'| split: ','
      assign products = null
      assign target_collection = null
      assign productSrc = 'PickProduct'
      assign c = 0
      assign limit = 4
      if request.page_type == 'collection' or preview_page_type == 'collection'
        assign target_collection = collection
        if target_collection == empty or target_collection == null
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
        endif
        paginate target_collection.products by 4
          assign products = target_collection.products  
        endpaginate
      else
        if productSrc == 'RelatedProduct'
          
    
    
        elsif productSrc == 'PickProduct'
          unless false 
            assign products = null | sort
            for handle in productHandles
              assign productH = all_products[handle] | sort
              assign products = products | concat: productH
            endfor
            assign limit = products.length
          else
            if 4 == 0
              paginate collections.all.products by 100000
                assign products = collections.all.products  | sort : "created_at"  | reverse
              endpaginate
            endif
          endunless
        else
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
          paginate target_collection.products by 4
            assign products = target_collection.products  
          endpaginate
        endif
      endif
      -%}
        
    {%- if products == null -%}
    {%- if count == 0 and 4 == 0 -%}
      {% if 'PickProduct' == 'RelatedProduct' -%}
          <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
        {% else %}
          <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
      {%- endif -%}
    {%- endif -%}
    {%-else-%}
      
      
    <div items-repeat data-id="g5pdE9X6Co" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--cg:30px;--cg-tablet:30px;--cg-mobile:24px;--gtc:repeat(4, minmax(0, 1fr));--gtc-tablet:repeat(4, minmax(0, 1fr));--gtc-mobile:repeat(2, minmax(0, 1fr))"
          class="gp-grid !gp-m-auto gp-w-full  gp-grid-flow-row"
      >
        
      <gp-same-height-v2
        class="gp-contents"
        style="visibility: hidden"
        gp-data='{"targetUid":"gjQujhUKHk","slidesToShow":{"desktop":4,"mobile":"2","tablet":4},"rowGap":{"desktop":"16px","mobile":"28px","tablet":"8px"}}'>
        
        {%- assign count = 0 -%}
        {%- for product in products limit: limit -%}
        {%- if product.id != empty -%}
        {%- assign count = count | plus: 1 -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
        {%- assign product_form_id = 'product-form-' | append: "g5pdE9X6Co" | append: product.id -%}
            <gp-product 
              class="gp-child-item-g5pdE9X6Co  gp-contents" 
              gp-context='{"variantSelected": {{ variant | json | escape }}, "quantity": 1 ,  "formId": "{{ product_form_id }}"}' 
              gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }},"pageContext" : {"pageType":"GP_INDEX","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":true}}'
            >
              {%- form 'product', product, id: product_form_id, class: 'form contents  gp-contents [&>div]:gp-grid [&>div]:gp-grid-rows-[subgrid] [&>div]:gp-gap-y-0 [&>div]:gp-grid-col-[subgrid] [&>*>div:nth-child(1)]:gp-contents',  data-type: 'add-to-cart-form', autocomplete: 'off' -%}
              <input type="hidden" name="id" value="{{ variant.id }}" />
              <input type="hidden" min="1" name="quantity"  value="{{ quantity }}" />
              <button type="submit" onclick="return false;" style="display:none;"></button>
                      
       
      
    <div
      parentTag="ProductList" id="gjQujhUKHk" data-id="gjQujhUKHk"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gjQujhUKHk gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gExAxlFtwn gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gnFZLVUQ6G",
      "pageContext": {"pageType":"GP_INDEX","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":true},
      "setting":{"arrowIcon":"","borderActive":{"borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false,"mobile":false,"tablet":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftClickOpenLightBox":{"desktop":"product-link"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftNavigationPosition":{"desktop":"none","mobile":"none","tablet":"none"},"galleryClickEffect":"preview","hoverEffect":"zoom","loop":{"desktop":true,"mobile":true,"tablet":true},"navigationPosition":{"desktop":"inside","mobile":"inside","tablet":"inside"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-images","speed":1,"type":{"desktop":"images","mobile":"images","tablet":"images"},"typeDisplay":"all-images","zoom":1.5,"zoomType":"default"},
      "styles":{"align":{"desktop":"center","mobile":"flex-start","tablet":"flex-start"},"aspectHeight":{"desktop":0,"mobile":0,"tablet":0},"aspectWidth":{"desktop":0,"mobile":0,"tablet":0},"dotActiveColor":{"desktop":"highlight"},"dotColor":{"desktop":"bg-1"},"ftAspectHeight":{"desktop":0,"mobile":0,"tablet":0},"ftAspectWidth":{"desktop":0,"mobile":0,"tablet":0},"ftCorner":{"bblr":"16px","bbrr":"16px","btlr":"16px","btrr":"16px","radiusType":"custom"},"ftLayout":{"desktop":"fill","mobile":"fill","tablet":"fill"},"ftShape":{"desktop":{"shape":"original","shapeLinked":true,"width":"100%"},"mobile":{"shape":"original","shapeLinked":true,"width":"100%"},"tablet":{"shape":"original","shapeLinked":true,"width":"100%"}},"height":{},"itemSpacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover","mobile":"cover","tablet":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature","tablet":"only-feature"},"ratioLayout":{"desktop":{},"mobile":{},"tablet":{}},"ratioLayoutRight":{"desktop":{},"mobile":{},"tablet":{}},"shape":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor1Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor2Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForBottom":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForFtOnly":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"spacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"verticalLayout":{"desktop":false,"mobile":false,"tablet":false},"verticalLayoutRow":{"desktop":true,"mobile":true,"tablet":true}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      data-id="gnFZLVUQ6G"
      class="gnFZLVUQ6G gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = product.media.first %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:center;--jc-tablet:flex-start;--jc-mobile:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--bblr:16px;--bbrr:16px;--btlr:16px;--btrr:16px;--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-tablet: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-mobile: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}} gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
      <div class="zoom-element !gp-max-w-none gp-absolute gp-left-0 gp-top-0 gp-opacity-0 gp-transition-opacity gp-bg-white gp-pointer-events-none">
            
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none image-zoom gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
          </div>
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:;--aspect-tablet:;--aspect-mobile:"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:2237/1678;--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      {{ featureImageOnlyOne }}
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)" class="gpdJJMCvz2 ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gpdJJMCvz2">
    <div
      
        class="gpdJJMCvz2 "
        style="--tt:default"
      >
      <div  >
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title"
          style="--w:100%;--ts:none;--ta:center;--line-clamp:2;--line-clamp-tablet:2;--line-clamp-mobile:0;--c:#242424;--ff:var(--g-font-Open-Sans, 'Open Sans'), var(--g-font-heading, heading);--ls:0px;--size:16px;--size-mobile:14px;--lh:150%;--weight:bold;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h2>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)" class="g0mjGuMw74 ">
      
    
  <gp-button >
  <div
    style="--ta:center"
    
  >
    <style>
    .g0mjGuMw74.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .g0mjGuMw74:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .g0mjGuMw74:hover .gp-button-icon {
      color: undefined;
    }

     .g0mjGuMw74 .gp-button-icon {
      color: #000000;
    }

    .g0mjGuMw74:hover .gp-button-price {
      color: undefined;
    }

    .g0mjGuMw74 .gp-button-price {
      color: #000000;
    }

    .g0mjGuMw74 .gp-product-dot-price {
       color: #000000;
    }

    .g0mjGuMw74:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="{{ product.url }}" target="_self" data-id="g0mjGuMw74" aria-label="View more"
      
      data-state="idle"
      class="g0mjGuMw74 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none hover:gp-bg-g-highlight gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:var(--g-c-highlight, highlight);--bg:#ffffff;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:28px;--h-tablet:28px;--h-mobile:28px;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:#000000"
    >
      
    <div
    class="gp-inline-flex gp-flex-row-reverse">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--ml:8px;--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817609069166952">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M220.24,132.24l-72,72a6,6,0,0,1-8.48-8.48L201.51,134H40a6,6,0,0,1,0-12H201.51L139.76,60.24a6,6,0,0,1,8.48-8.48l72,72A6,6,0,0,1,220.24,132.24Z" /></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:#000000"
      >
        {{ section.settings.gg0mjGuMw74_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  
      </div>
    </div>
    </div>
   
    
              {%- endform -%}
            </gp-product>
        {%- endif -%}
        {% endfor %}
        {%- if count == 0 and 4 == 0 -%}
          {% if 'PickProduct' == 'RelatedProduct' -%}
              <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
            {% else %}
              <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
          {%- endif -%}
        {%- endif -%}
      
      </gp-same-height-v2>
    
        
      </div>

    
    
      
    {%- endif -%}
    
{%- assign product = gpBkProduct -%}
{%- assign products = gpBkProducts -%}

      </div>
  </div>
  
  <div
    
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-gYokUVVSBC"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl)" class="gXu6OeM7iV ">
      
    <div
    data-id="gXu6OeM7iV"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr-mobile:16px;--bbrr-mobile:16px;--btlr-mobile:16px;--btrr-mobile:16px;--mb-mobile:var(--g-s-3xl)" class="g6iNnzVlOU ">
      

    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-same-height.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    
{%- assign gpBkProduct = product -%}
{%- assign gpBkProducts = products -%}
{%- liquid
      assign productHandles = 'ginger-massage-oil,cellulift-collagen-firming-cream,firm-tone-overnight-whip'| split: ','
      assign products = null
      assign target_collection = null
      assign productSrc = 'PickProduct'
      assign c = 0
      assign limit = 4
      if request.page_type == 'collection' or preview_page_type == 'collection'
        assign target_collection = collection
        if target_collection == empty or target_collection == null
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
        endif
        paginate target_collection.products by 4
          assign products = target_collection.products  
        endpaginate
      else
        if productSrc == 'RelatedProduct'
          
    
    
        elsif productSrc == 'PickProduct'
          unless false 
            assign products = null | sort
            for handle in productHandles
              assign productH = all_products[handle] | sort
              assign products = products | concat: productH
            endfor
            assign limit = products.length
          else
            if 3 == 0
              paginate collections.all.products by 100000
                assign products = collections.all.products  | sort : "created_at"  | reverse
              endpaginate
            endif
          endunless
        else
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
          paginate target_collection.products by 4
            assign products = target_collection.products  
          endpaginate
        endif
      endif
      -%}
        
    {%- if products == null -%}
    {%- if count == 0 and 3 == 0 -%}
      {% if 'PickProduct' == 'RelatedProduct' -%}
          <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
        {% else %}
          <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
      {%- endif -%}
    {%- endif -%}
    {%-else-%}
      
      
    <div items-repeat data-id="g6iNnzVlOU" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--cg:30px;--cg-tablet:30px;--cg-mobile:24px;--gtc:repeat(4, minmax(0, 1fr));--gtc-tablet:repeat(4, minmax(0, 1fr));--gtc-mobile:repeat(2, minmax(0, 1fr))"
          class="gp-grid !gp-m-auto gp-w-full  gp-grid-flow-row"
      >
        
      <gp-same-height-v2
        class="gp-contents"
        style="visibility: hidden"
        gp-data='{"targetUid":"gfyOo1gEMZ","slidesToShow":{"desktop":4,"mobile":2,"tablet":4},"rowGap":{"desktop":"16px","mobile":"28px","tablet":"8px"}}'>
        
        {%- assign count = 0 -%}
        {%- for product in products limit: limit -%}
        {%- if product.id != empty -%}
        {%- assign count = count | plus: 1 -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
        {%- assign product_form_id = 'product-form-' | append: "g6iNnzVlOU" | append: product.id -%}
            <gp-product 
              class="gp-child-item-g6iNnzVlOU  gp-contents" 
              gp-context='{"variantSelected": {{ variant | json | escape }}, "quantity": 1 ,  "formId": "{{ product_form_id }}"}' 
              gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }},"pageContext" : {"pageType":"GP_INDEX","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":true}}'
            >
              {%- form 'product', product, id: product_form_id, class: 'form contents  gp-contents [&>div]:gp-grid [&>div]:gp-grid-rows-[subgrid] [&>div]:gp-gap-y-0 [&>div]:gp-grid-col-[subgrid] [&>*>div:nth-child(1)]:gp-contents',  data-type: 'add-to-cart-form', autocomplete: 'off' -%}
              <input type="hidden" name="id" value="{{ variant.id }}" />
              <input type="hidden" min="1" name="quantity"  value="{{ quantity }}" />
              <button type="submit" onclick="return false;" style="display:none;"></button>
                      
       
      
    <div
      parentTag="ProductList" id="gfyOo1gEMZ" data-id="gfyOo1gEMZ"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gfyOo1gEMZ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gw9k4a2zXp gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"g-o7VxIJWf",
      "pageContext": {"pageType":"GP_INDEX","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":true},
      "setting":{"arrowIcon":"","borderActive":{"borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false,"mobile":false,"tablet":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftClickOpenLightBox":{"desktop":"none"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftNavigationPosition":{"desktop":"none","mobile":"none","tablet":"none"},"galleryClickEffect":"preview","hoverEffect":"zoom","loop":{"desktop":true,"mobile":true,"tablet":true},"navigationPosition":{"desktop":"inside","mobile":"inside","tablet":"inside"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-images","speed":1,"type":{"desktop":"images","mobile":"images","tablet":"images"},"typeDisplay":"all-images","zoom":1.5,"zoomType":"default"},
      "styles":{"align":{"desktop":"center","mobile":"flex-start","tablet":"flex-start"},"aspectHeight":{"desktop":0,"mobile":0,"tablet":0},"aspectWidth":{"desktop":0,"mobile":0,"tablet":0},"dotActiveColor":{"desktop":"highlight"},"dotColor":{"desktop":"bg-1"},"ftAspectHeight":{"desktop":0,"mobile":0,"tablet":0},"ftAspectWidth":{"desktop":0,"mobile":0,"tablet":0},"ftCorner":{"bblr":"16px","bbrr":"16px","btlr":"16px","btrr":"16px","radiusType":"custom"},"ftLayout":{"desktop":"fill","mobile":"fill","tablet":"fill"},"ftShape":{"desktop":{"shape":"original","shapeLinked":true,"width":"100%"},"mobile":{"shape":"original","shapeLinked":true,"width":"100%"},"tablet":{"shape":"original","shapeLinked":true,"width":"100%"}},"height":{},"itemSpacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover","mobile":"cover","tablet":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature","tablet":"only-feature"},"ratioLayout":{"desktop":{},"mobile":{},"tablet":{}},"ratioLayoutRight":{"desktop":{},"mobile":{},"tablet":{}},"shape":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor1Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor2Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForBottom":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForFtOnly":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"spacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"verticalLayout":{"desktop":false,"mobile":false,"tablet":false},"verticalLayoutRow":{"desktop":true,"mobile":true,"tablet":true}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr-mobile:16px;--bbrr-mobile:16px;--btlr-mobile:16px;--btrr-mobile:16px;--mb:var(--g-s-l)"
      data-id="g-o7VxIJWf"
      class="g-o7VxIJWf gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = product.media.first %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:center;--jc-tablet:flex-start;--jc-mobile:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--bblr:16px;--bbrr:16px;--btlr:16px;--btrr:16px;--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-tablet: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-mobile: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}} gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
      <div class="zoom-element !gp-max-w-none gp-absolute gp-left-0 gp-top-0 gp-opacity-0 gp-transition-opacity gp-bg-white gp-pointer-events-none">
            
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none image-zoom gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
          </div>
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:;--aspect-tablet:;--aspect-mobile:"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:2237/1678;--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      {{ featureImageOnlyOne }}
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)" class="gb7xCgwbdc ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gb7xCgwbdc">
    <div
      
        class="gb7xCgwbdc "
        style="--tt:default"
      >
      <div  >
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title"
          style="--w:100%;--ts:none;--ta:center;--line-clamp:2;--line-clamp-tablet:2;--line-clamp-mobile:0;--c:#242424;--ff:var(--g-font-Open-Sans, 'Open Sans'), var(--g-font-heading, heading);--ls:0px;--size:16px;--size-mobile:14px;--lh:150%;--weight:bold;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h2>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g9-yFHJKhF ">
      
    
  <gp-button >
  <div
    style="--ta:center"
    
  >
    <style>
    .g9-yFHJKhF.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .g9-yFHJKhF:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .g9-yFHJKhF:hover .gp-button-icon {
      color: undefined;
    }

     .g9-yFHJKhF .gp-button-icon {
      color: #000000;
    }

    .g9-yFHJKhF:hover .gp-button-price {
      color: undefined;
    }

    .g9-yFHJKhF .gp-button-price {
      color: #000000;
    }

    .g9-yFHJKhF .gp-product-dot-price {
       color: #000000;
    }

    .g9-yFHJKhF:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="{{ product.url }}" target="_self" data-id="g9-yFHJKhF" aria-label="View more"
      
      data-state="idle"
      class="g9-yFHJKhF gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none hover:gp-bg-g-highlight gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:var(--g-c-highlight, highlight);--bg:#ffffff;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:28px;--h-tablet:28px;--h-mobile:28px;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:#000000"
    >
      
    <div
    class="gp-inline-flex gp-flex-row-reverse">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--ml:8px;--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817609069166952">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M220.24,132.24l-72,72a6,6,0,0,1-8.48-8.48L201.51,134H40a6,6,0,0,1,0-12H201.51L139.76,60.24a6,6,0,0,1,8.48-8.48l72,72A6,6,0,0,1,220.24,132.24Z" /></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:#000000"
      >
        {{ section.settings.gg9-yFHJKhF_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  
      </div>
    </div>
    </div>
   
    
              {%- endform -%}
            </gp-product>
        {%- endif -%}
        {% endfor %}
        {%- if count == 0 and 3 == 0 -%}
          {% if 'PickProduct' == 'RelatedProduct' -%}
              <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
            {% else %}
              <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
          {%- endif -%}
        {%- endif -%}
      
      </gp-same-height-v2>
    
        
      </div>

    
    
      
    {%- endif -%}
    
{%- assign product = gpBkProduct -%}
{%- assign products = gpBkProducts -%}

      </div>
  </div>
  
  <div
    
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-gYokUVVSBC"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl)" class="g60EcV0_XK ">
      
    <div
    data-id="g60EcV0_XK"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:var(--g-s-3xl)" class="gZb9v4TA9R ">
      

    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-same-height.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    
{%- assign gpBkProduct = product -%}
{%- assign gpBkProducts = products -%}
{%- liquid
      assign productHandles = 'shapepro-compression-leggings'| split: ','
      assign products = null
      assign target_collection = null
      assign productSrc = 'PickProduct'
      assign c = 0
      assign limit = 4
      if request.page_type == 'collection' or preview_page_type == 'collection'
        assign target_collection = collection
        if target_collection == empty or target_collection == null
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
        endif
        paginate target_collection.products by 4
          assign products = target_collection.products  
        endpaginate
      else
        if productSrc == 'RelatedProduct'
          
    
    
        elsif productSrc == 'PickProduct'
          unless false 
            assign products = null | sort
            for handle in productHandles
              assign productH = all_products[handle] | sort
              assign products = products | concat: productH
            endfor
            assign limit = products.length
          else
            if 1 == 0
              paginate collections.all.products by 100000
                assign products = collections.all.products  | sort : "created_at"  | reverse
              endpaginate
            endif
          endunless
        else
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
          paginate target_collection.products by 4
            assign products = target_collection.products  
          endpaginate
        endif
      endif
      -%}
        
    {%- if products == null -%}
    {%- if count == 0 and 1 == 0 -%}
      {% if 'PickProduct' == 'RelatedProduct' -%}
          <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
        {% else %}
          <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
      {%- endif -%}
    {%- endif -%}
    {%-else-%}
      
      
    <div items-repeat data-id="gZb9v4TA9R" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--cg:30px;--cg-tablet:30px;--cg-mobile:24px;--gtc:repeat(4, minmax(0, 1fr));--gtc-tablet:repeat(4, minmax(0, 1fr));--gtc-mobile:repeat(2, minmax(0, 1fr))"
          class="gp-grid !gp-m-auto gp-w-full  gp-grid-flow-row"
      >
        
      <gp-same-height-v2
        class="gp-contents"
        style="visibility: hidden"
        gp-data='{"targetUid":"gc6cyO0X-J","slidesToShow":{"desktop":4,"mobile":2,"tablet":4},"rowGap":{"desktop":"16px","mobile":"28px","tablet":"8px"}}'>
        
        {%- assign count = 0 -%}
        {%- for product in products limit: limit -%}
        {%- if product.id != empty -%}
        {%- assign count = count | plus: 1 -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
        {%- assign product_form_id = 'product-form-' | append: "gZb9v4TA9R" | append: product.id -%}
            <gp-product 
              class="gp-child-item-gZb9v4TA9R  gp-contents" 
              gp-context='{"variantSelected": {{ variant | json | escape }}, "quantity": 1 ,  "formId": "{{ product_form_id }}"}' 
              gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }},"pageContext" : {"pageType":"GP_INDEX","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":true}}'
            >
              {%- form 'product', product, id: product_form_id, class: 'form contents  gp-contents [&>div]:gp-grid [&>div]:gp-grid-rows-[subgrid] [&>div]:gp-gap-y-0 [&>div]:gp-grid-col-[subgrid] [&>*>div:nth-child(1)]:gp-contents',  data-type: 'add-to-cart-form', autocomplete: 'off' -%}
              <input type="hidden" name="id" value="{{ variant.id }}" />
              <input type="hidden" min="1" name="quantity"  value="{{ quantity }}" />
              <button type="submit" onclick="return false;" style="display:none;"></button>
                      
       
      
    <div
      parentTag="ProductList" id="gc6cyO0X-J" data-id="gc6cyO0X-J"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gc6cyO0X-J gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gzz20zfKtP gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gRfoYEhye3",
      "pageContext": {"pageType":"GP_INDEX","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":true},
      "setting":{"arrowIcon":"","borderActive":{"borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false,"mobile":false,"tablet":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftClickOpenLightBox":{"desktop":"none"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftNavigationPosition":{"desktop":"none","mobile":"none","tablet":"none"},"galleryClickEffect":"preview","hoverEffect":"zoom","loop":{"desktop":true,"mobile":true,"tablet":true},"navigationPosition":{"desktop":"inside","mobile":"inside","tablet":"inside"},"otherImage":0,"pauseOnHover":true,"speed":1,"type":{"desktop":"images","mobile":"images","tablet":"images"},"typeDisplay":"all-images","zoom":1.5,"zoomType":"default"},
      "styles":{"align":{"desktop":"center","mobile":"flex-start","tablet":"flex-start"},"aspectHeight":{"desktop":0,"mobile":0,"tablet":0},"aspectWidth":{"desktop":0,"mobile":0,"tablet":0},"dotActiveColor":{"desktop":"highlight"},"dotColor":{"desktop":"bg-1"},"ftAspectHeight":{"desktop":0,"mobile":0,"tablet":0},"ftAspectWidth":{"desktop":0,"mobile":0,"tablet":0},"ftCorner":{"bblr":"16px","bbrr":"16px","btlr":"16px","btrr":"16px","radiusType":"custom"},"ftLayout":{"desktop":"fill","mobile":"fill","tablet":"fill"},"ftShape":{"desktop":{"shape":"original","shapeLinked":true,"width":"100%"},"mobile":{"shape":"original","shapeLinked":true,"width":"100%"},"tablet":{"shape":"original","shapeLinked":true,"width":"100%"}},"height":{},"itemSpacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover","mobile":"cover","tablet":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature","tablet":"only-feature"},"ratioLayout":{"desktop":{},"mobile":{},"tablet":{}},"ratioLayoutRight":{"desktop":{},"mobile":{},"tablet":{}},"shape":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor1Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor2Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForBottom":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForFtOnly":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"spacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"verticalLayout":{"desktop":false,"mobile":false,"tablet":false},"verticalLayoutRow":{"desktop":true,"mobile":true,"tablet":true}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      data-id="gRfoYEhye3"
      class="gRfoYEhye3 gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:center;--jc-tablet:flex-start;--jc-mobile:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--bblr:16px;--bbrr:16px;--btlr:16px;--btrr:16px;--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-tablet: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-mobile: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}} gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
      <div class="zoom-element !gp-max-w-none gp-absolute gp-left-0 gp-top-0 gp-opacity-0 gp-transition-opacity gp-bg-white gp-pointer-events-none">
            
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none image-zoom gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
          </div>
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:;--aspect-tablet:;--aspect-mobile:"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:2237/1678;--objf:fill;--objf-tablet:fill;--objf-mobile:fill"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      {{ featureImageOnlyOne }}
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)" class="gWuXqV9T3Z ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gWuXqV9T3Z">
    <div
      
        class="gWuXqV9T3Z "
        style="--tt:default"
      >
      <div  >
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title"
          style="--w:100%;--ts:none;--ta:center;--line-clamp:2;--line-clamp-tablet:2;--line-clamp-mobile:0;--c:#242424;--ff:var(--g-font-Open-Sans, 'Open Sans'), var(--g-font-heading, heading);--ls:0px;--size:16px;--size-mobile:14px;--lh:150%;--weight:bold;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h2>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gByCxboZvG ">
      
    
  <gp-button >
  <div
    style="--ta:center"
    
  >
    <style>
    .gByCxboZvG.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .gByCxboZvG:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gByCxboZvG:hover .gp-button-icon {
      color: undefined;
    }

     .gByCxboZvG .gp-button-icon {
      color: #000000;
    }

    .gByCxboZvG:hover .gp-button-price {
      color: undefined;
    }

    .gByCxboZvG .gp-button-price {
      color: #000000;
    }

    .gByCxboZvG .gp-product-dot-price {
       color: #000000;
    }

    .gByCxboZvG:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="{{ product.url }}" target="_self" data-id="gByCxboZvG" aria-label="View more"
      
      data-state="idle"
      class="gByCxboZvG gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none hover:gp-bg-g-highlight gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:var(--g-c-highlight, highlight);--bg:#ffffff;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:28px;--h-tablet:28px;--h-mobile:28px;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:#000000"
    >
      
    <div
    class="gp-inline-flex gp-flex-row-reverse">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--ml:8px;--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817609069166952">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M220.24,132.24l-72,72a6,6,0,0,1-8.48-8.48L201.51,134H40a6,6,0,0,1,0-12H201.51L139.76,60.24a6,6,0,0,1,8.48-8.48l72,72A6,6,0,0,1,220.24,132.24Z" /></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:#000000"
      >
        {{ section.settings.ggByCxboZvG_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  
      </div>
    </div>
    </div>
   
    
              {%- endform -%}
            </gp-product>
        {%- endif -%}
        {% endfor %}
        {%- if count == 0 and 1 == 0 -%}
          {% if 'PickProduct' == 'RelatedProduct' -%}
              <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
            {% else %}
              <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
          {%- endif -%}
        {%- endif -%}
      
      </gp-same-height-v2>
    
        
      </div>

    
    
      
    {%- endif -%}
    
{%- assign product = gpBkProduct -%}
{%- assign products = gpBkProducts -%}

      </div>
  </div>
  
            </div>
          </div>
        </div>
      </div>
    </gp-tab>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-tab.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4gaVQ.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4taVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4kaVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4saVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4jaVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* hebrew */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4iaVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* math */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B5caVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B5OaVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4vaVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4uaVQUwaEQbjB_mQ.woff) format('woff');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4gaVQUwaEQbjA.woff) format('woff');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 4",
    "tag": "section",
    "class": "gps-562474574982153126 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=562474574982153126)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggN6uNT1bjA_text","label":"ggN6uNT1bjA_text","default":"Our Best Sellers"},{"type":"html","id":"ggg5bD3TX4n_text","label":"ggg5bD3TX4n_text","default":"<p>The #1 Choice for Smoother, Firmer Skin – Loved by Thousands, Results You Can Feel.</p>"},{"type":"html","id":"ggYokUVVSBC_childItem_0","label":"ggYokUVVSBC_childItem_0","default":"<p>Tools</p>"},{"type":"html","id":"ggYokUVVSBC_childItem_1","label":"ggYokUVVSBC_childItem_1","default":"<p>Creams/Serums</p>"},{"type":"html","id":"ggYokUVVSBC_childItem_2","label":"ggYokUVVSBC_childItem_2","default":"<p>Clothing</p>"},{"type":"html","id":"gg0mjGuMw74_label","label":"gg0mjGuMw74_label","default":"View more"},{"type":"html","id":"gg9-yFHJKhF_label","label":"gg9-yFHJKhF_label","default":"View more"},{"type":"html","id":"ggByCxboZvG_label","label":"ggByCxboZvG_label","default":"View more"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
