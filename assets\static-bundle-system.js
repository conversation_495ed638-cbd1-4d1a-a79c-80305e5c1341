/**
 * Static Bundle System for Product Page Blocks
 * Handles static bundle configuration with exact design match
 * Version: 2.0 - Fixed Cart API Implementation
 */

class StaticBundleSystem {
  constructor() {
    this.init();
  }

  init() {
    // Initialize static bundle buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('bundle-panel__add-to-cart')) {
        e.preventDefault();
        this.handleBundleAddToCart(e.target);
      }
    });

    console.log('Static Bundle System initialized');
  }

  async handleBundleAddToCart(button) {
    console.log('🛒 Bundle Add to Cart clicked - Using theme\'s existing mechanism');

    const bundleProducts = button.dataset.bundleProducts;
    const bundleId = button.dataset.bundleId;
    const bundleName = button.dataset.bundleName;

    console.log('📦 Bundle Data:', {
      bundleProducts,
      bundleId,
      bundleName
    });

    if (!bundleProducts) {
      console.error('❌ No bundle products specified');
      return;
    }

    // Disable button and show loading
    button.disabled = true;
    const originalText = button.textContent;
    button.textContent = 'Adding to Cart...';

    try {
      // Parse product handles from collection
      const productHandles = bundleProducts.split(',').filter(handle => handle.trim());
      console.log('🔍 Product handles parsed:', productHandles);

      if (productHandles.length === 0) {
        throw new Error('No valid products in bundle collection');
      }

      // Get bundle variants for each product in collection
      console.log('🔄 Getting bundle variants...');
      const bundleItems = await this.getCollectionBundleVariants(productHandles, bundleId);
      console.log('✅ Bundle items found:', bundleItems);

      if (bundleItems.length === 0) {
        throw new Error('No bundle variants found in collection products');
      }

      // Use the Shopify Cart API
      console.log('🔄 Using Shopify Cart API...');
      await this.submitBundleUsingThemeMethod(bundleItems, bundleId, bundleName);

      // Success feedback
      button.textContent = `Added ${bundleItems.length} items to Cart!`;
      console.log('✅ Bundle added successfully using Cart API');

      setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
      }, 2000);

    } catch (error) {
      console.error('❌ Bundle add to cart failed:', error);
      button.textContent = 'Error - Try Again';
      setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
      }, 3000);
    }
  }

  async getCollectionBundleVariants(productHandles, bundleId) {
    console.log('🔍 Getting collection bundle variants for:', productHandles);
    const bundleItems = [];

    for (const handle of productHandles) {
      console.log(`📦 Processing product: ${handle}`);
      try {
        const product = await this.fetchProductData(handle);
        console.log(`✅ Product data fetched for ${handle}:`, product);

        const bundleVariant = this.findBundleVariant(product);
        console.log(`🔍 Bundle variant for ${handle}:`, bundleVariant);

        // Try Bundle variant first, fallback to Regular if Bundle not available
        console.log(`🔍 Bundle variant for ${handle}:`, bundleVariant);
        console.log(`🔍 All variants for ${handle}:`, product.variants?.map(v => ({
          id: v.id,
          title: v.title,
          available: v.available,
          inventory_quantity: v.inventory_quantity,
          inventory_management: v.inventory_management,
          inventory_policy: v.inventory_policy
        })));

        // Check if Bundle variant is available for purchase
        console.log(`🔍 Bundle variant availability check for ${handle}:`, {
          available: bundleVariant?.available,
          inventory_management: bundleVariant?.inventory_management,
          inventory_policy: bundleVariant?.inventory_policy,
          inventory_quantity: bundleVariant?.inventory_quantity
        });

        // ISSUE FOUND: inventory_management: 'shopify' means Shopify tracks inventory
        // Even if available: true, if inventory_quantity is 0, cart will reject it
        const hasInventoryIssue = bundleVariant?.inventory_management === 'shopify' &&
                                  bundleVariant?.inventory_quantity <= 0;

        if (hasInventoryIssue) {
          console.log(`⚠️ Bundle variant has inventory issue for ${handle}:`, {
            inventory_management: bundleVariant?.inventory_management,
            inventory_quantity: bundleVariant?.inventory_quantity,
            available: bundleVariant?.available,
            issue: 'Shopify tracks inventory but quantity is 0 - will be rejected by cart'
          });
        }

        const bundleAvailable = bundleVariant &&
          bundleVariant.available === true &&
          !hasInventoryIssue && (
            bundleVariant.inventory_management === null ||
            bundleVariant.inventory_management === undefined ||
            bundleVariant.inventory_policy === 'continue' ||
            (bundleVariant.inventory_management === 'shopify' && bundleVariant.inventory_quantity > 0)
          );

        let cartVariant;
        let variantType;

        if (bundleAvailable) {
          cartVariant = bundleVariant;
          variantType = 'Bundle';
          console.log(`✅ Using Bundle variant for ${handle}`);
        } else {
          // Fallback to Regular variant
          const regularVariant = product.variants?.find(v =>
            v.title === 'Regular' ||
            v.option1 === 'Regular' ||
            (v.available === true || v.inventory_management === null)
          );
          cartVariant = regularVariant;
          variantType = 'Regular (Bundle unavailable)';
          console.log(`⚠️ Bundle variant unavailable for ${handle}, using Regular variant`);
        }

        const canAddToCart = cartVariant && (
          cartVariant.available === true ||
          cartVariant.inventory_management === null ||
          cartVariant.inventory_management === undefined ||
          cartVariant.inventory_policy === 'continue'
        );

        if (canAddToCart) {
          const bundleItem = {
            id: cartVariant.id,
            quantity: 1,
            properties: {
              '_bundle_id': bundleId,
              '_bundle_type': 'collection_bundle',
              '_bundle_handle': handle,
              '_collection_bundle': 'true',
              '_variant_type': variantType
            }
          };
          bundleItems.push(bundleItem);
          console.log(`✅ Added bundle item for ${handle} (${variantType}):`, bundleItem);
        } else {
          console.warn(`⚠️ Cannot add variant to cart for product: ${handle}`, {
            bundleVariant,
            selectedVariant: cartVariant,
            available: cartVariant?.available,
            inventory_management: cartVariant?.inventory_management,
            canAddToCart
          });
        }
      } catch (error) {
        console.error(`❌ Failed to get bundle variant for ${handle}:`, error);
      }
    }

    console.log('📦 Final bundle items:', bundleItems);
    return bundleItems;
  }

  async submitBundleUsingThemeMethod(bundleItems, bundleId, bundleName) {
    console.log('🔄 Submitting all bundle items to cart...');
    console.log('🔍 Debug: Method started, no form variables should exist');

    try {

      // Use ALL bundle items with minimal properties
      console.log('🔄 Processing all bundle items...');

      const cartItems = bundleItems.map(item => {
        console.log(`🔍 Processing item:`, item);

        return {
          id: item.id,
          quantity: 1,
          properties: {
            //'Bundle ID': bundleId,
            //'Bundle Type': 'Collections Bundle',
            'Bundle Name': bundleName
          }
        };
      });

      console.log(`📦 Total items to add: ${cartItems.length}`);

      console.log('🔄 Using Shopify Cart API with items:', cartItems);
      console.log('🔍 Detailed items being sent:', cartItems.map(item => ({
        id: item.id,
        quantity: item.quantity,
        properties: Object.keys(item.properties || {})
      })));

      const response = await fetch('/cart/add.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          items: cartItems
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Cart API error:', response.status, errorText);
        throw new Error(`Cart API error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Cart API response:', result);
      console.log('🔍 Detailed response items:', result.items?.map(item => ({
        id: item.id,
        variant_id: item.variant_id,
        title: item.title,
        quantity: item.quantity,
        properties: item.properties
      })));

      // Check if the response actually contains items
      if (result.items && result.items.length > 0) {
        console.log('✅ Cart API successfully added items:', result.items.length);
      } else {
        console.log('⚠️ Cart API response has no items - possible variant issue');
      }

      // Verify what's actually in the cart
      setTimeout(async () => {
        try {
          const cartResponse = await fetch('/cart.js');
          const cartData = await cartResponse.json();
          console.log('🔍 ACTUAL CART CONTENTS:', {
            item_count: cartData.item_count,
            total_items: cartData.items.length,
            items: cartData.items.map(item => ({
              id: item.id,
              variant_id: item.variant_id,
              title: item.title,
              variant_title: item.variant_title,
              quantity: item.quantity,
              available: item.variant?.available,
              inventory_management: item.variant?.inventory_management
            }))
          });

          if (cartData.item_count === 0) {
            console.log('❌ CART IS EMPTY - Bundle variants were rejected by Shopify');
            console.log('💡 Bundle variants might not be available for purchase');
            console.log('💡 Check Shopify admin: Product > Variants > Bundle variant availability');
          } else {
            console.log('✅ Items confirmed in cart!');
          }
        } catch (error) {
          console.log('❌ Failed to verify cart:', error);
        }

        // Trigger cart events and open drawer
        document.dispatchEvent(new CustomEvent('cart:updated'));

        const cartIcon = document.querySelector('[data-cart-count], .cart-count-bubble, .header__icon--cart, [aria-label*="cart" i]');
        if (cartIcon) {
          cartIcon.click();
          console.log('✅ Opened cart using theme cart icon');
        }
      }, 1000);

      return result;

    } catch (error) {
      console.error('❌ Failed to submit bundle:', error);
      throw error;
    }
  }



  // Keep the old method for backward compatibility
  async getBundleVariants(productHandles, bundleId) {
    return this.getCollectionBundleVariants(productHandles, bundleId);
  }

  async fetchProductData(handle) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    try {
      const response = await fetch(`/products/${handle}.js`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch product ${handle}`);
      }

      const product = await response.json();

      if (!product || !product.variants || !Array.isArray(product.variants)) {
        throw new Error(`Invalid product data for ${handle}`);
      }

      return product;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  findBundleVariant(product) {
    console.log('🔍 Finding bundle variant for product:', product?.title || 'Unknown');

    if (!product || !product.variants || !Array.isArray(product.variants)) {
      console.log('❌ Invalid product data:', { product: !!product, variants: !!product?.variants, isArray: Array.isArray(product?.variants) });
      return null;
    }

    console.log('📦 Product variants:', product.variants.map(v => ({
      id: v.id,
      title: v.title,
      option1: v.option1,
      option2: v.option2,
      option3: v.option3,
      available: v.available
    })));

    const bundleVariant = product.variants.find(variant => {
      if (!variant) return false;

      const option1 = this.safeStringClean(variant.option1);
      const option2 = this.safeStringClean(variant.option2);
      const option3 = this.safeStringClean(variant.option3);

      const isBundle = option1 === 'bundle' || option2 === 'bundle' || option3 === 'bundle';

      if (isBundle) {
        console.log('✅ Found bundle variant:', {
          id: variant.id,
          title: variant.title,
          option1: variant.option1,
          option2: variant.option2,
          option3: variant.option3,
          available: variant.available
        });
      }

      return isBundle;
    });

    if (!bundleVariant) {
      console.log('❌ No bundle variant found for product:', product.title);
    }

    return bundleVariant;
  }

  safeStringClean(value) {
    if (value === null || value === undefined) return '';
    if (typeof value === 'string') return value.toLowerCase().trim();
    if (typeof value === 'number') return String(value).toLowerCase().trim();
    return String(value).toLowerCase().trim();
  }

  async addBundleToCart(items, bundleId, bundleName) {
    console.log('🛒 Adding bundle to cart:', {
      items,
      bundleId,
      bundleName
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000);

    try {
      const requestBody = { items };
      console.log('📤 Cart add request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch('/cart/add.js', {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      clearTimeout(timeoutId);
      console.log('📥 Cart add response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Failed to add bundle to cart';
        try {
          const errorData = await response.json();
          console.log('❌ Cart add error data:', errorData);
          if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.description) {
            errorMessage = errorData.description;
          }
        } catch (parseError) {
          console.log('❌ Error parsing error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log(`✅ Bundle "${bundleName}" added to cart successfully:`, result);

      // Store the result for potential retry
      this.lastBundleResult = {
        items: result.items,
        bundleId: bundleId,
        bundleName: bundleName
      };

      return result;

    } catch (error) {
      clearTimeout(timeoutId);
      console.error('❌ Cart add error:', error);
      throw error;
    }
  }

  async refreshCart() {
    try {
      console.log('🔄 Refreshing cart data...');

      // Fetch current cart
      const cartResponse = await fetch('/cart.js');
      const cartData = await cartResponse.json();
      console.log('📦 Current cart data:', cartData);

      // Update cart count in header
      const cartCountElements = document.querySelectorAll('[data-cart-count], .cart-count, #cart-count');
      cartCountElements.forEach(element => {
        if (element) {
          element.textContent = cartData.item_count;
          console.log('🔢 Updated cart count:', cartData.item_count);
        }
      });

      return cartData;

    } catch (error) {
      console.error('❌ Failed to refresh cart:', error);
      return null;
    }
  }

  triggerCartUpdateEvents() {
    console.log('📢 Triggering cart update events...');

    // Standard cart events first
    document.dispatchEvent(new CustomEvent('cart:updated'));
    document.dispatchEvent(new CustomEvent('cart:change'));
    document.dispatchEvent(new CustomEvent('cartUpdated'));

    // Update cart count
    this.updateCartCount();

    // Dawn theme specific cart refresh with error handling
    const cartDrawer = document.querySelector('cart-drawer');
    if (cartDrawer) {
      console.log('🛒 Found Dawn cart-drawer, triggering refresh...');

      try {
        // Method 1: Dispatch cart:updated event to cart drawer (safest)
        cartDrawer.dispatchEvent(new CustomEvent('cart:updated'));
        console.log('✅ Dispatched cart:updated to cart drawer');

        // Method 2: Use Dawn's built-in refresh method with error handling
        if (cartDrawer.renderContents && typeof cartDrawer.renderContents === 'function') {
          console.log('🔄 Attempting cartDrawer.renderContents()');
          try {
            cartDrawer.renderContents();
            console.log('✅ cartDrawer.renderContents() successful');
          } catch (renderError) {
            console.log('⚠️ cartDrawer.renderContents() failed:', renderError.message);
          }
        }

        // Method 3: Try to refresh cart sections safely
        if (cartDrawer.getSectionsToRender && cartDrawer.renderSections) {
          console.log('🔄 Using Dawn getSectionsToRender method');
          fetch('/cart.js')
            .then(response => response.json())
            .then(cart => {
              try {
                cartDrawer.renderSections(cartDrawer.getSectionsToRender(), cart);
                console.log('✅ Cart sections rendered successfully');
              } catch (sectionError) {
                console.log('⚠️ Cart section rendering failed:', sectionError.message);
              }
            })
            .catch(error => {
              console.log('⚠️ Failed to fetch cart for section rendering:', error.message);
            });
        }

      } catch (error) {
        console.log('⚠️ Cart drawer refresh error:', error.message);
      }
    }

    // Legacy theme compatibility
    if (window.theme && window.theme.cartUpdated) {
      console.log('🎨 Calling theme.cartUpdated()');
      try {
        window.theme.cartUpdated();
      } catch (error) {
        console.log('⚠️ theme.cartUpdated() failed:', error.message);
      }
    }

    if (window.refreshCartDrawer) {
      console.log('🛒 Calling refreshCartDrawer()');
      try {
        window.refreshCartDrawer();
      } catch (error) {
        console.log('⚠️ refreshCartDrawer() failed:', error.message);
      }
    }

    // Force cart drawer to open if it exists
    if (cartDrawer && cartDrawer.open && typeof cartDrawer.open === 'function') {
      console.log('🛒 Opening cart drawer');
      try {
        cartDrawer.open();
      } catch (error) {
        console.log('⚠️ Failed to open cart drawer:', error.message);
      }
    }
  }

  async updateCartCount() {
    try {
      console.log('🔄 Fetching latest cart data...');
      const response = await fetch('/cart.js');
      const cart = await response.json();

      console.log('📦 Current cart data:', {
        item_count: cart.item_count,
        total_items: cart.items.length,
        items: cart.items.map(item => ({
          title: item.title,
          variant_title: item.variant_title,
          quantity: item.quantity,
          id: item.id
        }))
      });

      // Update cart count elements
      const cartCountSelectors = [
        'cart-drawer .cart-count-bubble span',
        '.cart-count-bubble span',
        '[data-cart-count]',
        '.cart-count',
        '#cart-count',
        '.header__icon--cart .cart-count-bubble span'
      ];

      let updatedCount = 0;
      cartCountSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log(`🔍 Found ${elements.length} elements for selector: ${selector}`);
        elements.forEach(element => {
          if (element) {
            element.textContent = cart.item_count;
            updatedCount++;
            console.log(`🔢 Updated cart count (${selector}):`, cart.item_count);
          }
        });
      });

      console.log(`✅ Updated ${updatedCount} cart count elements`);

      return cart;

    } catch (error) {
      console.error('❌ Failed to update cart count:', error);
      return null;
    }
  }
}

// Initialize the static bundle system
function initializeStaticBundleSystem() {
  try {
    if (!window.staticBundleSystem) {
      window.staticBundleSystem = new StaticBundleSystem();
      console.log('Static Bundle System initialized successfully');
    }
  } catch (error) {
    console.error('Failed to initialize Static Bundle System:', error);
  }
}

// Initialize based on document state
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeStaticBundleSystem);
} else {
  initializeStaticBundleSystem();
}

// Handle theme section reloads
document.addEventListener('shopify:section:load', () => {
  setTimeout(initializeStaticBundleSystem, 100);
});

// Export for debugging
window.StaticBundleSystem = StaticBundleSystem;
