/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "60910267-55e8-4e0d-a9f9-79b26b3533dc": {
      "type": "rich-text",
      "blocks": {
        "template--18407212810569__60910267-55e8-4e0d-a9f9-79b26b3533dc-16795881913b1fb5f3-0": {
          "type": "heading",
          "settings": {
            "title": "Talk about your brand",
            "title_highlight_color": "#6d388b",
            "heading_size": "h1"
          }
        },
        "template--18407212810569__60910267-55e8-4e0d-a9f9-79b26b3533dc-16795881913b1fb5f3-1": {
          "type": "text",
          "settings": {
            "text": "<p>At [Your company], your satisfaction is our top priority. We encourage you to reach out with any questions, concerns, or feedback, as our dedicated team is always eager to assist you. Whether you need help with product inquiries or simply want to share your experience with us, we're just a message away. </p>"
          }
        }
      },
      "block_order": [
        "template--18407212810569__60910267-55e8-4e0d-a9f9-79b26b3533dc-16795881913b1fb5f3-0",
        "template--18407212810569__60910267-55e8-4e0d-a9f9-79b26b3533dc-16795881913b1fb5f3-1"
      ],
      "disabled": true,
      "settings": {
        "display_id": false,
        "visibility": "always-display",
        "desktop_content_position": "center",
        "content_alignment": "center",
        "full_width": true,
        "color_scheme": "background-1",
        "padding_top": 40,
        "padding_bottom": 12,
        "custom_colors_background": "#ffffff",
        "custom_gradient_background": "",
        "custom_colors_text": "#2e2a39",
        "custom_colors_solid_button_background": "#dd1d1d",
        "custom_colors_solid_button_text": "#ffffff",
        "custom_colors_outline_button": "#dd1d1d"
      }
    },
    "contact_form_U9ptC4": {
      "type": "contact-form",
      "blocks": {
        "field_row_B7EVcM": {
          "type": "field_row",
          "settings": {
            "input_1_enabled": true,
            "input_1_type": "name",
            "input_1_required": false,
            "input_1_error_message": "This field is required",
            "input_1_custom_name": "Custom input name",
            "input_2_enabled": true,
            "input_2_type": "email",
            "input_2_required": false,
            "input_2_error_message": "This field is required",
            "input_2_custom_name": "Custom input name"
          }
        },
        "field_row_HX8WGh": {
          "type": "field_row",
          "settings": {
            "input_1_enabled": true,
            "input_1_type": "phone",
            "input_1_required": false,
            "input_1_error_message": "This field is required",
            "input_1_custom_name": "Custom input name",
            "input_2_enabled": false,
            "input_2_type": "custom",
            "input_2_required": false,
            "input_2_error_message": "This field is required",
            "input_2_custom_name": "Custom input name"
          }
        },
        "textarea_trVaGc": {
          "type": "textarea",
          "settings": {
            "input_type": "comment",
            "input_required": false,
            "input_error_message": "This field is required",
            "input_custom_name": "Custom name"
          }
        }
      },
      "block_order": [
        "field_row_B7EVcM",
        "field_row_HX8WGh",
        "textarea_trVaGc"
      ],
      "settings": {
        "display_id": false,
        "visibility": "always-display",
        "title": "Get in touch",
        "title_highlight_color": "#6D388B",
        "heading_size": "h1",
        "button_full_width": true,
        "color_scheme": "background-1",
        "success_icon": "check_circle",
        "success_icon_filled": false,
        "success_text_color": "#28C100",
        "success_bg_color": "#F2FFED",
        "success_border_radius": 8,
        "success_border_width": 1,
        "success_border_color": "#28C100",
        "errors_icon": "error",
        "errors_icon_filled": false,
        "errors_text_color": "#FF0000",
        "errors_bg_color": "#fff5f5",
        "errors_border_radius": 8,
        "errors_border_width": 1,
        "errors_border_color": "#FF0000",
        "padding_top": 36,
        "padding_bottom": 36,
        "custom_colors_background": "#FFFFFF",
        "custom_gradient_background": "",
        "custom_colors_text": "#2E2A39",
        "custom_colors_solid_button_background": "#dd1d1d",
        "custom_colors_solid_button_text": "#ffffff"
      }
    }
  },
  "order": [
    "main",
    "60910267-55e8-4e0d-a9f9-79b26b3533dc",
    "contact_form_U9ptC4"
  ]
}
