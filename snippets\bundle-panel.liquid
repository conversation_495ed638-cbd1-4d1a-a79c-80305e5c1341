{%- comment -%}
  Bundle Panel - Collection-based bundle content
  Shows product card, collection products as free gifts, pricing and add to cart
{%- endcomment -%}

{%- liquid
  assign bundle_id = 'bundle-' | append: bundle_number | append: '-' | append: section_id
  assign collection = bundle_collection
  assign collection_products = collection.products

  # Separate products into main products and free gifts
  assign main_products = ''
  assign free_gifts = ''
  assign main_products_total = 0
  assign free_gifts_worth = 0
  assign bundle_products = ''
  assign total_individual_value = 0
  assign main_products_count = 0
  assign free_gifts_count = 0

  for product in collection_products limit: 20
    assign bundle_variant = null
    for variant in product.variants
      assign option1_clean = variant.option1 | default: '' | strip | downcase
      assign option2_clean = variant.option2 | default: '' | strip | downcase
      assign option3_clean = variant.option3 | default: '' | strip | downcase
      if option1_clean == 'bundle' or option2_clean == 'bundle' or option3_clean == 'bundle'
        assign bundle_variant = variant
        break
      endif
    endfor

    if bundle_variant
      # Add to bundle products list
      if bundle_products == ''
        assign bundle_products = product.handle
      else
        assign bundle_products = bundle_products | append: ',' | append: product.handle
      endif

      # Categorize as main product or free gift based on price
      if bundle_variant.price == 0
        # Free gift (£0.00 price)
        if free_gifts == ''
          assign free_gifts = product.id
        else
          assign free_gifts = free_gifts | append: ',' | append: product.id
        endif
        assign compare_price = bundle_variant.compare_at_price | default: 0
        assign free_gifts_worth = free_gifts_worth | plus: compare_price
        assign free_gifts_count = free_gifts_count | plus: 1
      else
        # Main product (has price)
        if main_products == ''
          assign main_products = product.id
        else
          assign main_products = main_products | append: ',' | append: product.id
        endif
        assign main_products_total = main_products_total | plus: bundle_variant.price
        assign main_products_count = main_products_count | plus: 1
      endif

      # Add to total individual value (for savings calculation)
      assign compare_price = bundle_variant.compare_at_price | default: bundle_variant.price
      assign total_individual_value = total_individual_value | plus: compare_price
    endif
  endfor

  assign savings = total_individual_value | minus: main_products_total
-%}

<div class="bundle-panel" data-bundle-id="{{ bundle_id }}">

  <!-- Bundle Header -->
  <div class="bundle-panel__header">
    <div class="bundle-panel__save-badge">Save {{ savings | money }} on this bundle</div>
  </div>

  <!-- Products Included Section -->
  <div class="bundle-panel__products-section">
    <div class="bundle-panel__section-header">
      <h4>Products Included</h4>
      <span class="bundle-panel__product-count">{{ main_products_count }} Products</span>
    </div>

    <div class="bundle-panel__products-list">
      {%- comment -%} First show main products {%- endcomment -%}
      {%- for product in collection_products limit: 20 -%}
        {%- if product.tags contains 'main-product-bundle' -%}
          {%- liquid
            assign bundle_variant = null
            for variant in product.variants
              assign option1_clean = variant.option1 | default: '' | strip | downcase
              assign option2_clean = variant.option2 | default: '' | strip | downcase
              assign option3_clean = variant.option3 | default: '' | strip | downcase
              if option1_clean == 'bundle' or option2_clean == 'bundle' or option3_clean == 'bundle'
                assign bundle_variant = variant
                break
              endif
            endfor
          -%}

          {%- if bundle_variant and bundle_variant.price > 0 -%}
            <div class="bundle-panel__product-item">
              <div class="bundle-panel__product-image">
                {%- if product.featured_image -%}
                  <img src="{{ product.featured_image | image_url: width: 80 }}"
                       alt="{{ product.title }}"
                       width="80"
                       height="80"
                       loading="lazy">
                {%- endif -%}
              </div>
              <div class="bundle-panel__product-info">
                <h5 class="bundle-panel__product-name">{{ product.title }}</h5>
                <p class="bundle-panel__product-price">Individual price: {{ bundle_variant.compare_at_price | default: bundle_variant.price | money }}</p>
              </div>
              <span class="bundle-panel__main-badge">Main</span>
            </div>
          {%- endif -%}
        {%- endif -%}
      {%- endfor -%}

      {%- comment -%} Then show other products {%- endcomment -%}
      {%- for product in collection_products limit: 20 -%}
        {%- unless product.tags contains 'main-product-bundle' -%}
          {%- liquid
            assign bundle_variant = null
            for variant in product.variants
              assign option1_clean = variant.option1 | default: '' | strip | downcase
              assign option2_clean = variant.option2 | default: '' | strip | downcase
              assign option3_clean = variant.option3 | default: '' | strip | downcase
              if option1_clean == 'bundle' or option2_clean == 'bundle' or option3_clean == 'bundle'
                assign bundle_variant = variant
                break
              endif
            endfor
          -%}

          {%- if bundle_variant and bundle_variant.price > 0 -%}
            <div class="bundle-panel__product-item">
              <div class="bundle-panel__product-image">
                {%- if product.featured_image -%}
                  <img src="{{ product.featured_image | image_url: width: 80 }}"
                       alt="{{ product.title }}"
                       width="80"
                       height="80"
                       loading="lazy">
                {%- endif -%}
              </div>
              <div class="bundle-panel__product-info">
                <h5 class="bundle-panel__product-name">{{ product.title }}</h5>
                <p class="bundle-panel__product-price">Individual price: {{ bundle_variant.compare_at_price | default: bundle_variant.price | money }}</p>
              </div>
            </div>
          {%- endif -%}
        {%- endunless -%}
      {%- endfor -%}
    </div>

    <div class="bundle-panel__total-value">
      <span>Total individual value: {{ total_individual_value | money }}</span>
    </div>
  </div>

  <!-- Free Gifts Section -->
  <div class="bundle-panel__free-gifts">
    <div class="bundle-panel__section-header">
      <h4>Free Gifts Included</h4>
      <span class="bundle-panel__gifts-worth">Worth {{ free_gifts_worth | money }}</span>
    </div>

    <div class="bundle-panel__gifts-grid">
      {%- for product in collection_products limit: 20 -%}
        {%- liquid
          assign bundle_variant = null
          for variant in product.variants
            assign option1_clean = variant.option1 | default: '' | strip | downcase
            assign option2_clean = variant.option2 | default: '' | strip | downcase
            assign option3_clean = variant.option3 | default: '' | strip | downcase
            if option1_clean == 'bundle' or option2_clean == 'bundle' or option3_clean == 'bundle'
              assign bundle_variant = variant
              break
            endif
          endfor
        -%}

        {%- if bundle_variant and bundle_variant.price == 0 -%}
          <div class="bundle-panel__gift-item">
            <div class="bundle-panel__gift-image">
              {%- if product.featured_image -%}
                <img src="{{ product.featured_image | image_url: width: 60 }}"
                     alt="{{ product.title }}"
                     width="60"
                     height="60"
                     loading="lazy">
              {%- else -%}
                <div class="bundle-panel__gift-placeholder">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#ccc"/>
                  </svg>
                </div>
              {%- endif -%}
            </div>
            <div class="bundle-panel__gift-info">
              <h5 class="bundle-panel__gift-name">{{ product.title }}</h5>
              <p class="bundle-panel__gift-price">
                {%- if bundle_variant.compare_at_price and bundle_variant.compare_at_price > 0 -%}
                  <s>{{ bundle_variant.compare_at_price | money }}</s>
                {%- endif -%}
                <span class="bundle-panel__free-text">FREE</span>
              </p>
            </div>
          </div>
        {%- endif -%}
      {%- endfor -%}
    </div>
  </div>

  <!-- Pricing Section -->
  <div class="bundle-panel__pricing">
    <div class="bundle-panel__price-row">
      <div class="bundle-panel__price-main">{{ main_products_total | money }}</div>
      <div class="bundle-panel__savings">Save {{ savings | money }}</div>
    </div>
    <div class="bundle-panel__price-compare">{{ total_individual_value | money }}</div>
  </div>

  <!-- Add to Cart Button -->
  <button class="bundle-panel__add-to-cart"
          data-bundle-products="{{ bundle_products }}"
          data-bundle-id="{{ bundle_id }}"
          data-bundle-name="{{ bundle_name }}">
    Add to Cart
  </button>

  <!-- Additional Info -->
  <div class="bundle-panel__info">
  </div>

</div>

<style>
  .bundle-panel {
    max-width: 450px;
    margin: 0 auto;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    background: #f8f9fa;
    border-radius: 16px;
  }

  /* Header */
  .bundle-panel__header {
    text-align: center;
    margin-bottom: 24px;
  }

  .bundle-panel__title {
    font-family: inherit;
    font-size: 1.5em;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
  }

  .bundle-panel__subtitle {
    font-family: inherit;
    font-size: 0.9em;
    color: #666;
    margin: 0 0 16px 0;
  }



  .bundle-panel__save-badge {
    background: #e8f4fd;
    color: #0066cc;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9em;
    font-weight: 500;
    text-align: center;
    margin-bottom: 20px;
    border: 1px solid #b3d9ff;
    width: 100%;
    box-sizing: border-box;
  }

  /* Section Headers */
  .bundle-panel__section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .bundle-panel__section-header h4 {
    font-family: inherit;
    font-size: 1.1em;
    font-weight: 600;
    margin: 0;
    color: #333;
  }

  .bundle-panel__product-count,
  .bundle-panel__gifts-worth {
    font-family: inherit;
    font-size: 0.9em;
    color: #666;
  }

  /* Products Section */
  .bundle-panel__products-section {
    margin-bottom: 10px;
  }

  .bundle-panel__products-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 10px;
  }

  .bundle-panel__product-item {
    display: flex;
    align-items: center;
    gap: 16px;
    background: #fff;
    position: relative;
  }

  .bundle-panel__product-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .bundle-panel__product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .bundle-panel__product-info {
    flex: 1;
  }

  .bundle-panel__product-name {
    font-family: inherit;
    font-size: 0.95em;
    font-weight: 500;
    margin: 0 0 4px 0;
    color: #333;
  }

  .bundle-panel__product-price {
    font-family: inherit;
    font-size: 0.85em;
    color: #666;
    margin: 0;
  }

  .bundle-panel__main-badge {
    background: {{ block.settings.main_badge_background | default: '#5cb85c' }};
    color: {{ block.settings.main_badge_text_color | default: '#ffffff' }};
    padding: 6px 12px;
    border-radius: 20px;
    font-family: inherit;
    font-size: 0.8em;
    font-weight: 500;
    margin-left: auto;
    flex-shrink: 0;
  }

  .bundle-panel__total-value {
    text-align: center;
    padding: 12px;
    background: #fff;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9em;
    color: #666;
  }

  /* Free Gifts Section */
  .bundle-panel__free-gifts {
    background: #e8f5e8;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 10px;
  }

  .bundle-panel__gifts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .bundle-panel__gift-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .bundle-panel__gift-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .bundle-panel__gift-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .bundle-panel__gift-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
  }

  .bundle-panel__gift-info {
    flex: 1;
  }

  .bundle-panel__gift-name {
    font-family: inherit;
    font-size: 0.9em;
    font-weight: 500;
    margin: 0 0 4px 0;
    color: #333;
    line-height: 1.2;
  }

  .bundle-panel__gift-price {
    font-family: inherit;
    font-size: 0.8em;
    margin: 0;
    line-height: 1.2;
  }

  .bundle-panel__gift-price s {
    color: #999;
    text-decoration: line-through;
    margin-right: 4px;
  }

  .bundle-panel__free-text {
    color: #28a745;
    font-weight: 500;
  }

  /* Pricing */
  .bundle-panel__pricing {
    margin-bottom: 20px;
  }

  .bundle-panel__price-row {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
  }

  .bundle-panel__price-main {
    font-family: inherit;
    font-size: 1.6em;
    font-weight: 600;
    color: #333;
  }

  .bundle-panel__price-compare {
    font-family: inherit;
    font-size: 1em;
    color: #999;
    text-decoration: line-through;
  }

  .bundle-panel__savings {
    font-family: inherit;
    font-size: 1em;
    color: #0066cc;
    font-weight: 500;
  }

  /* Add to Cart Button */
  .bundle-panel__add-to-cart {
    width: 100%;
    background: #5cb85c;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 16px;
    font-family: inherit;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-bottom: 16px;
  }

  .bundle-panel__add-to-cart:hover {
    background: #4cae4c;
  }

  .bundle-panel__add-to-cart:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  /* Additional Info */
  .bundle-panel__info {
    text-align: center;
  }

  .bundle-panel__info p {
    font-family: inherit;
    font-size: 0.8em;
    color: #666;
    margin: 0;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .bundle-panel {
      padding: 5px;
    }

    .bundle-panel__gifts-grid {
      grid-template-columns: 1fr;
    }

    .bundle-panel__price-main {
      font-size: 1.4em;
    }
  }
</style>
