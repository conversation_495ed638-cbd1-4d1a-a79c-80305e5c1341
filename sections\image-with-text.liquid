{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.desktop_padding_top }}px;
      padding-bottom: {{ section.settings.desktop_padding_bottom }}px;
    }

    #image-with-text-{{ section.id }} .image-with-text__media-item {
      width: {{ section.settings.desktop_media_width }}%;
    }
    #image-with-text-{{ section.id }} .image-with-text__text-item {
      width: {{ 100 | minus: section.settings.desktop_media_width }}%;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }

  .section-color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_section_colors_background.red }}, {{ section.settings.custom_section_colors_background.green }}, {{ section.settings.custom_section_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_section_gradient_background != blank %}{{ section.settings.custom_section_gradient_background }}{% else %}{{ section.settings.custom_section_colors_background }}{% endif %};
  }
{%- endstyle -%}

{%- liquid
  if section.settings.section_color_scheme == section.settings.color_scheme
    assign no_content_background = true
  endif
  if section.settings.section_color_scheme == 'custom' and section.settings.color_scheme == 'custom'
    unless section.settings.custom_colors_background == section.settings.custom_section_colors_background
      assign no_content_background = false
    endunless
  endif

  if settings.text_boxes_shadow_opacity == 0 and settings.text_boxes_border_thickness == 0 or settings.text_boxes_border_opacity == 0
    assign no_content_styles = true
  endif

  if no_content_background and no_content_styles
    assign padding_class = ' collapse-padding'
    assign same_bgs = true
  endif
-%}

<div id="image-with-text-{{ section.id }}" class="section-color-scheme-{{ section.id }} color-{{ section.settings.section_color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class="section-id-btn button" data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="image-with-text image-with-text--{{ section.settings.content_layout }} image-with-text--mobile-{{ section.settings.mobile_direction }}{% if section.settings.full_desktop_width and section.settings.content_layout != 'overlap' %} image-with-text--desktop-full-width{% endif %} page-width isolate{% if settings.text_boxes_border_thickness > 0 and settings.text_boxes_border_opacity > 0 and settings.media_border_thickness > 0 and settings.media_border_opacity > 0 %} collapse-borders{% endif %}{% unless section.settings.color_scheme == 'background-1' and settings.media_border_thickness > 0 and settings.text_boxes_shadow_opacity == 0 and settings.text_boxes_border_thickness == 0 or settings.text_boxes_border_opacity == 0 %} collapse-corners{% endunless %}{% if same_bgs %} same-colors{% else %} different-colors{% endif %}{{ padding_class }} section-{{ section.id }}-padding">
    <div class="image-with-text__grid grid grid--gapless grid--1-col{% if section.settings.layout == 'text_first' %} image-with-text__grid--reverse{% endif %} animate-item">
      <div class="image-with-text__media-item image-with-text__media-item--{{ section.settings.desktop_content_position }}{% if section.settings.mobile_full_media_width %} image-with-text__media--mobile-full{% endif %} grid__item">
        <div
          class="image-with-text__media image-with-text__media--{{ section.settings.height }} gradient color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} global-media-settings {% if section.settings.image != blank or section.settings.video != blank %}media{% else %}image-with-text__media--placeholder placeholder{% endif %}"
          {% if section.settings.height == 'adapt' and section.settings.video != blank %}
            style="padding-bottom: {{ 1 | divided_by: section.settings.video.aspect_ratio | times: 100 }}%;"
          {% elsif section.settings.height == 'adapt' and section.settings.image != blank %}
            style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"
          {% endif %}
        >
          {%- if section.settings.video != blank -%}
            <internal-video{% if section.settings.video_autoplay %} data-autoplay="true" data-no-play-btn="true"{% endif %}>
              <video
                poster="{{ section.settings.video.preview_image | image_url }}"
                {% if section.settings.video_loop %} loop{% endif %}
                {% if section.settings.video_autoplay %} muted{% endif %}
                width="100%" height="auto" preload="metadata" playsinline disablepictureinpicture
              >
                {% for source in section.settings.video.sources %}
                  <source
                    {% if section.settings.video_autoplay %}data-{% endif %}src="{{ source.url }}"
                    type="{{ source.mime_type }}"
                  >
                {% endfor %}
              </video>
              <button class="internal-video__play"{% if section.settings.video_autoplay %} style='visibility: hidden;'{% endif %}>
                <div class="play-button color-accent-1">
                  {%- render 'icon-play' -%}
                </div>
              </button>
            </internal-video>
          {%- elsif section.settings.image != blank -%}
            {%- capture sizes -%}
              (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
              (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / {{ section.settings.mobile_image_quanlity }})
            {%- endcapture -%}
            {{
              section.settings.image
              | image_url: width: 1500
              | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
            }}
          {%- else -%}
            {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}
        </div>
      </div>
      <div class="image-with-text__text-item grid__item">
        <div
          id="ImageWithText--{{ section.id }}"
          class="image-with-text__content image-with-text__content--{{ section.settings.desktop_content_position }} image-with-text__content--desktop-{{ section.settings.desktop_content_alignment }} image-with-text__content--mobile-{{ section.settings.mobile_content_alignment }} image-with-text__content--{{ section.settings.height }} gradient color-{{ section.settings.color_scheme }} color-scheme-{{ section.id }} content-container"
        >
          {%- for block in section.blocks -%}
            {% case block.type %}
              {%- when 'heading' -%}
                <h2 class="image-with-text__heading {{ block.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ block.settings.title_highlight_color }}' {{ block.shopify_attributes }}>
                  {{ block.settings.title }}
                </h2>
              {% when 'rating_stars' %}
                {% render 'rating-stars-block', block: block, margins: false, block_attributes: true %}
              {% when 'trustpilot_stars' %}
                {% render 'trustpilot-stars-block', block: block, margins: false, block_attributes: true %}
              {%- when 'caption' -%}
                <p
                  class="image-with-text__text image-with-text__text--caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.caption }}
                </p>
              {%- when 'text' -%}
                <div class="image-with-text__text rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                  {{ block.settings.text }}
                </div>
              {%- when 'button' -%}
                {%- if block.settings.button_label != blank -%}
                  <a
                    {% if block.settings.button_link == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.button_label | escape }}
                  </a>
                {%- endif -%}
              {% when 'atc_button' %}
                {% if block.settings.atc_product == blank %}
                  <button
                    id="SectionAtcBtn-{{ section.id }}"
                    type="button"
                    class="button main-product-atc button--has-spinner"
                    {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                      disabled
                    {% endif %}
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.button_label }}
                    <div class="loading-overlay__spinner">
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        class="spinner"
                        viewBox="0 0 66 66"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                      </svg>
                    </div>
                  </button>
                {% else %}
                  {% assign product_form_id = 'section-product-form-' | append: section.id | append: block.id %}
                  {% render 'separate-atc-btn',
                    product: block.settings.atc_product,
                    product_form_id: product_form_id,
                    label: block.settings.button_label,
                    skip_cart: block.settings.atc_skip_cart
                  %}
                {% endif %}
              {%- when 'text_with_icon' -%}
                {% render 'text-with-icon-block', block: block, mobile_align: true, block_attributes: true %}
              {% when 'image' %}
                <div
                  class="product-info__image-block product-info__image-block--mobile-alignment"
                  style="--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--mobile-image-alignment:{{ block.settings.mobile_alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                >
                  {% if block.settings.image != blank %}
                    <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%">
                      {%- capture sizes -%}
                        (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 | times: block.settings.width | divided_by: 100.0 }}px,
                        (min-width: 750px) calc(({{ block.settings.width }}vw - 130px) / 2), calc(({{ block.settings.width }}vw - 50px) / 2)
                      {%- endcapture -%}
                      {{
                        block.settings.image
                        | image_url: width: 1500
                        | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                      }}
                    </div>
                  {% else %}
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                  {% endif %}
                </div>
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.image-with-text.name",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "header",
      "content": "Or"
    },
    {
      "type": "video",
      "id": "video",
      "label": "Video",
      "info": "Use autoplay mp4 instead of GIFs & animated WEBPs."
    },
    {
      "type": "checkbox",
      "id": "video_autoplay",
      "label": "Video muted autoplay",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "video_loop",
      "label": "Video looping",
      "default": true
    },
    {
      "type": "select",
      "id": "height",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to media"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "adapt",
      "label": "Media height"
    },
    {
      "type": "header",
      "content": "Color schemes"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Content color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "select",
      "id": "section_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Section color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "checkbox",
      "id": "full_desktop_width",
      "label": "Full page width",
      "default": false,
      "info": "Only applied if \"Content layout\" is set to \"No overlap\""
    },
    {
      "type": "select",
      "id": "content_layout",
      "options": [
        {
          "value": "no-overlap",
          "label": "No overlap"
        },
        {
          "value": "overlap",
          "label": "Overlap"
        }
      ],
      "default": "no-overlap",
      "label": "Content layout"
    },
    {
      "type": "range",
      "id": "desktop_media_width",
      "label": "Desktop media width",
      "min": 25,
      "max": 75,
      "step": 5,
      "unit": "%",
      "default": 50
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "image_first",
          "label": "Media first"
        },
        {
          "value": "text_first",
          "label": "Media second"
        }
      ],
      "default": "image_first",
      "label": "Desktop media placement"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "middle",
          "label": "Middle"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "top",
      "label": "Desktop content position"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Desktop content alignment"
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "checkbox",
      "id": "mobile_full_media_width",
      "label": "Full media width",
      "default": false
    },
    {
      "type": "select",
      "id": "mobile_direction",
      "options": [
        {
          "value": "normal",
          "label": "Media first"
        },
        {
          "value": "reverse",
          "label": "Media second"
        }
      ],
      "default": "normal",
      "label": "Mobile media position"
    },
    {
      "type": "select",
      "id": "mobile_image_quanlity",
      "options": [
        {
          "value": "2",
          "label": "Standard"
        },
        {
          "value": "1",
          "label": "Enhanced"
        }
      ],
      "default": "2",
      "label": "Mobile image quality",
      "info": "For most images, Standard is a good balance between qualitty and performance. But, if your image requires it, select Enhanced."
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Mobile content alignment"
    },
    {
      "type": "header",
      "content": "Mobile padding"
    },
    {
      "type": "range",
      "id": "mobile_padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "mobile_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 16
    },
    {
      "type": "header",
      "content": "Desktop padding"
    },
    {
      "type": "range",
      "id": "desktop_padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "desktop_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom content color scheme",
      "info": "Applied if Content color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#2E2A39",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#FFFFFF",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    },
    {
      "type": "header",
      "content": "Custom section color scheme",
      "info": "Applied if Section color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_section_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_section_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.image-with-text.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Image with text",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        }
      ]
    },
    {
      "type": "rating_stars",
      "name": "Rating stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 4.8
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "select",
          "id": "bg_stars_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "full",
              "label": "Full"
            }
          ],
          "label": "Background stars style",
          "default": "full"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#ececec",
          "label": "Background stars color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        }
      ]
    },
    {
      "type": "trustpilot_stars",
      "name": "Trustpilot stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 5
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#00b67a",
          "label": "Active stars container color"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#c8c8c8",
          "label": "Background stars container color"
        },
        {
          "type": "color",
          "id": "star_symbol_color",
          "default": "#fff",
          "label": "Stars inside symbol color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.image-with-text.blocks.caption.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Add a tagline",
          "label": "t:sections.image-with-text.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.image-with-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-with-text.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "text_with_icon",
      "name": "Text with icon",
      "settings": [
        {
          "type": "header",
          "content": "Text"
        },
        {
          "type": "range",
          "id": "mobile_text_size",
          "min": 8,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 14,
          "label": "Mobile text size"
        },
        {
          "type": "range",
          "id": "desktop_text_size",
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Desktop text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "label": "Mobile text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "color",
          "id": "text_color",
          "default": "#121212",
          "label": "Text color"
        },
        {
          "type": "inline_richtext",
          "id": "text_1",
          "default": "Text with icon",
          "label": "Text #1"
        },
        {
          "type": "inline_richtext",
          "id": "text_2",
          "default": "Text with icon",
          "label": "Text #2"
        },
        {
          "type": "inline_richtext",
          "id": "text_3",
          "default": "Text with icon",
          "label": "Text #3"
        },
        {
          "type": "header",
          "content": "Icons"
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 100,
          "max": 200,
          "step": 5,
          "default": 120,
          "unit": "%",
          "label": "Icon scale",
          "info": "Relative to text size"
        },
        {
          "type": "color",
          "id": "icon_color",
          "default": "#121212",
          "label": "Icons color"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "check_circle",
          "label": "Icon #1",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "Filled icon #1"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon #1"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "check_circle",
          "label": "Icon #2",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "Filled icon #2"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon #2"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "check_circle",
          "label": "Icon #3",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_3",
          "default": false,
          "label": "Filled icon #3"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_3",
          "label": "Custom icon #3"
        },
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "width",
          "options": [
            {
              "value": "fit-content",
              "label": "Fit text"
            },
            {
              "value": "100%",
              "label": "Full"
            }
          ],
          "default": "100%",
          "label": "Width"
        },
        {
          "type": "select",
          "id": "direction",
          "options": [
            {
              "value": "horizontal",
              "label": "Horizontal"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "default": "vertical",
          "label": "Stacking direction",
          "info": "Applied when multiple texts are added."
        },
        {
          "type": "range",
          "id": "column_gap",
          "min": 0,
          "max": 6,
          "step": 0.5,
          "label": "Stacking spacing",
          "default": 3
        },
        {
          "type": "checkbox",
          "id": "enable_bg",
          "default": false,
          "label": "Enable background",
          "info": "The following settings are applied when this option is enabled."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background color",
          "default": "#F3F3F3"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "t:settings_schema.global.settings.corner_radius.label",
          "default": 40,
          "info": "Relative to font size"
        },
        {
          "type": "range",
          "id": "padding",
          "min": 1,
          "max": 5,
          "step": 0.5,
          "label": "Padding",
          "default": 3
        },
        {
          "type": "range",
          "id": "border_size",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Border thickness",
          "unit": "px",
          "default": 0
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#B7B7B7"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Width",
          "default": 100
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "label": "Mobile alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 0
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text.blocks.button.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.image-with-text.blocks.button.settings.button_label.label",
          "info": "t:sections.image-with-text.blocks.button.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        }
      ]
    },
    {
      "type": "atc_button",
      "name":"Add to Cart button",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Add to Cart"
        },
        {
          "type": "product",
          "id": "atc_product",
          "label": "ATC Custom product",
          "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
        },
        {
          "type": "checkbox",
          "id": "atc_skip_cart",
          "label": "ATC Custom product skip cart"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-with-text.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
