{{ 'section-hidden-variant-bundles.css' | asset_url | stylesheet_tag }}
<script src="{{ 'hidden-variant-bundles.js' | asset_url }}" defer="defer"></script>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --accent-color: {{ section.settings.custom_colors_accent.red }}, {{ section.settings.custom_colors_accent.green }}, {{ section.settings.custom_colors_accent.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
  }
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  
  <div class="page-width section-{{ section.id }}-padding">
    {% assign content_index = 0 %}
    
    {%- unless section.settings.title == blank -%}
      {% assign content_index = 1 %}
      <div class="title-wrapper--no-top-margin animate-item animate-item--child index-0">
        <h2 class="title {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
          {{ section.settings.title }}
        </h2>
        {% if section.settings.description != blank %}
          <div class="text-under-title">
            {{ section.settings.description }}
          </div>
        {% endif %}
      </div>
    {%- endunless -%}

    <!-- Hidden Variant Bundle System -->
    <hidden-variant-bundles 
      data-section="{{ section.id }}"
      class="animate-item animate-item--child index-{{ content_index }}"
    >
      <div class="bundle-summary">
        <h3 class="bundle-summary__title">Bundle Summary</h3>
        <div class="bundle-summary__price">Loading...</div>
        {%- liquid
          # Build product handles from blocks
          assign product_handles = ''
          for block in section.blocks
            if block.type == 'bundle_product' and block.settings.product != blank
              if product_handles != blank
                assign product_handles = product_handles | append: ','
              endif
              assign product_handles = product_handles | append: block.settings.product.handle
            endif
          endfor

          assign bundle_id = 'bundle-section-' | append: section.id
          assign bundle_name = section.settings.title | default: 'Bundle Deal'
        -%}

        <button class="bundle-add-to-cart button button--primary"
                data-bundle-products="{{ product_handles }}"
                data-bundle-id="{{ bundle_id }}"
                data-bundle-name="{{ bundle_name }}">
          Add Bundle to Cart
        </button>
      </div>
      
      <div class="bundle-products">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'bundle_product' -%}
              {%- if block.settings.product != blank -%}
                <div class="bundle-product" 
                     data-product-handle="{{ block.settings.product.handle }}"
                     {{ block.shopify_attributes }}>
                  <div class="bundle-product__image">
                    {%- if block.settings.product.featured_image -%}
                      <img src="{{ block.settings.product.featured_image | image_url: width: 300 }}" 
                           alt="{{ block.settings.product.title }}" 
                           loading="lazy">
                    {%- endif -%}
                  </div>
                  <div class="bundle-product__info">
                    <h4 class="bundle-product__title">{{ block.settings.product.title }}</h4>
                    <p class="bundle-product__price">Loading...</p>
                    {%- if block.settings.product.description != blank -%}
                      <div class="bundle-product__description">
                        {{ block.settings.product.description | truncate: 100 }}
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              {%- endif -%}
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </hidden-variant-bundles>
  </div>
</div>

{% comment %}
  IMPORTANT: This is the original Hidden Variant Bundle Section for client presentation.
  
  This section demonstrates the dynamic bundle system that:
  1. Automatically detects products with "Purchase Type" options
  2. Finds Bundle variants for each product
  3. Displays bundle pricing and handles cart management
  4. Synchronizes cart removal to maintain bundle integrity
  
  The section can be added to any page and will work with products that have:
  - A "Purchase Type" option with values like "Individual" and "Bundle"
  - Bundle variants with special pricing
  
  For the product page implementation, we've created a block-based system
  that integrates directly into the main product template.
{% endcomment %}

{% schema %}
{
  "name": "Bundles (Section)",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Bundle Deals",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Title highlight color",
      "default": "#6D388B"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "bundle_product",
      "name": "Bundle Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Hidden Variant Bundles",
      "blocks": [
        {
          "type": "bundle_product"
        },
        {
          "type": "bundle_product"
        },
        {
          "type": "bundle_product"
        }
      ]
    }
  ]
}
{% endschema %}
