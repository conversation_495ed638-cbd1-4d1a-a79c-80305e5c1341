{% comment %}
  Renders a collection card

  Accepts:
  - card_collection: {Object} Collection Liquid object
  - media_aspect_ratio: {String} Size of the product image card. Values are "square" and "portrait". Default is "square" (optional)
  - columns: {Number}
  - extend_height: {<PERSON><PERSON>an} Card height extends to available container space. Default: false (optional)
  - wrapper_class: {String} Wrapper class for card (optional)

  Usage:
  {% render 'card-collection' %}
{% endcomment %}

{%- liquid
  assign featured_image = card_collection.featured_image
  if custom_image and custom_image != blank
    assign featured_image = custom_image
  endif
  assign ratio = 1
  if featured_image and media_aspect_ratio == 'portrait'
    assign ratio = 0.8
  elsif featured_image and media_aspect_ratio == 'adapt'
    assign ratio = featured_image.aspect_ratio
  endif
  if ratio == 0 or ratio == null
    assign ratio = 1
  endif
  assign card_color_scheme = settings.card_color_scheme
  assign card_style = settings.card_style
  if wrapper_class == null or wrapper_class == 'none'
    assign card_color_scheme = settings.collection_card_color_scheme
    if custom_color_scheme
      assign card_color_scheme = custom_color_scheme
    endif
    assign card_style = settings.collection_card_style
  endif
-%}

<div class="card-wrapper animate-arrow {% if wrapper_class and wrapper_class != 'none' %}{{ wrapper_class }}{% else %}collection-card-wrapper{% endif %}">
  <div
    class="
      card
      card--{{ card_style }}
      {% if featured_image %} card--media{% else %} card--text{% endif %}
      {% if card_style == 'card' %} color-{{ card_color_scheme }} gradient{% endif %}
      {% if extend_height %} card--extend-height{% endif %}
      {% if featured_image == nil and card_style == 'card' %} ratio{% endif %}
    "
    style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
  >
    <div
      class="card__inner {% if card_style == 'standard' %}color-{{ card_color_scheme }} gradient{% endif %}{% if featured_image or card_style == 'standard' %} ratio{% endif %}"
      style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
    >
      {%- if featured_image -%}
        <div class="card__media">
          <div class="media media--transparent media--hover-effect">
            <img
              srcset="
                {%- if featured_image.width >= 165 -%}{{ featured_image | image_url: width: 165 }} 165w,{%- endif -%}
                {%- if featured_image.width >= 330 -%}{{ featured_image | image_url: width: 330 }} 330w,{%- endif -%}
                {%- if featured_image.width >= 535 -%}{{ featured_image | image_url: width: 535 }} 535w,{%- endif -%}
                {%- if featured_image.width >= 750 -%}{{ featured_image | image_url: width: 750 }} 750w,{%- endif -%}
                {%- if featured_image.width >= 1000 -%}{{ featured_image | image_url: width: 1000 }} 1000w,{%- endif -%}
                {%- if featured_image.width >= 1500 -%}{{ featured_image | image_url: width: 1500 }} 1500w,{%- endif -%}
                {%- if featured_image.width >= 3000 -%}{{ featured_image | image_url: width: 3000 }} 3000w,{%- endif -%}
                {{ featured_image | image_url }} {{ featured_image.width }}w
              "
              src="{{ featured_image | image_url: width: 1500 }}"
              sizes="
                (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: columns }}px,
                (min-width: 750px) {% if columns > 1 %}calc((100vw - 10rem) / 2){% else %}calc(100vw - 10rem){% endif %},
                calc(100vw - 3rem)
              "
              alt=""
              height="{{ featured_image.height }}"
              width="{{ featured_image.width }}"
              loading="lazy"
              class="motion-reduce"
            >
          </div>
        </div>
      {%- endif -%}
      <div class="card__content">
        <div class="card__information">
          <h3 class="card__heading">
            <a
              {% if card_collection == blank %}
                role="link" aria-disabled="true"
              {% else %}
                href="{{ card_collection.url }}"
              {% endif %}
              class="full-unstyled-link"
            >
              {% if custom_title and custom_title != blank %}
                {{ custom_title }}
              {%- elsif card_collection.title != blank -%}
                {{- card_collection.title | escape -}}
              {%- else -%}
                {{ 'onboarding.collection_title' | t }}
              {%- endif -%}
              {%- if card_collection.description == blank -%}
                <span class="icon-wrap">{% render 'icon-arrow' %}</span>
              {%- endif %}
            </a>
          </h3>
          {%- if card_collection.description != blank -%}
            <p class="card__caption">
              {{- card_collection.description | strip_html | truncatewords: 12 -}}
              <span class="icon-wrap">&nbsp;{% render 'icon-arrow' %}</span>
            </p>
          {%- endif -%}
        </div>
      </div>
    </div>
    {% if card_style == 'card' or featured_image %}
      <div class="card__content">
        <div class="card__information">
          <h3 class="card__heading">
            <a
              {% if card_collection == blank %}
                role="link" aria-disabled="true"
              {% else %}
                href="{{ card_collection.url }}"
              {% endif %}
              class="full-unstyled-link"
            >
              {% if custom_title and custom_title != blank %}
                {{ custom_title }}
              {%- elsif card_collection.title != blank -%}
                {{- card_collection.title | escape -}}
              {%- else -%}
                {{ 'onboarding.collection_title' | t }}
              {%- endif -%}
              {%- if featured_image or card_collection.description == blank -%}
                <span class="icon-wrap">{% render 'icon-arrow' %}</span>
              {%- endif %}
            </a>
          </h3>
          {%- if featured_image == null and card_collection.description != blank -%}
            <p class="card__caption">
              {{- card_collection.description | strip_html | truncatewords: 12 -}}
              <span class="icon-wrap">&nbsp;{% render 'icon-arrow' %}</span>
            </p>
          {%- endif -%}
        </div>
      </div>
    {% endif %}
  </div>
</div>
