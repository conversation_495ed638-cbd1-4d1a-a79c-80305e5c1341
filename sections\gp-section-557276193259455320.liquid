

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557276193259455320.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-557276193259455320.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-557276193259455320.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557276193259455320.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-557276193259455320.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557276193259455320.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557276193259455320.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557276193259455320.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557276193259455320.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557276193259455320.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557276193259455320.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557276193259455320.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-557276193259455320.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-557276193259455320.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557276193259455320.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-557276193259455320.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-557276193259455320.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-557276193259455320.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557276193259455320.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-557276193259455320.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-557276193259455320.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-557276193259455320.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557276193259455320.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557276193259455320.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557276193259455320.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557276193259455320.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-557276193259455320.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557276193259455320.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557276193259455320.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557276193259455320.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-557276193259455320.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557276193259455320.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-557276193259455320.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557276193259455320.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557276193259455320.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-557276193259455320.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-557276193259455320.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-557276193259455320.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-557276193259455320.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557276193259455320.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557276193259455320.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557276193259455320.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557276193259455320.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557276193259455320.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557276193259455320.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557276193259455320.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557276193259455320.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557276193259455320.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-557276193259455320.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557276193259455320.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-557276193259455320.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-557276193259455320.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-557276193259455320.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557276193259455320.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-557276193259455320.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-557276193259455320.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557276193259455320.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557276193259455320.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-557276193259455320.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-557276193259455320.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-557276193259455320.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557276193259455320.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557276193259455320.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-557276193259455320.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-557276193259455320.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-557276193259455320.gps.gpsil [style*="--mr-mobile:"]{margin-right:var(--mr-mobile)}.gps-557276193259455320.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-557276193259455320.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557276193259455320.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557276193259455320.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557276193259455320.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557276193259455320.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-557276193259455320.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-557276193259455320 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-557276193259455320 .gp-relative{position:relative}.gps-557276193259455320 .gp-z-1{z-index:1}.gps-557276193259455320 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557276193259455320 .gp-mb-0{margin-bottom:0}.gps-557276193259455320 .gp-flex{display:flex}.gps-557276193259455320 .gp-inline-flex{display:inline-flex}.gps-557276193259455320 .gp-grid{display:grid}.gps-557276193259455320 .gp-contents{display:contents}.gps-557276193259455320 .\!gp-hidden{display:none!important}.gps-557276193259455320 .gp-hidden{display:none}.gps-557276193259455320 .gp-h-auto{height:auto}.gps-557276193259455320 .gp-h-full{height:100%}.gps-557276193259455320 .gp-w-full{width:100%}.gps-557276193259455320 .gp-max-w-full{max-width:100%}.gps-557276193259455320 .gp-flex-none{flex:none}.gps-557276193259455320 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557276193259455320 .gp-flex-row-reverse{flex-direction:row-reverse}.gps-557276193259455320 .gp-flex-col{flex-direction:column}.gps-557276193259455320 .gp-items-center{align-items:center}.gps-557276193259455320 .gp-justify-center{justify-content:center}.gps-557276193259455320 .gp-gap-y-0{row-gap:0}.gps-557276193259455320 .gp-overflow-hidden{overflow:hidden}.gps-557276193259455320 .gp-break-words{overflow-wrap:break-word}.gps-557276193259455320 .gp-rounded-none{border-radius:0}.gps-557276193259455320 .gp-text-center{text-align:center}.gps-557276193259455320 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-557276193259455320 .gp-no-underline{text-decoration-line:none}.gps-557276193259455320 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259455320 .gp-duration-200{transition-duration:.2s}.gps-557276193259455320 .gp-duration-300{transition-duration:.3s}.gps-557276193259455320 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259455320 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-557276193259455320 .hover\:gp-bg-g-highlight:hover{background-color:var(--g-c-highlight)}}.gps-557276193259455320 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-557276193259455320 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-557276193259455320 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-557276193259455320 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-557276193259455320 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-557276193259455320 .tablet\:\!gp-hidden{display:none!important}.gps-557276193259455320 .tablet\:gp-hidden{display:none}.gps-557276193259455320 .tablet\:gp-h-auto{height:auto}.gps-557276193259455320 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-557276193259455320 .mobile\:\!gp-hidden{display:none!important}.gps-557276193259455320 .mobile\:gp-hidden{display:none}.gps-557276193259455320 .mobile\:gp-h-auto{height:auto}.gps-557276193259455320 .mobile\:gp-flex-none{flex:none}}.gps-557276193259455320 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557276193259455320 .\[\&_p\]\:gp-inline p{display:inline}.gps-557276193259455320 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-557276193259455320 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557276193259455320 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gQC1hSdwkJ" data-id="gQC1hSdwkJ"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:28px;--pl:15px;--pb:0px;--pr:15px;--pt-mobile:8px;--pb-mobile:18px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gQC1hSdwkJ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gJ8E5Yv1NQ gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gJvFFmA1KI" data-id="gJvFFmA1KI"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:#ffffff;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gJvFFmA1KI gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gUeD0hjTsi gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gM2QLPR0Cn" data-id="gM2QLPR0Cn"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:37px;--cg:8px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gM2QLPR0Cn gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gjybKtrEE9 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gXho7bJwoZ"
    role="presentation"
    class="gp-group/image gXho7bJwoZ gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mt:0px;--ml:0px;--mb:0px;--mr:-66px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--mb-mobile:2px;--mr-mobile:-106px;--pl-mobile:10px;--pb-mobile:0px;--pr-mobile:0px;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_523685320072364842-4df4c8c6-76d7-440b-b6f4-c85a3965530b.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_523685320072364842-4df4c8c6-76d7-440b-b6f4-c85a3965530b.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_523685320072364842-4df4c8c6-76d7-440b-b6f4-c85a3965530b.png" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--w:75%;--w-tablet:75%;--w-mobile:75%;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:32px;--bbrr:32px;--btlr:32px;--btrr:32px;--radiusType:custom;--shadow:7.66044443118978px 6.4278760968653925px 12px 10px rgba(18, 18, 18, 0.77)"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gU_AdDtHhx gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gbe78AAd43">
    <div
      parentTag="Col"
        class="gbe78AAd43 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt-mobile:32px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-Poppins, 'Poppins'), var(--g-font-heading, heading);--weight:400;--size:46px;--size-tablet:46px;--size-mobile:41px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggbe78AAd43_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_Nkm-T7qj">
    <div
      parentTag="Col"
        class="g_Nkm-T7qj "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg_Nkm-T7qj_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:16px;--ml:0px;--pl:0px;--ta:center"
    
  >
    <style>
    .gjzg3x-cbh.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 16px;
      border-bottom-right-radius: 16px;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      
    }

    .gjzg3x-cbh:hover::before {
      
      
    }

    .gjzg3x-cbh:hover .gp-button-icon {
      color: undefined;
    }

     .gjzg3x-cbh .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gjzg3x-cbh:hover .gp-button-price {
      color: undefined;
    }

    .gjzg3x-cbh .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gjzg3x-cbh .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gjzg3x-cbh:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/products/firmandtone" target="_self" data-id="gjzg3x-cbh" aria-label="<p><span style='font-size:15px;'>Get Yours</span></p>"
      
      data-state="idle"
      class="gjzg3x-cbh gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none hover:gp-bg-g-highlight gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:var(--g-c-highlight, highlight);--bg:#151515;--bblr:16px;--bbrr:16px;--btlr:16px;--btrr:16px;--shadow:none;--w:150px;--w-tablet:150px;--w-mobile:150px;--h:50px;--h-tablet:50px;--h-mobile:50px;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--c:var(--g-c-text-3, text-3);--size:24px;--size-tablet:16px;--size-mobile:14px;--ff:var(--g-font-Poppins, 'Poppins'), var(--g-font-body, body);--weight:400;--lh:180%;--lh-tablet:180%;--lh-mobile:180%"
    >
      
    <div
    class="gp-inline-flex gp-flex-row-reverse">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:24px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggjzg3x-cbh_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfedA.woff) format('woff');
}
/* devanagari */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJbecnFHGPezSQ.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJnecnFHGPezSQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 8",
    "tag": "section",
    "class": "gps-557276193259455320 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=557276193259455320)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggbe78AAd43_text","label":"ggbe78AAd43_text","default":"<span style=\"font-size:37px;\">Noticeable Results in No Time!</span>"},{"type":"html","id":"gg_Nkm-T7qj_text","label":"gg_Nkm-T7qj_text","default":"<p>Say goodbye to cellulite with the FIRM &amp; TONE TOOL – your go-to solution for smoother, firmer skin. Target stubborn areas and boost confidence with every session!</p>"},{"type":"html","id":"ggjzg3x-cbh_label","label":"ggjzg3x-cbh_label","default":"<p><span style=\"font-size:15px;\">Get Yours</span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
