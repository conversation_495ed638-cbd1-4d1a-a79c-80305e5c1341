<link rel="stylesheet" href="{{ 'component-article-card.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'section-featured-blog.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-article-card.css' | asset_url | stylesheet_tag }}</noscript>

{{ 'section-featured-blog.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign posts_displayed = section.settings.blog.articles_count
  if section.settings.post_limit <= section.settings.blog.articles_count
    assign posts_exceed_limit = true
    assign posts_displayed = section.settings.post_limit
  endif
-%}
<div class="blog color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %} content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width-desktop isolate{% if posts_displayed < 3 %} page-width-tablet{% endif %} section-{{ section.id }}-padding">
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link animate-item animate-item--child index-0{% if posts_displayed > 2 %} title-wrapper--self-padded-tablet-down{% else %} title-wrapper--self-padded-mobile{% endif %} title-wrapper--no-top-margin">
        <h2 id="SectionHeading-{{ section.id }}" class="blog__title {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
          {{ section.settings.title }}
        </h2>

        {%- if section.settings.blog != blank
          and section.settings.show_view_all
          and section.settings.post_limit < section.settings.blog.articles_count
        -%}
          <a
            href="{{ section.settings.blog.url }}"
            class="link underlined-link large-up-hide"
          >
            {{ 'sections.featured_blog.view_all' | t }}
          </a>
        {%- endif -%}
      </div>
    {%- endunless -%}
    {%- if section.settings.blog != blank and section.settings.blog.articles_count > 0 -%}
      <slider-component class="slider-mobile-gutter animate-item animate-item--child index-1">
        <ul
          id="Slider-{{ section.id }}"
          class="blog__posts articles-wrapper contains-card contains-card--article{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid grid--peek grid--2-col-tablet grid--{{ section.settings.columns_desktop }}-col-desktop slider {% if posts_displayed > 2 %}slider--tablet{% else %}slider--mobile{% endif %}"
          role="list"
        >
          {%- for article in section.settings.blog.articles limit: section.settings.post_limit -%}
            <li
              id="Slide-{{ section.id }}-{{ forloop.index }}"
              class="blog__post grid__item article slider__slide slider__slide--full-width"
            >
              {% render 'article-card',
                blog: section.settings.blog,
                article: article,
                media_aspect_ratio: 1.66,
                show_image: section.settings.show_image,
                show_date: section.settings.show_date,
                show_author: section.settings.show_author,
                show_excerpt: true
              %}
            </li>
          {%- endfor -%}
        </ul>
        {%- if posts_exceed_limit -%}
          <div class="slider-buttons no-js-hidden{% if section.settings.post_limit < 3 %} medium-hide{% endif %}{% if section.settings.post_limit < 2 %} small-hide{% endif %}">
            <button
              type="button"
              class="slider-button slider-button--prev"
              name="previous"
              aria-label="{{ 'general.slider.previous_slide' | t }}"
            >
              {% render 'icon-caret' %}
            </button>
            <div class="slider-counter caption">
              <span class="slider-counter--current">1</span>
              <span aria-hidden="true"> / </span>
              <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
              <span class="slider-counter--total">{{ section.settings.post_limit }}</span>
            </div>
            <button
              type="button"
              class="slider-button slider-button--next"
              name="next"
              aria-label="{{ 'general.slider.next_slide' | t }}"
            >
              {% render 'icon-caret' %}
            </button>
          </div>
        {%- endif -%}
      </slider-component>

      {%- if section.settings.show_view_all and section.settings.post_limit < section.settings.blog.articles_count -%}
        <div class="blog__view-all center small-hide medium-hide">
          <a
            href="{{ section.settings.blog.url }}"
            id="ViewAll-{{ section.id }}"
            class="blog__button button"
            aria-labelledby="ViewAll-{{ section.id }} SectionHeading-{{ section.id }}"
          >
            {{ 'sections.featured_blog.view_all' | t }}
          </a>
        </div>
      {%- endif -%}
    {%- else -%}
      <div class="blog-placeholder">
        <div class="placeholder media media--landscape">
          {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
        </div>
        <div class="blog-placeholder__content">
          <h2>
            {{ 'sections.featured_blog.onboarding_title' | t }}
          </h2>
          <p class="rte-width">
            {{ 'sections.featured_blog.onboarding_content' | t }}
          </p>
        </div>
      </div>
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.featured-blog.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Blog posts",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.featured-blog.settings.blog.label"
    },
    {
      "type": "range",
      "id": "post_limit",
      "min": 2,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.post_limit.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_image.label",
      "info": "t:sections.featured-blog.settings.show_image.info"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_author.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_view_all.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-blog.presets.name",
      "settings": {
        "blog": "News"
      }
    }
  ]
}
{% endschema %}
