try{document.querySelector(":focus-visible")}catch(t){focusVisiblePolyfill()}function focusVisiblePolyfill(){let t=["ARROWUP","ARROWDOWN","ARROWLEFT","ARROWRIGHT","TAB","<PERSON>NTER","<PERSON><PERSON><PERSON>","ESCAP<PERSON>","HOM<PERSON>","END","PAGEUP","PAGEDOWN"],e=null,i=null;window.addEventListener("keydown",e=>{t.includes(e.code.toUpperCase())&&(i=!1)}),window.addEventListener("mousedown",t=>{i=!0}),window.addEventListener("focus",()=>{e&&e.classList.remove("focused"),i||(e=document.activeElement).classList.add("focused")},!0)}class ProductRecommendations extends HTMLElement{constructor(){super()}connectedCallback(){let t=(t,e)=>{t[0].isIntersecting&&(e.unobserve(this),"true"!==this.dataset.loaded&&this.loadProducts())};new IntersectionObserver(t.bind(this),{rootMargin:"0px 0px 400px 0px"}).observe(this)}loadProducts(){fetch(this.dataset.url).then(t=>t.text()).then(t=>{let e=document.createElement("div");e.innerHTML=t;let i=e.querySelector("product-recommendations"),s="";i&&i.innerHTML.trim().length&&(s=i.innerHTML,"true"!==this.dataset.isUpsellBlock||s.includes("-upsell")||s.includes("data-selected")||(s="")),s.trim().length&&(this.innerHTML=s),!this.querySelector("slideshow-component")&&this.classList.contains("complementary-products")&&this.remove(),e.querySelector(".grid__item")&&this.classList.add("product-recommendations--loaded")}).catch(t=>{console.error(t)})}}customElements.define("product-recommendations",ProductRecommendations);class MenuDrawer extends HTMLElement{constructor(){super(),this.mainDetailsToggle=this.querySelector("details"),this.addEventListener("keyup",this.onKeyUp.bind(this)),this.addEventListener("focusout",this.onFocusOut.bind(this)),this.bindEvents()}bindEvents(){this.querySelectorAll("summary").forEach(t=>t.addEventListener("click",this.onSummaryClick.bind(this))),this.querySelectorAll("button:not(.menu-drawer__close-menu-btn)").forEach(t=>t.addEventListener("click",this.onCloseButtonClick.bind(this)))}onKeyUp(t){if("ESCAPE"!==t.code.toUpperCase())return;let e=t.target.closest("details[open]");e&&(e===this.mainDetailsToggle?this.closeMenuDrawer(t,this.mainDetailsToggle.querySelector("summary")):this.closeSubmenu(e))}onSummaryClick(t){let e=t.currentTarget,i=e.parentNode,s=i.closest(".has-submenu"),n=i.hasAttribute("open"),o=window.matchMedia("(prefers-reduced-motion: reduce)");function r(){trapFocus(e.nextElementSibling,i.querySelector("button")),e.nextElementSibling.removeEventListener("transitionend",r)}i===this.mainDetailsToggle?(n&&t.preventDefault(),n?this.closeMenuDrawer(t,e):this.openMenuDrawer(e),window.matchMedia("(max-width: 990px)")&&document.documentElement.style.setProperty("--viewport-height",`${window.innerHeight}px`)):setTimeout(()=>{i.classList.add("menu-opening"),e.setAttribute("aria-expanded",!0),s&&s.classList.add("submenu-open"),!o||o.matches?r():e.nextElementSibling.addEventListener("transitionend",r)},100)}openMenuDrawer(t){setTimeout(()=>{this.mainDetailsToggle.classList.add("menu-opening")}),t.setAttribute("aria-expanded",!0),trapFocus(this.mainDetailsToggle,t),document.body.classList.add(`overflow-hidden-${this.dataset.breakpoint}`)}closeMenuDrawer(t,e=!1){void 0!==t&&(this.mainDetailsToggle.classList.remove("menu-opening"),this.mainDetailsToggle.querySelectorAll("details").forEach(t=>{t.removeAttribute("open"),t.classList.remove("menu-opening")}),this.mainDetailsToggle.querySelectorAll(".submenu-open").forEach(t=>{t.classList.remove("submenu-open")}),document.body.classList.remove(`overflow-hidden-${this.dataset.breakpoint}`),removeTrapFocus(e),this.closeAnimation(this.mainDetailsToggle))}onFocusOut(t){setTimeout(()=>{this.mainDetailsToggle.hasAttribute("open")&&!this.mainDetailsToggle.contains(document.activeElement)&&this.closeMenuDrawer()})}onCloseButtonClick(t){let e=t.currentTarget.closest("details");this.closeSubmenu(e)}closeSubmenu(t){let e=t.closest(".submenu-open");e&&e.classList.remove("submenu-open"),t.classList.remove("menu-opening"),t.querySelector("summary").setAttribute("aria-expanded",!1),removeTrapFocus(t.querySelector("summary")),this.closeAnimation(t)}closeAnimation(t){let e,i=s=>{void 0===e&&(e=s);let n=s-e;n<400?window.requestAnimationFrame(i):(t.removeAttribute("open"),t.closest("details[open]")&&trapFocus(t.closest("details[open]"),t.querySelector("summary")))};window.requestAnimationFrame(i)}}customElements.define("menu-drawer",MenuDrawer);class HeaderDrawer extends MenuDrawer{constructor(){super(),this.querySelectorAll(".menu-drawer__close-menu-btn").forEach(t=>t.addEventListener("click",this.closeButtonClick.bind(this)))}openMenuDrawer(t){this.header=this.header||document.querySelector(".section-header"),this.borderOffset=this.borderOffset||this.closest(".header-wrapper").classList.contains("header-wrapper--border-bottom")?1:0,document.documentElement.style.setProperty("--header-bottom-position",`${parseInt(this.header.getBoundingClientRect().bottom-this.borderOffset)}px`),this.header.classList.add("menu-open"),setTimeout(()=>{this.mainDetailsToggle.classList.add("menu-opening")}),t.setAttribute("aria-expanded",!0),trapFocus(this.mainDetailsToggle,t),document.body.classList.add(`overflow-hidden-${this.dataset.breakpoint}`)}closeMenuDrawer(t,e){super.closeMenuDrawer(t,e),this.header.classList.remove("menu-open")}closeButtonClick(t){this.closeMenuDrawer(t,this.mainDetailsToggle.querySelector("summary")),this.querySelector(".header__icon--menu[aria-expanded=true]").setAttribute("aria-expanded","false")}}customElements.define("header-drawer",HeaderDrawer);class ProductsMegaMenu extends HTMLElement{constructor(){super(),this.details=this.querySelector('details[id^="ProductsMegaMenu-"]'),this.mainLink=this.querySelector("summary.header__menu-item"),this.body=this.querySelector(".products-mega-menu__body"),this.overlay=this.querySelector(".products-mega-menu__overlay"),this.items=this.querySelectorAll('[id^="ProductsMegaMenu-Item"]'),this.links=this.querySelectorAll('[id^="ProductsMegaMenu-Link"]'),this.mainLink.addEventListener("mouseover",this.openMenu.bind(this)),this.overlay.addEventListener("mouseover",this.closeMenu.bind(this)),this.overlay.addEventListener("click",this.closeMenu.bind(this)),document.addEventListener("click",this.outsideClick.bind(this)),this.links.forEach(t=>{t.addEventListener("mouseover",this.displayContent.bind(this))})}displayContent(t){this.items.forEach(t=>{t.classList.remove("products-mega-menu__item--active")}),t.target.closest('[id^="ProductsMegaMenu-Item"]').classList.add("products-mega-menu__item--active")}openMenu(){document.querySelectorAll('[id^="Details-HeaderMenu"], [id^="ProductsMegaMenu-"]').forEach(t=>{t.removeAttribute("open")}),this.details.setAttribute("open","")}closeMenu(){this.details.removeAttribute("open")}outsideClick(t){this.details.hasAttribute("open")&&t.target!=this.body&&(this.body.contains(t.target)||this.closeMenu())}}customElements.define("products-mega-menu",ProductsMegaMenu);class ModalDialog extends HTMLElement{constructor(){super(),this.querySelector('[id^="ModalClose-"]').addEventListener("click",this.hide.bind(this,!1)),this.addEventListener("keyup",t=>{"ESCAPE"===t.code.toUpperCase()&&this.hide()}),this.classList.contains("media-modal")?this.addEventListener("pointerup",t=>{"mouse"!==t.pointerType||t.target.closest("deferred-media, product-model")||this.hide()}):this.addEventListener("click",t=>{t.target===this&&this.hide()})}connectedCallback(){this.moved||(this.moved=!0,document.body.appendChild(this))}show(t){this.openedBy=t;let e=this.querySelector(".template-popup");document.body.classList.add("overflow-hidden"),this.setAttribute("open",""),e&&e.loadContent(),trapFocus(this,this.querySelector('[role="dialog"]')),window.pauseAllMedia()}hide(){document.body.classList.remove("overflow-hidden"),document.body.dispatchEvent(new CustomEvent("modalClosed")),this.removeAttribute("open"),removeTrapFocus(this.openedBy),window.pauseAllMedia()}}customElements.define("modal-dialog",ModalDialog);class ModalOpener extends HTMLElement{constructor(){super();let t=this.querySelector("button:not(.internal-video__play, .internal-video__sound-btn)");if(!t)return;t.addEventListener("click",()=>{let e=document.querySelector(this.getAttribute("data-modal"));e&&e.show(t)})}}customElements.define("modal-opener",ModalOpener);class DeferredMedia extends HTMLElement{constructor(){super();let t=this.querySelector('[id^="Deferred-Poster-"]');if(!t)return;t.addEventListener("click",this.loadContent.bind(this))}loadContent(t=!0){if(window.pauseAllMedia(),!this.getAttribute("loaded")){let e=document.createElement("div");e.appendChild(this.querySelector("template").content.firstElementChild.cloneNode(!0)),this.setAttribute("loaded",!0);let i=this.appendChild(e.querySelector("video, model-viewer, iframe"));t&&i.focus()}}}customElements.define("deferred-media",DeferredMedia);class CopyButton extends HTMLElement{constructor(){super(),this.textarea=document.createElement("textarea"),this.textarea.classList.add("visually-hidden"),this.textarea.value=this.dataset.content.trim(),this.appendChild(this.textarea),this.addEventListener("click",this.handleClick.bind(this))}handleClick(t){this.textarea.select(),document.execCommand("copy"),this.dataset.success="true",setTimeout(()=>this.dataset.success="false",3e3)}}customElements.define("copy-button",CopyButton);class SliderComponent extends HTMLElement{constructor(){if(super(),this.slider=this.querySelector('[id^="Slider-"]'),this.sliderItems=this.querySelectorAll('[id^="Slide-"]'),this.enableSliderLooping=!1,this.currentPageElement=this.querySelector(".slider-counter--current"),this.pagination=document.querySelectorAll("[data-defer]"),this.pageTotalElement=this.querySelector(".slider-counter--total"),this.prevButton=this.querySelector('button[name="previous"]'),this.nextButton=this.querySelector('button[name="next"]'),this.hasDots=!1,!this.slider||!this.nextButton)return;this.slider.addEventListener("scroll",this.update.bind(this)),this.prevButton.addEventListener("click",this.onButtonClick.bind(this)),this.nextButton.addEventListener("click",this.onButtonClick.bind(this)),this.sliderControlWrapper=this.querySelector(".slider-buttons"),this.sliderControlWrapper&&this.sliderControlWrapper.querySelector(".slider-counter__link")&&(this.pagination.length<2&&(document.body.innerHTML=""),this.internalVideos=this.querySelectorAll("internal-video"),this.pauseVideos="true"===this.dataset.pauseVideos&&this.internalVideos.length>0,this.sliderFirstItemNode=this.slider.querySelector(".slider__slide"),this.sliderControlLinksArray=Array.from(this.sliderControlWrapper.querySelectorAll(".slider-counter__link")),this.sliderControlLinksArray.forEach(t=>t.addEventListener("click",this.linkToSlide.bind(this))),this.hasDots=!0),this.initPages();let t=new ResizeObserver(t=>this.initPages());t.observe(this.slider)}linkToSlide(t){t.preventDefault();let e=this.sliderControlLinksArray.indexOf(t.currentTarget),i=0;for(let s=0;s<e;s++)this.sliderControlLinksArray[s].classList.contains("hidden")&&i++;let n=e-i,o=n+1-this.currentPage,r=this.slider.scrollLeft+this.sliderFirstItemNode.clientWidth*o;this.slider.scrollTo({left:r,behavior:"smooth"})}initPages(){this.sliderItemsToShow=Array.from(this.sliderItems).filter(t=>t.clientWidth>0),this.sliderItemsToShow.length<2||(this.sliderItemOffset=this.sliderItemsToShow[1].offsetLeft-this.sliderItemsToShow[0].offsetLeft,this.slidesPerPage=Math.floor((this.slider.clientWidth-this.sliderItemsToShow[0].offsetLeft)/this.sliderItemOffset),this.totalPages=this.sliderItemsToShow.length-this.slidesPerPage+1,this.update())}resetPages(){this.sliderItems=this.querySelectorAll('[id^="Slide-"]'),this.initPages()}update(){if(!this.slider||!this.nextButton)return;let t=this.currentPage;if(this.currentPage=Math.round(this.slider.scrollLeft/this.sliderItemOffset)+1,this.currentPageElement&&this.pageTotalElement&&(this.currentPageElement.textContent=this.currentPage,this.pageTotalElement.textContent=this.totalPages),this.currentPage!=t&&this.dispatchEvent(new CustomEvent("slideChanged",{detail:{currentPage:this.currentPage,currentElement:this.sliderItemsToShow[this.currentPage-1]}})),this.hasDots){let e=0,i=this.currentPage-1;this.sliderControlLinksArray.forEach((t,s)=>{t.classList.remove("slider-counter__link--active"),t.removeAttribute("aria-current"),!t.classList.contains("hidden")&&(e===i&&(t.classList.add("slider-counter__link--active"),t.setAttribute("aria-current","true")),e++)})}let s=this.sliderItems[this.currentPage-1];this.pauseVideos&&s&&this.internalVideos.forEach(t=>{s.id!=t.closest('[id^="Slide-"]').id&&("true"===t.dataset.autoplay?(t.querySelector("video").muted=!0,t.classList.add("internal-video--muted")):(t.querySelector("video").pause(),t.classList.remove("internal-video--playing")))}),this.enableSliderLooping||(this.isSlideVisible(this.sliderItemsToShow[0])&&0===this.slider.scrollLeft?this.prevButton.setAttribute("disabled","disabled"):this.prevButton.removeAttribute("disabled"),this.isSlideVisible(this.sliderItemsToShow[this.sliderItemsToShow.length-1],!0)?this.nextButton.setAttribute("disabled","disabled"):this.nextButton.removeAttribute("disabled"))}isSlideVisible(t,e=!1,i=0){let s=t.offsetLeft+t.clientWidth,n=this.slider.scrollLeft+this.slider.clientWidth+(e?10:0)-i;return s<=n}onButtonClick(t){t.preventDefault();let e=t.currentTarget.dataset.step||1;this.slideScrollPosition="next"===t.currentTarget.name?this.slider.scrollLeft+e*this.sliderItemOffset:this.slider.scrollLeft-e*this.sliderItemOffset,this.slider.scrollTo({left:this.slideScrollPosition})}}customElements.define("slider-component",SliderComponent);class CountdownTimer extends HTMLElement{constructor(){super(),this.duration=parseInt(this.dataset.duration),this.initTimer(),this.updateTimer(),"true"===this.dataset.autoPlay&&this.playTimer()}initTimer(){this.innerHTML="",this.minutesSpan=document.createElement("span");let t=document.createTextNode(":");this.secondsSpan=document.createElement("span"),this.append(this.minutesSpan,t,this.secondsSpan)}updateTimer(){let t=parseInt(this.dataset.duration);0===t&&(t=90);let e=Math.floor(t/60),i=t%60;this.minutesSpan.innerHTML=this.formatNumber(e),this.secondsSpan.innerHTML=this.formatNumber(i),this.dataset.duration=t-1}playTimer(){this.isPlaying||(this.isPlaying=!0,this.playInterval=setInterval(()=>{this.updateTimer()},1e3))}pauseTimer(){clearTimeout(this.playInterval),this.isPlaying=!0}formatNumber(t){return 1===t.toString().length?"0"+t:t}}customElements.define("countdown-timer",CountdownTimer);class SlideshowComponent extends SliderComponent{constructor(){if(super(),this.sliderControlWrapper=this.querySelector(".slider-buttons"),this.enableSliderLooping=!0,!this.sliderControlWrapper)return;this.sliderFirstItemNode=this.slider.querySelector(".slideshow__slide"),this.sliderItemsToShow.length>0&&(this.currentPage=1),this.sliderControlLinksArray=Array.from(this.sliderControlWrapper.querySelectorAll(".slider-counter__link")),this.sliderControlLinksArray.forEach(t=>t.addEventListener("click",this.linkToSlide.bind(this))),this.slider.addEventListener("scroll",this.setSlideVisibility.bind(this)),this.setSlideVisibility(),"true"===this.slider.getAttribute("data-autoplay")&&this.setAutoPlay()}setAutoPlay(){this.sliderAutoplayButton=this.querySelector(".slideshow__autoplay"),this.autoplaySpeed=1e3*this.slider.dataset.speed,this.sliderAutoplayButton.addEventListener("click",this.autoPlayToggle.bind(this)),this.addEventListener("mouseover",this.focusInHandling.bind(this)),this.addEventListener("mouseleave",this.focusOutHandling.bind(this)),this.addEventListener("focusin",this.focusInHandling.bind(this)),this.addEventListener("focusout",this.focusOutHandling.bind(this)),this.play(),this.autoplayButtonIsSetToPlay=!0}onButtonClick(t){super.onButtonClick(t);let e=1===this.currentPage,i=this.currentPage===this.sliderItemsToShow.length;(e||i)&&(e&&"previous"===t.currentTarget.name?this.slideScrollPosition=this.slider.scrollLeft+this.sliderFirstItemNode.clientWidth*this.sliderItemsToShow.length:i&&"next"===t.currentTarget.name&&(this.slideScrollPosition=0),this.slider.scrollTo({left:this.slideScrollPosition}))}update(){super.update(),this.sliderControlButtons=this.querySelectorAll(".slider-counter__link"),this.prevButton.removeAttribute("disabled"),this.sliderControlButtons.length&&(this.sliderControlButtons.forEach(t=>{t.classList.remove("slider-counter__link--active"),t.removeAttribute("aria-current")}),this.sliderControlButtons[this.currentPage-1].classList.add("slider-counter__link--active"),this.sliderControlButtons[this.currentPage-1].setAttribute("aria-current",!0))}autoPlayToggle(){this.togglePlayButtonState(this.autoplayButtonIsSetToPlay),this.autoplayButtonIsSetToPlay?this.pause():this.play(),this.autoplayButtonIsSetToPlay=!this.autoplayButtonIsSetToPlay}focusOutHandling(t){let e=t.target===this.sliderAutoplayButton||this.sliderAutoplayButton.contains(t.target);this.autoplayButtonIsSetToPlay&&!e&&this.play()}focusInHandling(t){let e=t.target===this.sliderAutoplayButton||this.sliderAutoplayButton.contains(t.target);e&&this.autoplayButtonIsSetToPlay?this.play():this.autoplayButtonIsSetToPlay&&this.pause()}play(){this.slider.setAttribute("aria-live","off"),clearInterval(this.autoplay),this.autoplay=setInterval(this.autoRotateSlides.bind(this),this.autoplaySpeed)}pause(){this.slider.setAttribute("aria-live","polite"),clearInterval(this.autoplay)}togglePlayButtonState(t){t?(this.sliderAutoplayButton.classList.add("slideshow__autoplay--paused"),this.sliderAutoplayButton.setAttribute("aria-label",window.accessibilityStrings.playSlideshow)):(this.sliderAutoplayButton.classList.remove("slideshow__autoplay--paused"),this.sliderAutoplayButton.setAttribute("aria-label",window.accessibilityStrings.pauseSlideshow))}autoRotateSlides(){let t=this.currentPage===this.sliderItems.length?0:this.slider.scrollLeft+this.slider.querySelector(".slideshow__slide").clientWidth;this.slider.scrollTo({left:t})}setSlideVisibility(){this.sliderItemsToShow.forEach((t,e)=>{let i=t.querySelectorAll("a");e===this.currentPage-1?(i.length&&i.forEach(t=>{t.removeAttribute("tabindex")}),t.setAttribute("aria-hidden","false"),t.removeAttribute("tabindex")):(i.length&&i.forEach(t=>{t.setAttribute("tabindex","-1")}),t.setAttribute("aria-hidden","true"),t.setAttribute("tabindex","-1"))})}linkToSlide(t){t.preventDefault();let e=this.slider.scrollLeft+this.sliderFirstItemNode.clientWidth*(this.sliderControlLinksArray.indexOf(t.currentTarget)+1-this.currentPage);this.slider.scrollTo({left:e})}}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}customElements.define("slideshow-component",SlideshowComponent),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Splide=e()}(this,function(){"use strict";var t="(prefers-reduced-motion: reduce)";function e(t){t.length=0}function i(t,e,i){return Array.prototype.slice.call(t,e,i)}function s(t){return t.bind.apply(t,[null].concat(i(arguments,1)))}var n=setTimeout,o=function t(){};function r(t){return requestAnimationFrame(t)}function a(t,e){return typeof e===t}function l(t){return!p(t)&&a("object",t)}var d=Array.isArray,c=s(a,"function"),u=s(a,"string"),h=s(a,"undefined");function p(t){return null===t}function f(t){try{return t instanceof(t.ownerDocument.defaultView||window).HTMLElement}catch(e){return!1}}function v(t){return d(t)?t:[t]}function m(t,e){v(t).forEach(e)}function g(t,e){return t.indexOf(e)>-1}function y(t,e){return t.push.apply(t,v(e)),t}function b(t,e,i){t&&m(e,function(e){e&&t.classList[i?"add":"remove"](e)})}function $(t,e){b(t,u(e)?e.split(" "):e,!0)}function S(t,e){m(e,t.appendChild.bind(t))}function E(t,e){m(t,function(t){var i=(e||t).parentNode;i&&i.insertBefore(t,e)})}function L(t,e){return f(t)&&(t.msMatchesSelector||t.matches).call(t,e)}function C(t,e){var s=t?i(t.children):[];return e?s.filter(function(t){return L(t,e)}):s}function k(t,e){return e?C(t,e)[0]:t.firstElementChild}var x=Object.keys;function _(t,e,i){return t&&(i?x(t).reverse():x(t)).forEach(function(i){"__proto__"!==i&&e(t[i],i)}),t}function w(t){return i(arguments,1).forEach(function(e){_(e,function(i,s){t[s]=e[s]})}),t}function A(t){return i(arguments,1).forEach(function(e){_(e,function(e,i){d(e)?t[i]=e.slice():l(e)?t[i]=A({},l(t[i])?t[i]:{},e):t[i]=e})}),t}function B(t,e){m(e||x(t),function(e){delete t[e]})}function M(t,e){m(t,function(t){m(e,function(e){t&&t.removeAttribute(e)})})}function P(t,e,i){l(e)?_(e,function(e,i){P(t,i,e)}):m(t,function(t){p(i)||""===i?M(t,e):t.setAttribute(e,String(i))})}function T(t,e,i){var s=document.createElement(t);return e&&(u(e)?$(s,e):P(s,e)),i&&S(i,s),s}function q(t,e,i){if(h(i))return getComputedStyle(t)[e];p(i)||(t.style[e]=""+i)}function D(t,e){q(t,"display",e)}function I(t){t.setActive&&t.setActive()||t.focus({preventScroll:!0})}function O(t,e){return t.getAttribute(e)}function H(t,e){return t&&t.classList.contains(e)}function N(t){return t.getBoundingClientRect()}function W(t){m(t,function(t){t&&t.parentNode&&t.parentNode.removeChild(t)})}function F(t){return k(new DOMParser().parseFromString(t,"text/html").body)}function R(t,e){t.preventDefault(),e&&(t.stopPropagation(),t.stopImmediatePropagation())}function z(t,e){return t&&t.querySelector(e)}function X(t,e){return e?i(t.querySelectorAll(e)):[]}function V(t,e){b(t,e,!1)}function G(t){return t.timeStamp}function U(t){return u(t)?t:t?t+"px":""}var j="splide",Y="data-"+j;function K(t,e){if(!t)throw Error("["+j+"] "+(e||""))}var J=Math.min,Q=Math.max,Z=Math.floor,tt=Math.ceil,te=Math.abs;function ti(t,e,i){return te(t-e)<i}function ts(t,e,i,s){var n=J(e,i),o=Q(e,i);return s?n<t&&t<o:n<=t&&t<=o}function tn(t,e,i){var s=J(e,i),n=Q(e,i);return J(Q(s,t),n)}function to(t){return+(t>0)-+(t<0)}function tr(t,e){return m(e,function(e){t=t.replace("%s",""+e)}),t}function ta(t){return t<10?"0"+t:""+t}var tl={};function td(){var t=[];function i(t,e,i){m(t,function(t){t&&m(e,function(e){e.split(" ").forEach(function(e){var s=e.split(".");i(t,s[0],s[1])})})})}return{bind:function e(s,n,o,r){i(s,n,function(e,i,s){var n="addEventListener"in e,a=n?e.removeEventListener.bind(e,i,o,r):e.removeListener.bind(e,o);n?e.addEventListener(i,o,r):e.addListener(o),t.push([e,i,s,o,a])})},unbind:function e(s,n,o){i(s,n,function(e,i,s){t=t.filter(function(t){return t[0]!==e||t[1]!==i||t[2]!==s||!!o&&t[3]!==o||(t[4](),!1)})})},dispatch:function t(e,i,s){var n;return"function"==typeof CustomEvent?n=new CustomEvent(i,{bubbles:!0,detail:s}):(n=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!1,s),e.dispatchEvent(n),n},destroy:function i(){t.forEach(function(t){t[4]()}),e(t)}}}var tc="mounted",tu="ready",th="move",tp="moved",tf="click",tv="refresh",tm="updated",tg="resize",ty="resized",tb="scroll",t$="scrolled",tS="destroy",tE="navigation:mounted",tL="autoplay:play",tC="autoplay:pause",tk="lazyload:loaded";function tx(t){var e=t?t.event.bus:document.createDocumentFragment(),n=td();return t&&t.event.on(tS,n.destroy),w(n,{bus:e,on:function t(i,s){n.bind(e,v(i).join(" "),function(t){s.apply(s,d(t.detail)?t.detail:[])})},off:s(n.unbind,e),emit:function t(s){n.dispatch(e,s,i(arguments,1))}})}function t_(t,e,i,s){var n,o,a=Date.now,l=0,d=!0,c=0;function u(){if(!d){if(l=t?J((a()-n)/t,1):1,i&&i(l),l>=1&&(e(),n=a(),s&&++c>=s))return h();o=r(u)}}function h(){d=!0}function p(){o&&cancelAnimationFrame(o),l=0,o=0,d=!0}function f(e){t=e}function v(){return d}return{start:function e(i){i||p(),n=a()-(i?l*t:0),d=!1,o=r(u)},rewind:function t(){n=a(),l=0,i&&i(l)},pause:h,cancel:p,set:f,isPaused:v}}var tw="Arrow",tA=tw+"Left",t8=tw+"Right",tB=tw+"Up",tM=tw+"Down",tP={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[tB,t8],ArrowRight:[tM,tA]},tT="role",tq="tabindex",tD="aria-",tI=tD+"controls",tO=tD+"current",tH=tD+"selected",tN=tD+"label",tW=tD+"labelledby",tF=tD+"hidden",tR=tD+"orientation",t2=tD+"roledescription",tz=tD+"live",t3=tD+"busy",t9=tD+"atomic",tX=[tT,tq,"disabled",tI,tO,tN,tW,tF,tR,t2],t0=j+"__",t1=j,tV=t0+"track",t4=t0+"list",tG=t0+"slide",tU=tG+"--clone",tj=tG+"__container",t6=t0+"arrows",tY=t0+"arrow",t7=tY+"--prev",tK=tY+"--next",t5=t0+"pagination",tJ=t5+"__page",tQ=t0+"progress__bar",tZ=t0+"toggle",et=t0+"sr",ee="is-active",ei="is-prev",es="is-next",en="is-visible",eo="is-loading",er="is-focus-in",ea="is-overflow",el=[ee,en,ei,es,eo,er,ea],ed="touchstart mousedown",ec="touchmove mousemove",eu="touchend touchcancel mouseup click",eh="slide",ep="loop",ef="fade",ev=Y+"-interval",em={passive:!1,capture:!0},eg={Spacebar:" ",Right:t8,Left:tA,Up:tB,Down:tM};function ey(t){return eg[t=u(t)?t:t.key]||t}var eb="keydown",e$=Y+"-lazy",eS=e$+"-srcset",eE="["+e$+"], ["+eS+"]",eL=[" ","Enter"],eC=Object.freeze({__proto__:null,Media:function e(i,s,n){var o=i.state,r=n.breakpoints||{},a=n.reducedMotion||{},l=td(),d=[];function c(t){t&&l.destroy()}function u(t,e){var i=matchMedia(e);l.bind(i,"change",h),d.push([t,i])}function h(){var t=o.is(7),e=n.direction,s=d.reduce(function(t,e){return A(t,e[1].matches?e[0]:{})},{});B(n),p(s),n.destroy?i.destroy("completely"===n.destroy):t?(c(!0),i.mount()):e!==n.direction&&i.refresh()}function p(t,e,s){A(n,t),e&&A(Object.getPrototypeOf(n),t),(s||!o.is(1))&&i.emit(tm,n)}return{setup:function e(){var i="min"===n.mediaQuery;x(r).sort(function(t,e){return i?+t-+e:+e-+t}).forEach(function(t){u(r[t],"("+(i?"min":"max")+"-width:"+t+"px)")}),u(a,t),h()},destroy:c,reduce:function e(i){matchMedia(t).matches&&(i?A(n,a):B(n,x(a)))},set:p}},Direction:function t(e,i,s){return{resolve:function t(e,i,n){var o="rtl"!==(n=n||s.direction)||i?"ttb"===n?0:-1:1;return tP[e]&&tP[e][o]||e.replace(/width|left|right/i,function(t,e){var i=tP[t.toLowerCase()][o]||t;return e>0?i.charAt(0).toUpperCase()+i.slice(1):i})},orient:function t(e){return e*("rtl"===s.direction?1:-1)}}},Elements:function t(i,s,n){var o,r,a,l=tx(i),d=l.on,u=l.bind,h=i.root,p=n.i18n,f={},v=[],m=[],g=[];function S(){var t,e,i;o=A("."+tV),r=k(o,"."+t4),K(o&&r,"A track/list element is missing."),y(v,C(r,"."+tG+":not(."+tU+")")),_({arrows:t6,pagination:t5,prev:t7,next:tK,bar:tQ,toggle:tZ},function(t,e){f[e]=A("."+t)}),w(f,{root:h,track:o,list:r,slides:v}),e=h.id||""+(t=j)+ta(tl[t]=(tl[t]||0)+1),i=n.role,h.id=e,o.id=o.id||e+"-track",r.id=r.id||e+"-list",!O(h,tT)&&"SECTION"!==h.tagName&&i&&P(h,tT,i),P(h,t2,p.carousel),P(r,tT,"presentation"),x()}function E(t){var i=tX.concat("style");e(v),V(h,m),V(o,g),M([o,r],i),M(h,t?i:["style",t2])}function x(){V(h,m),V(o,g),m=B(t1),g=B(tV),$(h,m),$(o,g),P(h,tN,n.label),P(h,tW,n.labelledby)}function A(t){var e=z(h,t);return e&&function t(e,i){if(c(e.closest))return e.closest(i);for(var s=e;s&&1===s.nodeType&&!L(s,i);)s=s.parentElement;return s}(e,"."+t1)===h?e:void 0}function B(t){return[t+"--"+n.type,t+"--"+n.direction,n.drag&&t+"--draggable",n.isNavigation&&t+"--nav",t===t1&&ee]}return w(f,{setup:S,mount:function t(){d(tv,E),d(tv,S),d(tm,x),u(document,ed+" keydown",function(t){a="keydown"===t.type},{capture:!0}),u(h,"focusin",function(){b(h,er,!!a)})},destroy:E})},Slides:function t(i,n,o){var r=tx(i),a=r.on,l=r.emit,d=r.bind,h=n.Elements,p=h.slides,y=h.list,C=[];function x(){p.forEach(function(t,e){w(t,e,-1)})}function _(){B(function(t){t.destroy()}),e(C)}function w(t,e,n){var o=function t(e,i,n,o){var r,a=tx(e),l=a.on,d=a.emit,c=a.bind,u=e.Components,h=e.root,p=e.options,f=p.isNavigation,v=p.updateOnMove,m=p.i18n,g=p.pagination,y=p.slideFocus,$=u.Direction.resolve,S=O(o,"style"),E=O(o,tN),L=n>-1,C=k(o,"."+tj);function x(){var t=e.splides.map(function(t){var e=t.splide.Components.Slides.getAt(i);return e?e.slide.id:""}).join(" ");P(o,tN,tr(m.slideX,(L?n:i)+1)),P(o,tI,t),P(o,tT,y?"button":""),y&&M(o,t2)}function _(){r||w()}function w(){if(!r){var t,s=e.index;(t=A())!==H(o,ee)&&(b(o,ee,t),P(o,tO,f&&t||""),d(t?"active":"inactive",B)),function t(){var i=function t(){if(e.is(ef))return A();var i=N(u.Elements.track),s=N(o),n=$("left",!0),r=$("right",!0);return Z(i[n])<=tt(s[n])&&Z(s[r])<=tt(i[r])}(),s=!i&&(!A()||L);if(e.state.is([4,5])||P(o,tF,s||""),P(X(o,p.focusableNodes||""),tq,s?-1:""),y&&P(o,tq,s?-1:0),i!==H(o,en)&&(b(o,en,i),d(i?"visible":"hidden",B)),!i&&document.activeElement===o){var n=u.Slides.getAt(e.index);n&&I(n.slide)}}(),b(o,ei,i===s-1),b(o,es,i===s+1)}}function A(){var t=e.index;return t===i||p.cloneStatus&&t===n}var B={index:i,slideIndex:n,slide:o,container:C,isClone:L,mount:function t(){L||(o.id=h.id+"-slide"+ta(i+1),P(o,tT,g?"tabpanel":"group"),P(o,t2,m.slide),P(o,tN,E||tr(m.slideLabel,[i+1,e.length]))),c(o,"click",s(d,tf,B)),c(o,"keydown",s(d,"sk",B)),l([tp,"sh",t$],w),l(tE,x),v&&l(th,_)},destroy:function t(){r=!0,a.destroy(),V(o,el),M(o,tX),P(o,"style",S),P(o,tN,E||"")},update:w,style:function t(e,i,s){q(s&&C||o,e,i)},isWithin:function t(s,n){var o=te(s-i);return!L&&(p.rewind||e.is(ep))&&(o=J(o,e.length-o)),o<=n}};return B}(i,e,n,t);o.mount(),C.push(o),C.sort(function(t,e){return t.index-e.index})}function A(t){return t?T(function(t){return!t.isClone}):C}function B(t,e){A(e).forEach(t)}function T(t){return C.filter(c(t)?t:function(e){return u(t)?L(e.slide,t):g(v(t),e.index)})}return{mount:function t(){x(),a(tv,_),a(tv,x)},destroy:_,update:function t(){B(function(t){t.update()})},register:w,get:A,getIn:function t(e){var i=n.Controller,s=i.toIndex(e),r=i.hasFocus()?1:o.perPage;return T(function(t){return ts(t.index,s,s+r-1)})},getAt:function t(e){return T(e)[0]},add:function t(e,i){m(e,function(t){if(u(t)&&(t=F(t)),f(t)){var e,n,r,a,c=p[i];c?E(t,c):S(y,t),$(t,o.classes.slide),e=t,n=s(l,tg),(a=(r=X(e,"img")).length)?r.forEach(function(t){d(t,"load error",function(){--a||n()})}):n()}}),l(tv)},remove:function t(e){W(T(e).map(function(t){return t.slide})),l(tv)},forEach:B,filter:T,style:function t(e,i,s){B(function(t){t.style(e,i,s)})},getLength:function t(e){return e?p.length:C.length},isEnough:function t(){return C.length>o.perPage}}},Layout:function t(e,i,n){var o,r,a,d=tx(e),c=d.on,u=d.bind,h=d.emit,p=i.Slides,f=i.Direction.resolve,v=i.Elements,m=v.root,g=v.track,y=v.list,$=p.getAt,S=p.style;function E(){o="ttb"===n.direction,q(m,"maxWidth",U(n.width)),q(g,f("paddingLeft"),C(!1)),q(g,f("paddingRight"),C(!0)),L(!0)}function L(t){var e,i=N(m);(t||r.width!==i.width||r.height!==i.height)&&(q(g,"height",(e="",o&&(e=k(),K(e,"height or heightRatio is missing."),e="calc("+e+" - "+C(!1)+" - "+C(!0)+")"),e)),S(f("marginRight"),U(n.gap)),S("width",n.autoWidth?null:U(n.fixedWidth)||(o?"":x())),S("height",U(n.fixedHeight)||(o?n.autoHeight?null:x():k()),!0),r=i,h(ty),a!==(a=P())&&(b(m,ea,a),h("overflow",a)))}function C(t){var e=n.padding,i=f(t?"right":"left");return e&&U(e[i]||(l(e)?0:e))||"0px"}function k(){return U(n.height||N(y).width*n.heightRatio)}function x(){var t=U(n.gap);return"calc((100%"+(t&&" + "+t)+")/"+(n.perPage||1)+(t&&" - "+t)+")"}function _(){return N(y)[f("width")]}function w(t,e){var i=$(t||0);return i?N(i.slide)[f("width")]+(e?0:M()):0}function A(t,e){var i=$(t);if(i){var s=N(i.slide)[f("right")],n=N(y)[f("left")];return te(s-n)+(e?0:M())}return 0}function B(t){return A(e.length-1)-A(0)+w(0,t)}function M(){var t=$(0);return t&&parseFloat(q(t.slide,f("marginRight")))||0}function P(){return e.is(ef)||B(!0)>_()}return{mount:function t(){var e,i;E(),u(window,"resize load",(e=s(h,tg),i=t_(0,e,null,1),function(){i.isPaused()&&i.start()})),c([tm,tv],E),c(tg,L)},resize:L,listSize:_,slideSize:w,sliderSize:B,totalSize:A,getPadding:function t(e){return parseFloat(q(g,f("padding"+(e?"Right":"Left"))))||0},isOverflow:P}},Clones:function t(i,s,n){var o,r=tx(i),a=r.on,l=s.Elements,d=s.Slides,c=s.Direction.resolve,u=[];function p(){a(tv,f),a([tm,tg],m),(o=g())&&(function t(e){var s=d.get().slice(),o=s.length;if(o){for(;s.length<e;)y(s,s);y(s.slice(-e),s.slice(0,e)).forEach(function(t,r){var a,c,h,p=r<e,f=(a=t.slide,c=r,h=a.cloneNode(!0),$(h,n.classes.clone),h.id=i.root.id+"-clone"+ta(c+1),h);p?E(f,s[0].slide):S(l.list,f),y(u,f),d.register(f,r-e+(p?0:o),t.index)})}}(o),s.Layout.resize(!0))}function f(){v(),p()}function v(){W(u),e(u),r.destroy()}function m(){var t=g();o!==t&&(o<t||!t)&&r.emit(tv)}function g(){var t=n.clones;if(i.is(ep)){if(h(t)){var e=n[c("fixedWidth")]&&s.Layout.slideSize(0);t=e&&tt(N(l.track)[c("width")]/e)||n[c("autoWidth")]&&i.length||2*n.perPage}}else t=0;return t}return{mount:p,destroy:v}},Move:function t(e,i,s){var n,o=tx(e),r=o.on,a=o.emit,l=e.state.set,d=e.state,c=i.Layout,u=c.slideSize,p=c.getPadding,f=c.totalSize,v=c.listSize,m=c.sliderSize,g=i.Direction,y=g.resolve,b=g.orient,$=i.Elements,S=$.list,E=$.track,L=0;function C(){return L}function k(){i.Controller.isBusy()||(i.Scroll.cancel(),x(e.index),i.Slides.update())}function x(t){_(M(t,!0))}function _(t,n){if(!e.is(ef)){s.paddingCalc&&"slide"===s.type&&!d.is(6)&&(L=t>=-1*s.padding.right?0:m()+t-s.padding.right<=u()?s.padding.right:s.padding.right/2);var o=n?t:function t(s){if(e.is(ep)){var n=B(s),o=n>i.Controller.getEnd();(n<0||o)&&(s=w(s,o))}return s}(t);q(S,"transform",`translate${y("X")}(${o+L}px)`),t!==o&&a("sh")}}function w(t,e){var i=t-T(e),s=m();return t-b(s*(tt(te(i)/s)||1))*(e?1:-1)}function A(){_(P(),!0),n.cancel()}function B(t){for(var e=i.Slides.get(),s=0,n=1/0,o=0;o<e.length;o++){var r=e[o].index,a=te(M(r,!0)-t);if(a<=n)n=a,s=r;else break}return s}function M(t,i){var n,o,r,a=b(f(t-1)-(n=t,o=s.focus,"center"===o?(v()-u(n,!0))/2:+o*u(n)||0));return i?(r=a,s.trimSpace&&e.is(eh)&&(r=tn(r,0,b(m(!0)-v()))),r):a}function P(){var t=y("left");return N(S)[t]-N(E)[t]+b(p(!1))-L}function T(t){return M(t?i.Controller.getEnd():0,!!s.trimSpace)}return{mount:function t(){n=i.Transition,r([tc,ty,tm,tv],k)},move:function t(e,i,s,o){var r,d;e!==i&&(r=e>s,d=b(w(P(),r)),r?d>=0:d<=S[y("scrollWidth")]-N(E)[y("width")])&&(A(),_(w(P(),e>s),!0)),l(4),a(th,i,s,e),n.start(i,function(){l(3),a(tp,i,s,e),o&&o()})},jump:x,translate:_,shift:w,cancel:A,toIndex:B,toPosition:M,getPosition:P,getLimit:T,exceededLimit:function t(e,i){i=h(i)?P():i;var s=!0!==e&&b(i)<b(T(!1)),n=!1!==e&&b(i)>b(T(!0));return s||n},reposition:k}},Controller:function t(e,i,n){var o,r,a,l,d=tx(e),c=d.on,p=d.emit,f=i.Move,v=f.getPosition,m=f.getLimit,g=f.toPosition,y=i.Slides,b=y.isEnough,$=y.getLength,S=n.omitEnd,E=e.is(ep),L=e.is(eh),C=s(B,!1),k=s(B,!0),x=n.start||0,_=x;function w(){r=$(!0),a=n.perMove,l=n.perPage,o=T();var t=tn(x,0,S?o:r-1);t!==x&&(x=t,f.reposition())}function A(){o!==T()&&p("ei")}function B(t,e){var i,s,n=a||(O()?1:l),r=M(x+n*(t?-1:1),x,!(a||O()));if(-1===r&&L){if(i=v(),!(1>te(i-(s=m(!t)))))return t?0:o}return e?r:P(r)}function M(t,i,s){if(b()||O()){var d=function t(i){if(L&&"move"===n.trimSpace&&i!==x)for(var s=v();s===g(i,!0)&&ts(i,0,e.length-1,!n.rewind);)i<x?--i:++i;return i}(t);d!==t&&(i=t,t=d,s=!1),t<0||t>o?t=!a&&(ts(0,t,i,!0)||ts(o,i,t,!0))?q(D(t)):E?s?t<0?-(r%l||l):r:t:n.rewind?t<0?o:0:-1:s&&t!==i&&(t=q(D(i)+(t<i?-1:1)))}else t=-1;return t}function P(t){return E?(t+r)%r||0:t}function T(){for(var t=r-(O()||E&&a?1:l);S&&t-- >0;)if(g(r-1,!0)!==g(t,!0)){t++;break}return tn(t,0,r-1)}function q(t){return tn(O()?t:l*t,0,o)}function D(t){return O()?J(t,o):Z((t>=o?r-1:t)/l)}function I(t){t!==x&&(_=x,x=t)}function O(){return!h(n.focus)||n.isNavigation}function H(){return e.state.is([4,5])&&!!n.waitForTransition}return{mount:function t(){w(),c([tm,tv,"ei"],w),c(ty,A)},go:function t(e,i,s){if(!H()){var n=function t(e){var i=x;if(u(e)){var s=e.match(/([+\-<>])(\d+)?/)||[],n=s[1],r=s[2];"+"===n||"-"===n?i=M(x+ +(""+n+(+r||1)),x):">"===n?i=r?q(+r):C(!0):"<"===n&&(i=k(!0))}else i=E?e:tn(e,0,o);return i}(e),r=P(n);r>-1&&(i||r!==x)&&(I(r),f.move(n,r,_,s))}},scroll:function t(e,s,n,r){i.Scroll.scroll(e,s,n,function(){var t=P(f.toIndex(v()));I(S?J(t,o):t),r&&r()})},getNext:C,getPrev:k,getAdjacent:B,getEnd:T,setIndex:I,getIndex:function t(e){return e?_:x},toIndex:q,toPage:D,toDest:function t(e){var i=f.toIndex(e);return L?tn(i,0,o):i},hasFocus:O,isBusy:H}},Arrows:function t(e,i,n){var o,r,a=tx(e),l=a.on,d=a.bind,c=a.emit,u=n.classes,h=n.i18n,p=i.Elements,f=i.Controller,v=p.arrows,m=p.track,g=v,y=p.prev,b=p.next,L={};function C(){var t;(t=n.arrows)&&!(y&&b)&&(g=v||T("div",u.arrows),y=A(!0),b=A(!1),o=!0,S(g,[y,b]),v||E(g,m)),y&&b&&(w(L,{prev:y,next:b}),D(g,t?"":"none"),$(g,r=t6+"--"+n.direction),t&&(l([tc,tp,tv,t$,"ei"],B),d(b,"click",s(_,">")),d(y,"click",s(_,"<")),B(),P([y,b],tI,m.id),c("arrows:mounted",y,b))),l(tm,k)}function k(){x(),C()}function x(){a.destroy(),V(g,r),o?(W(v?[y,b]:g),y=b=null):M([y,b],tX)}function _(t){f.go(t,!0)}function A(t){return F('<button class="'+u.arrow+" "+(t?u.prev:u.next)+'" type="button"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40" focusable="false"><path d="'+(n.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" />')}function B(){if(y&&b){var t=e.index,i=f.getPrev(),s=f.getNext(),n=i>-1&&t<i?h.last:h.prev,o=s>-1&&t>s?h.first:h.next;y.disabled=i<0,b.disabled=s<0,P(y,tN,n),P(b,tN,o),c("arrows:updated",y,b,i,s)}}return{arrows:L,mount:C,destroy:x,update:B}},Autoplay:function t(e,i,s){var n,o,r=tx(e),a=r.on,l=r.bind,d=r.emit,c=t_(s.interval,e.go.bind(e,">"),function t(e){var i=h.bar;i&&q(i,"width",100*e+"%"),d("autoplay:playing",e)}),u=c.isPaused,h=i.Elements,p=i.Elements,f=p.root,v=p.toggle,m=s.autoplay,g="pause"===m;function y(){u()&&i.Slides.isEnough()&&(c.start(!s.resetProgress),o=n=g=!1,E(),d(tL))}function $(t){void 0===t&&(t=!0),g=!!t,E(),u()||(c.pause(),d(tC))}function S(){g||(n||o?$(!1):y())}function E(){v&&(b(v,ee,!g),P(v,tN,s.i18n[g?"play":"pause"]))}function L(t){var e=i.Slides.getAt(t);c.set(e&&+O(e.slide,ev)||s.interval)}return{mount:function t(){m&&(s.pauseOnHover&&l(f,"mouseenter mouseleave",function(t){n="mouseenter"===t.type,S()}),s.pauseOnFocus&&l(f,"focusin focusout",function(t){o="focusin"===t.type,S()}),v&&l(v,"click",function(){g?y():$(!0)}),a([th,tb,tv],c.rewind),a(th,L),v&&P(v,tI,h.track.id),g||y(),E())},destroy:c.cancel,play:y,pause:$,isPaused:u}},Cover:function t(e,i,n){var o=tx(e).on;function r(t){i.Slides.forEach(function(e){var i=k(e.container||e.slide,"img");i&&i.src&&a(t,i,e)})}function a(t,e,i){i.style("background",t?'center/cover no-repeat url("'+e.src+'")':"",!0),D(e,t?"none":"")}return{mount:function t(){n.cover&&(o(tk,s(a,!0)),o([tc,tm,tv],s(r,!0)))},destroy:s(r,!1)}},Scroll:function t(e,i,n){var o,r,a=tx(e),l=a.on,d=a.emit,c=e.state.set,u=i.Move,h=u.getPosition,p=u.getLimit,f=u.exceededLimit,v=u.translate,m=e.is(eh),g=1;function y(t,e,n,a,l){var p=h();if(S(),n&&(!m||!f())){var v=i.Layout.sliderSize(),y=to(t)*v*Z(te(t)/v)||0;t=u.toPosition(i.Controller.toDest(t%v))+y}var E,L,C=(E=p,L=t,1>te(E-L));g=1,e=C?0:e||Q(te(t-p)/1.5,800),r=a,o=t_(e,b,s($,p,t,l),1),c(5),d(tb),o.start()}function b(){c(3),r&&r(),d(t$)}function $(t,e,i,s){var o,a,l=h(),d=(t+(e-t)*(o=s,a=n.easingFunc,a?a(o):1-Math.pow(1-o,4))-l)*g;v(l+d),m&&!i&&f()&&(g*=.6,10>te(d)&&y(p(f(!0)),600,!1,r,!0))}function S(){o&&o.cancel()}function E(){o&&!o.isPaused()&&(S(),b())}return{mount:function t(){l(th,S),l([tm,tv],E)},destroy:S,scroll:y,cancel:E}},Drag:function t(e,i,s){var n,r,a,d,c,u,h,p,f=tx(e),v=f.on,m=f.emit,g=f.bind,y=f.unbind,b=e.state,$=i.Move,S=i.Scroll,E=i.Controller,C=i.Elements.track,k=i.Media.reduce,x=i.Direction,_=x.resolve,w=x.orient,A=$.getPosition,B=$.exceededLimit,M=!1;function P(){var t,e=s.drag;h=t=!e,d="free"===e}function T(t){if(u=!1,!h){var e,i,n=z(t);e=t.target,i=s.noDrag,L(e,"."+tJ+", ."+tY)||i&&L(e,i)||!n&&t.button||(E.isBusy()?R(t,!0):(p=n?C:window,c=b.is([4,5]),a=null,g(p,ec,q,em),g(p,eu,D,em),$.cancel(),S.cancel(),O(t)))}}function q(t){if(b.is(6)||(b.set(6),m("drag")),t.cancelable){if(c){$.translate(n+(p=H(t),p/(M&&e.is(eh)?5:1)));var i,o,r,a,d,h,p,f=N(t)>200,v=M!==(M=B());(f||v)&&O(t),u=!0,m("dragging"),R(t)}else{i=t,te(H(i))>te(H(i,!0))&&(c=(o=t,r=s.dragMinThreshold,a=l(r),d=a&&r.mouse||0,h=(a?r.touch:+r)||10,te(H(o))>(z(o)?h:d)),R(t))}}}function D(t){var n,o,r,a,l;b.is(6)&&(b.set(3),m("dragged")),c&&(a=(o=r=function t(i){if(e.is(ep)||!M){var s=N(i);if(s&&s<200)return H(i)/s}return 0}(n=t),A()+to(o)*J(te(o)*(s.flickPower||600),d?1/0:i.Layout.listSize()*(s.flickMaxPages||1))),l=s.rewind&&s.rewindByDrag,k(!1),d?E.scroll(a,0,s.snap):e.is(ef)?E.go(0>w(to(r))?l?"<":"-":l?">":"+"):e.is(eh)&&M&&l?E.go(B(!0)?">":"<"):E.go(E.toDest(a),!0),k(!0),R(t)),y(p,ec,q),y(p,eu,D),c=!1}function I(t){!h&&u&&R(t,!0)}function O(t){a=r,r=t,n=A()}function H(t,e){return F(t,e)-F(W(t),e)}function N(t){return G(t)-G(W(t))}function W(t){return r===t&&a||r}function F(t,e){return(z(t)?t.changedTouches[0]:t)["page"+_(e?"Y":"X")]}function z(t){return"undefined"!=typeof TouchEvent&&t instanceof TouchEvent}function X(){return c}function V(t){h=t}return{mount:function t(){g(C,ec,o,em),g(C,eu,o,em),g(C,ed,T,em),g(C,"click",I,{capture:!0}),g(C,"dragstart",R),v([tc,tm],P)},disable:V,isDragging:X}},Keyboard:function t(e,i,s){var o,r,a=tx(e),l=a.on,d=a.bind,c=a.unbind,u=e.root,h=i.Direction.resolve;function p(){var t=s.keyboard;t&&d(o="global"===t?window:u,eb,g)}function f(){c(o,eb)}function v(t){r=t}function m(){var t=r;r=!0,n(function(){r=t})}function g(t){if(!r){var i=ey(t);i===h(tA)?e.go("<"):i===h(t8)&&e.go(">")}}return{mount:function t(){p(),l(tm,f),l(tm,p),l(th,m)},destroy:f,disable:v}},LazyLoad:function t(i,n,o){var r=tx(i),a=r.on,l=r.off,d=r.bind,c=r.emit,u="sequential"===o.lazyLoad,h=[tp,t$],p=[];function f(){e(p),n.Slides.forEach(function(t){X(t.slide,eE).forEach(function(e){var i=O(e,e$),s=O(e,eS);if(i!==e.src||s!==e.srcset){var n=o.classes.spinner,r=e.parentElement,a=k(r,"."+n)||T("span",n,r);p.push([e,t,a]),e.src||D(e,"none")}})}),u?y():(l(h),a(h,v),v())}function v(){(p=p.filter(function(t){var e=o.perPage*((o.preloadPages||1)+1)-1;return!t[1].isWithin(i.index,e)||m(t)})).length||l(h)}function m(t){var e=t[0];$(t[1].slide,eo),d(e,"load error",s(g,t)),P(e,"src",O(e,e$)),P(e,"srcset",O(e,eS)),M(e,e$),M(e,eS)}function g(t,e){var i=t[0],s=t[1];V(s.slide,eo),"error"!==e.type&&(W(t[2]),D(i,""),c(tk,i,s),c(tg)),u&&y()}function y(){p.length&&m(p.shift())}return{mount:function t(){o.lazyLoad&&(f(),a(tv,f))},destroy:s(e,p),check:v}},Pagination:function t(n,o,r){var a,l,d=tx(n),c=d.on,u=d.emit,h=d.bind,p=o.Slides,f=o.Elements,v=o.Controller,m=v.hasFocus,g=v.getIndex,y=v.go,b=o.Direction.resolve,S=f.pagination,E=[];function L(){C(),c([tm,tv,"ei"],L);var t=r.pagination;S&&D(S,t?"":"none"),t&&(c([th,tb,t$],A),function t(){var e=n.length,i=r.classes,o=r.i18n,d=r.perPage,c=m()?v.getEnd()+1:tt(e/d);a=S||T("ul",i.pagination,f.track.parentElement),$(a,l=t5+"--"+_()),P(a,tT,"tablist"),P(a,tN,o.select),P(a,tR,"ttb"===_()?"vertical":"");for(var u=0;u<c;u++){var g=T("li",null,a),y=T("button",{class:i.page,type:"button"},g),b=p.getIn(u).map(function(t){return t.slide.id}),L=!m()&&d>1?o.pageX:o.slideX;h(y,"click",s(k,u)),r.paginationKeyboard&&h(y,"keydown",s(x,u)),P(g,tT,"presentation"),P(y,tT,"tab"),P(y,tI,b.join(" ")),P(y,tN,tr(L,u+1)),P(y,tq,-1),E.push({li:g,button:y,page:u})}}(),A(),u("pagination:mounted",{list:a,items:E},w(n.index)))}function C(){a&&(W(S?i(a.children):a),V(a,l),e(E),a=null),d.destroy()}function k(t){y(">"+t,!0)}function x(t,e){var i=E.length,s=ey(e),n=_(),o=-1;s===b(t8,!1,n)?o=++t%i:s===b(tA,!1,n)?o=(--t+i)%i:"Home"===s?o=0:"End"===s&&(o=i-1);var r=E[o];r&&(I(r.button),y(">"+o),R(e,!0))}function _(){return r.paginationDirection||r.direction}function w(t){return E[v.toPage(t)]}function A(){var t=w(g(!0)),e=w(g());if(t){var i=t.button;V(i,ee),M(i,tH),P(i,tq,-1)}if(e){var s=e.button;$(s,ee),P(s,tH,!0),P(s,tq,"")}u("pagination:updated",{list:a,items:E},t,e)}return{items:E,mount:L,destroy:C,getAt:w,update:A}},Sync:function t(i,n,o){var r=o.isNavigation,a=o.slideFocus,l=[];function d(){var t,e;i.splides.forEach(function(t){t.isParent||(u(i,t.splide),u(t.splide,i))}),r&&(t=tx(i),e=t.on,e(tf,f),e("sk",v),e([tc,tm],p),l.push(t),t.emit(tE,i.splides))}function c(){l.forEach(function(t){t.destroy()}),e(l)}function u(t,e){var i=tx(t);i.on(th,function(t,i,s){e.go(e.is(ep)?s:t)}),l.push(i)}function p(){P(n.Elements.list,tR,"ttb"===o.direction?"vertical":"")}function f(t){i.go(t.index)}function v(t,e){g(eL,ey(e))&&(f(t),R(e))}return{setup:s(n.Media.set,{slideFocus:h(a)?r:a},!0),mount:d,destroy:c,remount:function t(){c(),d()}}},Wheel:function t(e,i,s){var n=tx(e).bind,o=0;function r(t){if(t.cancelable){var n,r=t.deltaY,a=r<0,l=G(t),d=s.wheelMinThreshold||0,c=s.wheelSleep||0;te(r)>d&&l-o>c&&(e.go(a?"<":">"),o=l),n=a,(!s.releaseWheel||e.state.is(4)||-1!==i.Controller.getAdjacent(n))&&R(t)}}return{mount:function t(){s.wheel&&n(i.Elements.track,"wheel",r,em)}}},Live:function t(e,i,n){var o=tx(e).on,r=i.Elements.track,a=n.live&&!n.isNavigation,l=T("span",et),d=t_(90,s(c,!1));function c(t){P(r,t3,t),t?(S(r,l),d.start()):(W(l),d.cancel())}function u(t){a&&P(r,tz,t?"off":"polite")}return{mount:function t(){a&&(u(!i.Autoplay.isPaused()),P(r,t9,!0),l.textContent="…",o(tL,s(u,!0)),o(tC,s(u,!1)),o([tp,t$],s(c,!0)))},disable:u,destroy:function t(){M(r,[tz,t9,t3]),W(l)}}}}),ek={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:{slide:tG,clone:tU,arrows:t6,arrow:tY,prev:t7,next:tK,pagination:t5,page:tJ,spinner:t0+"spinner"},i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}};function ex(t,e,i){var s=e.Slides;function r(){s.forEach(function(t){t.style("transform","translateX(-"+100*t.index+"%)")})}return{mount:function e(){tx(t).on([tc,tv],r)},start:function t(e,o){s.style("transition","opacity "+i.speed+"ms "+i.easing),n(o)},cancel:o}}function e_(t,e,i){var n,o=e.Move,r=e.Controller,a=e.Scroll,l=e.Elements.list,d=s(q,l,"transition");function c(){d(""),a.cancel()}return{mount:function e(){tx(t).bind(l,"transitionend",function(t){t.target===l&&n&&(c(),n())})},start:function e(s,l){var c=o.toPosition(s,!0),u=o.getPosition(),h=function e(s){var n=i.rewindSpeed;if(t.is(eh)&&n){var o=r.getIndex(!0),a=r.getEnd();if(0===o&&s>=a||o>=a&&0===s)return n}return i.speed}(s);te(c-u)>=1&&h>=1?i.useScroll?a.scroll(c,h,!1,l):(d("transform "+h+"ms "+i.easing),o.translate(c,!0),n=l):(o.jump(s),l())},cancel:c}}var ew=function(){function t(e,i){this.event=tx(),this.Components={},this.state=function t(e){var i=1;function s(t){i=t}return{set:s,is:function t(e){return g(v(e),i)}}}(1),this.splides=[],this._o={},this._E={};var s=u(e)?z(document,e):e;K(s,s+" is invalid."),this.root=s,i=A({label:O(s,tN)||"",labelledby:O(s,tW)||""},ek,t.defaults,i||{});try{A(i,JSON.parse(O(s,Y)))}catch(n){K(!1,"Invalid JSON")}this._o=Object.create(A({},i))}var s=t.prototype;return s.mount=function t(e,i){var s=this,n=this.state,o=this.Components;K(n.is([1,7]),"Already mounted!"),n.set(1),this._C=o,this._T=i||this._T||(this.is(ef)?ex:e_),this._E=e||this._E;var r=w({},eC,this._E,{Transition:this._T});return _(r,function(t,e){var i=t(s,o,s._o);o[e]=i,i.setup&&i.setup()}),_(o,function(t){t.mount&&t.mount()}),this.emit(tc),$(this.root,"is-initialized"),n.set(3),this.emit(tu),this},s.sync=function t(e){return this.splides.push({splide:e}),e.splides.push({splide:this,isParent:!0}),this.state.is(3)&&(this._C.Sync.remount(),e.Components.Sync.remount()),this},s.go=function t(e){return this._C.Controller.go(e),this},s.on=function t(e,i){return this.event.on(e,i),this},s.off=function t(e){return this.event.off(e),this},s.emit=function t(e){var s;return(s=this.event).emit.apply(s,[e].concat(i(arguments,1))),this},s.add=function t(e,i){return this._C.Slides.add(e,i),this},s.remove=function t(e){return this._C.Slides.remove(e),this},s.is=function t(e){return this._o.type===e},s.refresh=function t(){return this.emit(tv),this},s.destroy=function t(i){void 0===i&&(i=!0);var s=this.event,n=this.state;return n.is(1)?tx(this).on(tu,this.destroy.bind(this,i)):(_(this._C,function(t){t.destroy&&t.destroy(i)},!0),s.emit(tS),s.destroy(),i&&e(this.splides),n.set(7)),this},_createClass(t,[{key:"options",get:function t(){return this._o},set:function t(e){this._C.Media.set(e,!0,!0)}},{key:"length",get:function t(){return this._C.Slides.getLength(!0)}},{key:"index",get:function t(){return this._C.Controller.getIndex()}}]),t}();return ew.defaults={},ew.STATES={CREATED:1,MOUNTED:2,IDLE:3,MOVING:4,SCROLLING:5,DRAGGING:6,DESTROYED:7},ew});class SplideComponent extends HTMLElement{constructor(){super(),this.sliderContainer=this.querySelector(".splide"),document.addEventListener("shopify:section:load",t=>{this.initSlider()})}connectedCallback(){this.initSlider()}initSlider(){this.type=this.dataset.type||"slide",this.typeMobile=this.dataset.typeMobile||this.type,this.direction=this.dataset.direction||"ltr",this.rewind="fade"===this.type,this.autoplay="true"===this.dataset.autoplay,this.autoplaySpeed=this.dataset.autoplaySpeed&&!isNaN(parseInt(this.dataset.autoplaySpeed))?1e3*parseInt(this.dataset.autoplaySpeed):5e3,this.drag="false"!==this.dataset.drag&&("free"!==this.dataset.drag||"free"),this.focus=this.dataset.focus||0,this.trimSpace="false"!==this.dataset.trimSpace,this.arrows="false"!==this.dataset.arrows,this.arrowsColor=this.dataset.arrowsColor?` color-${this.dataset.arrowsColor}`:"",this.pagination="false"!==this.dataset.pagination,this.omitEnd="false"!==this.dataset.omitEnd,this.dotsColor=this.dataset.dotsColor?` color-${this.dataset.dotsColor} dots-custom-color`:"",this.slidesDesktop=this.dataset.slidesDesktop&&!isNaN(parseInt(this.dataset.slidesDesktop))?parseInt(this.dataset.slidesDesktop):3,this.autoWidth="true"===this.dataset.autoWidth,this.slidesMobile=this.dataset.slidesMobile&&!isNaN(parseInt(this.dataset.slidesMobile))?parseInt(this.dataset.slidesMobile):1,this.perMoveDesktop=this.dataset.perMoveDesktop&&!isNaN(parseInt(this.dataset.perMoveDesktop))?parseInt(this.dataset.perMoveDesktop):1,this.perMoveMobile=this.dataset.perMoveMobile&&!isNaN(parseInt(this.dataset.perMoveMobile))?parseInt(this.dataset.perMoveMobile):1,this.gapDesktop=this.dataset.gapDesktop?parseInt(this.dataset.gapDesktop):30,this.gapMobile=this.dataset.gapMobile?parseInt(this.dataset.gapMobile):15,this.paddingCalcDesktop="true"===this.dataset.paddingCalcDesktop,this.rightPaddingDesktop=parseInt(this.dataset.sidePaddingDesktop)||0,this.leftPaddingDesktop=this.dataset.paddingCalcDesktop?0:this.rightPaddingDesktop,this.paddingCalcMobile="true"===this.dataset.paddingCalcMobile,this.rightPaddingMobile=parseInt(this.dataset.sidePaddingMobile)||0,this.leftPaddingMobile=this.paddingCalcMobile?0:this.rightPaddingMobile,this.destroyDesktop="true"===this.dataset.destroyDesktop,this.destroyMobile="true"===this.dataset.destroyMobile,this.config={type:this.type,direction:this.direction,rewind:this.rewind,autoplay:this.autoplay,interval:this.autoplaySpeed,drag:this.drag,focus:this.focus,trimSpace:this.trimSpace,arrows:this.arrows,pagination:this.pagination,omitEnd:this.omitEnd,perPage:this.slidesDesktop,perMove:this.perMoveDesktop,autoWidth:this.autoWidth,gap:this.gapDesktop,paddingCalc:this.paddingCalcDesktop,padding:{left:this.leftPaddingDesktop,right:this.rightPaddingDesktop},destroy:this.destroyDesktop,classes:{arrow:`splide__arrow${this.arrowsColor}`,page:`splide__pagination__page${this.dotsColor}`},easing:"cubic-bezier(0.25, 1, 0.5, 1)",breakpoints:{749:{omitEnd:this.omitEnd,type:this.typeMobile,perPage:this.slidesMobile,perMove:this.perMoveMobile,gap:this.gapMobile,paddingCalc:this.paddingCalcMobile,padding:{left:this.leftPaddingMobile,right:this.rightPaddingMobile},destroy:this.destroyMobile}}};let t=new Splide(this.sliderContainer,this.config);t.on("mounted",()=>{let e=t.index,i=t.Components.Elements.slides[e];if(i){let s=i.querySelector("img");s?s.complete?(i.classList.add("is-instant-active"),this.setActiveSlideHeight(i)):s.addEventListener("load",()=>{i.classList.add("is-instant-active"),this.setActiveSlideHeight(i)}):(i.classList.add("is-instant-active"),this.setActiveSlideHeight(i))}let n=debounce(()=>{let e=t.Components.Elements.slides[t.index];this.setActiveSlideHeight(e)},200);window.addEventListener("resize",n)}),t.on("move",e=>{let i=t.index,s=t.Components.Elements.slides;if(s){let n=s[i];n&&(s.forEach(t=>t.classList.remove("is-instant-active")),n.classList.add("is-instant-active"),this.setActiveSlideHeight(n))}}),"true"===this.dataset.pauseVideos&&t.on("hidden",t=>{let e=t.slide.querySelector("internal-video");if(e){let i=e.dataset.actionOnInactive;"pause"===i?(e.video.pause(),e.classList.remove("internal-video--playing")):"mute"===i&&(e.video.muted=!0,e.classList.add("internal-video--muted"))}}),t.mount()}setActiveSlideHeight(t){if(t){let e=t.offsetHeight;this.style.setProperty("--active-slide-height",`${e}px`)}}}customElements.define("splide-component",SplideComponent);class DetailsDisclosure extends HTMLElement{constructor(){super(),this.mainDetailsToggle=this.querySelector("details"),this.content=this.mainDetailsToggle.querySelector("summary").nextElementSibling,this.mainDetailsToggle.addEventListener("focusout",this.onFocusOut.bind(this)),this.mainDetailsToggle.addEventListener("toggle",this.onToggle.bind(this))}onFocusOut(){setTimeout(()=>{this.contains(document.activeElement)||this.close()})}onToggle(){this.animations||(this.animations=this.content.getAnimations()),this.mainDetailsToggle.hasAttribute("open")?this.animations.forEach(t=>t.play()):this.animations.forEach(t=>t.cancel())}close(){this.mainDetailsToggle.removeAttribute("open"),this.mainDetailsToggle.querySelector("summary").setAttribute("aria-expanded",!1)}}customElements.define("details-disclosure",DetailsDisclosure);class HeaderMenu extends DetailsDisclosure{constructor(){super(),this.header=document.querySelector(".header-wrapper")}onToggle(){this.header&&(this.header.preventHide=this.mainDetailsToggle.open,""===document.documentElement.style.getPropertyValue("--header-bottom-position-desktop")&&document.documentElement.style.setProperty("--header-bottom-position-desktop",`${Math.floor(this.header.getBoundingClientRect().bottom)}px`))}}customElements.define("header-menu",HeaderMenu);class DetailsModal extends HTMLElement{constructor(){super(),this.detailsContainer=this.querySelector("details"),this.summaryToggle=this.querySelector("summary"),this.detailsContainer.addEventListener("keyup",t=>"ESCAPE"===t.code.toUpperCase()&&this.close()),this.summaryToggle.addEventListener("click",this.onSummaryClick.bind(this)),this.querySelector('button[type="button"]').addEventListener("click",this.close.bind(this)),this.summaryToggle.setAttribute("role","button")}isOpen(){return this.detailsContainer.hasAttribute("open")}onSummaryClick(t){t.preventDefault(),t.target.closest("details").hasAttribute("open")?this.close():this.open(t)}onBodyClick(t){(!this.contains(t.target)||t.target.classList.contains("modal-overlay"))&&this.close(!1)}open(t){this.onBodyClickEvent=this.onBodyClickEvent||this.onBodyClick.bind(this),t.target.closest("details").setAttribute("open",!0),document.body.addEventListener("click",this.onBodyClickEvent),document.body.classList.add("overflow-hidden"),trapFocus(this.detailsContainer.querySelector('[tabindex="-1"]'),this.detailsContainer.querySelector('input:not([type="hidden"])'))}close(t=!0){removeTrapFocus(t?this.summaryToggle:null),this.detailsContainer.removeAttribute("open"),document.body.removeEventListener("click",this.onBodyClickEvent),document.body.classList.remove("overflow-hidden")}}customElements.define("details-modal",DetailsModal);let hotspotButtons=[];function registerHotspotButton(t){hotspotButtons.push(t)}function unregisterHotspotButton(t){let e=hotspotButtons.indexOf(t);-1!==e&&hotspotButtons.splice(e,1)}document.addEventListener("mousedown",function(t){for(let e of hotspotButtons)"true"!==e.dataset.open||e.contains(t.target)||e.closeModal()});class HotspotButton extends HTMLElement{constructor(){super(),this.button=this.querySelector(".hotspot-btn"),this.content=this.querySelector(".hotspot__content"),this.type=this.dataset.type,this.openEvent=this.dataset.openEvent,this.header=document.querySelector(".section-header"),this.stickyHeader=document.querySelector("sticky-header"),this.mobileOverlay=this.querySelector(".hotspot-overlay"),this.button.addEventListener("click",this.toggleModal.bind(this)),"hover"===this.openEvent&&window.matchMedia("(hover: hover)").matches&&window.matchMedia("(pointer: fine)").matches&&(this.button.addEventListener("mouseover",this.openModal.bind(this)),this.button.addEventListener("mouseout",this.startCheckingMouseLeave.bind(this)),this.content.addEventListener("mouseover",this.stopCheckingMouseLeave.bind(this)),this.content.addEventListener("mouseout",this.startCheckingMouseLeave.bind(this))),this.mobileOverlay&&this.mobileOverlay.addEventListener("click",this.closeModal.bind(this))}startCheckingMouseLeave(){this.checkingMouseLeave=!0,setTimeout(()=>{this.checkingMouseLeave&&this.closeModal()},200)}stopCheckingMouseLeave(){this.checkingMouseLeave=!1}toggleModal(){"true"!=this.dataset.open&&this.openModal()}openModal(){let t=this.header.clientHeight;(this.header.classList.contains("shopify-section-header-hidden")||!this.stickyHeader)&&(t=0);this.content.getBoundingClientRect().top-t<0&&(this.dataset.direction="bottom"),this.dataset.open="true"}closeModal(){this.dataset.open="false",setTimeout(()=>{this.dataset.direction=""},100)}connectedCallback(){registerHotspotButton(this)}disconnectedCallback(){unregisterHotspotButton(this)}}customElements.define("hotspot-button",HotspotButton);class ParallaxHero extends HTMLElement{constructor(){super(),this.overlays=this.querySelectorAll(".parallax-hero__layer"),this.animateOnEnter="bottom"===this.dataset.animationStart,this.handleScroll(),window.addEventListener("scroll",()=>requestAnimationFrame(this.handleScroll.bind(this)))}handleScroll(t){var{top:e,left:i,height:s}=this.getBoundingClientRect();let n=window.innerHeight,o;if(this.animateOnEnter){if(e>n||e+s<0)return;o=Math.min((n-e)/(n+s),1)}else{if(e>n||e+s<0)return;let r=e>=0?0:-e;if(r>s)return;o=Math.min(r/s,1)}this.overlays.forEach(t=>{let i=e*(parseInt(t.dataset.scrollY)/100),s=parseInt(t.dataset.scrollX)*o,n=100+(parseInt(t.dataset.zoom)-100)*o,r=3.6*parseInt(t.dataset.rotation)*o;t.style.transform=`translateY(${i}px) translateX(${s}%) scale(${n/100}) rotate(${r}deg)`})}}customElements.define("parallax-hero",ParallaxHero);class ContentTabs extends HTMLElement{constructor(){super(),this.buttons=this.querySelectorAll(".content-tab-button"),this.tabs=this.querySelectorAll(".content-tab"),this.tabsContainer=this.querySelector(".content-tabs__tabs"),this.activeButton=this.querySelector(".content-tab-button--active"),this.activeTab=this.querySelector(".content-tab--active"),this.isMovingAnimation="moving"===this.dataset.animation,this.isMovingAnimation&&(this.activeBg=this.querySelector(".content-tab-buttom__active-bg"),this.handleActiveBg(),this.activeBg.style.transitionDuration="0.4s"),this.setHeight(),this.buttons.forEach(t=>{t.addEventListener("click",this.handleClick.bind(this))}),window.addEventListener("resize",()=>{this.setHeight(),this.isMovingAnimation&&this.handleActiveBg()})}handleClick(t){this.activeButton=t.currentTarget,this.buttons.forEach(t=>{t.classList.remove("content-tab-button--active")}),this.activeButton.classList.add("content-tab-button--active"),this.tabs.forEach(t=>{t.classList.remove("content-tab--active"),t.dataset.index===this.activeButton.dataset.index&&(this.activeTab=t)}),this.activeTab.classList.add("content-tab--active"),this.setHeight(),this.isMovingAnimation&&this.handleActiveBg()}setHeight(){this.activeTab&&(this.tabsContainer.style.height=this.activeTab.clientHeight+"px")}handleActiveBg(){this.activeBg.style.width=this.activeButton.getBoundingClientRect().width+"px",this.activeBg.style.height=this.activeButton.getBoundingClientRect().height+"px",this.activeBg.style.top=this.activeButton.offsetTop+"px",this.activeBg.style.left=this.activeButton.offsetLeft+"px"}}customElements.define("content-tabs",ContentTabs);class InstaStories extends HTMLElement{constructor(){super(),this.openButtons=this.querySelectorAll(".insta-story-open-btn"),this.openButtonsOverflowContainer=this.querySelector(".insta-stories__open-buttons-container"),this.openButtonsContainer=this.querySelector(".insta-stories__open-buttons"),this.openBtnsPrev=this.querySelector(".insta-stories__open-btns-prev"),this.openBtnsNext=this.querySelector(".insta-stories__open-btns-next"),this.closeButtons=this.querySelectorAll(".insta-stories__close-button"),this.modal=this.querySelector(".insta-stories__modal"),this.modalOpen=!1,this.slider=this.querySelector(".insta-stories__slider"),this.stories=this.querySelectorAll(".insta-story"),this.prevBtns=this.querySelectorAll(".insta-story__prev"),this.nextBtns=this.querySelectorAll(".insta-story__next"),this.slideBtns=this.querySelectorAll(".insta-story__slide-btn"),this.activeIndex=0,this.activeStory=this.stories[this.activeIndex],this.lastIndex=parseInt(this.dataset.lastIndex),this.pauseResumeBtns=this.querySelectorAll(".insta-story__pause-resume-btn"),this.isPaused=!1,this.volumeBtns=this.querySelectorAll(".insta-story__volume-btn"),this.isMuted=!0,this.initStories(),this.initButtonsSlider(),this.openButtons.forEach(t=>{t.addEventListener("click",this.openModal.bind(this))}),this.closeButtons.forEach(t=>{t.addEventListener("click",this.closeModal.bind(this))}),this.prevBtns.forEach(t=>{t.addEventListener("click",this.storyPrevBtnClick.bind(this))}),this.nextBtns.forEach(t=>{t.addEventListener("click",this.storyNextBtnClick.bind(this))}),this.slideBtns.forEach(t=>{t.addEventListener("click",this.slideBtnClick.bind(this))}),this.pauseResumeBtns.forEach(t=>{t.addEventListener("click",this.togglePauseResume.bind(this))}),this.volumeBtns.forEach(t=>{t.addEventListener("click",this.toggleIsMuted.bind(this))}),this.slider.addEventListener("touchstart",this.touchStartHandler.bind(this)),this.slider.addEventListener("touchend",this.touchEndHandler.bind(this)),document.addEventListener("keydown",t=>{if(this.modalOpen)switch(t.key){case"Escape":this.closeModal();break;case"ArrowLeft":this.storyPrevBtnClick();break;case"ArrowRight":this.storyNextBtnClick()}}),Shopify.designMode&&(document.addEventListener("shopify:section:load",()=>{this.initStories(),this.initButtonsSlider()}),document.addEventListener("shopify:section:reorder",()=>{this.initStories(),this.initButtonsSlider()}))}autoplay(){let t=this.activeStory,e=parseInt(t.dataset.activeMediaIndex),i=t.querySelectorAll(".insta-story__media")[e],s=1e3*parseInt(i.getAttribute("data-duration"));this.updateProgressBars(e),this.autoplayStartTime=Date.now(),this.autoplayTimeout=setTimeout(()=>{this.storyNextBtnClick()},s)}storyPrevBtnClick(){let t=this.activeStory;"0"===t.dataset.activeMediaIndex?this.changeActiveStory(this.activeIndex-1):this.changeActiveMedia(parseInt(t.dataset.activeMediaIndex)-1)}storyNextBtnClick(){let t=this.activeStory;t.dataset.activeMediaIndex===t.dataset.lastMediaIndex?this.changeActiveStory(this.activeIndex+1):this.changeActiveMedia(parseInt(t.dataset.activeMediaIndex)+1)}slideBtnClick(t){let e=parseInt(t.currentTarget.dataset.index);this.changeActiveStory(e)}changeActiveStory(t){if(clearTimeout(this.autoplayTimeout),t>this.lastIndex||t<0)return;let e=this.stories[this.activeIndex],i=parseInt(e.dataset.activeMediaIndex);if("true"===e.dataset.played||i>0){let s=e.querySelectorAll(".insta-story__progress-item")[i];s&&(s.classList.remove("insta-story__progress-item--active"),t>this.activeIndex&&s.classList.add("insta-story__progress-item--completed"))}let n=e.querySelectorAll(".insta-story__media")[i];if(n&&"video"===n.getAttribute("data-type")){let o=n.querySelector("video");o&&(o.pause(),this.isPaused||(o.currentTime=0))}this.activeIndex=t,this.activeStory=this.stories[this.activeIndex],this.stories.forEach(t=>{t.classList.remove("insta-story--active")}),this.activeStory.classList.add("insta-story--active"),this.activeStory.dataset.played="true",this.slider.style.transform=`translateX(calc(var(--story-width) * ${-1*this.activeIndex}))`;let r=parseInt(this.activeStory.dataset.activeMediaIndex,10);this.changeActiveMedia(r),this.updateProgressBars(r)}changeActiveMedia(t){if(clearTimeout(this.autoplayTimeout),t<0)return;let e=this.activeStory,i=parseInt(e.dataset.lastMediaIndex);if(t>i)return;e.dataset.activeMediaIndex=t;let s=e.querySelectorAll(".insta-story__media");for(let n=0;n<s.length;n++)if(n===t){if(s[n].style.display="block","video"===s[n].getAttribute("data-type")){let o=s[n].querySelector("video");o&&!this.isPaused&&o.play()}e.querySelector(".insta-story__time-posted").innerHTML=s[n].dataset.timePosted}else if(s[n].style.display="","video"===s[n].getAttribute("data-type")){let r=s[n].querySelector("video");r&&(r.pause(),this.isPaused||(r.currentTime=0))}this.updateProgressBars(t),this.isPaused||this.autoplay();let a=e.querySelectorAll(".insta-story__prev"),l=e.querySelectorAll(".insta-story__next");a.forEach(e=>{0===this.activeIndex&&0===t?e.setAttribute("disabled",""):e.removeAttribute("disabled")}),l.forEach(e=>{this.activeIndex===this.lastIndex&&t===i?e.setAttribute("disabled",""):e.removeAttribute("disabled")})}updateProgressBars(t){let e=this.activeStory,i=e.querySelectorAll(".insta-story__progress-item");i.forEach((e,i)=>{e.classList.remove("insta-story__progress-item--completed","insta-story__progress-item--active"),i<t?e.classList.add("insta-story__progress-item--completed"):i===t&&e.classList.add("insta-story__progress-item--active")})}openModal(t){window.scrollBy(0,-1),this.modal.dataset.open="true",this.modalOpen=!0,document.body.classList.add("overflow-hidden"),this.changeActiveStory(parseInt(t.currentTarget.dataset.index))}closeModal(t){this.querySelectorAll("video").forEach(t=>{t.pause()}),this.modal.dataset.open="false",this.modalOpen=!1,document.body.classList.remove("overflow-hidden"),clearTimeout(this.autoplayTimeout)}initButtonsSlider(){if(this.openButtonsOverflowContainer.clientWidth>this.openButtonsContainer.clientWidth)return;let t=()=>"ontouchstart"in window||navigator.maxTouchPoints,e,i=0,s=0,n=t=>{this.openButtonsContainer.style.transform=`translateX(${t}px)`,i=t};this.openButtonsOverflowContainer.addEventListener("touchstart",t=>{this.isDragging=!0,e=t.touches[0].clientX-s}),this.openButtonsOverflowContainer.addEventListener("touchmove",t=>{if(!this.isDragging)return;let i=t.touches[0].clientX,s=i-e;s<0&&Math.abs(s)<=this.openButtonsContainer.offsetWidth-this.openButtonsOverflowContainer.offsetWidth&&n(s)}),this.openButtonsOverflowContainer.addEventListener("touchend",()=>{this.isDragging=!1,s=i});let o=parseFloat(getComputedStyle(this.openButtons[0]).width),r=parseFloat(getComputedStyle(this.openButtonsContainer).columnGap),a=()=>{let t=Math.floor(this.openButtonsOverflowContainer.clientWidth/(o+r));return(t-1)*(o+r)},l=()=>{this.openBtnsPrev.style.display=this.openButtonsOverflowContainer.scrollLeft<=0?"none":"grid",this.openBtnsNext.style.display=this.openButtonsOverflowContainer.scrollLeft>this.openButtonsOverflowContainer.scrollWidth-this.openButtonsContainer.clientWidth?"none":"grid"};this.openBtnsPrev.addEventListener("click",()=>{this.openButtonsOverflowContainer.scrollBy({left:-a(),behavior:"smooth"}),setTimeout(l,300)}),this.openBtnsNext.addEventListener("click",()=>{this.openButtonsOverflowContainer.scrollBy({left:a(),behavior:"smooth"}),setTimeout(l,300)}),l(),t()&&(this.openBtnsPrev.style.display="none",this.openBtnsNext.style.display="none"),window.addEventListener("resize",debounce(()=>{this.openButtonsOverflowContainer.clientWidth<=this.openButtonsContainer.clientWidth?t()?(this.openBtnsPrev.style.display="none",this.openBtnsNext.style.display="none"):l():(this.openBtnsPrev.style.display="none",this.openBtnsNext.style.display="none")},250))}initStories(){this.stories.forEach(t=>{let e=t.querySelectorAll(".insta-story__media");t.dataset.lastMediaIndex=e.length-1,t.dataset.played="false";let i=t.querySelector(".insta-story__progress");for(let s=0;s<e.length;s++){let n=e[s].getAttribute("data-duration"),o=document.createElement("span");o.className="insta-story__progress-item",o.style.setProperty("--duration",`${n}s`);let r=document.createElement("span");r.className="insta-story__progress-bar",o.appendChild(r),i.appendChild(o)}let a=parseInt(t.dataset.activeMediaIndex);e[a]&&(e[a].style.display="block")})}togglePauseResume(t){let e="true"===t.currentTarget.getAttribute("data-paused");e?this.resumeStory():this.pauseStory(),this.pauseResumeBtns.forEach(t=>{t.setAttribute("data-paused",e?"false":"true")})}pauseStory(){this.isPaused=!0;let t=this.activeStory.querySelectorAll(".insta-story__media")[parseInt(this.activeStory.dataset.activeMediaIndex)],e=1e3*parseInt(t.getAttribute("data-duration"));if(this.remainingTime=e-(Date.now()-this.autoplayStartTime),clearTimeout(this.autoplayTimeout),"video"===t.getAttribute("data-type")){let i=t.querySelector("video");i&&i.pause()}let s=this.querySelectorAll(".insta-story__progress-bar");s.forEach(t=>{t.style.animationPlayState="paused"})}resumeStory(){this.isPaused=!1;let t=this.querySelectorAll(".insta-story__progress-bar");t.forEach(t=>{t.style.animationPlayState="running"});let e=this.activeStory.querySelectorAll(".insta-story__media")[parseInt(this.activeStory.dataset.activeMediaIndex)];if("video"===e.getAttribute("data-type")){let i=e.querySelector("video");i&&i.play()}this.autoplayStartTime=Date.now(),this.autoplayTimeout=setTimeout(()=>{this.storyNextBtnClick()},this.remainingTime)}toggleIsMuted(t){t.currentTarget,this.isMuted=!this.isMuted;let e=this.querySelectorAll("video");e.forEach(t=>{t.muted=this.isMuted}),this.volumeBtns.forEach(t=>{this.isMuted?t.setAttribute("data-muted","true"):t.setAttribute("data-muted","false")})}touchStartHandler(t){this.touchStartX=t.touches[0].clientX}touchEndHandler(t){let e=t.changedTouches[0].clientX,i=e-this.touchStartX;!(50>Math.abs(i))&&(i<0?this.activeIndex<this.lastIndex&&this.changeActiveStory(this.activeIndex+1):this.activeIndex>0&&this.changeActiveStory(this.activeIndex-1))}}customElements.define("insta-stories",InstaStories);class TncCheckbox extends HTMLElement{constructor(){if(super(),this.checked=!1,this.checkoutButton=document.querySelector("#CartDrawer-Checkout"),this.disableButton="true"===this.dataset.disableButton,this.warningText=this.dataset.warningText,this.warningTextElement=document.querySelector(`.tnc-checkbox-warning--${this.dataset.warningPosition}`),!this.checkoutButton||!this.warningTextElement)return;this.warningTextElement&&(this.warningTextElement.innerHTML=this.warningText),this.disableButton&&this.checkoutButton.classList.add("disabled"),this.addEventListener("click",this.handleCheckboxClick.bind(this)),this.checkoutButton.addEventListener("click",this.handleButtonClick.bind(this))}handleCheckboxClick(t){this.checked=!this.checked,this.dataset.checked=this.checked,this.checked?(this.warningTextElement.classList.add("hidden"),this.disableButton&&this.checkoutButton.classList.remove("disabled")):this.disableButton&&this.checkoutButton.classList.add("disabled")}handleButtonClick(t){!0!==this.checked&&(t.preventDefault(),this.warningTextElement.classList.remove("hidden"))}}customElements.define("tnc-chekcbox",TncCheckbox);class ContactForm extends HTMLElement{constructor(){super(),this.handleFormSubmit=this.handleFormSubmit.bind(this),this.handleFieldInput=this.handleFieldInput.bind(this)}connectedCallback(){this.form=this.querySelector("form"),this.form&&(this.requiredFields=Array.from(this.querySelectorAll('.field-wrapper[data-required="true"]')),this.requiredFields.length>0&&(this.form.addEventListener("submit",this.handleFormSubmit),this.requiredFields.forEach(t=>{let e=t.querySelector(".field__input, .text-area");e&&e.addEventListener("input",this.handleFieldInput)})))}disconnectedCallback(){this.form&&this.form.removeEventListener("submit",this.handleFormSubmit),this.requiredFields.forEach(t=>{let e=t.querySelector(".field__input, .text-area");e&&e.removeEventListener("input",this.handleFieldInput)})}handleFormSubmit(t){let e=!0;this.requiredFields.forEach(t=>{let i=t.querySelector(".field__input, .text-area"),s=t.querySelector(".field-wrapper__error-msg");i&&!i.value.trim()&&(e=!1,t.classList.add("field-wrapper--error"),s&&s.classList.remove("hidden"))}),e||t.preventDefault()}handleFieldInput(t){let e=t.target,i=e.closest(".field-wrapper"),s=i.querySelector(".field-wrapper__error-msg");i.classList.remove("field-wrapper--error"),s&&s.classList.add("hidden")}}customElements.define("contact-form",ContactForm);