/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Färger",
      "settings": {
        "colors_solid_button_labels": {
          "label": "Tydlig knappetikett",
          "info": "Används som förgrundsfärg på accentfärger."
        },
        "colors_accent_1": {
          "label": "Accent 1",
          "info": "Används för tydlig knappbakgrund."
        },
        "colors_accent_2": {
          "label": "Accent 2"
        },
        "header__1": {
          "content": "Primärfärger"
        },
        "header__2": {
          "content": "Sekundära färger"
        },
        "colors_text": {
          "label": "Text",
          "info": "Används som förgrundsfärg på bakgrundsfärger."
        },
        "colors_outline_button_labels": {
          "label": "Knapp-kontur",
          "info": "Används också för textlänkar."
        },
        "colors_background_1": {
          "label": "Bakgrund 1"
        },
        "colors_background_2": {
          "label": "Bakgrund 2"
        },
        "gradient_accent_1": {
          "label": "Tonad accent 1"
        },
        "gradient_accent_2": {
          "label": "Tonad accent 2"
        },
        "gradient_background_1": {
          "label": "Tonad bakgrund 1"
        },
        "gradient_background_2": {
          "label": "Tonad bakgrund 2"
        }
      }
    },
    "typography": {
      "name": "Typografi",
      "settings": {
        "type_header_font": {
          "label": "Typsnitt",
          "info": "Om du väljer ett annat typsnitt kan det påverka butikshastigheten. [Lär dig mer om typsnitt i system.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "header__1": {
          "content": "Rubriker"
        },
        "header__2": {
          "content": "Brödtext"
        },
        "type_body_font": {
          "label": "Typsnitt",
          "info": "Om du väljer ett annat typsnitt kan det påverka butikshastigheten. [Lär dig mer om typsnitt i system.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "heading_scale": {
          "label": "Skala för teckenstorlek"
        },
        "body_scale": {
          "label": "Skala för teckenstorlek"
        }
      }
    },
    "styles": {
      "name": "Ikoner",
      "settings": {
        "accent_icons": {
          "options__3": {
            "label": "Knappkontur"
          },
          "options__4": {
            "label": "Text"
          },
          "label": "Färg"
        }
      }
    },
    "social-media": {
      "name": "Sociala medier",
      "settings": {
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https://twitter.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "https://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "http://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/user/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Sociala konton"
        }
      }
    },
    "currency_format": {
      "name": "Valutaformat",
      "settings": {
        "content": "Valutakoder",
        "currency_code_enabled": {
          "label": "Visa valutakoder"
        },
        "paragraph": "Priser i varukorgen och kassan visar alltid valutakoder. T.ex. 1,00 USD."
      }
    },
    "layout": {
      "name": "Layout",
      "settings": {
        "page_width": {
          "label": "Sidbredd"
        },
        "spacing_sections": {
          "label": "Mellanrum mellan mallavsnitt"
        },
        "header__grid": {
          "content": "Rutnät"
        },
        "paragraph__grid": {
          "content": "Påverkar områden med flera kolumner eller rader."
        },
        "spacing_grid_horizontal": {
          "label": "Horisontellt utrymme"
        },
        "spacing_grid_vertical": {
          "label": "Vertikalt utrymme"
        }
      }
    },
    "search_input": {
      "name": "Sökbeteende",
      "settings": {
        "header": {
          "content": "Sökförslag"
        },
        "predictive_search_enabled": {
          "label": "Aktivera sökförslag"
        },
        "predictive_search_show_vendor": {
          "label": "Visa produktsäljare",
          "info": "Synlig när sökförslag är aktiverat."
        },
        "predictive_search_show_price": {
          "label": "Visa produktpris",
          "info": "Synlig när sökförslag är aktiverat."
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Kant"
        },
        "header__shadow": {
          "content": "Skugga"
        },
        "blur": {
          "label": "Suddighet"
        },
        "corner_radius": {
          "label": "Hörnradie"
        },
        "horizontal_offset": {
          "label": "Horisontell kompensation"
        },
        "vertical_offset": {
          "label": "Vertikal kompensation"
        },
        "thickness": {
          "label": "Tjocklek"
        },
        "opacity": {
          "label": "Opacitet"
        },
        "image_padding": {
          "label": "Bild-padding"
        },
        "text_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Textjustering"
        }
      }
    },
    "badges": {
      "name": "Brickor",
      "settings": {
        "position": {
          "options__1": {
            "label": "Nere till vänster"
          },
          "options__2": {
            "label": "Nere till höger"
          },
          "options__3": {
            "label": "Överst till vänster"
          },
          "options__4": {
            "label": "Överst till höger"
          },
          "label": "Position på kort"
        },
        "sale_badge_color_scheme": {
          "label": "Färgschema för försäljningsbricka"
        },
        "sold_out_badge_color_scheme": {
          "label": "Utsålt färgschema för bricka"
        }
      }
    },
    "buttons": {
      "name": "Knappar"
    },
    "variant_pills": {
      "name": "Variantkapslar",
      "paragraph": "Variantpiller är ett sätt att visa produktvarianter på. [Mer information](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"
    },
    "inputs": {
      "name": "Inmatningar"
    },
    "content_containers": {
      "name": "Innehållsbehållare"
    },
    "popups": {
      "name": "Rullgardinsmenyer och popup-rutor",
      "paragraph": "Påverkar områden såsom rullgardinsmenyer för navigering, popup-modalfönster och popup-rutor för varukorg."
    },
    "media": {
      "name": "Media"
    },
    "drawers": {
      "name": "Lådor"
    },
    "cart": {
      "name": "Varukorg",
      "settings": {
        "cart_type": {
          "label": "Varukorgstyp",
          "drawer": {
            "label": "Låda"
          },
          "page": {
            "label": "Sida"
          },
          "notification": {
            "label": "Popup-avisering"
          }
        },
        "show_vendor": {
          "label": "Visa säljare"
        },
        "show_cart_note": {
          "label": "Aktivera varukorgsanteckning"
        },
        "cart_drawer": {
          "header": "Varukorgspanel",
          "collection": {
            "label": "Produktserie",
            "info": "Synlig när varukorgspanelen är tom."
          }
        }
      }
    },
    "cards": {
      "name": "Produktkort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "collection_cards": {
      "name": "Kategorikort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "blog_cards": {
      "name": "Bloggkort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "logo": {
      "name": "Logotyp",
      "settings": {
        "logo_image": {
          "label": "Logotyp"
        },
        "logo_width": {
          "label": "Logotypbredd för dator",
          "info": "Logotypbredd optimeras automatiskt för mobilen."
        },
        "favicon": {
          "label": "Favicon-bild",
          "info": "Kommer skalas ned till 32 x 32 px"
        }
      }
    },
    "brand_information": {
      "name": "Varumärkesinformation",
      "settings": {
        "brand_headline": {
          "label": "Rubrik"
        },
        "brand_description": {
          "label": "Beskrivning"
        },
        "brand_image": {
          "label": "Bild"
        },
        "brand_image_width": {
          "label": "Bildbredd"
        },
        "paragraph": {
          "content": "Lägg till en varumärkesbeskrivning i din butiks sidfot."
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Padding för avsnitt",
        "padding_top": "Övre padding",
        "padding_bottom": "Nedre padding"
      },
      "spacing": "Radavstånd",
      "colors": {
        "accent_1": {
          "label": "Accent 1"
        },
        "accent_2": {
          "label": "Accent 2"
        },
        "background_1": {
          "label": "Bakgrund 1"
        },
        "background_2": {
          "label": "Bakgrund 2"
        },
        "inverse": {
          "label": "Omvänd"
        },
        "label": "Färgschema",
        "has_cards_info": "Uppdatera dina temainställningar för att ändra kortets färgsättning."
      },
      "heading_size": {
        "label": "Rubrikstorlek",
        "options__1": {
          "label": "Liten"
        },
        "options__2": {
          "label": "Medel"
        },
        "options__3": {
          "label": "Stor"
        },
        "options__4": {
          "label": "Extra stor"
        }
      }
    },
    "announcement-bar": {
      "name": "Meddelandefält",
      "blocks": {
        "announcement": {
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_alignment": {
              "label": "Textjustering",
              "options__1": {
                "label": "Vänster"
              },
              "options__2": {
                "label": "Centrera"
              },
              "options__3": {
                "label": "Höger"
              }
            },
            "link": {
              "label": "Länk"
            }
          },
          "name": "Meddelande"
        }
      }
    },
    "collage": {
      "name": "Kollage",
      "settings": {
        "heading": {
          "label": "Rubrik"
        },
        "desktop_layout": {
          "label": "Layout för dator",
          "options__1": {
            "label": "Stort block till vänster"
          },
          "options__2": {
            "label": "Stort block till höger"
          }
        },
        "mobile_layout": {
          "label": "Mobil layout",
          "options__1": {
            "label": "Kollage"
          },
          "options__2": {
            "label": "Kolumn"
          }
        },
        "card_styles": {
          "label": "Kortstil",
          "info": "Du kan uppdatera kortstilar för produkter, kategorier och bloggen i temainställningarna.",
          "options__1": {
            "label": "Använd individuella kortstilar"
          },
          "options__2": {
            "label": "Styla alla som produktkort"
          }
        }
      },
      "blocks": {
        "image": {
          "settings": {
            "image": {
              "label": "Bild"
            }
          },
          "name": "Bild"
        },
        "product": {
          "settings": {
            "product": {
              "label": "Produkt"
            },
            "secondary_background": {
              "label": "Visa sekundär bakgrund"
            },
            "second_image": {
              "label": "Visa andra bild på hovring"
            }
          },
          "name": "Produkt"
        },
        "collection": {
          "settings": {
            "collection": {
              "label": "Produktserie"
            }
          },
          "name": "Produktserie"
        },
        "video": {
          "settings": {
            "cover_image": {
              "label": "Omslagsbild"
            },
            "video_url": {
              "label": "URL",
              "info": "Video spelas i en pop-up om avsnittet innehåller andra block.",
              "placeholder": "Använd en YouTube- eller en Vimeo-URL"
            },
            "description": {
              "label": "Alternativtext för video",
              "info": "Beskriv videon för kunder som använder skärmläsare. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"
            }
          },
          "name": "Video"
        }
      },
      "presets": {
        "name": "Kollage"
      }
    },
    "collection-list": {
      "name": "Kollektionslista",
      "settings": {
        "title": {
          "label": "Rubrik"
        },
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Porträtt"
          },
          "options__3": {
            "label": "Fyrkantig"
          },
          "info": "Lägg till bilder genom att redigera dina produktserier. [Mer information](https://help.shopify.com/manual/products/collections)"
        },
        "swipe_on_mobile": {
          "label": "Aktivera swipe på mobilen"
        },
        "show_view_all": {
          "label": "Aktivera \"Visa alla\"-knappen om listan innehåller fler produktserier än vad som visas"
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        },
        "header_mobile": {
          "content": "Mobil layout"
        },
        "columns_mobile": {
          "label": "Antal kolumner på mobil",
          "options__1": {
            "label": "1 kolumn"
          },
          "options__2": {
            "label": "2 kolumner"
          }
        }
      },
      "blocks": {
        "featured_collection": {
          "settings": {
            "collection": {
              "label": "Produktserie"
            }
          },
          "name": "Produktserie"
        }
      },
      "presets": {
        "name": "Kollektionslista"
      }
    },
    "contact-form": {
      "name": "Kontaktformulär",
      "presets": {
        "name": "Kontaktformulär"
      }
    },
    "custom-liquid": {
      "name": "Anpassa Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Anpassa Liquid",
          "info": "Lägg till appfragment eller annan Liquid-kod för att skapa avancerade anpassningar."
        }
      },
      "presets": {
        "name": "Anpassa Liquid"
      }
    },
    "featured-blog": {
      "name": "Blogginlägg",
      "settings": {
        "heading": {
          "label": "Rubrik"
        },
        "blog": {
          "label": "Blogg"
        },
        "post_limit": {
          "label": "Antalet blogginlägg att visa"
        },
        "show_view_all": {
          "label": "Aktivera \"Visa alla\"-knappen om bloggen innehåller fler blogginlägg än vad som visas"
        },
        "show_image": {
          "label": "Visa framhävd bild",
          "info": "Använd en bild med bildformat 3:2, för bästa resultat. [Mer information](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "show_date": {
          "label": "Visa datum"
        },
        "show_author": {
          "label": "Visa författare"
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        }
      },
      "presets": {
        "name": "Blogginlägg"
      }
    },
    "featured-collection": {
      "name": "Utvald produktserie",
      "settings": {
        "title": {
          "label": "Rubrik"
        },
        "collection": {
          "label": "Produktserie"
        },
        "products_to_show": {
          "label": "Maximalt antal produkter att visa"
        },
        "show_view_all": {
          "label": "Aktivera \"Visa alla\" om produktserien innehåller fler produkter än vad som visas"
        },
        "header": {
          "content": "Produktkort"
        },
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Porträtt"
          },
          "options__3": {
            "label": "Fyrkantig"
          }
        },
        "show_secondary_image": {
          "label": "Visa andra bild på hovring"
        },
        "show_vendor": {
          "label": "Visa säljare"
        },
        "show_rating": {
          "label": "Visa produktbetyg",
          "info": "Lägg till en produktbedömningsapp för att visa ett betyg. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "enable_quick_buy": {
          "label": "Aktivera knapp för snabb tilläggning",
          "info": "Optimal med varukorg av popup- eller lådtyp."
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        },
        "description": {
          "label": "Beskrivning"
        },
        "show_description": {
          "label": "Visa produktseriebeskrivning från Shopify-admin"
        },
        "description_style": {
          "label": "Beskrivningsstil",
          "options__1": {
            "label": "Brödtext"
          },
          "options__2": {
            "label": "Underrubrik"
          },
          "options__3": {
            "label": "Stora bokstäver"
          }
        },
        "view_all_style": {
          "options__1": {
            "label": "Länk"
          },
          "options__2": {
            "label": "Knappkontur"
          },
          "options__3": {
            "label": "Tydlig knapp"
          },
          "label": "\"Visa alla\"-stil"
        },
        "enable_desktop_slider": {
          "label": "Aktivera karusell på dator"
        },
        "full_width": {
          "label": "Ge produkterna full bredd"
        },
        "header_mobile": {
          "content": "Mobil layout"
        },
        "columns_mobile": {
          "label": "Antal kolumner på mobil",
          "options__1": {
            "label": "1 kolumn"
          },
          "options__2": {
            "label": "2 kolumner"
          }
        },
        "swipe_on_mobile": {
          "label": "Aktivera swipe på mobilen"
        }
      },
      "presets": {
        "name": "Utvald produktserie"
      }
    },
    "footer": {
      "name": "Sidfot",
      "blocks": {
        "link_list": {
          "settings": {
            "heading": {
              "label": "Rubrik"
            },
            "menu": {
              "label": "Meny",
              "info": "Visa endast menyobjekt på toppnivå."
            }
          },
          "name": "Meny"
        },
        "text": {
          "settings": {
            "heading": {
              "label": "Rubrik"
            },
            "subtext": {
              "label": "Undertext"
            }
          },
          "name": "Text"
        },
        "brand_information": {
          "name": "Varumärkesinformation",
          "settings": {
            "paragraph": {
              "content": "Detta block kommer visa din varumärkesinformation. [Redigera varumärkesinformation.](/editor?context=theme&category=brand%20information)"
            },
            "header__1": {
              "content": "Sociala medier-ikoner"
            },
            "show_social": {
              "label": "Visa sociala medier-ikoner",
              "info": "För att visa dina sociala medier-konton, länka dem i dina [tema-inställningar](/editor?context=theme&category=social%20media)."
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Visa e-postregistrering"
        },
        "newsletter_heading": {
          "label": "Rubrik"
        },
        "header__1": {
          "info": "Prenumeranter som lagts till automatiskt till listan med ”accepterar marknadsföring”. [Mer information](https://help.shopify.com/manual/customers/manage-customers)",
          "content": "E-postregistrering"
        },
        "header__2": {
          "content": "Sociala medier-ikoner",
          "info": "För att visa dina sociala medier-konton, länka dem i dina [tema-inställningar](/editor?context=theme&category=social%20media)."
        },
        "show_social": {
          "label": "Visa sociala medier-ikoner"
        },
        "header__3": {
          "content": "Land/region-väljare"
        },
        "header__4": {
          "info": "Om du vill lägga till land/region går du till dina [marknadsinställningar.](/admin/settings/markets)"
        },
        "enable_country_selector": {
          "label": "Aktivera land/region-väljare"
        },
        "header__5": {
          "content": "Språkväljare"
        },
        "header__6": {
          "info": "Om du vill lägga till ett språk går du till dina [språkinställningar.](/admin/settings/languages)"
        },
        "enable_language_selector": {
          "label": "Aktivera språkväljare"
        },
        "header__7": {
          "content": "Betalningsmetoder"
        },
        "payment_enable": {
          "label": "Visa betalningsikoner"
        },
        "margin_top": {
          "label": "Övre marginal"
        },
        "header__8": {
          "content": "Policylänkar",
          "info": "Gå till dina [policyinställningar ](/admin/settings/legal) för att lägga till butikspolicyer."
        },
        "show_policy": {
          "label": "Visa policylänkar"
        },
        "header__9": {
          "content": "Följ på Shop",
          "info": "Visa följarknappen för din butik i Shop-appen. [Mer information](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        },
        "enable_follow_on_shop": {
          "label": "Aktivera Följ på Shop"
        }
      }
    },
    "header": {
      "name": "Rubrik",
      "settings": {
        "logo_position": {
          "label": "Desktoplogotypens position",
          "options__1": {
            "label": "Mitten till vänster"
          },
          "options__2": {
            "label": "Överst till vänster"
          },
          "options__3": {
            "label": "Längst upp i mitten"
          },
          "options__4": {
            "label": "Mitten centrerat"
          }
        },
        "menu": {
          "label": "Meny"
        },
        "show_line_separator": {
          "label": "Visa avskiljande linje"
        },
        "margin_bottom": {
          "label": "Nedre marginal"
        },
        "menu_type_desktop": {
          "label": "Typ av skrivbordsmeny",
          "info": "Menytyp optimeras automatiskt för mobila enheter.",
          "options__1": {
            "label": "Rullgardinsmeny"
          },
          "options__2": {
            "label": "Megameny"
          }
        },
        "mobile_layout": {
          "content": "Mobil layout"
        },
        "mobile_logo_position": {
          "label": "Placering av logotyp på mobil",
          "options__1": {
            "label": "Centrera"
          },
          "options__2": {
            "label": "Vänster"
          }
        },
        "logo_header": {
          "content": "Logotyp"
        },
        "logo_help": {
          "content": "Redigera din logotyp i [temainställningarna](/editor?context=theme&category=logo)."
        },
        "sticky_header_type": {
          "label": "Fast sidhuvud",
          "options__1": {
            "label": "Inga"
          },
          "options__2": {
            "label": "Vid bläddring uppåt"
          },
          "options__3": {
            "label": "Alltid"
          },
          "options__4": {
            "label": "Alltid, minska logotypstorlek"
          }
        }
      }
    },
    "image-banner": {
      "name": "Bildbanner",
      "settings": {
        "image": {
          "label": "Första bild"
        },
        "image_2": {
          "label": "Andra bild"
        },
        "color_scheme": {
          "info": "Synlig när ruta visas."
        },
        "stack_images_on_mobile": {
          "label": "Stapla bilder på mobil"
        },
        "adapt_height_first_image": {
          "label": "Anpassa avsnittets höjd till storleken på första bilden.",
          "info": "Skriver över inställning för bildbanner-höjden när den markeras."
        },
        "show_text_box": {
          "label": "Visa ruta på skrivbord"
        },
        "image_overlay_opacity": {
          "label": "Bildens opacitet för överlagring"
        },
        "header": {
          "content": "Mobil layout"
        },
        "show_text_below": {
          "label": "Visa ruta på mobil"
        },
        "image_height": {
          "label": "Banner-höjd",
          "options__1": {
            "label": "Anpassa efter första bilden"
          },
          "options__2": {
            "label": "Liten"
          },
          "options__3": {
            "label": "Medel"
          },
          "info": "Använd en bild med bildformat 3:2, för bästa resultat. [Mer information](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
          "options__4": {
            "label": "Stor"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Längst upp till vänster"
          },
          "options__2": {
            "label": "Längst upp i mitten"
          },
          "options__3": {
            "label": "Längst upp till höger"
          },
          "options__4": {
            "label": "Mitten till vänster"
          },
          "options__5": {
            "label": "Mitten centrerat"
          },
          "options__6": {
            "label": "Mitten till höger"
          },
          "options__7": {
            "label": "Längst ner till vänster"
          },
          "options__8": {
            "label": "Längst ner i mitten"
          },
          "options__9": {
            "label": "Längst ner till höger"
          },
          "label": "Innehållsposition på dator"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Linjering av innehåll på dator"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Linjering av innehåll på mobil"
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Rubrik"
            }
          },
          "name": "Rubrik"
        },
        "text": {
          "settings": {
            "text": {
              "label": "Beskrivning"
            },
            "text_style": {
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Underrubrik"
              },
              "options__3": {
                "label": "Stora bokstäver"
              },
              "label": "Textstil"
            }
          },
          "name": "Text"
        },
        "buttons": {
          "settings": {
            "button_label_1": {
              "label": "Första knappetikett",
              "info": "Lämna etiketten tom eller dölj knappen."
            },
            "button_link_1": {
              "label": "Första knapplänk"
            },
            "button_style_secondary_1": {
              "label": "Använd stil för knappkontur"
            },
            "button_label_2": {
              "label": "Andra knappetikett",
              "info": "Lämna etiketten tom eller dölj knappen."
            },
            "button_link_2": {
              "label": "Andra knapplänk"
            },
            "button_style_secondary_2": {
              "label": "Använd stil för knappkontur"
            }
          },
          "name": "Knappar"
        }
      },
      "presets": {
        "name": "Bildbanner"
      }
    },
    "image-with-text": {
      "name": "Bild med text",
      "settings": {
        "image": {
          "label": "Bild"
        },
        "height": {
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Liten"
          },
          "options__3": {
            "label": "Medel"
          },
          "label": "Bildhöjd",
          "options__4": {
            "label": "Stor"
          }
        },
        "layout": {
          "options__1": {
            "label": "Bild först"
          },
          "options__2": {
            "label": "Andra bild"
          },
          "label": "Placering av datorbild",
          "info": "Bild först är standard-layout för mobil."
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Liten"
          },
          "options__2": {
            "label": "Medel"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Bildbredd för dator",
          "info": "Bilden optimeras automatiskt för mobilen."
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Justering av innehåll på skrivbord",
          "options__2": {
            "label": "Centrera"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Högst upp"
          },
          "options__2": {
            "label": "Mitten"
          },
          "options__3": {
            "label": "Längst ner"
          },
          "label": "Innehållsposition på dator"
        },
        "content_layout": {
          "options__1": {
            "label": "Ingen överlappning"
          },
          "options__2": {
            "label": "Överlappande"
          },
          "label": "Innehållslayout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Linjering av innehåll på mobil",
          "options__2": {
            "label": "Centrera"
          }
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Rubrik"
            }
          },
          "name": "Rubrik"
        },
        "text": {
          "settings": {
            "text": {
              "label": "Innehåll"
            },
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Underrubrik"
              }
            }
          },
          "name": "Text"
        },
        "button": {
          "settings": {
            "button_label": {
              "label": "Knappetikett",
              "info": "Lämna etiketten tom eller dölj knappen."
            },
            "button_link": {
              "label": "Knapplänk"
            }
          },
          "name": "Knapp"
        },
        "caption": {
          "name": "Rubrik",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Underrubrik"
              },
              "options__2": {
                "label": "Stora bokstäver"
              }
            },
            "caption_size": {
              "label": "Textstorlek",
              "options__1": {
                "label": "Liten"
              },
              "options__2": {
                "label": "Medel"
              },
              "options__3": {
                "label": "Stor"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Bild med text"
      }
    },
    "main-article": {
      "name": "Blogginlägg",
      "blocks": {
        "featured_image": {
          "settings": {
            "image_height": {
              "label": "Bildhöjd på utvald bild",
              "options__1": {
                "label": "Anpassa till bild"
              },
              "options__2": {
                "label": "Liten"
              },
              "options__3": {
                "label": "Medel"
              },
              "info": "Använd en bild med bildformat 16:9, för bästa resultat. [Mer information](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
              "options__4": {
                "label": "Stor"
              }
            }
          },
          "name": "Utvald bild"
        },
        "title": {
          "settings": {
            "blog_show_date": {
              "label": "Visa datum"
            },
            "blog_show_author": {
              "label": "Visa författare"
            }
          },
          "name": "Titel"
        },
        "content": {
          "name": "Innehåll"
        },
        "share": {
          "name": "Dela",
          "settings": {
            "featured_image_info": {
              "content": "Om du inkluderar en länk i inlägg på sociala medier kommer sidans utvalda bild att visas som förhandsgranskningsbild. [Läs mer](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Ett butiksnamn och en beskrivning inkluderas med förhandsgranskningsbilden. [Läs mer](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Text"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blogginlägg",
      "settings": {
        "header": {
          "content": "Kort för blogginlägg"
        },
        "show_image": {
          "label": "Visa framhävd bild"
        },
        "paragraph": {
          "content": "Ändra utdrag genom att redigera dina blogginlägg. [Mer information](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"
        },
        "show_date": {
          "label": "Visa datum"
        },
        "show_author": {
          "label": "Visa författare"
        },
        "layout": {
          "label": "Desktoplayout",
          "options__1": {
            "label": "Rutnät"
          },
          "options__2": {
            "label": "Kollage"
          },
          "info": "Inläggen staplas på mobilen."
        },
        "image_height": {
          "label": "Bildhöjd på utvald bild",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Liten"
          },
          "options__3": {
            "label": "Medel"
          },
          "options__4": {
            "label": "Stor"
          },
          "info": "Använd en bild med bildformat 3:2, för bästa resultat. [Mer information](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-cart-footer": {
      "name": "Delsumma",
      "blocks": {
        "subtotal": {
          "name": "Delsumma"
        },
        "buttons": {
          "name": "Knapp för att betala i kassan"
        }
      }
    },
    "main-cart-items": {
      "name": "Artiklar"
    },
    "main-collection-banner": {
      "name": "Banner för produktserie",
      "settings": {
        "paragraph": {
          "content": "Lägg till en beskrivning eller en bild genom att redigera din produktserie. [Mer information](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Visa produktseriebeskrivning"
        },
        "show_collection_image": {
          "label": "Visa produktseriebild",
          "info": "Använd en bild med bildformat 16:9, för bästa resultat. [Mer information](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Produktrutnät",
      "settings": {
        "products_per_page": {
          "label": "Produkter per rad"
        },
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Porträtt"
          },
          "options__3": {
            "label": "Fyrkantig"
          }
        },
        "show_secondary_image": {
          "label": "Visa andra bild på hovring"
        },
        "show_vendor": {
          "label": "Visa säljare"
        },
        "header__1": {
          "content": "Filtrering och sortering"
        },
        "enable_tags": {
          "label": "Aktivera filtrering",
          "info": "Anpassa filter med Search & Discovery-appen. [Mer information](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_filtering": {
          "label": "Aktivera filtrering",
          "info": "Anpassa filter med Search & Discovery-appen. [Mer information](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Aktivera sortering"
        },
        "header__3": {
          "content": "Produktkort"
        },
        "show_rating": {
          "label": "Visa produktbetyg",
          "info": "Lägg till en produktbedömningsapp för att visa ett betyg. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"
        },
        "enable_quick_buy": {
          "label": "Aktivera knapp för snabb tilläggning",
          "info": "Optimal med varukorg av popup- eller lådtyp."
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        },
        "header_mobile": {
          "content": "Mobil layout"
        },
        "columns_mobile": {
          "label": "Antal kolumner på mobil",
          "options__1": {
            "label": "1 kolumn"
          },
          "options__2": {
            "label": "2 kolumner"
          }
        },
        "filter_type": {
          "label": "Filterlayout för dator",
          "options__1": {
            "label": "Liggande"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Låda"
          },
          "info": "Låda är standardlayouten för mobil."
        }
      }
    },
    "main-list-collections": {
      "name": "Kollektionslistsida",
      "settings": {
        "title": {
          "label": "Rubrik"
        },
        "sort": {
          "label": "Sortera kollektioner efter:",
          "options__1": {
            "label": "Alfabetiskt, A–Ö"
          },
          "options__2": {
            "label": "Alfabetiskt, Ö–A"
          },
          "options__3": {
            "label": "Datum, nytt till gammalt"
          },
          "options__4": {
            "label": "Datum, gammalt till nytt"
          },
          "options__5": {
            "label": "Produktantal, högt till lågt"
          },
          "options__6": {
            "label": "Produktantal, lågt till högt"
          }
        },
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Porträtt"
          },
          "options__3": {
            "label": "Fyrkantig"
          },
          "info": "Lägg till bilder genom att redigera dina produktserier. [Mer information](https://help.shopify.com/manual/products/collections)"
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        },
        "header_mobile": {
          "content": "Mobil layout"
        },
        "columns_mobile": {
          "label": "Antal kolumner på mobil",
          "options__1": {
            "label": "1 kolumn"
          },
          "options__2": {
            "label": "2 kolumner"
          }
        }
      }
    },
    "main-page": {
      "name": "Sida"
    },
    "main-password-footer": {
      "name": "Lösenord sidfot"
    },
    "main-password-header": {
      "name": "Lösenord sidhuvud",
      "settings": {
        "logo_header": {
          "content": "Logotyp"
        },
        "logo_help": {
          "content": "Redigera din logotyp i temainställningarna."
        }
      }
    },
    "main-product": {
      "name": "Produktinformation",
      "blocks": {
        "text": {
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Textning"
              },
              "options__3": {
                "label": "Stor bokstav"
              }
            }
          },
          "name": "Text"
        },
        "variant_picker": {
          "settings": {
            "picker_type": {
              "label": "Typ",
              "options__1": {
                "label": "Rullgardinsmeny"
              },
              "options__2": {
                "label": "Kapslar"
              }
            }
          },
          "name": "Variantväljare"
        },
        "buy_buttons": {
          "settings": {
            "show_dynamic_checkout": {
              "label": "Visa dynamiska utcheckningsknappar",
              "info": "Genom att använda tillgängliga betalningsmetoder i butiken kan kunder se valt alternativ, till exempel PayPal eller Apple Pay. [Läs mer](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          },
          "name": "Köpknappar"
        },
        "share": {
          "settings": {
            "featured_image_info": {
              "content": "Om du inkluderar en länk i inlägg på sociala medier kommer sidans utvalda bild att visas som förhandsgranskningsbild. [Läs mer](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Ett butiksnamn och en beskrivning inkluderas med förhandsgranskningsbilden. [Läs mer](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Text"
            }
          },
          "name": "Dela"
        },
        "collapsible_tab": {
          "settings": {
            "heading": {
              "info": "Inkludera en rubrik som beskriver innehållet.",
              "label": "Rubrik"
            },
            "content": {
              "label": "Radinnehåll"
            },
            "page": {
              "label": "Radinnehåll från sida"
            },
            "icon": {
              "label": "Ikon",
              "options__1": {
                "label": "Inga"
              },
              "options__2": {
                "label": "Äpple"
              },
              "options__3": {
                "label": "Banan"
              },
              "options__4": {
                "label": "Flaska"
              },
              "options__5": {
                "label": "Låda"
              },
              "options__6": {
                "label": "Morot"
              },
              "options__7": {
                "label": "Chattbubbla"
              },
              "options__8": {
                "label": "Bock"
              },
              "options__9": {
                "label": "Urklipp"
              },
              "options__10": {
                "label": "Mejeriprodukt"
              },
              "options__11": {
                "label": "Fri från mejeriprodukter"
              },
              "options__12": {
                "label": "Torkare"
              },
              "options__13": {
                "label": "Öga"
              },
              "options__14": {
                "label": "Eld"
              },
              "options__15": {
                "label": "Glutenfri"
              },
              "options__16": {
                "label": "Hjärta"
              },
              "options__17": {
                "label": "Strykjärn"
              },
              "options__18": {
                "label": "Blad"
              },
              "options__19": {
                "label": "Läder"
              },
              "options__20": {
                "label": "Blixt"
              },
              "options__21": {
                "label": "Läppstift"
              },
              "options__22": {
                "label": "Lås"
              },
              "options__23": {
                "label": "Kartnål"
              },
              "options__24": {
                "label": "Utan nötter"
              },
              "options__25": {
                "label": "Byxor"
              },
              "options__26": {
                "label": "Tassavtryck"
              },
              "options__27": {
                "label": "Peppar"
              },
              "options__28": {
                "label": "Parfym"
              },
              "options__29": {
                "label": "Flygplan"
              },
              "options__30": {
                "label": "Växt"
              },
              "options__31": {
                "label": "Prislapp"
              },
              "options__32": {
                "label": "Frågetecken"
              },
              "options__33": {
                "label": "Återvinn"
              },
              "options__34": {
                "label": "Gå tillbaka"
              },
              "options__35": {
                "label": "Linjal"
              },
              "options__36": {
                "label": "Serveringsfat"
              },
              "options__37": {
                "label": "Skjorta"
              },
              "options__38": {
                "label": "Sko"
              },
              "options__39": {
                "label": "Siluett"
              },
              "options__40": {
                "label": "Snöflinga"
              },
              "options__41": {
                "label": "Stjärna"
              },
              "options__42": {
                "label": "Stoppur"
              },
              "options__43": {
                "label": "Lastbil"
              },
              "options__44": {
                "label": "Tvätt"
              }
            }
          },
          "name": "Rad som kan döljas"
        },
        "popup": {
          "settings": {
            "link_label": {
              "label": "Länketikett"
            },
            "page": {
              "label": "Sida"
            }
          },
          "name": "Pop-up"
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Pris"
        },
        "quantity_selector": {
          "name": "Kvantitetsväljare"
        },
        "pickup_availability": {
          "name": "Hämtningsmöjligheter"
        },
        "description": {
          "name": "Beskrivning"
        },
        "custom_liquid": {
          "name": "Anpassa Liquid",
          "settings": {
            "custom_liquid": {
              "label": "Anpassa Liquid",
              "info": "Lägg till appfragment eller annan Liquid-kod för att skapa avancerade anpassningar."
            }
          }
        },
        "rating": {
          "name": "Produktbetyg",
          "settings": {
            "paragraph": {
              "content": "Lägg till en produktbedömningsapp för att visa ett betyg. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Kompletterande produkter",
          "settings": {
            "paragraph": {
              "content": "Lägg till appen Search & Discovery för att välja tilläggsprodukter. [Mer information](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Rubrik"
            },
            "make_collapsible_row": {
              "label": "Visa som komprimerbar rad"
            },
            "icon": {
              "info": "Synlig när komprimerbar rad visas."
            },
            "product_list_limit": {
              "label": "Maximalt antal produkter att visa"
            },
            "products_per_page": {
              "label": "Antal produkter per sida"
            },
            "pagination_style": {
              "label": "Pagineringsstil",
              "options": {
                "option_1": "Prickar",
                "option_2": "Räknare",
                "option_3": "Siffror"
              }
            },
            "product_card": {
              "heading": "Produktkort"
            },
            "image_ratio": {
              "label": "Bildförhållande",
              "options": {
                "option_1": "Porträtt",
                "option_2": "Fyrkantig"
              }
            },
            "enable_quick_add": {
              "label": "Aktivera knapp för snabb tilläggning"
            }
          }
        },
        "icon_with_text": {
          "name": "Ikon med text",
          "settings": {
            "layout": {
              "label": "Layout",
              "options__1": {
                "label": "Liggande"
              },
              "options__2": {
                "label": "Stående"
              }
            },
            "content": {
              "label": "Innehåll",
              "info": "Välj en ikon eller lägg till en bild för varje kolumn eller rad."
            },
            "heading": {
              "info": "Lämna rubriketiketten tom för att dölja ikonkolumnen."
            },
            "icon_1": {
              "label": "Första ikon"
            },
            "image_1": {
              "label": "Första bild"
            },
            "heading_1": {
              "label": "Första rubrik"
            },
            "icon_2": {
              "label": "Andra ikon"
            },
            "image_2": {
              "label": "Andra bild"
            },
            "heading_2": {
              "label": "Andra rubrik"
            },
            "icon_3": {
              "label": "Tredje ikon"
            },
            "image_3": {
              "label": "Tredje bild"
            },
            "heading_3": {
              "label": "Tredje rubrik"
            }
          }
        },
        "sku": {
          "name": "Lagerhållningsenhet",
          "settings": {
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Underrubrik"
              },
              "options__3": {
                "label": "Stora bokstäver"
              }
            }
          }
        },
        "inventory": {
          "name": "Lagerstatus",
          "settings": {
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Underrubrik"
              },
              "options__3": {
                "label": "Stora bokstäver"
              }
            },
            "inventory_threshold": {
              "label": "Tröskel för låg lagernivå",
              "info": "Välj 0 för att alltid visa i lager om tillgängligt."
            },
            "show_inventory_quantity": {
              "label": "Visa inventering"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Media",
          "info": "Mer information om [mediatyper.](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Aktivera video-loopning"
        },
        "enable_sticky_info": {
          "label": "Aktivera fast innehåll på desktop"
        },
        "hide_variants": {
          "label": "Dölj andra varianters media efter att du valt en variant"
        },
        "gallery_layout": {
          "label": "Desktoplayout",
          "options__1": {
            "label": "Staplade"
          },
          "options__2": {
            "label": "2 kolumner"
          },
          "options__3": {
            "label": "Miniatyrbilder"
          },
          "options__4": {
            "label": "Miniatyrbildskarusell"
          }
        },
        "media_size": {
          "label": "Mediabredd för dator",
          "options__1": {
            "label": "Liten"
          },
          "options__2": {
            "label": "Medel"
          },
          "options__3": {
            "label": "Stor"
          },
          "info": "Media optimeras automatiskt för mobilen."
        },
        "mobile_thumbnails": {
          "label": "Mobil layout",
          "options__1": {
            "label": "2 kolumner"
          },
          "options__2": {
            "label": "Visa miniatyrbilder"
          },
          "options__3": {
            "label": "Dölj miniatyrbilder"
          }
        },
        "media_position": {
          "label": "Datorns media-position",
          "info": "Positionen optimeras automatiskt för mobilen.",
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Höger"
          }
        },
        "image_zoom": {
          "label": "Bildzoom",
          "info": "Klicka och svep över för att öppna lightbox på mobilen.",
          "options__1": {
            "label": "Öppna lightbox"
          },
          "options__2": {
            "label": "Klicka och svep över"
          },
          "options__3": {
            "label": "Ingen zoom"
          }
        },
        "constrain_to_viewport": {
          "label": "Begränsa media till skärmhöjd"
        },
        "media_fit": {
          "label": "Mediaanpassning",
          "options__1": {
            "label": "Original"
          },
          "options__2": {
            "label": "Fyll"
          }
        }
      }
    },
    "main-search": {
      "name": "Sökresultat",
      "settings": {
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Porträtt"
          },
          "options__3": {
            "label": "Fyrkantig"
          }
        },
        "show_secondary_image": {
          "label": "Visa andra bild på hovring"
        },
        "show_vendor": {
          "label": "Visa säljare"
        },
        "header__1": {
          "content": "Produktkort"
        },
        "header__2": {
          "content": "Bloggkort",
          "info": "Bloggkortsstilar tillämpas även för sidkort i sökresultat. Uppdatera dina temainställningar för att ändra kortstilar."
        },
        "article_show_date": {
          "label": "Visa datum"
        },
        "article_show_author": {
          "label": "Visa författare"
        },
        "show_rating": {
          "label": "Visa produktbetyg",
          "info": "Lägg till en produktbedömningsapp för att visa ett betyg. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        },
        "header_mobile": {
          "content": "Mobil layout"
        },
        "columns_mobile": {
          "label": "Antal kolumner på mobil",
          "options__1": {
            "label": "1 kolumn"
          },
          "options__2": {
            "label": "2 kolumner"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Multikolumn",
      "settings": {
        "title": {
          "label": "Rubrik"
        },
        "image_width": {
          "label": "Bildbredd",
          "options__1": {
            "label": "En tredjedels bredd av kolumn"
          },
          "options__2": {
            "label": "Halva kolumnens bredd"
          },
          "options__3": {
            "label": "Hela kolumnens bredd"
          }
        },
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Porträtt"
          },
          "options__3": {
            "label": "Fyrkantig"
          },
          "options__4": {
            "label": "Cirkel"
          }
        },
        "column_alignment": {
          "label": "Kolumnjustering",
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          }
        },
        "background_style": {
          "label": "Sekundär bakgrund",
          "options__1": {
            "label": "Inga"
          },
          "options__2": {
            "label": "Visa som kolumnbakgrund"
          }
        },
        "button_label": {
          "label": "Knappetikett"
        },
        "button_link": {
          "label": "Knapplänk"
        },
        "swipe_on_mobile": {
          "label": "Aktivera swipe på mobilen"
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        },
        "header_mobile": {
          "content": "Mobil layout"
        },
        "columns_mobile": {
          "label": "Antal kolumner på mobil",
          "options__1": {
            "label": "1 kolumn"
          },
          "options__2": {
            "label": "2 kolumner"
          }
        }
      },
      "blocks": {
        "column": {
          "settings": {
            "image": {
              "label": "Bild"
            },
            "title": {
              "label": "Rubrik"
            },
            "text": {
              "label": "Beskrivning"
            },
            "link_label": {
              "label": "Länketikett"
            },
            "link": {
              "label": "Länk"
            }
          },
          "name": "Kolumn"
        }
      },
      "presets": {
        "name": "Multikolumn"
      }
    },
    "newsletter": {
      "name": "E-postregistrering",
      "settings": {
        "full_width": {
          "label": "Ge avsnittet full bredd"
        },
        "paragraph": {
          "content": "Varje e-postprenumeration skapar ett kundkonto. [Mer information](https://help.shopify.com/manual/customers)"
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Rubrik"
            }
          },
          "name": "Rubrik"
        },
        "paragraph": {
          "settings": {
            "paragraph": {
              "label": "Beskrivning"
            }
          },
          "name": "Underrubrik"
        },
        "email_form": {
          "name": "E-postformulär"
        }
      },
      "presets": {
        "name": "E-postregistrering"
      }
    },
    "page": {
      "name": "Sida",
      "settings": {
        "page": {
          "label": "Sida"
        }
      },
      "presets": {
        "name": "Sida"
      }
    },
    "rich-text": {
      "name": "Rich text",
      "settings": {
        "full_width": {
          "label": "Ge avsnittet full bredd"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Innehållsposition på skrivbord",
          "info": "Positionen optimeras automatiskt för mobilen."
        },
        "content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Innehållsjustering"
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Rubrik"
            }
          },
          "name": "Rubrik"
        },
        "text": {
          "settings": {
            "text": {
              "label": "Beskrivning"
            }
          },
          "name": "Text"
        },
        "buttons": {
          "settings": {
            "button_label_1": {
              "label": "Första knappetikett",
              "info": "Lämna etiketten tom eller dölj knappen."
            },
            "button_link_1": {
              "label": "Första knapplänk"
            },
            "button_style_secondary_1": {
              "label": "Använd stil för knappkontur"
            },
            "button_label_2": {
              "label": "Andra knappetikett",
              "info": "Lämna etiketten tom eller dölj knappen."
            },
            "button_link_2": {
              "label": "Andra knapplänk"
            },
            "button_style_secondary_2": {
              "label": "Använd stil för knappkontur"
            }
          },
          "name": "Knappar"
        },
        "caption": {
          "name": "Rubrik",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Underrubrik"
              },
              "options__2": {
                "label": "Stora bokstäver"
              }
            },
            "caption_size": {
              "label": "Textstorlek",
              "options__1": {
                "label": "Liten"
              },
              "options__2": {
                "label": "Medel"
              },
              "options__3": {
                "label": "Stor"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Rich text"
      }
    },
    "apps": {
      "name": "Appar",
      "settings": {
        "include_margins": {
          "label": "Gör avsnittsmarginaler likadana som temat"
        }
      },
      "presets": {
        "name": "Appar"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Rubrik"
        },
        "cover_image": {
          "label": "Omslagsbild"
        },
        "video_url": {
          "label": "URL",
          "placeholder": "Använd en YouTube- eller en Vimeo-URL",
          "info": "Video spelas upp på sidan."
        },
        "description": {
          "label": "Alternativtext för video",
          "info": "Beskriv videon för kunder som använder skärmläsare. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"
        },
        "image_padding": {
          "label": "Lägg till bild-padding",
          "info": "Välj bild-padding om du inte vill att din omslagsbild ska beskäras."
        },
        "full_width": {
          "label": "Ge avsnittet full bredd"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Utvald produkt",
      "blocks": {
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            },
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Textning"
              },
              "options__3": {
                "label": "Stora bokstäver"
              }
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Pris"
        },
        "quantity_selector": {
          "name": "Kvantitetsväljare"
        },
        "variant_picker": {
          "name": "Variantväljare",
          "settings": {
            "picker_type": {
              "label": "Typ",
              "options__1": {
                "label": "Rullgardinsmeny"
              },
              "options__2": {
                "label": "Kapslar"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Köpknappar",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Visa dynamiska kassaknappar",
              "info": "Genom att använda de betalningsmetoder som finns tillgängliga i din butik kan kunder se det alternativ de föredrar, som PayPal eller Apple Pay. [Mer information](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Beskrivning"
        },
        "share": {
          "name": "Dela",
          "settings": {
            "featured_image_info": {
              "content": "Om du inkluderar en länk i inlägg på sociala medier kommer sidans utvalda bild att visas som förhandsgranskningsbild. [Mer information](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Ett butiksnamn och en beskrivning inkluderas med förhandsgranskningsbilden. [Mer information](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "custom_liquid": {
          "name": "Anpassa Liquid",
          "settings": {
            "custom_liquid": {
              "label": "Anpassa Liquid"
            }
          }
        },
        "rating": {
          "name": "Produktbetyg",
          "settings": {
            "paragraph": {
              "content": "Lägg till en produktbedömningsapp för att visa ett betyg. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "Lagerhållningsenhet",
          "settings": {
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Underrubrik"
              },
              "options__3": {
                "label": "Stora bokstäver"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Produkt"
        },
        "secondary_background": {
          "label": "Visa sekundär bakgrund"
        },
        "header": {
          "content": "Media",
          "info": "Läs mer om [mediatyper](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Aktivera videoslingor"
        },
        "hide_variants": {
          "label": "Dölj media för varianter som inte har valts på datorn"
        },
        "media_position": {
          "label": "Datorns media-position",
          "info": "Positionen optimeras automatiskt för mobilen.",
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Höger"
          }
        }
      },
      "presets": {
        "name": "Utvald produkt"
      }
    },
    "email-signup-banner": {
      "name": "Banner för e-postregistrering",
      "settings": {
        "paragraph": {
          "content": "Varje e-postprenumeration skapar ett kundkonto. [Mer information](https://help.shopify.com/manual/customers)"
        },
        "image": {
          "label": "Bakgrundsbild"
        },
        "show_background_image": {
          "label": "Visa bakgrundsbild"
        },
        "show_text_box": {
          "label": "Visa ruta på skrivbord"
        },
        "image_overlay_opacity": {
          "label": "Bildens opacitet för överlagring"
        },
        "color_scheme": {
          "info": "Synlig när ruta visas."
        },
        "show_text_below": {
          "label": "Visa innehåll under bild på mobil",
          "info": "Använd en bild med bildformat 16:9, för bästa resultat. [Mer information](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "image_height": {
          "label": "Banner-höjd",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Liten"
          },
          "options__3": {
            "label": "Medel"
          },
          "options__4": {
            "label": "Stor"
          },
          "info": "Använd en bild med bildformat 16:9, för bästa resultat. [Mer information](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "desktop_content_position": {
          "options__4": {
            "label": "Mitten till vänster"
          },
          "options__5": {
            "label": "Mitten centrerat"
          },
          "options__6": {
            "label": "Mitten till höger"
          },
          "options__7": {
            "label": "Längst ner till vänster"
          },
          "options__8": {
            "label": "Längst ner i mitten"
          },
          "options__9": {
            "label": "Längst ner till höger"
          },
          "options__1": {
            "label": "Längst upp till vänster"
          },
          "options__2": {
            "label": "Längst upp i mitten"
          },
          "options__3": {
            "label": "Längst upp till höger"
          },
          "label": "Innehållsposition på dator"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Justering av innehåll på skrivbord"
        },
        "header": {
          "content": "Mobil layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Linjering av innehåll på mobil"
        }
      },
      "blocks": {
        "heading": {
          "name": "Rubrik",
          "settings": {
            "heading": {
              "label": "Rubrik"
            }
          }
        },
        "paragraph": {
          "name": "Stycke",
          "settings": {
            "paragraph": {
              "label": "Beskrivning"
            },
            "text_style": {
              "options__1": {
                "label": "Brödtext"
              },
              "options__2": {
                "label": "Underrubrik"
              },
              "label": "Textstil"
            }
          }
        },
        "email_form": {
          "name": "E-postformulär"
        }
      },
      "presets": {
        "name": "Banner för e-postregistrering"
      }
    },
    "slideshow": {
      "name": "Bildspel",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Full bredd"
          },
          "options__2": {
            "label": "Rutnät"
          }
        },
        "slide_height": {
          "label": "Bildhöjd",
          "options__1": {
            "label": "Anpassa efter första bilden"
          },
          "options__2": {
            "label": "Liten"
          },
          "options__3": {
            "label": "Medel"
          },
          "options__4": {
            "label": "Stor"
          }
        },
        "slider_visual": {
          "label": "Pagineringsstil",
          "options__1": {
            "label": "Räknare"
          },
          "options__2": {
            "label": "Prickar"
          },
          "options__3": {
            "label": "Siffror"
          }
        },
        "auto_rotate": {
          "label": "Auto-rotera bilder"
        },
        "change_slides_speed": {
          "label": "Byt bilder varje"
        },
        "mobile": {
          "content": "Mobil layout"
        },
        "show_text_below": {
          "label": "Visa innehåll under bilder på mobil"
        },
        "accessibility": {
          "content": "Tillgänglighet",
          "label": "Beskrivning bildspel",
          "info": "Beskriv bildspelet för kunder som använder skärmläsare."
        }
      },
      "blocks": {
        "slide": {
          "name": "Bild",
          "settings": {
            "image": {
              "label": "Bild"
            },
            "heading": {
              "label": "Rubrik"
            },
            "subheading": {
              "label": "Underrubrik"
            },
            "button_label": {
              "label": "Knappetikett",
              "info": "Lämna etiketten tom eller dölj knappen."
            },
            "link": {
              "label": "Knapplänk"
            },
            "secondary_style": {
              "label": "Använd stil för knappkontur"
            },
            "box_align": {
              "label": "Innehållsposition på dator",
              "options__1": {
                "label": "Överst till vänster"
              },
              "options__2": {
                "label": "Längst upp i mitten"
              },
              "options__3": {
                "label": "Överst till höger"
              },
              "options__4": {
                "label": "Mitten till vänster"
              },
              "options__5": {
                "label": "Mitten centrerat"
              },
              "options__6": {
                "label": "Mitten till höger"
              },
              "options__7": {
                "label": "Nere till vänster"
              },
              "options__8": {
                "label": "Längst ner i mitten"
              },
              "options__9": {
                "label": "Nere till höger"
              },
              "info": "Positionen optimeras automatiskt för mobilen."
            },
            "show_text_box": {
              "label": "Visa ruta på skrivbord"
            },
            "text_alignment": {
              "label": "Justering av innehåll på skrivbord",
              "option_1": {
                "label": "Vänster"
              },
              "option_2": {
                "label": "Centrera"
              },
              "option_3": {
                "label": "Höger"
              }
            },
            "image_overlay_opacity": {
              "label": "Bildens opacitet för överlagring"
            },
            "color_scheme": {
              "info": "Synlig när ruta visas."
            },
            "text_alignment_mobile": {
              "label": "Linjering av innehåll på mobil",
              "options__1": {
                "label": "Vänster"
              },
              "options__2": {
                "label": "Centrera"
              },
              "options__3": {
                "label": "Höger"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Bildspel"
      }
    },
    "collapsible_content": {
      "name": "Innehåll som kan döljas",
      "settings": {
        "caption": {
          "label": "Rubrik"
        },
        "heading": {
          "label": "Rubrik"
        },
        "heading_alignment": {
          "label": "Rubriklinjering",
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          }
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Ingen behållare"
          },
          "options__2": {
            "label": "Radhållare"
          },
          "options__3": {
            "label": "Avsnittshållare"
          }
        },
        "open_first_collapsible_row": {
          "label": "Öppna den första raden som kan döljas"
        },
        "header": {
          "content": "Bildlayout"
        },
        "image": {
          "label": "Bild"
        },
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Liten"
          },
          "options__3": {
            "label": "Stor"
          }
        },
        "desktop_layout": {
          "label": "Layout för dator",
          "options__1": {
            "label": "Bild först"
          },
          "options__2": {
            "label": "Andra bild"
          },
          "info": "Bild visas alltid först på mobil."
        },
        "container_color_scheme": {
          "label": "Färgschema för container",
          "info": "Synligt när layouten är inställd på rad- eller avsnittscontainer."
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Rad som kan döljas",
          "settings": {
            "heading": {
              "info": "Inkludera en rubrik som beskriver innehållet.",
              "label": "Rubrik"
            },
            "row_content": {
              "label": "Radinnehåll"
            },
            "page": {
              "label": "Radinnehåll från sida"
            },
            "icon": {
              "label": "Ikon",
              "options__1": {
                "label": "Inga"
              },
              "options__2": {
                "label": "Äpple"
              },
              "options__3": {
                "label": "Banan"
              },
              "options__4": {
                "label": "Flaska"
              },
              "options__5": {
                "label": "Låda"
              },
              "options__6": {
                "label": "Morot"
              },
              "options__7": {
                "label": "Chattbubbla"
              },
              "options__8": {
                "label": "Bock"
              },
              "options__9": {
                "label": "Urklipp"
              },
              "options__10": {
                "label": "Mejeriprodukt"
              },
              "options__11": {
                "label": "Fri från mejeriprodukter"
              },
              "options__12": {
                "label": "Torkare"
              },
              "options__13": {
                "label": "Öga"
              },
              "options__14": {
                "label": "Eld"
              },
              "options__15": {
                "label": "Glutenfri"
              },
              "options__16": {
                "label": "Hjärta"
              },
              "options__17": {
                "label": "Strykjärn"
              },
              "options__18": {
                "label": "Blad"
              },
              "options__19": {
                "label": "Läder"
              },
              "options__20": {
                "label": "Blixt"
              },
              "options__21": {
                "label": "Läppstift"
              },
              "options__22": {
                "label": "Lås"
              },
              "options__23": {
                "label": "Kartnål"
              },
              "options__24": {
                "label": "Utan nötter"
              },
              "options__25": {
                "label": "Byxor"
              },
              "options__26": {
                "label": "Tassavtryck"
              },
              "options__27": {
                "label": "Peppar"
              },
              "options__28": {
                "label": "Parfym"
              },
              "options__29": {
                "label": "Flygplan"
              },
              "options__30": {
                "label": "Växt"
              },
              "options__31": {
                "label": "Prislapp"
              },
              "options__32": {
                "label": "Frågetecken"
              },
              "options__33": {
                "label": "Återvinn"
              },
              "options__34": {
                "label": "Gå tillbaka"
              },
              "options__35": {
                "label": "Linjal"
              },
              "options__36": {
                "label": "Serveringsfat"
              },
              "options__37": {
                "label": "Skjorta"
              },
              "options__38": {
                "label": "Sko"
              },
              "options__39": {
                "label": "Siluett"
              },
              "options__40": {
                "label": "Snöflinga"
              },
              "options__41": {
                "label": "Stjärna"
              },
              "options__42": {
                "label": "Stoppur"
              },
              "options__43": {
                "label": "Lastbil"
              },
              "options__44": {
                "label": "Tvätt"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Innehåll som kan döljas"
      }
    },
    "main-account": {
      "name": "Konto"
    },
    "main-activate-account": {
      "name": "Kontoaktivering"
    },
    "main-addresses": {
      "name": "Adresser"
    },
    "main-login": {
      "name": "Inloggning"
    },
    "main-order": {
      "name": "Order"
    },
    "main-register": {
      "name": "Registrering"
    },
    "main-reset-password": {
      "name": "Lösenordsåterställning"
    },
    "related-products": {
      "name": "Relaterade produkter",
      "settings": {
        "heading": {
          "label": "Rubrik"
        },
        "products_to_show": {
          "label": "Maximalt antal produkter att visa"
        },
        "columns_desktop": {
          "label": "Antalet kolumner på skrivbordet"
        },
        "paragraph__1": {
          "content": "Dynamiska rekommendationer använder order- och produktinformation för att ändras och förbättras över tid. [Mer information](https://help.shopify.com/themes/development/recommended-products)"
        },
        "header__2": {
          "content": "Produktkort"
        },
        "image_ratio": {
          "label": "Bildförhållande",
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Porträtt"
          },
          "options__3": {
            "label": "Fyrkantig"
          }
        },
        "show_secondary_image": {
          "label": "Visa andra bild på hovring"
        },
        "show_vendor": {
          "label": "Visa säljare"
        },
        "show_rating": {
          "label": "Visa produktbetyg",
          "info": "Lägg till en produktbedömningsapp för att visa ett betyg. [Mer information](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"
        },
        "header_mobile": {
          "content": "Mobil layout"
        },
        "columns_mobile": {
          "label": "Antal kolumner på mobil",
          "options__1": {
            "label": "1 kolumn"
          },
          "options__2": {
            "label": "2 kolumner"
          }
        }
      }
    },
    "multirow": {
      "name": "Flera rader",
      "settings": {
        "image": {
          "label": "Bild"
        },
        "image_height": {
          "options__1": {
            "label": "Anpassa till bild"
          },
          "options__2": {
            "label": "Liten"
          },
          "options__3": {
            "label": "Medel"
          },
          "options__4": {
            "label": "Stor"
          },
          "label": "Bildhöjd"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Liten"
          },
          "options__2": {
            "label": "Medel"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Bildbredd för dator",
          "info": "Bilden optimeras automatiskt för mobilen."
        },
        "heading_size": {
          "options__1": {
            "label": "Liten"
          },
          "options__2": {
            "label": "Medel"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Rubrikstorlek"
        },
        "text_style": {
          "options__1": {
            "label": "Brödtext"
          },
          "options__2": {
            "label": "Underrubrik"
          },
          "label": "Textstil"
        },
        "button_style": {
          "options__1": {
            "label": "Tydlig knapp"
          },
          "options__2": {
            "label": "Knappkontur"
          },
          "label": "Knappstil"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Justering av innehåll på skrivbord"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Högst upp"
          },
          "options__2": {
            "label": "Mitten"
          },
          "options__3": {
            "label": "Längst ner"
          },
          "label": "Innehållsposition på skrivbord",
          "info": "Positionen optimeras automatiskt för mobilen."
        },
        "image_layout": {
          "options__1": {
            "label": "Alternera från vänster"
          },
          "options__2": {
            "label": "Alternera från höger"
          },
          "options__3": {
            "label": "Vänsterjusterad"
          },
          "options__4": {
            "label": "Högerjusterad"
          },
          "label": "Placering av datorbild",
          "info": "Placeringen optimeras automatiskt för mobilen."
        },
        "container_color_scheme": {
          "label": "Färgschema för container"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vänster"
          },
          "options__2": {
            "label": "Centrera"
          },
          "options__3": {
            "label": "Höger"
          },
          "label": "Linjering av innehåll på mobil"
        },
        "header_mobile": {
          "content": "Mobil layout"
        }
      },
      "blocks": {
        "row": {
          "name": "Rad",
          "settings": {
            "image": {
              "label": "Bild"
            },
            "caption": {
              "label": "Rubrik"
            },
            "heading": {
              "label": "Rubrik"
            },
            "text": {
              "label": "Text"
            },
            "button_label": {
              "label": "Knappetikett"
            },
            "button_link": {
              "label": "Knapplänk"
            }
          }
        }
      },
      "presets": {
        "name": "Flera rader"
      }
    }
  }
}
