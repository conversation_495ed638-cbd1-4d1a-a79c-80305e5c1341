

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557276193259258712.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-557276193259258712.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557276193259258712.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-557276193259258712.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557276193259258712.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557276193259258712.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557276193259258712.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557276193259258712.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557276193259258712.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557276193259258712.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557276193259258712.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-557276193259258712.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-557276193259258712.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557276193259258712.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-557276193259258712.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-557276193259258712.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-557276193259258712.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557276193259258712.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-557276193259258712.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-557276193259258712.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-557276193259258712.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557276193259258712.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557276193259258712.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557276193259258712.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557276193259258712.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-557276193259258712.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557276193259258712.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557276193259258712.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557276193259258712.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-557276193259258712.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557276193259258712.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-557276193259258712.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557276193259258712.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557276193259258712.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-557276193259258712.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-557276193259258712.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557276193259258712.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557276193259258712.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557276193259258712.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557276193259258712.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557276193259258712.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557276193259258712.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557276193259258712.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557276193259258712.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-557276193259258712.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557276193259258712.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-557276193259258712.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557276193259258712.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-557276193259258712.gps.gpsil [style*="--z:"]{z-index:var(--z)}.gps-557276193259258712.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-557276193259258712.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-557276193259258712.gps.gpsil [style*="--bgi-tablet:"]{background-image:var(--bgi-tablet)}.gps-557276193259258712.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557276193259258712.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-557276193259258712.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-557276193259258712.gps.gpsil [style*="--pc-tablet:"]{place-content:var(--pc-tablet)}.gps-557276193259258712.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-557276193259258712.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557276193259258712.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557276193259258712.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-557276193259258712.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-557276193259258712.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-557276193259258712.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-557276193259258712.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-557276193259258712.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-557276193259258712.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557276193259258712.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557276193259258712.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-557276193259258712.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-557276193259258712.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-557276193259258712.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557276193259258712.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557276193259258712.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-557276193259258712.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-557276193259258712.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-557276193259258712.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-557276193259258712 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-557276193259258712 .gp-invisible{visibility:hidden}.gps-557276193259258712 .gp-absolute{position:absolute}.gps-557276193259258712 .gp-relative{position:relative}.gps-557276193259258712 .gp-top-0{top:0}.gps-557276193259258712 .gp-z-1{z-index:1}.gps-557276193259258712 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557276193259258712 .gp-mb-0{margin-bottom:0}.gps-557276193259258712 .gp-flex{display:flex}.gps-557276193259258712 .gp-inline-flex{display:inline-flex}.gps-557276193259258712 .gp-grid{display:grid}.gps-557276193259258712 .\!gp-hidden{display:none!important}.gps-557276193259258712 .gp-hidden{display:none}.gps-557276193259258712 .gp-h-full{height:100%}.gps-557276193259258712 .gp-w-full{width:100%}.gps-557276193259258712 .gp-max-w-full{max-width:100%}.gps-557276193259258712 .gp-shrink-0{flex-shrink:0}.gps-557276193259258712 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557276193259258712 .gp-flex-row-reverse{flex-direction:row-reverse}.gps-557276193259258712 .gp-flex-col{flex-direction:column}.gps-557276193259258712 .gp-items-center{align-items:center}.gps-557276193259258712 .\!gp-justify-center{justify-content:center!important}.gps-557276193259258712 .gp-justify-center{justify-content:center}.gps-557276193259258712 .gp-gap-y-0{row-gap:0}.gps-557276193259258712 .gp-overflow-hidden{overflow:hidden}.gps-557276193259258712 .gp-break-words{overflow-wrap:break-word}.gps-557276193259258712 .gp-rounded-none{border-radius:0}.gps-557276193259258712 .gp-text-center{text-align:center}.gps-557276193259258712 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-557276193259258712 .gp-no-underline{text-decoration-line:none}.gps-557276193259258712 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259258712 .gp-duration-200{transition-duration:.2s}.gps-557276193259258712 .gp-duration-300{transition-duration:.3s}.gps-557276193259258712 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259258712 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-557276193259258712 .hover\:gp-bg-g-highlight:hover{background-color:var(--g-c-highlight)}}.gps-557276193259258712 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-557276193259258712 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-557276193259258712 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-557276193259258712 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-557276193259258712 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-557276193259258712 .tablet\:gp-absolute{position:absolute}.gps-557276193259258712 .tablet\:\!gp-hidden{display:none!important}.gps-557276193259258712 .tablet\:gp-hidden{display:none}.gps-557276193259258712 .tablet\:\!gp-content-stretch{align-content:stretch!important}.gps-557276193259258712 .tablet\:\!gp-justify-center{justify-content:center!important}}@media (max-width:767px){.gps-557276193259258712 .mobile\:gp-relative{position:relative}.gps-557276193259258712 .mobile\:\!gp-hidden{display:none!important}.gps-557276193259258712 .mobile\:gp-hidden{display:none}.gps-557276193259258712 .mobile\:\!gp-content-stretch{align-content:stretch!important}.gps-557276193259258712 .mobile\:\!gp-justify-center{justify-content:center!important}}@media (max-width:1024px){.gps-557276193259258712 .tablet\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}@media (max-width:767px){.gps-557276193259258712 .mobile\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}.gps-557276193259258712 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-557276193259258712 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-557276193259258712 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-557276193259258712 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-557276193259258712 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557276193259258712 .\[\&_p\]\:gp-inline p{display:inline}.gps-557276193259258712 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-557276193259258712 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557276193259258712 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gy3QoBea7O" data-id="gy3QoBea7O"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gy3QoBea7O gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ghFTmDIJKs gp-relative gp-flex gp-flex-col"
    >
      
    <div class="gp-flex gp-w-full !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center" style="--h:auto;--h-tablet:auto;--h-mobile:500px;--d:flex;--d-mobile:none;--d-tablet:none;--mt:auto">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"backupFileKey":"gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png","height":800,"src":"https://cdn.shopify.com/s/files/1/0823/8599/4056/files/gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png","storage":"FILE_CONTENT","width":1800},"lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"backupFileKey":"gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png","height":800,"src":"https://cdn.shopify.com/s/files/1/0823/8599/4056/files/gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png","storage":"FILE_CONTENT","width":1800},"lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"backupFileKey":"gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png","height":800,"src":"https://cdn.shopify.com/s/files/1/0823/8599/4056/files/gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png","storage":"FILE_CONTENT","width":1800},"lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"gADtvqGrsn","enableParallax":false,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"},"mobile":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"center"},"contentPosition2Col":{"desktop":"center"},"aspectRatio":{"desktop":"16/9","mobile":"auto"}}'
        gp-href=""
        
        class="gADtvqGrsn gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:none;--d-tablet:none;--op:100%;--pt:auto;--h:auto;--h-tablet:auto;--h-mobile:500px;--w:var(--g-ct-w);--w-tablet:var(--g-ct-w);--w-mobile:100%;--aspect:auto;--aspect-tablet:auto"
        data-id="gADtvqGrsn"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center"
        style="--h:auto;--h-tablet:auto;--h-mobile:500px;--aspect:auto;--aspect-tablet:auto;--bs:solid;--bw:0px 0px 0px 0px;--bc:#000000;--bgc:var(--g-c-bg-2);--shadow:none"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xODAwLTE4MDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE4MDAiIGhlaWdodD0iMTgwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiBmaWxsPSJ1cmwoI2ctMTgwMC0xODAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTgwMCIgdG89IjE4MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xODAwLTE4MDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE4MDAiIGhlaWdodD0iMTgwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiBmaWxsPSJ1cmwoI2ctMTgwMC0xODAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTgwMCIgdG89IjE4MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        title
        class="adaptive-hero-banner gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwMCIgaGVpZ2h0PSI4MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4MDAtODAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxODAwIiBoZWlnaHQ9IjgwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgwMCIgaGVpZ2h0PSI4MDAiIGZpbGw9InVybCgjZy0xODAwLTgwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4MDAiIHRvPSIxODAwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+"
        data-src="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}"
        width="100%"
        alt=""
        style="--aspect:auto;--aspect-tablet:auto;--d:block;--d-tablet:block;--d-mobile:none;--h:auto;--h-tablet:auto;--h-mobile:500px;--w:var(--g-ct-w);--w-tablet:var(--g-ct-w);--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0 round 0px 0px 0px 0px)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background gp_lazybg"
              style="--bgi:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwMCIgaGVpZ2h0PSI4MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4MDAtODAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxODAwIiBoZWlnaHQ9IjgwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgwMCIgaGVpZ2h0PSI4MDAiIGZpbGw9InVybCgjZy0xODAwLTgwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4MDAiIHRvPSIxODAwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+);--bgi-tablet:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xODAwLTE4MDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE4MDAiIGhlaWdodD0iMTgwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiBmaWxsPSJ1cmwoI2ctMTgwMC0xODAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTgwMCIgdG89IjE4MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--bgi-mobile:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xODAwLTE4MDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE4MDAiIGhlaWdodD0iMTgwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgwMCIgaGVpZ2h0PSIxODAwIiBmaWxsPSJ1cmwoI2ctMTgwMC0xODAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTgwMCIgdG89IjE4MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--bgc:var(--g-c-bg-2);--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
  <img
      
      
      draggable="false"
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg   gp_lazyload"
      data-src="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" data-srcset="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="lazy image desktop"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
  <img
      
      
      draggable="false"
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg gp_lazybg_tl  gp_lazyload"
      data-src="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" data-srcset="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="lazy image tablet"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
  <img
      
      
      draggable="false"
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg  gp_lazybg_mb gp_lazyload"
      data-src="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" data-srcset="{{ "gempages_523685320072364842-e3ab9686-f6a1-4109-bdec-b46a0f696d44.png" | file_url }}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="lazy image mobile"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
            </div></div>
          </div>
          
          </div>
          
       
      
    <div
      id data-id
        style="--pl:0px;--pr:0px;--pt:32px;--pb:32px;--pl-tablet:16px;--pr-tablet:16px;--pt-tablet:16px;--pb-tablet:16px;--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:center;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full gp-absolute tablet:gp-absolute mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-center gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center;--o:1"
      class="g42c1dd1tV gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdvGFClAs4">
    <div
      parentTag="Col"
        class="gdvGFClAs4 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggdvGFClAs4_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button style="--pos:static;--top:0px">
  <div
    style="--bs:none;--bw:5px 5px 5px 5px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;--mt:-46px;--pt:0px;--ta:center"
    
  >
    <style>
    .gcl0x5fWQ1.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 16px;
      border-bottom-right-radius: 16px;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      
    }

    .gcl0x5fWQ1:hover::before {
      
      
    }

    .gcl0x5fWQ1:hover .gp-button-icon {
      color: undefined;
    }

     .gcl0x5fWQ1 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gcl0x5fWQ1:hover .gp-button-price {
      color: undefined;
    }

    .gcl0x5fWQ1 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gcl0x5fWQ1 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gcl0x5fWQ1:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/products/firmandtone" target="_self" data-id="gcl0x5fWQ1" aria-label="<p>Claim Offer</p>"
      
      data-state="idle"
      class="gcl0x5fWQ1 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none hover:gp-bg-g-highlight gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:var(--g-c-highlight, highlight);--bg:#121212;--bblr:16px;--bbrr:16px;--btlr:16px;--btrr:16px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:36px;--pr:36px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:21px;--size-tablet:16px;--size-mobile:14px;--ff:var(--g-font-42dot-Sans, '42dot Sans'), var(--g-font-body, body);--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold"
    >
      
    <div
    class="gp-inline-flex gp-flex-row-reverse">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--ml:8px;--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817550824440168">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M224.49,136.49l-72,72a12,12,0,0,1-17-17L187,140H40a12,12,0,0,1,0-24H187L135.51,64.48a12,12,0,0,1,17-17l72,72A12,12,0,0,1,224.49,136.49Z" /></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:21px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggcl0x5fWQ1_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@import url('https://fonts.googleapis.com/css?family=42dot+Sans:400&display=swap');</style>

{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-557276193259258712 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=557276193259258712)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggdvGFClAs4_text","label":"ggdvGFClAs4_text","default":"This is your text block. Click to edit and make it your own. Share your product's story<br/>                  or services offered. Get creative and make it yours!"},{"type":"html","id":"ggcl0x5fWQ1_label","label":"ggcl0x5fWQ1_label","default":"<p>Claim Offer</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
