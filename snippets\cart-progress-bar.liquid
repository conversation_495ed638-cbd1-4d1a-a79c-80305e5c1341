{% liquid
  assign is_quantity = false
  if block.settings.goal_type == 'quantity'
    assign is_quantity = true
  endif

  if is_quantity
    assign goal_multiplier = 1.0
    assign goal_max_reference = items_count
  else
    assign goal_multiplier = 100.00
    assign goal_max_reference = cart.items_subtotal_price
  endif
  
  assign goal = block.settings.goal | times: goal_multiplier
  assign goal_progress = goal_max_reference | divided_by: goal | times: 100.0

  assign goal_reached = false
  if goal_progress >= 100.00
    assign goal_reached = true
    assign goal_progress = 100
  endif
%}

{% capture goal_difference_html %}
  {% if is_quantity %}
    {{ goal | minus: items_count | round }}
  {% else %}
    <span>{{ goal | minus: cart.items_subtotal_price | money_without_trailing_zeros }}</span>
  {% endif %}
{% endcapture %}

<div class="cart-progress accent-color-{{ block.settings.accent_color }}" style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;" {{ block.shopify_attributes }}>
  <p class="cart-progress__text">
    {% if goal_reached == false %}
      {{ block.settings.progress_message | replace: '[amount]', goal_difference_html }}
    {% else %}
      {{ block.settings.success_message }}
    {% endif %}
  </p>
  <div class="cart-progress__bar">
    <div class="cart-progress__bar__progress" style="width: {{ goal_progress }}%;">
      <div class="cart-progress__bar__badge">
        {%- if block.settings.custom_icon != blank -%}
          <img
            src="{{ block.settings.custom_icon | image_url }}"
            {% if block.settings.custom_icon.alt != blank %}
              alt="{{ block.settings.custom_icon.alt | escape }}"
            {% else %}
              role="presentation"
            {% endif %}
            height="200"
            width="200"
            loading="lazy"
          >
        {% elsif block.settings.icon != blank %}
          <span class="material-symbols-outlined{% if block.settings.icon_filled %} filled{% endif %}">
            {{ block.settings.icon | strip | downcase | replace: ' ', '_' }}
          </span>
        {%- endif -%}
      </div>
    </div>
  </div>
</div>
