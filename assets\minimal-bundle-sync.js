/**
 * Minimal Bundle Synchronization System
 * Lightweight, optimized for performance, no memory leaks
 */

class MinimalBundleSync {
  constructor() {
    this.isProcessing = false;
    this.init();
  }

  init() {
    // Use event delegation with minimal processing
    document.addEventListener('click', this.handleClick.bind(this), { passive: false });
    console.log('🔗 Minimal Bundle Sync initialized');
  }

  handleClick(event) {
    // Only process if in cart area
    if (!event.target.closest('.cart-drawer, .cart-page, #cart-drawer')) return;
    
    // Check if it's a remove button (any button that might remove items)
    const button = event.target.closest('button');
    if (!button) return;
    
    // Look for common remove button patterns
    const className = button.className || '';
    const isRemoveButton = className.includes('remove') || 
                          className.includes('delete') || 
                          className.includes('trash') || 
                          className.includes('Trash') || 
                          className.includes('Delete');
    
    if (isRemoveButton) {
      this.handleRemove(button, event);
    }
  }

  handleRemove(button, event) {
    if (this.isProcessing) return;

    // Find the cart item
    const cartItem = button.closest('.cart-item, .cart-drawer-item, [id*="CartDrawer-Item"]');
    if (!cartItem) return;

    // Check for bundle ID
    const bundleId = this.getBundleId(cartItem);
    if (!bundleId) return;

    console.log(`🗑️ Bundle removal triggered: ${bundleId}`);
    
    // Prevent default removal
    event.preventDefault();
    event.stopPropagation();
    
    // Remove entire bundle
    this.removeBundle(bundleId);
  }

  getBundleId(cartItem) {
    // Check data attribute
    if (cartItem.dataset.bundleId) {
      return cartItem.dataset.bundleId;
    }

    // Check for Bundle ID in text content
    const text = cartItem.textContent || '';
    const match = text.match(/Bundle ID:\s*([^\s\n]+)/);
    return match ? match[1] : null;
  }

  async removeBundle(bundleId) {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      // Get current cart
      const response = await fetch('/cart.js');
      const cart = await response.json();
      
      // Find bundle items
      const bundleItems = cart.items.filter(item => 
        (item.properties['Bundle ID'] === bundleId) || 
        (item.properties._bundle_id === bundleId)
      );

      if (bundleItems.length === 0) {
        console.log('No bundle items found');
        return;
      }

      console.log(`Removing ${bundleItems.length} bundle items`);

      // Remove all bundle items
      for (const item of bundleItems) {
        await fetch('/cart/change.js', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: item.key, quantity: 0 })
        });
      }

      // Trigger cart update
      document.dispatchEvent(new CustomEvent('cart:updated'));
      
      console.log(`✅ Bundle removed: ${bundleId}`);

    } catch (error) {
      console.error('Bundle removal failed:', error);
    } finally {
      this.isProcessing = false;
    }
  }
}

// Initialize
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.minimalBundleSync = new MinimalBundleSync();
  });
} else {
  window.minimalBundleSync = new MinimalBundleSync();
}

// Manual test
window.testMinimalBundleSync = function() {
  console.log('Testing minimal bundle sync...');
  const buttons = document.querySelectorAll('button');
  console.log(`Found ${buttons.length} buttons`);
  
  const removeButtons = Array.from(buttons).filter(btn => {
    const className = btn.className || '';
    return className.includes('remove') || 
           className.includes('delete') || 
           className.includes('trash') || 
           className.includes('Trash') || 
           className.includes('Delete');
  });
  
  console.log(`Found ${removeButtons.length} potential remove buttons`);
  removeButtons.forEach((btn, i) => {
    console.log(`${i + 1}. ${btn.className}`);
  });
};
