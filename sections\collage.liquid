{{ 'collage.css' | asset_url | stylesheet_tag }}
{{ 'component-modal-video.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient isolate content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width{% if section.settings.title == blank %} no-heading{% endif %} section-{{ section.id }}-padding">
    <h2 class="collage-wrapper-title {{ section.settings.heading_size }} title-with-highlight animate-item animate-item--child index-0" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
      {{ section.settings.title }}
    </h2>
    <div class="collage{% if section.settings.mobile_layout == 'collage' %} collage--mobile{% endif %}">
      {%- for block in section.blocks -%}
        <div
          class="collage__item collage__item--{{ block.type }} collage__item--{{ section.settings.desktop_layout }} animate-item animate-item--child"
          style="--index:{{ forloop.index }};"
          {{ block.shopify_attributes }}
        >
          {%- case block.type -%}
            {%- when 'image' -%}
              <div class="collage-card {% if section.settings.card_styles == 'none' %}global-media-settings{% else %}card-wrapper {{ section.settings.card_styles }} color-{{ settings.card_color_scheme }}{% endif %}">
                <div
                  class="media media--transparent ratio"
                  {% if block.settings.image != blank %}
                    style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%"
                  {% else %}
                    style="--ratio-percent: 100%"
                  {% endif %}
                >
                  {%- if block.settings.image != blank -%}
                    {%- capture sizes -%}(min-width: {{ settings.page_width }}px) {% if section.blocks.size == 1 %}calc({{ settings.page_width }}px - 100px){% else %}{{ settings.page_width | minus: 100 | times: 0.67 | round }}px{% endif %}, (min-width: 750px){% if section.blocks.size == 1 %} calc(100vw - 100px){% else %} 500px{% endif %}, calc(100vw - 30px){%- endcapture -%}
                    {{
                      block.settings.image
                      | image_url: width: 3000
                      | image_tag: loading: 'lazy', sizes: sizes, widths: '550, 720, 990, 1100, 1500, 2200, 3000'
                    }}
                  {%- else -%}
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                  {%- endif -%}
                </div>
              </div>
            {%- when 'product' -%}
              {% render 'card-product',
                card_product: block.settings.product,
                media_aspect_ratio: 'adapt',
                show_secondary_image: block.settings.second_image,
                extend_height: true
              %}
            {%- when 'collection' -%}
              {% render 'card-collection',
                card_collection: block.settings.collection,
                media_aspect_ratio: 'adapt',
                columns: 2,
                extend_height: true,
                wrapper_class: section.settings.card_styles
              %}
            {%- when 'video' -%}
              <div class="collage-card {% if section.settings.card_styles == 'none' %}global-media-settings{% else %}{{ section.settings.card_styles }} color-{{ settings.card_color_scheme }}{% endif %}">
                <noscript>
                  <a
                    href="{{ block.settings.video_url }}"
                    class="collage-card__link"
                  >
                    <div
                      class="media media--transparent ratio"
                      {% if block.settings.cover_image != blank %}
                        style="--ratio-percent: {{ 1 | divided_by: block.settings.cover_image.aspect_ratio | times: 100 }}%"
                      {% else %}
                        style="--ratio-percent: 100%"
                      {% endif %}
                    >
                      {%- if block.settings.cover_image != blank -%}
                        {%- capture sizes -%}
                          (min-width: {{ settings.page_width }}px)
                          {% if section.blocks.size == 1 -%}
                            calc({{ settings.page_width }}px - 100px)
                          {%- else -%}
                            {{- settings.page_width | minus: 100 | times: 0.67 | round }}px
                          {%- endif -%}
                          , (min-width: 750px)
                          {%- if section.blocks.size == 1 %} calc(100vw - 100px){% else %} 500px{% endif -%}
                          , calc(100vw - 30px)
                        {%- endcapture -%}
                        {{
                          block.settings.cover_image
                          | image_url: width: 3000
                          | image_tag: loading: 'lazy', sizes: sizes, widths: '550, 720, 990, 1100, 1500, 2200, 3000'
                        }}
                      {%- else -%}
                        {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                      {%- endif -%}
                    </div>
                  </a>
                </noscript>
                <modal-opener class="no-js-hidden" data-modal="#PopupModal-{{ block.id }}">
                  <div class="deferred-media">
                    <button
                      class="deferred-media__poster full-unstyled-link"
                      type="button"
                      aria-label="{{ 'sections.video.load_video' | t: description: block.settings.description | escape }}"
                      aria-haspopup="dialog"
                      data-media-id="{{ block.settings.video_url.id }}"
                    >
                      <div
                        class="media media--transparent ratio"
                        {% if block.settings.cover_image != blank %}
                          style="--ratio-percent: {{ 1 | divided_by: block.settings.cover_image.aspect_ratio | times: 100 }}%"
                        {% else %}
                          style="--ratio-percent: 100%"
                        {% endif %}
                      >
                        <span class="deferred-media__poster-button motion-reduce">
                          {%- render 'icon-play' -%}
                        </span>

                        {%- if block.settings.cover_image != blank -%}
                          {%- capture sizes -%}
                            (min-width: {{ settings.page_width }}px)
                            {% if section.blocks.size == 1 -%}
                              calc({{ settings.page_width }}px - 100px)
                            {%- else -%}
                              {{- settings.page_width | minus: 100 | times: 0.67 | round }}px
                            {%- endif -%}
                            , (min-width: 750px)
                            {%- if section.blocks.size == 1 %} calc(100vw - 100px){% else %} 500px{% endif -%}
                            , calc(100vw - 30px)
                          {%- endcapture -%}
                          {{
                            block.settings.cover_image
                            | image_url: width: 3000
                            | image_tag: loading: 'lazy', sizes: sizes, widths: '550, 720, 990, 1100, 1500, 2200, 3000'
                          }}
                        {%- else -%}
                          {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                        {%- endif -%}
                      </div>
                    </button>
                  </div>
                </modal-opener>
                <modal-dialog id="PopupModal-{{ block.id }}" class="modal-video media-modal color-background-1">
                  <div
                    class="modal-video__content"
                    role="dialog"
                    aria-label="{{ block.settings.description | escape }}"
                    aria-modal="true"
                    tabindex="-1"
                  >
                    <button
                      id="ModalClose-{{ block.id }}"
                      type="button"
                      class="modal-video__toggle"
                      aria-label="{{ 'accessibility.close' | t }}"
                    >
                      {% render 'icon-close' %}
                    </button>
                    <div class="modal-video__content-info">
                      <deferred-media class="modal-video__video template-popup">
                        <template>
                          {%- if block.settings.video_url.type == 'youtube' -%}
                            <iframe
                              src="https://www.youtube.com/embed/{{ block.settings.video_url.id }}?enablejsapi=1"
                              class="js-youtube"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.description | escape }}"
                            ></iframe>
                          {%- else -%}
                            <iframe
                              src="https://player.vimeo.com/video/{{ block.settings.video_url.id }}"
                              class="js-vimeo"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.description | escape }}"
                            ></iframe>
                          {%- endif -%}
                        </template>
                      </deferred-media>
                    </div>
                  </div>
                </modal-dialog>
              </div>
          {%- endcase -%}
        </div>
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.collage.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Multimedia collage",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "select",
      "id": "desktop_layout",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collage.settings.desktop_layout.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.collage.settings.desktop_layout.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.collage.settings.desktop_layout.label"
    },
    {
      "type": "select",
      "id": "mobile_layout",
      "options": [
        {
          "value": "collage",
          "label": "t:sections.collage.settings.mobile_layout.options__1.label"
        },
        {
          "value": "column",
          "label": "t:sections.collage.settings.mobile_layout.options__2.label"
        }
      ],
      "default": "column",
      "label": "t:sections.collage.settings.mobile_layout.label"
    },
    {
      "type": "select",
      "id": "card_styles",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collage.settings.card_styles.options__1.label"
        },
        {
          "value": "product-card-wrapper",
          "label": "t:sections.collage.settings.card_styles.options__2.label"
        }
      ],
      "default": "product-card-wrapper",
      "info": "t:sections.collage.settings.card_styles.info",
      "label": "t:sections.collage.settings.card_styles.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "To change the card color scheme, update your theme settings. Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "t:sections.collage.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.collage.blocks.image.settings.image.label"
        }
      ]
    },
    {
      "type": "product",
      "name": "t:sections.collage.blocks.product.name",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "t:sections.collage.blocks.product.settings.product.label"
        },
        {
          "type": "checkbox",
          "id": "second_image",
          "default": false,
          "label": "t:sections.collage.blocks.product.settings.second_image.label"
        }
      ]
    },
    {
      "type": "collection",
      "name": "t:sections.collage.blocks.collection.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collage.blocks.collection.settings.collection.label"
        }
      ]
    },
    {
      "type": "video",
      "name": "t:sections.collage.blocks.video.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "cover_image",
          "label": "t:sections.collage.blocks.video.settings.cover_image.label"
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": ["youtube", "vimeo"],
          "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
          "label": "t:sections.collage.blocks.video.settings.video_url.label",
          "placeholder": "t:sections.collage.blocks.video.settings.video_url.placeholder",
          "info": "t:sections.collage.blocks.video.settings.video_url.info"
        },
        {
          "type": "text",
          "id": "description",
          "default": "Describe the video",
          "label": "t:sections.collage.blocks.video.settings.description.label",
          "info": "t:sections.collage.blocks.video.settings.description.info"
        }
      ]
    }
  ],
  "max_blocks": 3,
  "presets": [
    {
      "name": "t:sections.collage.presets.name",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "product"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
