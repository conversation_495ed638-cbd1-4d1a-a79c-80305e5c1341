{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  .hotspots-image-container-{{ section.id }} .hotspot__content {
    left: calc(min(max({{ section.settings.hotspot_modal_mobile_width | divided_by: 20.0 | plus: 1 }}rem, var(--offset-x)), calc(100% - {{ section.settings.hotspot_modal_mobile_width | divided_by: 20.0 | plus: 1 }}rem)));
    width: 100%;
    max-width: {{ section.settings.hotspot_modal_mobile_width | divided_by: 10.0 | plus: 1 }}rem;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }

    .hotspots-image-container-{{ section.id }} .hotspot__content {
      left: var(--offset-x);
      max-width: {{ section.settings.hotspot_modal_desktop_width | divided_by: 10.0 }}rem;
      {% if section.settings.contain_hotspot_modal %}
        left: calc(min(max({{ section.settings.hotspot_modal_desktop_width | divided_by: 20.0 | plus: 1 }}rem, var(--offset-x)), calc(100% - {{ section.settings.hotspot_modal_desktop_width | divided_by: 20.0 | plus: 1 }}rem)));
      {% endif %}
    }
  }

  @media screen and (max-width: 749px) {
    .hotspots-image-container-{{ section.id }} .hotspot__content--fixed {
      position: fixed;
      max-width: none;
      width: 100%;
      left: 0;
      top: auto;
      bottom: 0;
      transform: none !important;
      z-index: 4;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }

  .section-color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_section_colors_background.red }}, {{ section.settings.custom_section_colors_background.green }}, {{ section.settings.custom_section_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_section_gradient_background != blank %}{{ section.settings.custom_section_gradient_background }}{% else %}{{ section.settings.custom_section_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

{% liquid
  assign content_empty = true
  if section.settings.title != blank or section.settings.text != blank or section.settings.button_label!= blank or section.settings.atc_button_label != blank
    assign content_empty = false
  endif

  assign content_same_bg = false
  if section.settings.section_color_scheme == section.settings.color_scheme
    assign content_same_bg = true
  endif
  if section.settings.section_color_scheme == 'custom' and section.settings.color_scheme == 'custom'
    unless section.settings.custom_colors_background == section.settings.custom_section_colors_background
      assign content_same_bg = false
    endunless
  endif

  if content_same_bg
    assign content_bg_class = 'same-bg'
  else
    assign content_bg_class = 'different-bg'
  endif
%}

<div class="section-color-scheme-{{ section.id }} color-{{ section.settings.section_color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="section-{{ section.id }}-padding{% unless section.settings.full_page_width %} page-width{% endunless %}">
    <div
      class="
        hotspots-image-container
        hotspots-image-container-{{ section.id }}
        hotspots-image-container--content-{{ section.settings.desktop_content_position }}
        hotspots-image-container--mobile-content-{{ section.settings.mobile_content_position }}
        hotspots-image--{{ content_bg_class }}
        hotspots-image-container--{{ section.settings.desktop_image_width }}
        animate-item
        {% if content_empty %} hotspots-image-container--content-empty{% endif %}
      "
      style="--max-width:{{ section.settings.image_max_width | divided_by: 10.0 }}rem"
    >
      <div
        class="hotspots-image__content{% if section.settings.hide_content_on_mobile %} mobile-hidden{% endif %}{% if section.settings.content_corner_radius %} content--border-radius{% endif %} gradient color-{{ section.settings.color_scheme }} color-scheme-{{ section.id }}"
      >
        <div class="content-rte hotspots-image__content-container {{ section.settings.desktop_content_alignment }}">
          {% if section.settings.title != blank %}
            <h2 class="{{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
              {{ section.settings.title }}
            </h2>
          {% endif %}
          {% if section.settings.text != blank %}
            <div class="rte">
              {{ section.settings.text }}
            </div>
          {% endif %}
          {%- if section.settings.button_label != blank -%}
            <a
              {% if section.settings.link %}
                href="{{ section.settings.link }}"
              {% else %}
                role="link" aria-disabled="true"
              {% endif %}
              class="button {% if section.settings.button_style_secondary %}button--secondary{% else %}button--primary{% endif %}"
            >
              {{- section.settings.button_label | escape -}}
            </a>
          {%- endif -%}
          {%- if section.settings.atc_button_label != blank -%}
            <button
              id="SectionAtcBtn-{{ section.id }}"
              type="button"
              class="button main-product-atc button--has-spinner"
              {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                disabled
              {% endif %}
            >
              {{ section.settings.atc_button_label | escape }}
              <div class="loading-overlay__spinner">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </button>
          {%- endif -%}
        </div>
      </div>
      <div
        class="hotspots-image"
        style="--max-width:{{ section.settings.image_max_width | divided_by: 10.0 }}rem"
      >
        {% if section.settings.image != blank %}
          <div class="hotspots-image__image{% if section.settings.mobile_image != blank %} mobile-hidden{% endif %}{% if section.settings.image_corner_radius %} image--corner-radius{% endif %}">
            <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%">
              {%- capture sizes -%}
                (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px,
                (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px))
              {%- endcapture -%}
              {{
                section.settings.image
                | image_url: width: 1500
                | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500, 2000'
              }}
            </div>
          </div>
          {% if section.settings.mobile_image != blank %}
            <div class="hotspots-image__mobile-image desktop-hidden{% if section.settings.image_corner_radius %} image--corner-radius{% endif %}">
              <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: section.settings.mobile_image.aspect_ratio | times: 100 }}%">
                {%- capture sizes -%}
                  (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px))
                {%- endcapture -%}
                {{
                  section.settings.mobile_image
                  | image_url: width: 1500
                  | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500, 2000'
                }}
              </div>
            </div>
          {% endif %}
        {% else %}
          {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
        {% endif %}

        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'hotspot' %}
              <hotspot-button
                class="hotspot hotspot--{{ section.settings.mobile_modal_position }}{% if block.settings.enable_mobile_position %} hotspot--mobile-position{% endif %}"
                data-type="product"
                data-open-event="{{ section.settings.hotspot_open_event }}"
                data-open="false"
                data-section="{{ section.id }}"
                style="--offset-x:{{ block.settings.offset_x }}%;--offset-y:{{ block.settings.offset_y }}%;--mobile-offset-x:{{ block.settings.mobile_offset_x }}%;--mobile-offset-y:{{ block.settings.mobile_offset_y }}%;"
                {{ block.shopify_attributes }}
              >
                <button
                  class="hotspot-btn color-{{ section.settings.hotspots_color_scheme }}{% if section.settings.display_hotspots_animation %} ripple-animation{% endif %}"
                >
                  {% render 'material-icon', icon: 'add' %}
                </button>
                {% if section.settings.mobile_modal_position == 'fixed' %}
                  <div class="hotspot-overlay">&nbsp</div>
                {% endif %}
                <div
                  class="hotspot__content hotspot__content--{{ section.settings.mobile_modal_position }} color-{{ section.settings.hotspot_content_color_scheme }} {{ block.settings.alignment }}"
                >
                  <div class="hotspot__content__image-and-text{% if section.settings.hotspot_image_size == 'small' %} hotspot__content__image-and-text--small-image{% endif %}">
                    {% if block.settings.product.featured_image != blank and section.settings.display_image %}
                      <div class="hotspot__content__image">
                        {{
                          block.settings.product.featured_image
                          | image_url: width: 750
                          | image_tag: loading: 'lazy'
                        }}
                      </div>
                    {% endif %}
                    <div class="hotspot__content__title-and_text">
                      <h3 class="hotspot__content__title hotspot__content__title-{{ section.settings.modal_heading_size }}">
                        {% if block.settings.product != blank %}
                          {{ block.settings.product.title }}
                        {% else %}
                          Product title
                        {% endif %}
                      </h3>
                      {% if section.settings.display_price %}
                        {% render 'price',
                          product: block.settings.product,
                          use_variant: false,
                          show_badges: section.settings.display_discount_badge
                        %}
                      {% endif %}
                      {% if block.settings.text != blank %}
                        <div class="hotspot__content__text rte">
                          {{ block.settings.text }}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  {% if section.settings.modal_button_label != blank and block.settings.product != blank %}
                    <div class="hotspot__content__btn">
                      {% if section.settings.button_function == 'atc' %}
                        {% assign product_form_id = 'section-product-form-'
                          | append: section.id
                          | append: forloop.index
                        %}
                        {% render 'separate-atc-btn',
                          product: block.settings.product,
                          product_form_id: product_form_id,
                          label: section.settings.modal_button_label,
                          class: 'button--full-width',
                          display_price: section.settings.display_price_in_btn,
                          skip_cart: section.settings.skip_cart
                        %}
                      {% else %}
                        <a
                          href="{{ block.settings.product.url }}"
                          class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                          {{ block.shopify_attributes }}
                        >
                          {{ section.settings.modal_button_label | escape }}
                        </a>
                      {% endif %}
                    </div>
                  {% endif %}
                </div>
              </hotspot-button>
          {% endcase %}
        {% endfor %}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Shoppable image",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Shoppable image",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Use this section to hightlight multiple products with one image that contains all of them</p>"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.slideshow.blocks.slide.settings.button_label.label",
      "info": "t:sections.slideshow.blocks.slide.settings.button_label.info"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:sections.slideshow.blocks.slide.settings.link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
      "default": false
    },
    {
      "type": "text",
      "id": "atc_button_label",
      "label": "Add to Cart button label",
      "info": "Leave the label blank to hide the Add to Cart button."
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "label": "Desktop content position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        },
        {
          "value": "left",
          "label": "Left"
        }
      ],
      "default": "top"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "label": "Desktop content alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "right"
        }
      ],
      "default": "center"
    },
    {
      "type": "checkbox",
      "id": "hide_content_on_mobile",
      "label": "Hide content on mobile",
      "default": false
    },
    {
      "type": "select",
      "id": "mobile_content_position",
      "label": "Mobile content position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "top"
    },
    {
      "type": "checkbox",
      "id": "content_corner_radius",
      "label": "Enable content corner radius",
      "default": true,
      "info": "This option is automatically optimized for all layout & color scheme settings."
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Content color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "select",
      "id": "section_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Section color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        {
          "value": "limited",
          "label": "Limited"
        },
        {
          "value": "unlimited",
          "label": "Maximum possible"
        }
      ],
      "label": "Desktop image width",
      "default": "limited"
    },
    {
      "type": "range",
      "id": "image_max_width",
      "min": 300,
      "max": 1000,
      "step": 50,
      "unit": "px",
      "label": "Image max width",
      "default": 900
    },
    {
      "type": "checkbox",
      "id": "full_page_width",
      "label": "Use full page width",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "image_corner_radius",
      "label": "Enable image corner radius",
      "default": false,
      "info": "This option is automatically optimized for all layout & color scheme settings."
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Mobile image",
      "info": "If this is empty, the image for desktop will be applied to mobile."
    },
    {
      "type": "header",
      "content": "Hotspot buttons & modals"
    },
    {
      "type": "select",
      "id": "hotspot_open_event",
      "options": [
        {
          "value": "click",
          "label": "Click"
        },
        {
          "value": "hover",
          "label": "Hover"
        }
      ],
      "label": "Open modal on:",
      "default": "click"
    },
    {
      "type": "select",
      "id": "hotspots_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "inverse",
      "label": "Hotspot buttons color scheme"
    },
    {
      "type": "checkbox",
      "id": "display_hotspots_animation",
      "label": "Display button ripple animation",
      "default": true
    },
    {
      "type": "select",
      "id": "hotspot_content_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "background-1",
      "label": "Hotspots modal color scheme"
    },
    {
      "type": "select",
      "id": "hotspot_image_size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "full",
          "label": "Full width"
        }
      ],
      "label": "Modal image width",
      "default": "small"
    },
    {
      "type": "checkbox",
      "id": "contain_hotspot_modal",
      "label": "Contain modals inside the main image",
      "default": false,
      "info": "Prevents modals from getting displayed outside the image on desktop. This option is automatically turned on for mobile."
    },
    {
      "type": "range",
      "id": "hotspot_modal_desktop_width",
      "min": 200,
      "max": 600,
      "step": 20,
      "unit": "px",
      "label": "Hotspot modal desktop width",
      "default": 400
    },
    {
      "type": "range",
      "id": "hotspot_modal_mobile_width",
      "min": 100,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Hotspot modal mobile width",
      "default": 300
    },
    {
      "type": "header",
      "content": "Product information"
    },
    {
      "type": "select",
      "id": "modal_heading_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "mobile_modal_position",
      "options": [
        {
          "value": "inline",
          "label": "Over/under the button"
        },
        {
          "value": "fixed",
          "label": "Fixed to page"
        }
      ],
      "default": "inline",
      "label": "Mobile modal position"
    },
    {
      "type": "checkbox",
      "id": "display_image",
      "label": "Display product featured image",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "display_price",
      "label": "Display price",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "display_discount_badge",
      "label": "Display price discount badge",
      "default": true
    },
    {
      "type": "text",
      "id": "modal_button_label",
      "label": "Button label",
      "default": "Add to Cart",
      "info": "Leave empty to hide the button"
    },
    {
      "type": "select",
      "id": "button_function",
      "options": [
        {
          "value": "link",
          "label": "Link to product page"
        },
        {
          "value": "atc",
          "label": "Add to cart"
        }
      ],
      "default": "atc",
      "label": "Button function"
    },
    {
      "type": "checkbox",
      "id": "display_price_in_btn",
      "label": "Display price in the button",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "skip_cart",
      "label": "Skip cart",
      "info": "Applied if button fucntion is set to Add to cart",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom content color scheme",
      "info": "Applied if Content color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#2E2A39",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#FFFFFF",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    },
    {
      "type": "header",
      "content": "Custom section color scheme",
      "info": "Applied if Section color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_section_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_section_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    }
  ],
  "blocks": [
    {
      "type": "hotspot",
      "name": "Product hotspot",
      "settings": [
        {
          "type": "header",
          "content": "Hotspot position"
        },
        {
          "type": "range",
          "id": "offset_x",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Horizontal offset",
          "default": 30
        },
        {
          "type": "range",
          "id": "offset_y",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Vertical offset",
          "default": 30
        },
        {
          "type": "header",
          "content": "Mobile hotspot position"
        },
        {
          "type": "checkbox",
          "id": "enable_mobile_position",
          "label": "Enable custom mobile positioning",
          "info": "Enable this option if you are using a separate image for mobile."
        },
        {
          "type": "range",
          "id": "mobile_offset_x",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Mobile horizontal offset",
          "default": 30
        },
        {
          "type": "range",
          "id": "mobile_offset_y",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Mobile vertical offset",
          "default": 30
        },
        {
          "type": "header",
          "content": "Product"
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Description"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Text alignment"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Shoppable image",
      "blocks": [
        {
          "type": "hotspot",
          "settings": {
            "offset_x": 30,
            "offset_y": 30
          }
        },
        {
          "type": "hotspot",
          "settings": {
            "offset_x": 40,
            "offset_y": 70
          }
        },
        {
          "type": "hotspot",
          "settings": {
            "offset_x": 70,
            "offset_y": 50
          }
        }
      ]
    }
  ]
}
{% endschema %}
