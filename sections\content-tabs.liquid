{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    {% unless section.settings.container_width == 'normal' %}
      max-width: {{ section.settings.container_width }}rem;
    {% endunless %}
  }
  .content-tabs-{{ section.id }} .content-tabs__buttons {
    font-size: {{ section.settings.buttons_mobile_font_size | divided_by: 10.0 }}rem;
    border-radius: {{ section.settings.button_border_radius | divided_by: 10.0 }}em;
  }
  .content-tabs-{{ section.id }} .content-tabs__buttons--outlined {
    border-radius: calc({{ section.settings.button_border_radius | divided_by: 10.0 }}em + var(--padding));
  }
  .content-tabs-{{ section.id }} .content-tab-button, .content-tabs-{{ section.id }} .content-tab-buttom__active-bg {
    border-radius: {{ section.settings.button_border_radius | divided_by: 10.0 }}em;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
    .content-tabs-{{ section.id }} .content-tab-button {
      font-size: {{ section.settings.buttons_desktop_font_size | divided_by: 10.0 }}rem;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}

  .tabs-color-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_tabs_color.red }}, {{ section.settings.custom_tabs_color.green }}, {{ section.settings.custom_tabs_color.blue }};
    --color-foreground: {{ section.settings.custom_tabs_color_label.red }}, {{ section.settings.custom_tabs_color_label.green }}, {{ section.settings.custom_tabs_color_label.blue }};
  }
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width section-{{ section.id }}-padding">
    <content-tabs 
      class='content-tabs content-tabs-{{ section.id }}'
      data-animation="{{ section.settings.active_button_animation }}"
    >
      <div class="content-tabs__header content-tabs__header--{{ section.settings.header_layout }}{% if section.settings.desktop_full_size_buttons %} content-tab-buttons--desktop-expand{% endif %}{% if section.settings.mobile_full_size_buttons %} content-tab-buttons--mobile-expand{% endif %}">
        {% assign animate_index = 0 %}
        {% if section.settings.title != blank or section.settings.text != blank %}
          {% assign animate_index = 1 %}
          <div class='content-tabs__heading-content content-rte animate-item animate-item--child index-0'>
            {%- unless section.settings.title == blank -%}
              <h2 class="content-tabs__heading {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
                {{ section.settings.title }}
              </h2>
            {%- endunless -%}
            {%- unless section.settings.text == blank -%}
              <div class='rte'>
                {{ section.settings.text }}
              </div>
            {%- endunless -%}
          </div>
        {% endif %}
        <div class='content-tabs__buttons flex-center content-tabs__buttons--{{ section.settings.buttons_style }} content-tabs__buttons--animation-{{ section.settings.active_button_animation }} tabs-color-{{ section.id }} color-{{ section.settings.buttons_color_scheme }} animate-item animate-item--child index-{{ animate_index }}'>
          {% for block in section.blocks %}
            <button class='content-tab-button flex-center{% if forloop.index0 == 0 %} content-tab-button--active{% endif %}' data-index="{{ forloop.index0 }}" {{ block.shopify_attributes }}>
              {% if block.settings.btn_icon != blank %}
                {% render 'material-icon', icon: block.settings.btn_icon, filled: block.settings.filled_btn_icon %}
              {%- endif -%}
              <span>{{ block.settings.btn_label }}</span>
            </button>
          {% endfor %}
          {% if section.settings.active_button_animation == 'moving' %}
            <div class='content-tab-buttom__active-bg'>&nbsp</div>
          {% endif %}
        </div>
      </div>
      <div class='content-tabs__tabs animate-item animate-item--child index-{{ animate_index | plus: 1 }}'>
        {% for block in section.blocks %}
          {% liquid
            assign has_media = false
            if block.settings.image != blank or block.settings.video != blank
              assign has_media = true
            endif
          %}
          <div class='content-tab{% if forloop.index0 == 0 %} content-tab--active{% endif %}{% if block.settings.desktop_media_width == 'half' %} content-tab--2-column{% endif %}' data-index="{{ forloop.index0 }}" {{ block.shopify_attributes}}>
            {% if has_media %}
              <div 
                class="content-tab__media media media--transparent ratio global-media-settings" 
                {% if block.settings.video != blank %}
                  style="--ratio-percent: {{ 1 | divided_by: block.settings.video.aspect_ratio | times: 100 }}%"
                {% else %}
                  style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%"
                {% endif %}
              >
                {% if block.settings.video != blank %}
                  {% render 'video-player', block: block %}
                {% else %}
                  {%- capture sizes -%}
                    (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                    (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px))
                  {%- endcapture -%}
                  {{
                    block.settings.image
                    | image_url: width: 1500
                    | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
                  }}
                {% endif %}
              </div>
            {% endif %}
            <div class='content-tab__content content-rte {{ block.settings.desktop_text_alignment }} {{ block.settings.mobile_text_alignment }}{% if block.settings.mobile_media_position == 'media_second' %} mobile-order-first{% endif %}{% if block.settings.desktop_media_position == 'media_second' %} desktop-order-first{% endif %}'>
              {% if block.settings.title != blank %}
                <h2 class="{{ block.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ block.settings.title_highlight_color }}'>
                  {{ block.settings.title }}
                </h2>
              {% endif %}
              {% if block.settings.text != blank %}
                <div class="rte">
                  {{ block.settings.text }}
                </div>
              {% endif %}
              {%- if block.settings.button_label != blank -%}
                <a
                  {% if block.settings.link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.link }}"
                  {% endif %}
                  class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.button_label | escape }}
                </a>
              {%- endif -%}
              {%- if block.settings.atc_button_label != blank -%}
                {% if block.settings.product == blank %}
                  <button
                    id="SectionAtcBtn-{{ section.id }}"
                    type="button"
                    class="button main-product-atc button--has-spinner"
                    {% if product == blank or product.selected_or_first_available_variant.available == false %}
                      disabled
                    {% endif %}
                  >
                    {{ block.settings.atc_button_label | escape }}
                    <div class="loading-overlay__spinner">
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        class="spinner"
                        viewBox="0 0 66 66"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                      </svg>
                    </div>
                  </button>
                {% else %}
                  {% assign product_form_id = 'section-product-form-'
                    | append: section.id
                    | append: forloop.index
                  %}
                  {% render 'separate-atc-btn',
                    product: block.settings.product,
                    product_form_id: product_form_id,
                    label: block.settings.atc_button_label,
                    skip_cart: block.settings.atc_skip_cart
                  %}
                {% endif %}
              {%- endif -%}
            </div>
          </div>
        {% endfor %}
      </div>
    </content-tabs>
  </div>
</div>

{% schema %}
{
  "name": "Content tabs",
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Content tabs",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Tabs"
    },
    {
      "type": "select",
      "id": "container_width",
      "options": [
        {
          "value": "80",
          "label": "Extra small"
        },
        {
          "value": "100",
          "label": "Small"
        },
        {
          "value": "120",
          "label": "Medium"
        },
        {
          "value": "normal",
          "label": "Normal"
        }
      ],
      "default": "100",
      "label": "Container width"
    },
    {
      "type": "select",
      "id": "buttons_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "accent-1",
      "label": "Tab buttons color",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Buttons corner radius",
      "default": 6
    },
    {
      "type": "select",
      "id": "buttons_style",
      "options": [
        {
          "value": "outlined",
          "label": "Outlined"
        },
        {
          "value": "shadow",
          "label": "Drop shadow"
        }
      ],
      "default": "outlined",
      "label": "Buttons container style"
    },
    {
      "type": "select",
      "id": "active_button_animation",
      "options": [
        {
          "value": "opacity",
          "label": "Opacity"
        },
        {
          "value": "moving",
          "label": "Moving"
        }
      ],
      "default": "moving",
      "label": "Active button animation type",
      "info": "Choosing \"Moving\" is only recommended if all your buttons fit in one row."
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "header_layout",
      "options": [
        {
          "value": "vertical",
          "label": "Vertical"
        },
        {
          "value": "horizontal",
          "label": "Horizontal"
        }
      ],
      "default": "horizontal",
      "label": "Desktop header layout"
    },
    {
      "type": "range",
      "id": "buttons_desktop_font_size",
      "min": 14,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Buttons font size",
      "default": 15
    },
    {
      "type": "checkbox",
      "id": "desktop_full_size_buttons",
      "label": "Expand buttons to full width",
      "default": false,
      "info": "Evently expands all buttons to fit whole container width. Recommended only if all your buttons fit in one row."
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "range",
      "id": "buttons_mobile_font_size",
      "min": 8,
      "max": 25,
      "step": 1,
      "unit": "px",
      "label": "Buttons font size",
      "default": 13
    },
    {
      "type": "checkbox",
      "id": "mobile_full_size_buttons",
      "label": "Expand buttons to full width",
      "default": true,
      "info": "Evently expands all buttons to fit whole container width. Recommended only if all your buttons fit in one row."
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    },
    {
      "type": "header",
      "content": "Custom tab buttons color",
      "info": "Applied if Tab buttons color setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_tabs_color",
      "default": "#000000",
      "label": "Tab buttons & conntainer border color"
    },
    {
      "type": "color",
      "id": "custom_tabs_color_label",
      "default": "#FFFFFF",
      "label": "Active button label color"
    }
  ],
  "blocks": [
    {
      "type": "tab",
      "name": "Tab",
      "settings": [
        {
          "type": "header",
          "content": "Tab button"
        },
        {
          "type": "inline_richtext",
          "id": "btn_label",
          "label": "Tab button label",
          "default": "Tab button"
        },
        {
          "type": "text",
          "id": "btn_icon",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_btn_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "header",
          "content": "Media"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "header",
          "content": "Or"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Video looping",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "muted_autoplay",
          "label": "Muted autoplay",
          "info": "Use this option instead of adding GIFs. The quality is better and the loading time is much quicker. Alternatively, if you disable this option, a play button will be displayed."
        },
        {
          "type": "select",
          "id": "btn_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "accent-1",
          "label": "Play button color scheme"
        },
        {
          "type": "checkbox",
          "id": "btn_animation",
          "label": "Enable button ripple animation",
          "default": false
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Tab heading",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h3",
              "label": "Extra small"
            },
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this section to display different benefits or different products that are sorted by clickable tabs.</p>"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.slideshow.blocks.slide.settings.button_label.label",
          "info": "t:sections.slideshow.blocks.slide.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.slideshow.blocks.slide.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
          "default": false
        },
        {
          "type": "text",
          "id": "atc_button_label",
          "label": "Add to Cart button label",
          "info": "Leave the label blank to hide the Add to Cart button."
        },
        {
          "type": "checkbox",
          "id": "atc_skip_cart",
          "label": "ATC Custom product skip cart"
        },
        {
          "type": "product",
          "id": "product",
          "label": "Add to Cart product",
          "info": "If empty, the product from the product page will be added in the cart (with the selected variant and quantity)."
        },
        {
          "type": "header",
          "content": "Desktop layout"
        },
        {
          "type": "select",
          "id": "desktop_media_width",
          "options": [
            {
              "value": "half",
              "label": "Half of the tab"
            },
            {
              "value": "full",
              "label": "Full width of the tab"
            }
          ],
          "default": "half",
          "label": "Desktop media (image/video) width"
        },
        {
          "type": "select",
          "id": "desktop_media_position",
          "options": [
            {
              "value": "media_first",
              "label": "Media first"
            },
            {
              "value": "media_second",
              "label": "Media second"
            }
          ],
          "default": "media_first",
          "label": "Desktop media placement"
        },
        {
          "type": "select",
          "id": "desktop_text_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Desktop text alignment"
        },
        {
          "type": "header",
          "content": "Mobile layout"
        },
        {
          "type": "select",
          "id": "mobile_media_position",
          "options": [
            {
              "value": "media_first",
              "label": "Media first"
            },
            {
              "value": "media_second",
              "label": "Media second"
            }
          ],
          "default": "media_first",
          "label": "Mobile media placement"
        },
        {
          "type": "select",
          "id": "mobile_text_alignment",
          "options": [
            {
              "value": "mobile-left",
              "label": "Left"
            },
            {
              "value": "mobile-center",
              "label": "Center"
            },
            {
              "value": "mobile-right",
              "label": "Right"
            }
          ],
          "default": "mobile-center",
          "label": "Mobile text alignment"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Content tabs",
      "blocks": [
        {
          "type": "tab"
        },
        {
          "type": "tab"
        },
        {
          "type": "tab"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
