/* Bundle Cart Grouping Styles */

.cart-bundle-group {
  border: 3px solid #4CAF50 !important;
  border-radius: 8px;
  margin-bottom: 1rem !important;
  background: #f0f8f0 !important;
  overflow: hidden;
  position: relative;
}

.cart-bundle-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: #f0f0f0;
  border-bottom: 1px solid #e0e0e0;
}

.cart-bundle-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cart-bundle-count {
  font-size: 0.875rem;
  color: #666;
  font-weight: normal;
}

.cart-bundle-remove {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  color: #999;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-bundle-remove:hover {
  color: #e74c3c;
}

.cart-bundle-remove .icon {
  width: 16px;
  height: 16px;
}

.cart-bundle-items {
  padding: 0;
}

.cart-bundle-item {
  border-bottom: 1px solid #e8e8e8;
  background: white;
  position: relative;
}

.cart-bundle-item:last-child {
  border-bottom: none;
}

.cart-bundle-item .cart-item__media {
  padding: 0.75rem;
}

.cart-bundle-item .cart-item__details {
  padding: 0.75rem;
  padding-left: 0;
}

.cart-bundle-item .cart-item__image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

/* Bundle badge for individual items */
.cart-bundle-item::before {
  content: "Bundle Item";
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #4CAF50;
  color: white;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 12px;
  font-weight: 500;
  z-index: 1;
}

/* Hide individual remove buttons for bundle items */
.cart-bundle-item .cart-drawer-item__cart-remove-button {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cart-bundle-header {
    padding: 0.5rem 0.75rem;
  }
  
  .cart-bundle-title {
    font-size: 0.9rem;
  }
  
  .cart-bundle-count {
    font-size: 0.8rem;
  }
  
  .cart-bundle-item .cart-item__image {
    width: 50px;
    height: 50px;
  }
}

/* Animation for bundle removal */
.cart-bundle-group.removing {
  opacity: 0.5;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

/* Bundle summary styling */
.cart-bundle-summary {
  padding: 0.75rem 1rem;
  background: #f8f8f8;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.cart-bundle-total-price {
  color: #2c5aa0;
  font-size: 1.1rem;
}

.cart-bundle-savings {
  color: #e74c3c;
  font-size: 0.875rem;
}

/* Hover effects */
.cart-bundle-group:hover {
  border-color: #ccc;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

/* Bundle collapse/expand functionality */
.cart-bundle-items.collapsed {
  display: none;
}

.cart-bundle-header.collapsible {
  cursor: pointer;
}

.cart-bundle-header.collapsible::after {
  content: "▼";
  margin-left: auto;
  transition: transform 0.2s ease;
}

.cart-bundle-header.collapsible.collapsed::after {
  transform: rotate(-90deg);
}
