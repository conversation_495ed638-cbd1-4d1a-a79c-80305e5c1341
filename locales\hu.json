/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "general": {
    "password_page": {
      "login_form_heading": "Belépés a webáruházba jelszóval:",
      "login_password_button": "<PERSON>ép<PERSON> jelszóval",
      "login_form_password_label": "Je<PERSON><PERSON><PERSON>",
      "login_form_password_placeholder": "Saját jelsz<PERSON>",
      "login_form_error": "Téves jels<PERSON>t írtál be.",
      "login_form_submit": "Belép<PERSON>",
      "admin_link_html": "Te vagy a webáruh<PERSON>z tula<PERSON>? <a href=\"/admin\" class=\"link underlined-link\">Itt tudsz bejelentkezni</a>",
      "powered_by_shopify_html": "A bolt szolgáltatója a {{ shopify }}"
    },
    "social": {
      "alt_text": {
        "share_on_facebook": "Megosztás a Facebookon",
        "share_on_twitter": "Közzététel a Twitteren",
        "share_on_pinterest": "Közzététel a Pinteresten"
      },
      "links": {
        "twitter": "Twitter",
        "facebook": "Facebook",
        "pinterest": "Pinterest",
        "instagram": "Instagram",
        "tumblr": "Tumblr",
        "snapchat": "Snapchat",
        "youtube": "YouTube",
        "vimeo": "Vimeo",
        "tiktok": "TikTok"
      }
    },
    "continue_shopping": "Vásárlás folytatása",
    "pagination": {
      "label": "Tördelés",
      "page": "{{ number }}. oldal",
      "next": "Következő oldal",
      "previous": "Előző oldal"
    },
    "search": {
      "search": "Keresés",
      "reset": "Keresőszó törlése"
    },
    "cart": {
      "view": "Kosár megtekintése ({{ count }})",
      "item_added": "Betettük a terméket a kosárba",
      "view_empty_cart": "Kosár megtekintése"
    },
    "share": {
      "copy_to_clipboard": "Hivatkozás másolása",
      "share_url": "Hivatkozás",
      "success_message": "A vágólapra másoltuk a hivatkozást.",
      "close": "Megosztás befejezése"
    },
    "slider": {
      "of": "/",
      "next_slide": "Következő dia",
      "previous_slide": "Előző dia",
      "name": "Csúszka"
    }
  },
  "newsletter": {
    "label": "E-mail-cím",
    "success": "Köszönjük a feliratkozást",
    "button_label": "Feliratkozás"
  },
  "accessibility": {
    "skip_to_text": "Ugrás a tartalomhoz",
    "close": "Bezárás",
    "unit_price_separator": "/",
    "vendor": "Forgalmazó:",
    "error": "Hiba",
    "refresh_page": "Ha kiválasztasz egy lehetőséget, a teljes oldal frissül.",
    "link_messages": {
      "new_window": "Tartalom megnyitása új ablakban.",
      "external": "Külső webhelyet nyit meg."
    },
    "loading": "Betöltés folyamatban…",
    "skip_to_product_info": "Kihagyás, és ugrás a termékadatokra",
    "total_reviews": "összes értékelés",
    "star_reviews_info": "{{ rating_max }}/{{ rating_value }} csillag",
    "collapsible_content_title": "Összecsukható tartalom",
    "complementary_products": "Kiegészítő termékek"
  },
  "blogs": {
    "article": {
      "blog": "Blog",
      "read_more_title": "Továbbiak: {{ title }}",
      "comments": {
        "one": "{{ count }} hozzászólás",
        "other": "{{ count }} hozzászólás"
      },
      "moderated": "Felhívjuk a figyelmedet, hogy a hozzászólásokat jóvá kell hagyni a közzétételük előtt.",
      "comment_form_title": "Hozzászólás írása",
      "name": "Név",
      "email": "E-mail-cím",
      "message": "Hozzászólás",
      "post": "Hozzászólás elküldése",
      "back_to_blog": "Vissza a blogba",
      "share": "Cikk megosztása",
      "success": "Elküldtük a hozzászólásodat. Köszönjük!",
      "success_moderated": "Elküldtük a hozzászólásodat. Blogunkat moderáljuk, ezért egy kis idő múlva tesszük csak közzé a hozzászólást."
    }
  },
  "onboarding": {
    "product_title": "Példa terméknévre",
    "collection_title": "Kollekció neve"
  },
  "products": {
    "product": {
      "add_to_cart": "Hozzáadás a kosárhoz",
      "description": "Leírás",
      "on_sale": "Akciós",
      "product_variants": "Termékváltozatok",
      "quantity": {
        "label": "Mennyiség",
        "input_label": "{{ product }} mennyisége",
        "increase": "{{ product }} mennyiségének növelése",
        "decrease": "{{ product }} mennyiségének csökkentése",
        "minimum_of": "Minimum: {{ quantity }}",
        "maximum_of": "Maximum: {{ quantity }}",
        "multiples_of": "Növekvés: {{ quantity }}",
        "in_cart_html": "Kosárban lévő mennyiség: <span class=\"quantity-cart\">{{ quantity }}</span>"
      },
      "price": {
        "from_price_html": "Legalacsonyabb ár: {{ price }}",
        "regular_price": "Normál ár",
        "sale_price": "Akciós ár",
        "unit_price": "Egységár"
      },
      "share": "A termék megosztása",
      "sold_out": "Elfogyott",
      "unavailable": "Nincs készleten",
      "vendor": "Forgalmazó",
      "video_exit_message": "{{ title }}: a teljes képernyős videó ugyanabban az ablakban nyílik meg.",
      "xr_button": "Megtekintés a saját környezetben",
      "xr_button_label": "Megtekintés a saját környezetben: kiterjesztettvalóság-alapú ablakban töltődik be az elem",
      "pickup_availability": {
        "view_store_info": "Webáruház adatai",
        "check_other_stores": "Kapható más webáruházakban?",
        "pick_up_available": "Személyesen átvehető",
        "pick_up_available_at_html": "Személyesen átvehető itt: <span class=\"color-foreground\">{{ location_name }}</span>",
        "pick_up_unavailable_at_html": "Személyesen egyelőre nem vehető át itt: <span class=\"color-foreground\">{{ location_name }}</span>",
        "unavailable": "Nem sikerült betölteni az átvehetőségi adatokat",
        "refresh": "Frissítés"
      },
      "media": {
        "open_media": "{{ index }}. médiafájl megnyitása a modális párbeszédpanelen",
        "play_model": "Lejátszás a 3D-megjelenítőben",
        "play_video": "Videó lejátszása",
        "gallery_viewer": "Galériamegjelenítő",
        "load_image": "{{ index }}. kép betöltése galérianézetben",
        "load_model": "{{ index }}. térhatású modell betöltése galérianézetben",
        "load_video": "{{ index }}. videó lejátszása galérianézetben",
        "image_available": "{{ index }}. kép betöltve galérianézetben"
      },
      "view_full_details": "Minden részlet megtekintése",
      "include_taxes": "Tartalmazza az adót.",
      "shipping_policy_html": "A <a href=\"{{ link }}\">szállítási költséget</a> a megrendeléskor számítjuk ki.",
      "choose_options": "Válassz a lehetőségek közül",
      "choose_product_options": "Termékváltozatok – {{ product_name }}",
      "value_unavailable": "{{ option_value }} – Nincs készleten",
      "variant_sold_out_or_unavailable": "A változat elfogyott vagy nincs készleten",
      "inventory_in_stock": "Raktáron",
      "inventory_in_stock_show_count": "{{ quantity }} raktáron",
      "inventory_low_stock": "Alacsony készlet",
      "inventory_low_stock_show_count": "Alacsony készlet: csak {{ quantity }} van raktáron",
      "inventory_out_of_stock": "Nincs készleten",
      "inventory_out_of_stock_continue_selling": "Raktáron",
      "sku": "Termékváltozat"
    },
    "modal": {
      "label": "Médiatár"
    },
    "facets": {
      "apply": "Alkalmaz",
      "clear": "Törlés",
      "clear_all": "Az összes eltávolítása",
      "from": "Ettől:",
      "filter_and_sort": "Szűrés és rendezés",
      "filter_by_label": "Szűrés:",
      "filter_button": "Szűrés",
      "filters_selected": {
        "one": "{{ count }} elem kijelölve",
        "other": "{{ count }} elem kijelölve"
      },
      "max_price": "A legmagasabb ár {{ price }}",
      "product_count": {
        "one": "{{ count }}/{{ product_count }} termék",
        "other": "{{ count }}/{{ product_count }} termék"
      },
      "product_count_simple": {
        "one": "{{ count }} termék",
        "other": "{{ count }} termék"
      },
      "reset": "Alaphelyzet",
      "sort_button": "Rendezés",
      "sort_by_label": "Rendezési szempont:",
      "to": "Eddig:",
      "clear_filter": "Szűrő eltávolítása",
      "filter_selected_accessibility": "{{ type }} ({{ count }} szűrő kiválasztva)",
      "show_more": "Több részlet",
      "show_less": "Kevesebb részlet"
    }
  },
  "templates": {
    "search": {
      "no_results": "Nincs találat erre: {{ terms }}. Ellenőrizd a helyesírást, vagy írj be egy másik szót vagy kifejezést.",
      "results_with_count": {
        "one": "{{ count }} találat",
        "other": "{{ count }} találat"
      },
      "title": "Találatok",
      "page": "Oldal",
      "products": "Termékek",
      "search_for": "Keresés erre: {{ terms }}",
      "results_with_count_and_term": {
        "one": "{{ count }} találat erre: {{ terms }}",
        "other": "{{ count }} találat erre: {{ terms }}"
      },
      "results_pages_with_count": {
        "one": "{{ count }} oldal",
        "other": "{{ count }} oldal"
      },
      "results_suggestions_with_count": {
        "one": "{{ count }} javaslat",
        "other": "{{ count }} javaslat"
      },
      "results_products_with_count": {
        "one": "{{ count }} termék",
        "other": "{{ count }} termék"
      },
      "suggestions": "Javaslatok",
      "pages": "Oldal"
    },
    "cart": {
      "cart": "Kosár"
    },
    "contact": {
      "form": {
        "name": "Név",
        "email": "E-mail-cím",
        "phone": "Telefonszám",
        "comment": "Hozzászólás",
        "send": "Küldés",
        "post_success": "Köszönjük, hogy írtál nekünk. A lehető legrövidebb időn belül válaszolni fogunk.",
        "error_heading": "Kérjük, helyesbítsd a következőket:",
        "title": "Kapcsolattartói űrlap"
      }
    },
    "404": {
      "title": "Nem találjuk az oldalt",
      "subtext": "404-es hiba történt."
    }
  },
  "sections": {
    "header": {
      "announcement": "Közlemény",
      "menu": "Menü",
      "cart_count": {
        "one": "{{ count }} elem",
        "other": "{{ count }} elem"
      }
    },
    "cart": {
      "title": "Kosár",
      "caption": "Kosárban lévő termékek",
      "remove_title": "{{ title }} eltávolítása",
      "subtotal": "Részösszeg:",
      "new_subtotal": "Új részösszeg:",
      "note": "Megjegyzések a rendeléssel kapcsolatban",
      "checkout": "Megrendelés",
      "empty": "A kosarad üres",
      "cart_error": "Hiba történt a kosár frissítése közben. Próbálkozz újra.",
      "cart_quantity_error_html": "Ebből a termékből legfeljebb {{ quantity }} darabot rakhatsz a kosárba.",
      "taxes_and_shipping_policy_at_checkout_html": "Az adókat és a <a href=\"{{ link }}\">szállítási költséget</a> a megrendeléskor számítjuk ki",
      "taxes_included_but_shipping_at_checkout": "Tartalmazza a megrendeléskor kiszámított adót és szállítási költséget.",
      "taxes_included_and_shipping_policy_html": "Tartalmazza az adót. A megrendeléskor kiszámított <a href=\"{{ link }}\">szállítási költség</a>.",
      "taxes_and_shipping_at_checkout": "Az adókat és a szállítási költséget a megrendeléskor számítjuk ki",
      "headings": {
        "product": "Termék",
        "price": "Ár",
        "total": "Végösszeg",
        "quantity": "Mennyiség",
        "image": "Termék képe"
      },
      "update": "Frissítés",
      "login": {
        "title": "Már van fiókod?",
        "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Jelentkezz be</a> a gyorsabb fizetéshez."
      }
    },
    "footer": {
      "payment": "Fizetési módok"
    },
    "featured_blog": {
      "view_all": "Az összes megtekintése",
      "onboarding_title": "Blogbejegyzés",
      "onboarding_content": "Itt foglalhatod össze a vásárlóidnak, miről szól a blogbejegyzésed"
    },
    "featured_collection": {
      "view_all": "Az összes megtekintése",
      "view_all_label": "Az ebben a kollekcióban szereplő összes termék megtekintése: {{ collection_name }}"
    },
    "collection_list": {
      "view_all": "Az összes megtekintése"
    },
    "collection_template": {
      "title": "Kollekció",
      "empty": "Nincs találat",
      "use_fewer_filters_html": "Használj kevesebb szűrőt, vagy <a class=\"{{ class }}\" href=\"{{ link }}\">távolítsd el az összeset</a>."
    },
    "video": {
      "load_video": "Videó betöltése: {{ description }}"
    },
    "slideshow": {
      "load_slide": "Dia betöltése",
      "previous_slideshow": "Előző dia",
      "next_slideshow": "Következő dia",
      "pause_slideshow": "Diavetítés megállítása",
      "play_slideshow": "Diavetítés indítása",
      "carousel": "Forgótár",
      "slide": "Dia"
    },
    "page": {
      "title": "Oldal címe"
    }
  },
  "localization": {
    "country_label": "Ország/régió",
    "language_label": "Nyelv",
    "update_language": "Nyelv módosítása",
    "update_country": "Ország/régió frissítése"
  },
  "customer": {
    "account": {
      "title": "Fiók",
      "details": "Fiókadatok",
      "view_addresses": "Címek megtekintése",
      "return": "Vissza a fiókadatokhoz"
    },
    "account_fallback": "Fiók",
    "activate_account": {
      "title": "Fiók aktiválása",
      "subtext": "A fiók aktiválásához hozz létre egy jelszót.",
      "password": "Jelszó",
      "password_confirm": "Jelszó megerősítése",
      "submit": "Fiók aktiválása",
      "cancel": "Meghívás elutasítása"
    },
    "addresses": {
      "title": "Címek",
      "default": "Alapértelmezett",
      "add_new": "Új cím hozzáadása",
      "edit_address": "Cím szerkesztése",
      "first_name": "Utónév",
      "last_name": "Vezetéknév",
      "company": "Cégnév",
      "address1": "1. cím",
      "address2": "2. cím",
      "city": "Település",
      "country": "Ország/régió",
      "province": "Megye",
      "zip": "Irányítószám",
      "phone": "Telefonszám",
      "set_default": "Beállítás alapértelmezett címként",
      "add": "Cím hozzáadása",
      "update": "Cím frissítése",
      "cancel": "Mégse",
      "edit": "Szerkesztés",
      "delete": "Törlés",
      "delete_confirm": "Biztos, hogy törlöd a címet?"
    },
    "log_in": "Bejelentkezés",
    "log_out": "Kijelentkezés",
    "login_page": {
      "cancel": "Mégse",
      "create_account": "Fiók létrehozása",
      "email": "E-mail-cím",
      "forgot_password": "Elfelejtetted a jelszavadat?",
      "guest_continue": "Tovább",
      "guest_title": "Folytatás vendégként",
      "password": "Jelszó",
      "title": "Felhasználónév",
      "sign_in": "Bejelentkezés",
      "submit": "Küldés"
    },
    "orders": {
      "title": "Korábbi rendelések",
      "order_number": "Megrendelés",
      "order_number_link": "Rendelés száma: {{ number }}",
      "date": "Dátum",
      "payment_status": "Fizetési állapot",
      "fulfillment_status": "Teljesítési állapot",
      "total": "Végösszeg",
      "none": "Még nem rendeltél semmit."
    },
    "recover_password": {
      "title": "Új jelszó létrehozása",
      "subtext": "Küldünk egy e-mailt, amellyel új jelszót készíthetsz magadnak.",
      "success": "E-mailben elküldtük a jelszó módosításához szükséges hivatkozást."
    },
    "register": {
      "title": "Fiók létrehozása",
      "first_name": "Utónév",
      "last_name": "Vezetéknév",
      "email": "E-mail-cím",
      "password": "Jelszó",
      "submit": "Létrehozás"
    },
    "reset_password": {
      "title": "Új fiókjelszó létrehozása",
      "subtext": "Írd be az új jelszót",
      "password": "Jelszó",
      "password_confirm": "Jelszó megerősítése",
      "submit": "Új jelszó készítése"
    },
    "order": {
      "title": "Megrendelés: {{ name }}",
      "date_html": "Megrendelés dátuma: {{ date }}",
      "cancelled_html": "Megrendelés lemondva: {{ date }}",
      "cancelled_reason": "Ok: {{ reason }}",
      "billing_address": "Számlázási cím",
      "payment_status": "Fizetési állapot",
      "shipping_address": "Szállítási cím",
      "fulfillment_status": "Teljesítési állapot",
      "discount": "Kedvezmény",
      "shipping": "Szállítás",
      "tax": "Adó",
      "product": "Termék",
      "sku": "Termékváltozat",
      "price": "Ár",
      "quantity": "Mennyiség",
      "total": "Végösszeg",
      "fulfilled_at_html": "Teljesítés dátuma: {{ date }}",
      "track_shipment": "Csomagkövetés",
      "tracking_url": "Hivatkozás a csomagkövetéshez",
      "tracking_company": "Futárszolgálat",
      "tracking_number": "Fuvarlevélszám",
      "subtotal": "Részösszeg",
      "total_duties": "Vámok",
      "total_refunded": "Visszatérítve"
    }
  },
  "gift_cards": {
    "issued": {
      "title": "Íme a(z) {{ shop }} boltban levásárolható, {{ value }} értékű ajándékkártyád!",
      "subtext": "Ajándékkártya",
      "gift_card_code": "Ajándékkártya kódja",
      "shop_link": "Vásárlás folytatása",
      "remaining_html": "Egyenleg: {{ balance }}",
      "add_to_apple_wallet": "Hozzáadás az Apple Wallethoz",
      "qr_image_alt": "Ezt a QR-kódot beszkennelve beválthatod az ajándékkártyát.",
      "copy_code": "Kód másolása",
      "expired": "Lejárt",
      "copy_code_success": "Sikeres volt a kód másolása",
      "print_gift_card": "Nyomtatás"
    }
  }
}
