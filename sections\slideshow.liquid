{%- if section.settings.slide_height == 'adapt_image' and section.blocks.first.settings.image != blank -%}
  {%- style -%}
    @media screen and (max-width: 749px) {
      #Slider-{{ section.id }}::before,
      #Slider-{{ section.id }} .media::before,
      #Slider-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
        {% if section.blocks.first.settings.mobile_image != blank  %}
          padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.mobile_image.aspect_ratio | times: 100 }}%;
        {% else %}
          padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
        {% endif %}
        content: '';
        display: block;
      }
    }

    @media screen and (min-width: 750px) {
      #Slider-{{ section.id }}::before,
      #Slider-{{ section.id }} .media::before {
        padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {%- endstyle -%}
{%- endif -%}

<slideshow-component
  class="slider-mobile-gutter{% if section.settings.layout == 'grid' %} page-width{% endif %}{% if section.settings.show_text_below %} mobile-text-below{% endif %} {{ section.settings.visibility }}"
  role="region"
  aria-roledescription="{{ 'sections.slideshow.carousel' | t }}"
  aria-label="{{ section.settings.accessibility_info | escape }}"
>
  {%- if section.settings.auto_rotate and section.blocks.size > 1 -%}
    <div class="slideshow__controls slideshow__controls--top slideshow__controls--desktop-{{ section.settings.desktop_pagination_position }}{% if section.settings.mobile_pagination_position == 'over' and section.settings.show_text_below == false %} slideshow__controls--mobile-over{% endif %} slider-buttons no-js-hidden{% if section.settings.show_text_below %} slideshow__controls--border-radius-mobile{% endif %}">
      <button
        type="button"
        class="slider-button slider-button--prev"
        name="previous"
        aria-label="{{ 'sections.slideshow.previous_slideshow' | t }}"
        aria-controls="Slider-{{ section.id }}"
      >
        {% render 'icon-caret' %}
      </button>
      <div class="slider-counter slider-counter--{{ section.settings.slider_visual }}{% if section.settings.slider_visual == 'counter' or section.settings.slider_visual == 'numbers' %} caption{% endif %}">
        {%- if section.settings.slider_visual == 'counter' -%}
          <span class="slider-counter--current">1</span>
          <span aria-hidden="true"> / </span>
          <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
          <span class="slider-counter--total">{{ section.blocks.size }}</span>
        {%- else -%}
          <div class="slideshow__control-wrapper">
            {%- for block in section.blocks -%}
              <button
                class="slider-counter__link slider-counter__link--{{ section.settings.slider_visual }} link"
                aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                aria-controls="Slider-{{ section.id }}"
              >
                {%- if section.settings.slider_visual == 'numbers' -%}
                  {{ forloop.index -}}
                {%- else -%}
                  <span class="dot"></span>
                {%- endif -%}
              </button>
            {%- endfor -%}
          </div>
        {%- endif -%}
      </div>
      <button
        type="button"
        class="slider-button slider-button--next"
        name="next"
        aria-label="{{ 'sections.slideshow.next_slideshow' | t }}"
        aria-controls="Slider-{{ section.id }}"
      >
        {% render 'icon-caret' %}
      </button>

      {%- if section.settings.auto_rotate -%}
        <button
          type="button"
          class="slideshow__autoplay slider-button no-js-hidden{% if section.settings.auto_rotate == false %} slideshow__autoplay--paused{% endif %}"
          aria-label="{{ 'sections.slideshow.pause_slideshow' | t }}"
        >
          {%- render 'icon-pause' -%}
          {%- render 'icon-play' -%}
        </button>
      {%- endif -%}
    </div>
    <noscript>
      <div class="slider-buttons">
        <div class="slider-counter">
          {%- for block in section.blocks -%}
            <a
              href="#Slide-{{ section.id }}-{{ forloop.index }}"
              class="slider-counter__link link"
              aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
            >
              {{ forloop.index }}
            </a>
          {%- endfor -%}
        </div>
      </div>
    </noscript>
  {%- endif -%}

  <div
    class="slideshow banner banner--{{ section.settings.slide_height }} grid grid--1-col slider slider--everywhere{% if section.settings.show_text_below %} banner--mobile-bottom{% endif %}{% if section.blocks.first.settings.image == blank %} slideshow--placeholder{% endif %}"
    id="Slider-{{ section.id }}"
    aria-live="polite"
    aria-atomic="true"
    data-autoplay="{{ section.settings.auto_rotate }}"
    data-speed="{{ section.settings.change_slides_speed }}"
  >
    {%- for block in section.blocks -%}
      <style>
        #Slide-{{ section.id }}-{{ forloop.index }} .banner__media::after {
          opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};
        }
        .color-scheme-{{ section.id }}-{{ block.id }}.color-custom {
          --color-background: {{ block.settings.custom_colors_background.red }}, {{ block.settings.custom_colors_background.green }}, {{ block.settings.custom_colors_background.blue }};
          --gradient-background: {% if block.settings.custom_gradient_background != blank %}{{ block.settings.custom_gradient_background }}{% else %}{{ block.settings.custom_colors_background }}{% endif %};
          --color-foreground: {{ block.settings.custom_colors_text.red }}, {{ block.settings.custom_colors_text.green }}, {{ block.settings.custom_colors_text.blue }};
          --color-button: {{ block.settings.custom_colors_solid_button_background.red }}, {{ block.settings.custom_colors_solid_button_background.green }}, {{ block.settings.custom_colors_solid_button_background.blue }};
          --color-button-text: {{ block.settings.custom_colors_solid_button_text.red }}, {{ block.settings.custom_colors_solid_button_text.green }}, {{ block.settings.custom_colors_solid_button_text.blue }};
          --color-base-outline-button-labels: {{ block.settings.custom_colors_outline_button.red }}, {{ block.settings.custom_colors_outline_button.green }}, {{ block.settings.custom_colors_outline_button.blue }};
        }
      </style>
      <div
        class="slideshow__slide grid__item grid--1-col slider__slide"
        id="Slide-{{ section.id }}-{{ forloop.index }}"
        {{ block.shopify_attributes }}
        role="group"
        aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
        aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
        tabindex="-1"
      >
        <div class="slideshow__media banner__media media{% if block.settings.image == blank %} placeholder{% endif %}">
          {%- if block.settings.image -%}
            {% liquid
              assign height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round 
              if block.settings.mobile_image != blank
                assign main_image_class = 'mobile-hidden'
              else 
                assign main_image_class = ''
              endif
            %}
            {{
              block.settings.image
              | image_url: width: 3840
              | image_tag:
                loading: 'lazy',
                height: height,
                sizes: '100vw',
                class: main_image_class,
                widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
            }}
            {% if block.settings.mobile_image != blank %}
              {% assign height = block.settings.mobile_image.width | divided_by: block.settings.mobile_image.aspect_ratio | round %}
              {{
                block.settings.mobile_image
                | image_url: width: 3840
                | image_tag:
                  loading: 'lazy',
                  height: height,
                  sizes: '100vw',
                  class: 'desktop-hidden',
                  widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
              }}
            {% endif %}
          {%- else -%}
            {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}
        </div>
        <div class="slideshow__text-wrapper banner__content banner__content--{{ block.settings.box_align }} page-width{% if block.settings.show_text_box == false %} banner--desktop-transparent{% endif %}">
          <div class="slideshow__text banner__box banner--transparent-{{ block.settings.transparent_container_color }} content-container content-container--full-width-mobile color-{{ block.settings.color_scheme }} color-scheme-{{ section.id }}-{{ block.id }} gradient slideshow__text--{{ block.settings.text_alignment }} slideshow__text-mobile--{{ block.settings.text_alignment_mobile }}">
            {%- if block.settings.heading != blank -%}
              <h2 class="banner__heading {{ block.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ block.settings.title_highlight_color }}'>
                {{ block.settings.heading }}
              </h2>
            {%- endif -%}
            {%- if block.settings.subheading != blank -%}
              <div class="banner__text" {{ block.shopify_attributes }}>
                <span>{{ block.settings.subheading }}</span>
              </div>
            {%- endif -%}
            {%- if block.settings.button_label != blank -%}
              <div class="banner__buttons">
                <a
                  {% if block.settings.link %}
                    href="{{ block.settings.link }}"
                  {% else %}
                    role="link" aria-disabled="true"
                  {% endif %}
                  class="button {% if block.settings.button_style_secondary %}button--secondary{% else %}button--primary{% endif %}"
                >
                  {{- block.settings.button_label -}}
                </a>
              </div>
            {%- endif -%}
            {%- if block.settings.atc_button_label != blank -%}
              <div class="banner__buttons">
                {% if block.settings.atc_product == blank %}
                  <button
                    id="SectionAtcBtn-{{ section.id }}-{{ forloop.index }}"
                    type="button"
                    class="button main-product-atc button--has-spinner"
                    {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                      disabled
                    {% endif %}
                  >
                    {{ block.settings.atc_button_label }}
                    <div class="loading-overlay__spinner">
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        class="spinner"
                        viewBox="0 0 66 66"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                      </svg>
                    </div>
                  </button>
                {% else %}
                  {% assign product_form_id = 'section-product-form-'
                    | append: section.id
                    | append: block.id
                  %}
                  {% render 'separate-atc-btn',
                    product: block.settings.atc_product,
                    product_form_id: product_form_id,
                    label: block.settings.atc_button_label,
                    skip_cart: block.settings.atc_skip_cart
                  %}
                {% endif %}
              </div>
            {%- endif -%}
          </div>
        </div>
      </div>
    {%- endfor -%}
  </div>

  {%- if section.blocks.size > 1 and section.settings.auto_rotate == false -%}
    <div class="slideshow__controls slideshow__controls--desktop-{{ section.settings.desktop_pagination_position }}{% if section.settings.mobile_pagination_position == 'over' and section.settings.show_text_below == false %} slideshow__controls--mobile-over{% endif %} slider-buttons no-js-hidden{% if section.settings.show_text_below %} slideshow__controls--border-radius-mobile{% endif %}">
      <button
        type="button"
        class="slider-button slider-button--prev"
        name="previous"
        aria-label="{{ 'sections.slideshow.previous_slideshow' | t }}"
        aria-controls="Slider-{{ section.id }}"
      >
        {% render 'icon-caret' %}
      </button>
      <div class="slider-counter slider-counter--{{ section.settings.slider_visual }}{% if section.settings.slider_visual == 'counter' or section.settings.slider_visual == 'numbers' %} caption{% endif %}">
        {%- if section.settings.slider_visual == 'counter' -%}
          <span class="slider-counter--current">1</span>
          <span aria-hidden="true"> / </span>
          <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
          <span class="slider-counter--total">{{ section.blocks.size }}</span>
        {%- else -%}
          <div class="slideshow__control-wrapper">
            {%- for block in section.blocks -%}
              <button
                class="slider-counter__link slider-counter__link--{{ section.settings.slider_visual }} link"
                aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                aria-controls="Slider-{{ section.id }}"
              >
                {%- if section.settings.slider_visual == 'numbers' -%}
                  {{ forloop.index -}}
                {%- else -%}
                  <span class="dot"></span>
                {%- endif -%}
              </button>
            {%- endfor -%}
          </div>
        {%- endif -%}
      </div>
      <button
        type="button"
        class="slider-button slider-button--next"
        name="next"
        aria-label="{{ 'sections.slideshow.next_slideshow' | t }}"
        aria-controls="Slider-{{ section.id }}"
      >
        {% render 'icon-caret' %}
      </button>

      {%- if section.settings.auto_rotate -%}
        <button
          type="button"
          class="slideshow__autoplay slider-button no-js-hidden{% if section.settings.auto_rotate == false %} slideshow__autoplay--paused{% endif %}"
          aria-label="{{ 'sections.slideshow.pause_slideshow' | t }}"
        >
          {%- render 'icon-pause' -%}
          {%- render 'icon-play' -%}
        </button>
      {%- endif -%}
    </div>
    <noscript>
      <div class="slider-buttons">
        <div class="slider-counter">
          {%- for block in section.blocks -%}
            <a
              href="#Slide-{{ section.id }}-{{ forloop.index }}"
              class="slider-counter__link link"
              aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
            >
              {{ forloop.index }}
            </a>
          {%- endfor -%}
        </div>
      </div>
    </noscript>
  {%- endif -%}
</slideshow-component>

{%- if request.design_mode -%}
  <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.slideshow.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "full_bleed",
          "label": "t:sections.slideshow.settings.layout.options__1.label"
        },
        {
          "value": "grid",
          "label": "t:sections.slideshow.settings.layout.options__2.label"
        }
      ],
      "default": "full_bleed",
      "label": "t:sections.slideshow.settings.layout.label"
    },
    {
      "type": "select",
      "id": "slide_height",
      "options": [
        {
          "value": "adapt_image",
          "label": "t:sections.slideshow.settings.slide_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.slideshow.settings.slide_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.slideshow.settings.slide_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.slideshow.settings.slide_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.slideshow.settings.slide_height.label"
    },
    {
      "type": "select",
      "id": "slider_visual",
      "options": [
        {
          "value": "dots",
          "label": "t:sections.slideshow.settings.slider_visual.options__2.label"
        },
        {
          "value": "counter",
          "label": "t:sections.slideshow.settings.slider_visual.options__1.label"
        },
        {
          "value": "numbers",
          "label": "t:sections.slideshow.settings.slider_visual.options__3.label"
        }
      ],
      "default": "dots",
      "label": "t:sections.slideshow.settings.slider_visual.label"
    },
    {
      "type": "select",
      "id": "desktop_pagination_position",
      "options": [
        {
          "value": "under",
          "label": "Under the section"
        },
        {
          "value": "over",
          "label": "Over the section"
        }
      ],
      "default": "over",
      "label": "Desktop pagination position"
    },
    {
      "type": "checkbox",
      "id": "auto_rotate",
      "label": "t:sections.slideshow.settings.auto_rotate.label",
      "default": false
    },
    {
      "type": "range",
      "id": "change_slides_speed",
      "min": 3,
      "max": 9,
      "step": 2,
      "unit": "s",
      "label": "t:sections.slideshow.settings.change_slides_speed.label",
      "default": 5
    },
    {
      "type": "header",
      "content": "t:sections.slideshow.settings.mobile.content"
    },
    {
      "type": "checkbox",
      "id": "show_text_below",
      "label": "Show content below images on mobile",
      "default": true
    },
    {
      "type": "select",
      "id": "mobile_pagination_position",
      "options": [
        {
          "value": "under",
          "label": "Under the section"
        },
        {
          "value": "over",
          "label": "Over the section"
        }
      ],
      "default": "under",
      "label": "Mobile pagination position",
      "info": "Over the section is only available if Show content below images on mobile is disabled."
    },
    {
      "type": "header",
      "content": "t:sections.slideshow.settings.accessibility.content"
    },
    {
      "type": "text",
      "id": "accessibility_info",
      "label": "t:sections.slideshow.settings.accessibility.label",
      "info": "t:sections.slideshow.settings.accessibility.info",
      "default": "Slideshow about our brand"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "t:sections.slideshow.blocks.slide.name",
      "limit": 5,
      "settings": [
        {
          "type": "header",
          "content": "Images"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.slideshow.blocks.slide.settings.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image",
          "info": "If empty, the main image will also be displayed on mobile."
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "t:sections.slideshow.blocks.slide.settings.image_overlay_opacity.label",
          "default": 0
        },
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "Image slide",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Tell your brand's story through images",
          "label": "t:sections.slideshow.blocks.slide.settings.subheading.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.slideshow.blocks.slide.settings.button_label.label",
          "info": "t:sections.slideshow.blocks.slide.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.slideshow.blocks.slide.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
          "default": false
        },
        {
          "type": "text",
          "id": "atc_button_label",
          "label": "Add to Cart button label",
          "info": "Leave the label blank to hide the Add to Cart button."
        },
        {
          "type": "product",
          "id": "atc_product",
          "label": "ATC Custom product",
          "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
        },
        {
          "type": "checkbox",
          "id": "atc_skip_cart",
          "label": "ATC Custom product skip cart"
        },
        {
          "type": "select",
          "id": "box_align",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__1.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__2.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__3.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__4.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__5.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__6.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__7.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__8.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__9.label"
            }
          ],
          "default": "middle-center",
          "label": "t:sections.slideshow.blocks.slide.settings.box_align.label",
          "info": "t:sections.slideshow.blocks.slide.settings.box_align.info"
        },
        {
          "type": "checkbox",
          "id": "show_text_box",
          "label": "t:sections.slideshow.blocks.slide.settings.show_text_box.label",
          "default": true
        },
        {
          "type": "select",
          "id": "text_alignment",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_1.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_2.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.label"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "background-1",
          "label": "Color scheme",
          "info": "Visible when container is displayed. Custom color scheme is edited at the bottom of block settings."
        },
        {
          "type": "select",
          "id": "transparent_container_color",
          "label": "Transparent container text color",
          "options": [
            {
              "value": "white",
              "label": "White"
            },
            {
              "value": "black",
              "label": "Black"
            }
          ],
          "default": "white"
        },
        {
          "type": "header",
          "content": "t:sections.slideshow.settings.mobile.content"
        },
        {
          "type": "select",
          "id": "text_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__2.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.label"
        },
        {
          "type": "header",
          "content": "Custom color scheme",
          "info": "Applied if Color scheme setting is set to Custom."
        },
        {
          "type": "color",
          "id": "custom_colors_background",
          "default": "#FFFFFF",
          "label": "Background color"
        },
        {
          "type": "color_background",
          "id": "custom_gradient_background",
          "label": "Gradient background",
          "info": "If added, replaces Background color when applicable."
        },
        {
          "type": "color",
          "id": "custom_colors_text",
          "default": "#2E2A39",
          "label": "Text"
        },
        {
          "type": "color",
          "id": "custom_colors_solid_button_background",
          "default": "#dd1d1d",
          "label": "Solid button background"
        },
        {
          "type": "color",
          "id": "custom_colors_solid_button_text",
          "default": "#ffffff",
          "label": "Solid button label"
        },
        {
          "type": "color",
          "id": "custom_colors_outline_button",
          "default": "#dd1d1d",
          "label": "Outline button"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.slideshow.presets.name",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
