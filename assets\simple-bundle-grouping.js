/**
 * Simple Bundle Grouping - Direct DOM Manipulation
 * Works by directly modifying existing cart items instead of finding containers
 */

class SimpleBundleGrouping {
  constructor() {
    this.init();
  }

  init() {
    console.log('🎯 Simple Bundle Grouping initialized');
    
    // Listen for cart updates
    document.addEventListener('cart:updated', () => {
      console.log('🔄 Cart updated, applying bundle grouping...');
      setTimeout(() => this.applyBundleGrouping(), 500);
    });

    // Listen for cart drawer opening
    this.listenForCartDrawer();
    
    // Initial attempt
    setTimeout(() => this.applyBundleGrouping(), 1000);
  }

  listenForCartDrawer() {
    // Listen for cart drawer state changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target;
          if (target.tagName === 'CART-DRAWER' && target.classList.contains('is-open')) {
            console.log('🛒 Cart drawer opened, applying bundle grouping...');
            setTimeout(() => this.applyBundleGrouping(), 300);
          }
        }
      });
    });

    // Observe the entire document for cart drawer changes
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ['class']
    });
  }

  applyBundleGrouping() {
    console.log('🎯 Applying simple bundle grouping...');
    
    // Find all cart items directly
    const cartItems = document.querySelectorAll('[id*="CartDrawer-Item-"], .cart-drawer-item, .cart-item');
    console.log(`Found ${cartItems.length} cart items`);
    
    if (cartItems.length === 0) {
      console.log('No cart items found');
      return;
    }

    // Group items by bundle ID
    const bundleGroups = new Map();
    const regularItems = [];

    cartItems.forEach(item => {
      const bundleId = this.getBundleId(item);
      if (bundleId) {
        console.log(`Item ${item.id} belongs to bundle: ${bundleId}`);
        if (!bundleGroups.has(bundleId)) {
          bundleGroups.set(bundleId, []);
        }
        bundleGroups.get(bundleId).push(item);
      } else {
        regularItems.push(item);
      }
    });

    console.log(`Found ${bundleGroups.size} bundle groups`);

    // Apply visual grouping
    bundleGroups.forEach((items, bundleId) => {
      this.createVisualGroup(bundleId, items);
    });
  }

  getBundleId(item) {
    // Check data attribute
    const bundleId = item.getAttribute('data-bundle-id');
    if (bundleId) return bundleId;

    // Check for bundle properties in the content
    const bundleInfo = item.querySelector('.cart-item__bundle-info');
    if (bundleInfo) {
      const bundleIdElement = bundleInfo.querySelector('dd');
      if (bundleIdElement) return bundleIdElement.textContent.trim();
    }

    // Check product options for Bundle ID
    const productOptions = item.querySelectorAll('.product-option');
    for (const option of productOptions) {
      const text = option.textContent;
      if (text.includes('Bundle ID:')) {
        const match = text.match(/Bundle ID:\s*([^\s\n]+)/);
        if (match) return match[1];
      }
    }

    return null;
  }

  createVisualGroup(bundleId, items) {
    if (items.length === 0) return;

    console.log(`Creating visual group for bundle: ${bundleId} with ${items.length} items`);

    // Get bundle name from first item
    const bundleName = this.getBundleName(items[0]) || 'Bundle';

    // Create bundle wrapper
    const bundleWrapper = document.createElement('div');
    bundleWrapper.className = 'simple-bundle-group';
    bundleWrapper.setAttribute('data-bundle-id', bundleId);
    bundleWrapper.style.cssText = `
      border: 3px solid #4CAF50;
      border-radius: 8px;
      margin: 1rem 0;
      background: #f0f8f0;
      padding: 1rem;
      position: relative;
    `;

    // Create bundle header
    const bundleHeader = document.createElement('div');
    bundleHeader.className = 'simple-bundle-header';
    bundleHeader.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #4CAF50;
      font-weight: bold;
    `;

    bundleHeader.innerHTML = `
      <span style="font-size: 1.1rem;">📦 ${bundleName} (${items.length} items)</span>
      <button type="button" class="simple-bundle-remove" data-bundle-id="${bundleId}" 
              style="background: #e74c3c; color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;">
        Remove Bundle
      </button>
    `;

    // Add remove button functionality
    const removeButton = bundleHeader.querySelector('.simple-bundle-remove');
    removeButton.addEventListener('click', () => {
      this.removeBundleGroup(bundleId);
    });

    bundleWrapper.appendChild(bundleHeader);

    // Create items container
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'simple-bundle-items';
    itemsContainer.style.cssText = `
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    `;

    // Move items into bundle group
    items.forEach((item, index) => {
      // Clone the item
      const itemClone = item.cloneNode(true);
      itemClone.style.cssText = `
        background: white;
        padding: 0.5rem;
        border-radius: 4px;
        border: 1px solid #ddd;
        position: relative;
      `;

      // Add bundle badge
      const badge = document.createElement('span');
      badge.textContent = 'Bundle Item';
      badge.style.cssText = `
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: #4CAF50;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        z-index: 1;
      `;
      itemClone.appendChild(badge);

      // Hide individual remove buttons
      const removeButtons = itemClone.querySelectorAll('cart-remove-button, .cart-remove');
      removeButtons.forEach(btn => btn.style.display = 'none');

      itemsContainer.appendChild(itemClone);

      // Hide original item
      item.style.display = 'none';
      item.classList.add('bundle-grouped');
    });

    bundleWrapper.appendChild(itemsContainer);

    // Insert bundle group before first item
    const firstItem = items[0];
    firstItem.parentNode.insertBefore(bundleWrapper, firstItem);

    console.log(`✅ Bundle group created for ${bundleId}`);
  }

  getBundleName(item) {
    // Check for bundle name in properties
    const productOptions = item.querySelectorAll('.product-option');
    for (const option of productOptions) {
      const text = option.textContent;
      if (text.includes('Bundle Name:')) {
        const match = text.match(/Bundle Name:\s*([^\n]+)/);
        if (match) return match[1].trim();
      }
    }
    return 'Bundle';
  }

  removeBundleGroup(bundleId) {
    console.log(`🗑️ Removing bundle group: ${bundleId}`);
    
    // Remove the visual group
    const bundleGroup = document.querySelector(`[data-bundle-id="${bundleId}"].simple-bundle-group`);
    if (bundleGroup) {
      bundleGroup.remove();
    }

    // Show original items
    const originalItems = document.querySelectorAll(`[data-bundle-id="${bundleId}"].bundle-grouped`);
    originalItems.forEach(item => {
      item.style.display = '';
      item.classList.remove('bundle-grouped');
    });

    // Trigger bundle removal via existing system
    if (window.bundleCartSync) {
      window.bundleCartSync.removeBundleById(bundleId);
    }
  }
}

// Initialize
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.simpleBundleGrouping = new SimpleBundleGrouping();
  });
} else {
  window.simpleBundleGrouping = new SimpleBundleGrouping();
}

// Manual trigger
window.applyBundleGrouping = function() {
  if (window.simpleBundleGrouping) {
    window.simpleBundleGrouping.applyBundleGrouping();
  }
};
