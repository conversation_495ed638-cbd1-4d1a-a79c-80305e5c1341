{%- style -%}
  {% if section.settings.transparent_header %}
    .hero-{{ section.id }} {
      margin-top: calc(var(--header-height) * -1);
      --additional-padding: var(--header-height);
    }
    .section-header:not(.scrolled-past-header) .header-wrapper {
      background: none;
      transition: background 0.2s;
      border-bottom: none;
    }
    .section-header:not(.scrolled-past-header) .header__icon,
    .section-header:not(.scrolled-past-header) .header__menu-item--main {
      --color-foreground: var(--color-base-{{ section.settings.transparent_header_text_color }});
      transition: color 0.2s;
    }
    .section-header:not(.scrolled-past-header) .header__menu-item--main:hover {
      --color-foreground: var(--color-base-{{ section.settings.transparent_header_hover_link_color }});
      color: rgb(var(--color-foreground));
    }
    .section-header:not(.scrolled-past-header) .header-wrapper .header__active-menu-item-v2 {
      --color-background: var(--color-base-{{ section.settings.transparent_header_text_color }});
      --color-foreground: var(--color-base-{{ section.settings.transparent_header_highlighted_link_color }});
      transition: background 0.2s, color 0.2s;
    }
    {% if section.settings.transparent_header_logo == 'secondary' %}
      .section-header:not(.scrolled-past-header) .header-wrapper .header__heading-logo--main {
        display: none;
      }
      .section-header:not(.scrolled-past-header) .header-wrapper .header__heading-logo--secondary {
        display: inline-block;
      }
    {% endif %}
  {% endif %}
  {% if section.settings.hide_announcement_bars %}
    .shopify-section-group-header-group .horizontal-ticker, .shopify-section-group-header-group .announcement-bar {
      display: none !important;
    }
  {% endif %}
  
  .section-{{ section.id }}-padding {
    padding-top: calc({{ section.settings.padding_top | times: 0.75 | round: 0 }}px + var( --additional-padding));
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  .hero-{{ section.id }} .hero-slide__content-container {
    {% if section.settings.min_mobile_height_type == 'pixels' %}
      min-height: {{ section.settings.mobile_pixels_height }}px;
    {% elsif section.settings.min_mobile_height_type == '100vh' %}
      min-height: 100vh;
    {% endif %}
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: calc({{ section.settings.padding_top }}px + var( --additional-padding));
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
    .hero-{{ section.id }} .hero-slide__content-container {
      {% if section.settings.min_desktop_height_type == 'pixels' %}
        min-height: {{ section.settings.desktop_pixels_height }}px;
      {% elsif section.settings.min_desktop_height_type == '100vh' %}
        min-height: 100vh;
      {% endif %}
    }
  }
{%- endstyle -%}

{% liquid
  assign has_desktop_bg = false
  if section.settings.desktop_bg_image != blank or section.settings.desktop_bg_video != blank
    assign has_desktop_bg = true
  endif
  assign has_mobile_bg = false
  if section.settings.mobile_bg_image != blank or section.settings.mobile_bg_video != blank
    assign has_mobile_bg = true
  endif
%}

<parallax-hero class="parallax-hero hero hero-{{ section.id }} hero-slide {{ section.settings.visibility }}" data-animation-start='{{ section.settings.animation_start }}'>
  <div class='hero-slide__background media media--transparent{% if has_mobile_bg %} hero-slide__background--has-mobile{% endif %} color-scheme-{{ section.id }}'>
    {% if has_desktop_bg %}
      {% if section.settings.desktop_bg_video != blank %}
        {{
          section.settings.desktop_bg_video
          | video_tag:
            image_size: '2000x',
            loop: true,
            muted: true,
            playsinline: true,
            autoplay: true,
            class: "hero-slide__background__desktop-media"
        }}
      {% else %}
        {%- liquid
          assign image_height = section.settings.desktop_bg_image.width | divided_by: section.settings.desktop_bg_image.aspect_ratio
          assign sizes = '100vw'
        -%}
        {{
          section.settings.desktop_bg_image
          | image_url: width: 3840
          | image_tag:
            loading: 'lazy',
            width: section.settings.desktop_bg_image.width,
            height: image_height,
            sizes: sizes,
            widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840',
            class: "hero-slide__background__desktop-media"
        }}
      {% endif %}
      {% if has_mobile_bg %}
        {% if section.settings.mobile_bg_video != blank %}
          {{
            section.settings.mobile_bg_video
            | video_tag:
              image_size: '2000x',
              loop: true,
              muted: true,
              playsinline: true,
              autoplay: true,
              class: "hero-slide__background__mobile-media desktop-hidden"
          }}
        {% else %}
          {%- liquid
            assign image_height = section.settings.mobile_bg_image.width | divided_by: section.settings.mobile_bg_image.aspect_ratio
            assign sizes = '100vw'
          -%}
          {{
            section.settings.mobile_bg_image
            | image_url: width: 3840
            | image_tag:
              loading: 'lazy',
              width: section.settings.mobile_bg_image.width,
              height: image_height,
              sizes: sizes,
              widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840',
            class: "hero-slide__background__mobile-media desktop-hidden"
          }}
        {% endif %}
      {% endif %}
    {% endif %}
  </div>
  <div class='hero-slide__overlay' style='--overlay-color:{{ section.settings.overlay_color }};--overlay-opacity:{{ section.settings.overlay_opacity }}%;'>&nbsp</div>
  {% for block in section.blocks %}
    {% case block.type %}
      {% when 'content' %}
        {% liquid
          if block.settings.content_max_width == 'pixels'
            assign content_max_width = block.settings.content_pixels_max_width | append: 'px'
          elsif block.settings.content_max_width == 'percentage'
            assign content_max_width = block.settings.content_percentage_max_width | append: '%'
          endif
        %}
        <style>
          .color-scheme-{{ section.id }}-{{ block.id }}.color-custom {
            --color-foreground: {{ block.settings.custom_colors_text.red }}, {{ block.settings.custom_colors_text.green }}, {{ block.settings.custom_colors_text.blue }};
            --color-button: {{ block.settings.custom_colors_solid_button_background.red }}, {{ block.settings.custom_colors_solid_button_background.green }}, {{ block.settings.custom_colors_solid_button_background.blue }};
            --color-button-text: {{ block.settings.custom_colors_solid_button_text.red }}, {{ block.settings.custom_colors_solid_button_text.green }}, {{ block.settings.custom_colors_solid_button_text.blue }};
            --color-base-outline-button-labels: {{ block.settings.custom_colors_outline_button.red }}, {{ block.settings.custom_colors_outline_button.green }}, {{ block.settings.custom_colors_outline_button.blue }};
          }
        </style>
        <div 
          class='parallax-hero__layer hero-slide__content-container page-width{% if block.settings.full_page_width %} page-width--full{% endif %} section-{{ section.id }}-padding'
          style="--content-max-width:{{ content_max_width }};
          --content-vertical-position:{{ block.settings.content_vertical_position }};
          --content-horizontal-position:{{ block.settings.content_horizontal_position }};
          --content-text-alignment:{{ block.settings.content_text_alignment }};
          --mobile-content-vertical-position:{{ block.settings.mobile_content_vertical_position }};
          --mobile-content-text-alignment:{{ block.settings.mobile_content_text_alignment }};
          --z-index:{{ block.settings.z_index }};
          --heading-size:{{ block.settings.heading_size | divided_by: 10.0 }}rem;
          --mobile-heading-size:{{ block.settings.mobile_heading_size | divided_by: 10.0 }}rem;"
          data-scroll-y="{{ block.settings.vertical_scrollng_speed }}"
          data-scroll-x="{{ block.settings.horizontal_scrollng_speed }}"
          data-zoom="{{ block.settings.zoom_level }}"
          data-rotation="{{ block.settings.rotation }}"
          {{ block.shopify_attributes }}
        >
          <div 
            class='hero-slide__content{% if block.settings.content_max_width != 'unlimited' %} hero-slide__content--limited{% endif %} color-{{ block.settings.color_scheme }} color-scheme-{{ section.id }}-{{ block.id }} animate-item'
          >
            {% if block.settings.heading_prefix != blank %}
              <p class='hero__heading-prefix {{ block.settings.heading_prefix_size }}'>
                {{ block.settings.heading_prefix }}
              </p>
            {% endif %}
            {% if block.settings.heading != blank %}
              <h2 class='hero__heading title-with-highlight' style='--hightlight-color:{{ block.settings.title_highlight_color }}'>
                {{ block.settings.heading }}
              </h2>
            {% endif %}
            {% if block.settings.heading_suffix != blank %}
              <p class='hero__heading-suffix {{ block.settings.heading_prefix_size }}'>
                {{ block.settings.heading_suffix }}
              </p>
            {% endif %}
            {% if block.settings.text != blank %}
              <div class='hero__text rte'>
                {{ block.settings.text }}
              </div>
            {% endif %}
            {% unless block.settings.button_label_1 == blank and block.settings.button_label_2 == blank %}
              <div class='hero__buttons'>
                {%- if block.settings.button_label_1 != blank -%}
                  <a
                    {% if block.settings.button_link_1 == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link_1 }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary_1 %} button--secondary{% else %} button--primary{% endif %}"
                  >
                    {{- block.settings.button_label_1 | escape -}}
                  </a>
                {%- endif -%}
                {%- if block.settings.button_label_2 != blank -%}
                  <a
                    {% if block.settings.button_link_2 == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link_2 }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary_2 %} button--secondary{% else %} button--primary{% endif %}"
                  >
                    {{- block.settings.button_label_2 | escape -}}
                  </a>
                {%- endif -%}
              </div>
            {% endunless %}
          </div>
        </div>
      {% when 'image' %}
        {% liquid
          assign has_mobile_img = false
          if block.settings.mobile_image != blank
            assign has_mobile_img = true
          endif
        %}
        <div 
          class='parallax-hero__layer parallax-hero__image-layer hero-slide__background media media--transparent{% if has_mobile_img %} hero-slide__background--has-mobile{% endif %}'
          style="--z-index:{{ block.settings.z_index }};"
          data-scroll-y="{{ block.settings.vertical_scrollng_speed }}"
          data-scroll-x="{{ block.settings.horizontal_scrollng_speed }}"
          data-zoom="{{ block.settings.zoom_level }}"
          data-rotation="{{ block.settings.rotation }}"
          {{ block.shopify_attributes }}
        >
          {% if block.settings.desktop_image != blank %}
            {%- liquid
              assign image_height = block.settings.desktop_image.width | divided_by: block.settings.desktop_image.aspect_ratio
              assign sizes = '100vw'
            -%}
            {{
              block.settings.desktop_image
              | image_url: width: 3840
              | image_tag:
                loading: 'lazy',
                width: block.settings.desktop_image.width,
                height: image_height,
                sizes: sizes,
                widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840',
                class: "hero-slide__background__desktop-media"
            }}
          {% endif %}
          {% if has_mobile_img %}
            {%- liquid
              assign image_height = block.settings.mobile_image.width | divided_by: block.settings.mobile_image.aspect_ratio
              assign sizes = '100vw'
            -%}
            {{
              block.settings.mobile_image
              | image_url: width: 3840
              | image_tag:
                loading: 'lazy',
                width: block.settings.mobile_image.width,
                height: image_height,
                sizes: sizes,
                widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840',
              class: "hero-slide__background__mobile-media desktop-hidden"
            }}
          {% endif %}
        </div>
    {% endcase %}
  {% endfor %}
</parallax-hero>

{% schema %}
{
  "name": "3D Parallax hero",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Animation"
    },
    {
      "type": "select",
      "id": "animation_start",
      "options": [
        {
          "value": "top",
          "label": "Once the top edge leaves the viewport"
        },
        {
          "value": "bottom",
          "label": "Once the top edge comes into the viewport"
        }
      ],
      "label": "Start animation once:",
      "default": "top",
      "info": "If the section is not placed at the very top of the page, the second option usually works better."
    },
    {
      "type": "header",
      "content": "Height"
    },
    {
      "type": "select",
      "id": "min_desktop_height_type",
      "label": "Minimum desktop height",
      "options": [
        {
          "value": "autofit",
          "label": "None (it content)"
        },
        {
          "value": "pixels",
          "label": "Amount in pixels"
        },
        {
          "value": "100vh",
          "label": "Viewport height"
        }
      ],
      "default": "pixels"
    },
    {
      "type": "number",
      "id": "desktop_pixels_height",
      "label": "Desktop minimum height in pixels",
      "default": 600
    },
    {
      "type": "select",
      "id": "min_mobile_height_type",
      "label": "Minimum mobile height",
      "options": [
        {
          "value": "autofit",
          "label": "None (it content)"
        },
        {
          "value": "pixels",
          "label": "Custom amount of pixels"
        },
        {
          "value": "100vh",
          "label": "Viewport height"
        }
      ],
      "default": "pixels"
    },
    {
      "type": "number",
      "id": "mobile_pixels_height",
      "label": "Mobile minimum height in pixels",
      "default": 400
    },
    {
      "type": "header",
      "content": "Header"
    },
    {
      "type": "checkbox",
      "id": "transparent_header",
      "label": "Transparent header",
      "default": false,
      "info": "Fits the hero under the header & makes it transparent. Scrolled sticky header is not affected. Top padding is automatically added (height of the header)."
    },
    {
      "type": "select",
      "id": "transparent_header_text_color",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "text",
          "label": "Text"
        }
      ],
      "default": "text",
      "label": "Transparent header links & icons color"
    },
    {
      "type": "select",
      "id": "transparent_header_highlighted_link_color",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "text",
          "label": "Text"
        }
      ],
      "default": "background-1",
      "label": "Transparent header highlighted link text color"
    },
    {
      "type": "select",
      "id": "transparent_header_hover_link_color",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "text",
          "label": "Text"
        }
      ],
      "default": "accent-1",
      "label": "Transparent header hovered link text color"
    },
    {
      "type": "select",
      "id": "transparent_header_logo",
      "options": [
        {
          "value": "main",
          "label": "Primary"
        },
        {
          "value": "secondary",
          "label": "Secondary"
        }
      ],
      "default": "main",
      "label": "Transparent header logo",
      "info": "Add a secondary logo in Theme settings > Logo."
    },
    {
      "type": "checkbox",
      "id": "hide_announcement_bars",
      "label": "Hide announcement bars",
      "default": false,
      "info": "HIGHLY RECOMMENDED when Transparent header is enabled. Hides all announcement bars & horizontal tickers inside the header group on this page only."
    },
    {
      "type": "header",
      "content": "Background"
    },
    {
      "type": "image_picker",
      "id": "desktop_bg_image",
      "label": "Desktop background image"
    },
    {
      "type": "video",
      "id": "desktop_bg_video",
      "label": "Desktop background video"
    },
    {
      "type": "image_picker",
      "id": "mobile_bg_image",
      "label": "Mobile background image"
    },
    {
      "type": "video",
      "id": "mobile_bg_video",
      "label": "Mobile background video"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Background overlay color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 0,
      "unit": "%",
      "label": "Background overlay opacity"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 200,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 200,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 50
    }
  ],
  "blocks": [
    {
      "type": "content",
      "name": "Content",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Parallax"
        },
        {
          "type": "range",
          "id": "vertical_scrollng_speed",
          "min": -300,
          "max": 300,
          "step": 10,
          "default": 0,
          "unit": "%",
          "label": "Vertical scrolling",
          "info": "Controls vertical movement of the element when scrolling. 0% is regular scrolling speed. Negative values move the element upwards, and positive values move it downwards. At 100%, the element stays fixed on the screen."
        },
        {
          "type": "range",
          "id": "horizontal_scrollng_speed",
          "min": -300,
          "max": 300,
          "step": 10,
          "default": 0,
          "unit": "%",
          "label": "Horizontal scrolling",
          "info": "Controls horizontal movement of the element when scrolling. Negative values move the element left, positive values move it right. At 0%, the element doesn't move horizontally."
        },
        {
          "type": "range",
          "id": "zoom_level",
          "min": 0,
          "max": 400,
          "step": 10,
          "default": 100,
          "unit": "%",
          "label": "Zoom level",
          "info": "Zoom level that the element will have at the end of the animation (the section is scrolled past). 100% will leave the element the same size during the whole animation."
        },
        {
          "type": "range",
          "id": "rotation",
          "min": -100,
          "max": 100,
          "step": 2,
          "default": 0,
          "unit": "%",
          "label": "Rotation",
          "info": "Rotation that the element will have at the end of the animation (the section is scrolled past). Negatives rotate it to the left, positive to the right."
        },
        {
          "type": "range",
          "id": "z_index",
          "min": 0,
          "max": 10,
          "step": 1,
          "default": 0,
          "label": "Stacking index",
          "info": "Element with a higher stacking index will appear in front of elements with a lower one."
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "text",
          "id": "heading_prefix",
          "label": "Heading prefix"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "label": "Heading",
          "default": "Parallax hero",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "range",
          "id": "heading_size",
          "min": 30,
          "max": 150,
          "step": 5,
          "default": 80,
          "unit": "px",
          "label": "Desktop heading size"
        },
        {
          "type": "range",
          "id": "mobile_heading_size",
          "min": 18,
          "max": 90,
          "step": 3,
          "default": 36,
          "unit": "px",
          "label": "Mobile heading size"
        },
        {
          "type": "text",
          "id": "heading_suffix",
          "label": "Header suffix"
        },
        {
          "type": "select",
          "id": "heading_prefix_size",
          "options": [
            {
              "value": "h5",
              "label": "Extra small"
            },
            {
              "value": "h4",
              "label": "Small"
            },
            {
              "value": "h3",
              "label": "Medium"
            },  
            {
              "value": "h2",
              "label": "Large"
            }
          ],
          "default": "h4",
          "label": "Heading prefix & suffix size"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Create a cool perspective scrolling effect using multiple image layers.</p>"
        },
        {
          "type": "text",
          "id": "button_label_1",
          "default": "Button label",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_1.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_1.info"
        },
        {
          "type": "url",
          "id": "button_link_1",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_1.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_1",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_1.label"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_2",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_2.label"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "background-1",
          "label": "Color scheme",
          "info": "Custom color scheme is edited at the bottom of block settings."
        },
        {
          "type": "header",
          "content": "Desktop content layout"
        },
        {
          "type": "checkbox",
          "id": "full_page_width",
          "label": "Use full page width",
          "default": false
        },
        {
          "type": "select",
          "id": "content_max_width",
          "options": [
            {
              "value": "unlimited",
              "label": "Unlimited"
            },
            {
              "value": "pixels",
              "label": "Amount in pixels"
            },
            {
              "value": "percentage",
              "label": "Amount in percentage"
            }
          ],
          "label": "Content maximum width"
        },
        {
          "type": "number",
          "id": "content_pixels_max_width",
          "label": "Content maximum width in pixels",
          "default": 500
        },
        {
          "type": "range",
          "id": "content_percentage_max_width",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Content maximum width in percentage",
          "default": 50,
          "info": "Related to the container (full page if the option \"Use full page width\" is checked. Automatically set to 100% on mobile."
        },
        {
          "type": "select",
          "id": "content_vertical_position",
          "options": [
            {
              "value": "flex-start",
              "label": "Top"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Bottom"
            }
          ],
          "label": "Content vertical position",
          "default": "center"
        },
        {
          "type": "select",
          "id": "content_horizontal_position",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "label": "Content horizontal position",
          "default": "center"
        },
        {
          "type": "select",
          "id": "content_text_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "label": "Content text alignment",
          "default": "center"
        },
        {
          "type": "header",
          "content": "Mobile content layout"
        },
        {
          "type": "select",
          "id": "mobile_content_vertical_position",
          "options": [
            {
              "value": "flex-start",
              "label": "Top"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Bottom"
            }
          ],
          "label": "Mobile content vertical position",
          "default": "center"
        },
        {
          "type": "select",
          "id": "mobile_content_text_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "label": "Mobile content text alignment",
          "default": "center"
        },
        {
          "type": "header",
          "content": "Custom color scheme",
          "info": "Applied if Color scheme setting is set to Custom."
        },
        {
          "type": "color",
          "id": "custom_colors_text",
          "default": "#2E2A39",
          "label": "Text"
        },
        {
          "type": "color",
          "id": "custom_colors_solid_button_background",
          "default": "#dd1d1d",
          "label": "Solid button background"
        },
        {
          "type": "color",
          "id": "custom_colors_solid_button_text",
          "default": "#ffffff",
          "label": "Solid button label"
        },
        {
          "type": "color",
          "id": "custom_colors_outline_button",
          "default": "#dd1d1d",
          "label": "Outline button"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image layer",
      "limit": 10,
      "settings": [
        {
          "type": "header",
          "content": "Image"
        },
        {
          "type": "image_picker",
          "id": "desktop_image",
          "label": "Desktop image"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image",
          "info": "If empty, the desktop image will be applied"
        },
        {
          "type": "checkbox",
          "id": "hide_on_mobile",
          "label": "Hide on mobile",
          "default": false
        },
        {
          "type": "header",
          "content": "Parallax"
        },
        {
          "type": "range",
          "id": "vertical_scrollng_speed",
          "min": -300,
          "max": 300,
          "step": 10,
          "default": 0,
          "unit": "%",
          "label": "Vertical scrolling",
          "info": "Controls vertical movement of the element when scrolling. 0% is regular scrolling speed. Negative values move the element upwards, and positive values move it downwards. At 100%, the element stays fixed on the screen."
        },
        {
          "type": "range",
          "id": "horizontal_scrollng_speed",
          "min": -300,
          "max": 300,
          "step": 10,
          "default": 0,
          "unit": "%",
          "label": "Horizontal scrolling",
          "info": "Controls horizontal movement of the element when scrolling. Negative values move the element left, positive values move it right. At 0%, the element doesn't move horizontally."
        },
        {
          "type": "range",
          "id": "zoom_level",
          "min": 0,
          "max": 400,
          "step": 10,
          "default": 0,
          "unit": "%",
          "label": "Zoom level",
          "info": "Zoom level that the element will have at the end of the animation (the section is scrolled past)."
        },
        {
          "type": "range",
          "id": "rotation",
          "min": -100,
          "max": 100,
          "step": 2,
          "default": 0,
          "unit": "%",
          "label": "Rotation",
          "info": "Rotation that the element will have at the end of the animation (the section is scrolled past). Negatives rotate it to the left, positive to the right."
        },
        {
          "type": "range",
          "id": "z_index",
          "min": 0,
          "max": 10,
          "step": 1,
          "default": 1,
          "label": "Stacking index",
          "info": "Element with a higher stacking index will appear in front of elements with a lower one."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "3D Parallax hero",
      "blocks": [
        {
          "type": "content"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}
