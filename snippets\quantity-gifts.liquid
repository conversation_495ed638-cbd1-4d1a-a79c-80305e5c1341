{% liquid
  assign item_count = 0
  if block.settings.gift_1 != blank
    assign item_count = item_count | plus: 1
  endif
  if block.settings.gift_2 != blank
    assign item_count = item_count | plus: 1
  endif
  if block.settings.gift_3 != blank
    assign item_count = item_count | plus: 1
  endif
  if block.settings.gift_4 != blank
    assign item_count = item_count | plus: 1
  endif
%}

<div
  {{ block.shopify_attributes }}
  class="quantity-gifts-container"
  style="
    --margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;
    --margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;
  "
>
  <quantity-gifts
    class="quantity-gifts quantity-gifts-{{ item_count }}"
    id="quantity-gifts-{{ section.id }}"
    data-section="{{ section.id }}"
    style="--item-count: {{ item_count }};--unlocked-bg-color:{{ block.settings.unlocked_bg_color }};--unlocked-border-color:{{ block.settings.unlocked_border_color }};--gift-box-tie-color:{{ block.settings.gift_box_tie_color }};"
  >
    {% unless block.settings.gift_1 == blank %}
      <div
        class="quantity-gift"
        data-quantity="{{ block.settings.gift_1_quantity }}"
        data-unlocked="false"
        data-product="{{ block.settings.gift_1.selected_or_first_available_variant.id }}"
      >
        <div class="quantity-gift__container color-{{ block.settings.unlocked_color }}">
          <div class="quantity-gift__lock color-background-1">
            <span class="quantity-gift__lock__top">{{ block.settings.gift_1_top_text }}</span>
            <span class="lock"></span>
            <span class="quantity-gift__lock__bottom">{{ block.settings.gift_1_bottom_text }}</span>
          </div>
          <div class="quantity-gift__image">
            {% liquid
              assign gift_1_image = block.settings.gift_1.featured_image
              if block.settings.gift_1_image != blank
                assign gift_1_image = block.settings.gift_1_image
              endif
            %}
            <img
              src="{{ gift_1_image | image_url: width: 300 }}"
              alt=""
              width="auto"
              height="auto"
              loading="lazy"
            >
          </div>
          <div class="gift-box">
            <div class="gift-box-body color-{{ block.settings.gift_box_color }}">
              <div class="gift-box-lid">
                <div class="gift-box-bowtie">&nbsp</div>
              </div>
            </div>
          </div>
        </div>
        <h3 class="quantity-gift__title">{{ block.settings.gift_1.title }}</h3>
      </div>
    {% endunless %}
    {% unless block.settings.gift_2 == blank %}
      <div
        class="quantity-gift"
        data-quantity="{{ block.settings.gift_2_quantity }}"
        data-unlocked="false"
        data-product="{{ block.settings.gift_2.selected_or_first_available_variant.id }}"
      >
        <div class="quantity-gift__container color-{{ block.settings.unlocked_color }}">
          <div class="quantity-gift__lock color-background-1">
            <span class="quantity-gift__lock__top">{{ block.settings.gift_2_top_text }}</span>
            <span class="lock"></span>
            <span class="quantity-gift__lock__bottom">{{ block.settings.gift_2_bottom_text }}</span>
          </div>
          <div class="quantity-gift__image">
            {% liquid
              assign gift_2_image = block.settings.gift_2.featured_image
              if block.settings.gift_2_image != blank
                assign gift_2_image = block.settings.gift_2_image
              endif
            %}
            <img
              src="{{ gift_2_image | image_url: width: 300 }}"
              alt=""
              width="auto"
              height="auto"
              loading="lazy"
            >
          </div>
          <div class="gift-box">
            <div class="gift-box-body color-{{ block.settings.gift_box_color }}">
              <div class="gift-box-lid">
                <div class="gift-box-bowtie">&nbsp</div>
              </div>
            </div>
          </div>
        </div>
        <h3 class="quantity-gift__title">{{ block.settings.gift_2.title }}</h3>
      </div>
    {% endunless %}
    {% unless block.settings.gift_3 == blank %}
      <div
        class="quantity-gift"
        data-quantity="{{ block.settings.gift_3_quantity }}"
        data-unlocked="false"
        data-product="{{ block.settings.gift_3.selected_or_first_available_variant.id }}"
      >
        <div class="quantity-gift__container color-{{ block.settings.unlocked_color }}">
          <div class="quantity-gift__lock color-background-1">
            <span class="quantity-gift__lock__top">{{ block.settings.gift_3_top_text }}</span>
            <span class="lock"></span>
            <span class="quantity-gift__lock__bottom">{{ block.settings.gift_3_bottom_text }}</span>
          </div>
          <div class="quantity-gift__image">
            {% liquid
              assign gift_3_image = block.settings.gift_3.featured_image
              if block.settings.gift_3_image != blank
                assign gift_3_image = block.settings.gift_3_image
              endif
            %}
            <img
              src="{{ gift_3_image | image_url: width: 300 }}"
              alt=""
              width="auto"
              height="auto"
              loading="lazy"
            >
          </div>
          <div class="gift-box">
            <div class="gift-box-body color-{{ block.settings.gift_box_color }}">
              <div class="gift-box-lid">
                <div class="gift-box-bowtie">&nbsp</div>
              </div>
            </div>
          </div>
        </div>
        <h3 class="quantity-gift__title">{{ block.settings.gift_3.title }}</h3>
      </div>
    {% endunless %}
    {% unless block.settings.gift_4 == blank %}
      <div
        class="quantity-gift"
        data-quantity="{{ block.settings.gift_4_quantity }}"
        data-unlocked="false"
        data-product="{{ block.settings.gift_4.selected_or_first_available_variant.id }}"
      >
        <div class="quantity-gift__container color-{{ block.settings.unlocked_color }}">
          <div class="quantity-gift__lock color-background-1">
            <span class="quantity-gift__lock__top">{{ block.settings.gift_4_top_text }}</span>
            <span class="lock"></span>
            <span class="quantity-gift__lock__bottom">{{ block.settings.gift_4_bottom_text }}</span>
          </div>
          <div class="quantity-gift__image">
            {% liquid
              assign gift_4_image = block.settings.gift_4.featured_image
              if block.settings.gift_4_image != blank
                assign gift_4_image = block.settings.gift_4_image
              endif
            %}
            <img
              src="{{ gift_4_image | image_url: width: 300 }}"
              alt=""
              width="auto"
              height="auto"
              loading="lazy"
            >
          </div>
          <div class="gift-box">
            <div class="gift-box-body color-{{ block.settings.gift_box_color }}">
              <div class="gift-box-lid">
                <div class="gift-box-bowtie">&nbsp</div>
              </div>
            </div>
          </div>
        </div>
        <h3 class="quantity-gift__title">{{ block.settings.gift_4.title }}</h3>
      </div>
    {% endunless %}
  </quantity-gifts>
  {% if item_count == 0 %}
    <p class="title h2 center">Select your product/products</p>
  {% endif %}
</div>
