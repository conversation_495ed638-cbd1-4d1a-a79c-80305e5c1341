

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557276193259651928.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-557276193259651928.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-557276193259651928.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557276193259651928.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557276193259651928.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557276193259651928.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557276193259651928.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557276193259651928.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557276193259651928.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557276193259651928.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557276193259651928.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-557276193259651928.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557276193259651928.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-557276193259651928.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-557276193259651928.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-557276193259651928.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557276193259651928.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-557276193259651928.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-557276193259651928.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-557276193259651928.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557276193259651928.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-557276193259651928.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557276193259651928.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557276193259651928.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557276193259651928.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-557276193259651928.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557276193259651928.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557276193259651928.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-557276193259651928.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557276193259651928.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-557276193259651928.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557276193259651928.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-557276193259651928.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-557276193259651928.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-557276193259651928.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557276193259651928.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557276193259651928.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-557276193259651928.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-557276193259651928.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-557276193259651928.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-557276193259651928.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-557276193259651928.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-557276193259651928.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557276193259651928.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557276193259651928.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557276193259651928.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557276193259651928.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557276193259651928.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557276193259651928.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557276193259651928.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557276193259651928.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-557276193259651928.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557276193259651928.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-557276193259651928.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-557276193259651928.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-557276193259651928.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557276193259651928.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-557276193259651928.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-557276193259651928.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-557276193259651928.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-557276193259651928.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-557276193259651928.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557276193259651928.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-557276193259651928.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-557276193259651928.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-557276193259651928.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-557276193259651928.gps.gpsil [style*="--mt-tablet:"]{margin-top:var(--mt-tablet)}.gps-557276193259651928.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-557276193259651928.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-557276193259651928.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557276193259651928.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557276193259651928.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-557276193259651928.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-557276193259651928.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-557276193259651928.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-557276193259651928.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-557276193259651928.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-557276193259651928.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-557276193259651928.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557276193259651928.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557276193259651928.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-557276193259651928.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-557276193259651928.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-557276193259651928.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-557276193259651928.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-557276193259651928.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-557276193259651928.gps.gpsil [style*="--mr-mobile:"]{margin-right:var(--mr-mobile)}.gps-557276193259651928.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-557276193259651928.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-557276193259651928.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-557276193259651928.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-557276193259651928.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-557276193259651928.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557276193259651928.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557276193259651928.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557276193259651928.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557276193259651928.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-557276193259651928.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-557276193259651928.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-557276193259651928.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-557276193259651928 .gp-rotate-0,.gps-557276193259651928 .gp-rotate-180,.gps-557276193259651928 .mobile\:gp-rotate-0,.gps-557276193259651928 .mobile\:gp-rotate-180,.gps-557276193259651928 .tablet\:gp-rotate-0,.gps-557276193259651928 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-557276193259651928 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-557276193259651928 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-557276193259651928 .gp-static{position:static}.gps-557276193259651928 .\!gp-relative{position:relative!important}.gps-557276193259651928 .gp-relative{position:relative}.gps-557276193259651928 .gp-left-0{left:0}.gps-557276193259651928 .gp-right-0{right:0}.gps-557276193259651928 .gp-z-1{z-index:1}.gps-557276193259651928 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557276193259651928 .gp-my-0{margin-bottom:0;margin-top:0}.gps-557276193259651928 .gp-mb-0{margin-bottom:0}.gps-557276193259651928 .gp-block{display:block}.gps-557276193259651928 .\!gp-flex{display:flex!important}.gps-557276193259651928 .gp-flex{display:flex}.gps-557276193259651928 .gp-inline-flex{display:inline-flex}.gps-557276193259651928 .gp-grid{display:grid}.gps-557276193259651928 .gp-contents{display:contents}.gps-557276193259651928 .\!gp-hidden{display:none!important}.gps-557276193259651928 .gp-hidden{display:none}.gps-557276193259651928 .gp-aspect-square{aspect-ratio:1/1}.gps-557276193259651928 .gp-h-auto{height:auto}.gps-557276193259651928 .gp-h-full{height:100%}.gps-557276193259651928 .\!gp-min-h-full{min-height:100%!important}.gps-557276193259651928 .gp-w-\[12px\]{width:12px}.gps-557276193259651928 .gp-w-full{width:100%}.gps-557276193259651928 .gp-max-w-full{max-width:100%}.gps-557276193259651928 .gp-flex-none{flex:none}.gps-557276193259651928 .gp-shrink-0{flex-shrink:0}.gps-557276193259651928 .gp-rotate-0{--tw-rotate:0deg}.gps-557276193259651928 .gp-rotate-0,.gps-557276193259651928 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-557276193259651928 .gp-rotate-180{--tw-rotate:180deg}.gps-557276193259651928 .gp-cursor-pointer{cursor:pointer}.gps-557276193259651928 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-557276193259651928 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557276193259651928 .\!gp-flex-row{flex-direction:row!important}.gps-557276193259651928 .gp-flex-row{flex-direction:row}.gps-557276193259651928 .gp-flex-col{flex-direction:column}.gps-557276193259651928 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-557276193259651928 .gp-items-center{align-items:center}.gps-557276193259651928 .gp-justify-center{justify-content:center}.gps-557276193259651928 .gp-justify-between{justify-content:space-between}.gps-557276193259651928 .gp-gap-2{gap:8px}.gps-557276193259651928 .gp-gap-y-0{row-gap:0}.gps-557276193259651928 .gp-overflow-hidden{overflow:hidden}.gps-557276193259651928 .gp-rounded-full{border-radius:9999px}.gps-557276193259651928 .gp-text-center{text-align:center}.gps-557276193259651928 .gp-leading-\[0\]{line-height:0}.gps-557276193259651928 .gp-text-g-warning{color:var(--g-c-warning)}.gps-557276193259651928 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-557276193259651928 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-557276193259651928 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259651928 .gp-duration-200{transition-duration:.2s}.gps-557276193259651928 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-557276193259651928 .tablet\:gp-static{position:static}.gps-557276193259651928 .tablet\:gp-left-0{left:0}.gps-557276193259651928 .tablet\:gp-right-0{right:0}.gps-557276193259651928 .tablet\:gp-block{display:block}.gps-557276193259651928 .tablet\:\!gp-hidden{display:none!important}.gps-557276193259651928 .tablet\:gp-hidden{display:none}.gps-557276193259651928 .tablet\:gp-h-auto{height:auto}.gps-557276193259651928 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-557276193259651928 .tablet\:gp-flex-none{flex:none}.gps-557276193259651928 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-557276193259651928 .tablet\:gp-rotate-0,.gps-557276193259651928 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-557276193259651928 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-557276193259651928 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-557276193259651928 .tablet\:gp-flex-row{flex-direction:row}.gps-557276193259651928 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-557276193259651928 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-557276193259651928 .mobile\:gp-absolute{position:absolute}.gps-557276193259651928 .mobile\:gp-left-0{left:0}.gps-557276193259651928 .mobile\:gp-right-0{right:0}.gps-557276193259651928 .mobile\:gp-block{display:block}.gps-557276193259651928 .mobile\:\!gp-hidden{display:none!important}.gps-557276193259651928 .mobile\:gp-hidden{display:none}.gps-557276193259651928 .mobile\:gp-h-auto{height:auto}.gps-557276193259651928 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-557276193259651928 .mobile\:gp-flex-none{flex:none}.gps-557276193259651928 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-557276193259651928 .mobile\:gp-rotate-0,.gps-557276193259651928 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-557276193259651928 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-557276193259651928 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-557276193259651928 .mobile\:gp-flex-row{flex-direction:row}.gps-557276193259651928 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-557276193259651928 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-557276193259651928 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-557276193259651928 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-557276193259651928 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-557276193259651928 .\[\&\>svg\]\:gp-w-full>svg{width:100%}@media (max-width:1024px){.gps-557276193259651928 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-557276193259651928 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-557276193259651928 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557276193259651928 .\[\&_p\]\:gp-inline p{display:inline}.gps-557276193259651928 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557276193259651928 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gimpPT-HS2" data-id="gimpPT-HS2"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:0px;--pt:var(--g-s-3xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--mt-mobile:-32px;--pt-mobile:0px;--pb-mobile:70px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gimpPT-HS2 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gcvrpRqjyC gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gmolrS3Hhs" data-id="gmolrS3Hhs"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gmolrS3Hhs gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gT36YEpHFm gp-relative gp-flex gp-flex-col"
    >
      
    <gp-carousel data-id="gC09kb6v4r"  id="gp-root-carousel-gC09kb6v4r-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"gC09kb6v4r-{{section.id}}","setting":{"animationMode":"ease-in","arrow":{"desktop":true,"mobile":false,"tablet":false},"arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"arrowButtonSize":{"desktop":{"height":"32px","padding":{"linked":true},"shapeLinked":true,"shapeValue":"1/1","width":"32px"}},"arrowCustom":"<svg height=\"100%\" width=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-6 h-6\"  viewBox=\"0 0 512 512\" fill=\"currentColor\">\n              <path fill=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" fill=\"currentColor\" d=\"M32 256a16 16 0 0 1 16 -16h377.376l-100.704 -100.672a16 16 0 0 1 22.656 -22.656l128 128a16 16 0 0 1 0 22.656l-128 128a16 16 0 0 1 -22.656 -22.656L425.376 272H48A16 16 0 0 1 32 256z\" /></svg>","arrowGapToEachSide":"16","arrowIconSize":{"desktop":22,"mobile":20},"autoplay":false,"autoplayTimeout":2,"childItem":["Slide 1 Copy","Slide 1","Slide 1 Copy"],"controlOverContent":{"desktop":false},"dot":{"desktop":true,"mobile":true,"tablet":true},"dotActiveColor":{"desktop":"#242424"},"dotColor":{"desktop":"#B4B4B4","mobile":"#B4B4B4"},"dotGapToCarousel":{"desktop":16,"mobile":16,"tablet":16},"dotSize":{"desktop":12,"mobile":12,"tablet":12},"dotStyle":{"desktop":"outside","mobile":"inside","tablet":"outside"},"enableDrag":{"desktop":true},"itemNumber":{"desktop":2,"mobile":1,"tablet":2},"loop":{"desktop":false},"navigationStyle":{"desktop":"outside","mobile":"none","tablet":"none"},"pauseOnHover":true,"roundedArrow":{"desktop":{"radiusType":"small"}},"sneakPeak":{"desktop":false,"mobile":false,"tablet":false},"sneakPeakOffsetCenter":{"desktop":50,"mobile":50,"tablet":50},"sneakPeakOffsetForward":{"desktop":50,"mobile":50,"tablet":50},"sneakPeakType":{"desktop":"forward"},"vertical":{"desktop":false,"mobile":false,"tablet":false}},"styles":{"align":{"desktop":"center"},"playSpeed":500,"sizeSetting":{"desktop":{"height":"auto","width":"100%"},"mobile":{"height":"auto","width":"100%"},"tablet":{"height":"auto","width":"100%"}},"spacing":{"desktop":"30"}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gC09kb6v4r"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt-mobile:-2px;--mb-mobile:0px;--mr-mobile:0px;--pb-mobile:0px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:auto;--h-tablet:auto;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous gC09kb6v4r-{{section.id}} gp-carousel-arrow-gC09kb6v4r gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-relative  tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:none;--d-mobile:none;background-color:;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--w:22px;--w-tablet:22px;--w-mobile:20px;--h:22px;--h-tablet:22px;--h-mobile:20px"
  >
    <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M32 256a16 16 0 0 1 16 -16h377.376l-100.704 -100.672a16 16 0 0 1 22.656 -22.656l128 128a16 16 0 0 1 0 22.656l-128 128a16 16 0 0 1 -22.656 -22.656L425.376 272H48A16 16 0 0 1 32 256z" /></svg>
    </div>
      <style>
    .gp-carousel-arrow-gC09kb6v4r {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gC09kb6v4r::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gC09kb6v4r {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gC09kb6v4r::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gC09kb6v4r {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gC09kb6v4r::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gC09kb6v4r-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:30px;--cg-tablet:30px;--cg:30px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--minw:calc(100% / 2 - 15px);--minw-tablet:calc(100% / 2 - 15px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 2 - 15px);--maxw-tablet:calc(100% / 2 - 15px);--maxw-mobile:calc(100% / 1 - 0px);--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gC09kb6v4r giBR0VUOBT"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
       
      
    <div
      parentTag="CarouselItem" id="gHpEEbZ9ps" data-id="gHpEEbZ9ps"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pl:var(--g-s-2xl);--pb:var(--g-s-2xl);--pr:var(--g-s-2xl);--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gHpEEbZ9ps gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="giRSbyXjqT gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g1KlXc2zb_"
    role="presentation"
    class="gp-group/image g1KlXc2zb_ gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_523685320072364842-794d2cbb-be4b-41fc-8295-95dda73f0722.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNDQ3LTQ1NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgZmlsbD0idXJsKCNnLTQ0Ny00NTQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii00NDciIHRvPSI0NDciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_523685320072364842-794d2cbb-be4b-41fc-8295-95dda73f0722.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNDQ3LTQ1NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgZmlsbD0idXJsKCNnLTQ0Ny00NTQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii00NDciIHRvPSI0NDciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNDQ3LTQ1NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDQ3IiBoZWlnaHQ9IjQ1NCIgZmlsbD0idXJsKCNnLTQ0Ny00NTQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii00NDciIHRvPSI0NDciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="{{ "gempages_523685320072364842-794d2cbb-be4b-41fc-8295-95dda73f0722.jpg" | file_url }}"
        width="100%"
        alt=""
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--w:80%;--w-tablet:80%;--w-mobile:80%;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
       
      
    <div
      parentTag="Col" id="gxbSJrspXe" data-id="gxbSJrspXe"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pb-mobile:8px;--cg:8px;--pc:start;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gxbSJrspXe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gqCw5uCoKh gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gzlpode08Y" data-id="gzlpode08Y"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:12px;--pt:0px;--pb:8px;--cg:2px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gzlpode08Y gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="g1oJVdzLAl gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gmaUC-tU5u"
    >
      <div 
      data-id="gmaUC-tU5u"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ga2trq9NCv gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] g_vKeZbLyq"
    >
      <div 
      data-id="g_vKeZbLyq"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gUpyqs-Cl3 gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gYgetBrRDo"
    >
      <div 
      data-id="gYgetBrRDo"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g9VtMOADZf gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gm-8-V_ZFl"
    >
      <div 
      data-id="gm-8-V_ZFl"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gUBipSi-Ju gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gBlSMC02ch"
    >
      <div 
      data-id="gBlSMC02ch"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gCPhQNIJC8 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdv9PTDKys">
    <div
      parentTag="Col"
        class="gdv9PTDKys "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:12px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-Poppins, 'Poppins'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggdv9PTDKys_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvkfdPFMGk">
    <div
      parentTag="Col"
        class="gvkfdPFMGk "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#242424;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggvkfdPFMGk_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gWTWapjIKv">
    <div
      parentTag="Col"
        class="gWTWapjIKv "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#575757;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggWTWapjIKv_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--minw:calc(100% / 2 - 15px);--minw-tablet:calc(100% / 2 - 15px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 2 - 15px);--maxw-tablet:calc(100% / 2 - 15px);--maxw-mobile:calc(100% / 1 - 0px);--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gC09kb6v4r gppf6BlS-6"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
       
      
    <div
      parentTag="CarouselItem" id="giIzXwTG25" data-id="giIzXwTG25"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pl:var(--g-s-2xl);--pb:var(--g-s-2xl);--pr:var(--g-s-2xl);--mb-mobile:0px;--pt-mobile:var(--g-s-2xl);--pl-mobile:var(--g-s-2xl);--pb-mobile:var(--g-s-2xl);--pr-mobile:var(--g-s-2xl);--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="giIzXwTG25 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gJBw2winan gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gX799F8ewD"
    role="presentation"
    class="gp-group/image gX799F8ewD gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_523685320072364842-8e03e808-8f04-4b8b-9b3f-e45ce2bca100.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNTAwLTQ2NyI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgZmlsbD0idXJsKCNnLTUwMC00NjcpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii01MDAiIHRvPSI1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_523685320072364842-8e03e808-8f04-4b8b-9b3f-e45ce2bca100.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNTAwLTQ2NyI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgZmlsbD0idXJsKCNnLTUwMC00NjcpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii01MDAiIHRvPSI1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNTAwLTQ2NyI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQ2NyIgZmlsbD0idXJsKCNnLTUwMC00NjcpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii01MDAiIHRvPSI1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="{{ "gempages_523685320072364842-8e03e808-8f04-4b8b-9b3f-e45ce2bca100.jpg" | file_url }}"
        width="100%"
        alt=""
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--w:80%;--w-tablet:80%;--w-mobile:80%;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
       
      
    <div
      parentTag="Col" id="gbORyKvOtc" data-id="gbORyKvOtc"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pb-mobile:var(--g-s-s);--cg:8px;--pc:start;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gbORyKvOtc gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gGQBIjvXqP gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gO-UyS0_T4" data-id="gO-UyS0_T4"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:12px;--pt:0px;--pb:var(--g-s-s);--cg:2px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gO-UyS0_T4 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gTTstK69RK gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] goUHJLne2i"
    >
      <div 
      data-id="goUHJLne2i"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gu3dt81JWM gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gJPVYg7iZC"
    >
      <div 
      data-id="gJPVYg7iZC"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gaVsY1STzv gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gyr7u-zitP"
    >
      <div 
      data-id="gyr7u-zitP"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ge6O4qb_N_ gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gTOHe-23V9"
    >
      <div 
      data-id="gTOHe-23V9"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gSIAJoYnjA gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gUvsRPwqBb"
    >
      <div 
      data-id="gUvsRPwqBb"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M171.328 163.808 241.216 25.344A16.512 16.512 0 0 1 256 16c5.856 0 11.712 3.104 14.88 9.344l69.888 138.464 156.736 22.272A17.184 17.184 0 0 1 512 202.24a17.536 17.536 0 0 1 -5.44 14.24l-112.736 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192a16.64 16.64 0 0 1 -4.672 1.6c-10.944 1.92 -21.376 -8.128 -19.2 -20.544l26.56 -151.36L5.536 216.48a17.6 17.6 0 0 1 -5.504 -12.896 18.56 18.56 0 0 1 2.72 -9.664 16.416 16.416 0 0 1 11.84 -7.84l156.736 -22.272zM256 384.864a16 16 0 0 1 7.424 1.792l117.952 60.608 -22.208 -126.624a18.08 18.08 0 0 1 5.184 -16.16l93.024 -88.64 -129.664 -18.432a16.8 16.8 0 0 1 -12.576 -9.216L256.032 71.136 256 71.232v313.6z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gXjQqKIwKR gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g0EojfYvZ6">
    <div
      parentTag="Col"
        class="g0EojfYvZ6 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:12px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-Poppins, 'Poppins'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg0EojfYvZ6_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3RRJsZoac">
    <div
      parentTag="Col"
        class="g3RRJsZoac "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#242424;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gg3RRJsZoac_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gbXfrdVy2p">
    <div
      parentTag="Col"
        class="gbXfrdVy2p "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#575757;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggbXfrdVy2p_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggeeo5bF-k">
    <div
      parentTag="Col"
        class="ggeeo5bF-k "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:0px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#242424;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gggeeo5bF-k_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--minw:calc(100% / 2 - 15px);--minw-tablet:calc(100% / 2 - 15px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 2 - 15px);--maxw-tablet:calc(100% / 2 - 15px);--maxw-mobile:calc(100% / 1 - 0px);--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gC09kb6v4r gG_1cyfbSG"
      data-index="2"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
       
      
    <div
      parentTag="CarouselItem" id="gw86jm7Ptp" data-id="gw86jm7Ptp"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pl:var(--g-s-2xl);--pb:var(--g-s-2xl);--pr:var(--g-s-2xl);--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gw86jm7Ptp gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="g6HDZ8kwYU gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gon0SCTPl_"
    role="presentation"
    class="gp-group/image gon0SCTPl_ gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_523685320072364842-2a7383f4-2f22-4ef5-a1e0-19c7bf379f5b.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAyMyIgaGVpZ2h0PSI4NTkiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwMjMtODU5Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMDIzIiBoZWlnaHQ9Ijg1OSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAyMyIgaGVpZ2h0PSI4NTkiIGZpbGw9InVybCgjZy0xMDIzLTg1OSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwMjMiIHRvPSIxMDIzIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_523685320072364842-2a7383f4-2f22-4ef5-a1e0-19c7bf379f5b.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAyMyIgaGVpZ2h0PSI4NTkiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwMjMtODU5Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMDIzIiBoZWlnaHQ9Ijg1OSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAyMyIgaGVpZ2h0PSI4NTkiIGZpbGw9InVybCgjZy0xMDIzLTg1OSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwMjMiIHRvPSIxMDIzIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAyMyIgaGVpZ2h0PSI4NTkiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwMjMtODU5Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMDIzIiBoZWlnaHQ9Ijg1OSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAyMyIgaGVpZ2h0PSI4NTkiIGZpbGw9InVybCgjZy0xMDIzLTg1OSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwMjMiIHRvPSIxMDIzIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+"
        data-src="{{ "gempages_523685320072364842-2a7383f4-2f22-4ef5-a1e0-19c7bf379f5b.jpg" | file_url }}"
        width="100%"
        alt=""
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
       
      
    <div
      parentTag="Col" id="gVQW8tgPrE" data-id="gVQW8tgPrE"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pb-mobile:var(--g-s-s);--cg:8px;--pc:center;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gVQW8tgPrE gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center;--o-mobile:0"
      class="gCcBYJpnTP gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gRFlSkNvh2" data-id="gRFlSkNvh2"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:12px;--pt:0px;--pb:var(--g-s-s);--cg:2px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gRFlSkNvh2 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="g3E-Y5mxql gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gfY89UdPNq"
    >
      <div 
      data-id="gfY89UdPNq"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gKRmmqhaHk gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gUKq-vZWCn"
    >
      <div 
      data-id="gUKq-vZWCn"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gQAyAA64I7 gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] gHOSvdJhxu"
    >
      <div 
      data-id="gHOSvdJhxu"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="grxSDqYgRK gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] g_9IS7Uq3p"
    >
      <div 
      data-id="g_9IS7Uq3p"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gZl_EvSxzb gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
      class="gp-leading-[0] g8ecrgk30V"
    >
      <div 
      data-id="g8ecrgk30V"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:0px;--bc:transparent;--bg:transparent"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-warning, warning);--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-warning"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M171.328 163.808 241.216 25.344A16.512 16.512 0 0 1 256 16c5.856 0 11.712 3.104 14.88 9.344l69.888 138.464 156.736 22.272A17.184 17.184 0 0 1 512 202.24a17.536 17.536 0 0 1 -5.44 14.24l-112.736 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192a16.64 16.64 0 0 1 -4.672 1.6c-10.944 1.92 -21.376 -8.128 -19.2 -20.544l26.56 -151.36L5.536 216.48a17.6 17.6 0 0 1 -5.504 -12.896 18.56 18.56 0 0 1 2.72 -9.664 16.416 16.416 0 0 1 11.84 -7.84l156.736 -22.272zM256 384.864a16 16 0 0 1 7.424 1.792l117.952 60.608 -22.208 -126.624a18.08 18.08 0 0 1 5.184 -16.16l93.024 -88.64 -129.664 -18.432a16.8 16.8 0 0 1 -12.576 -9.216L256.032 71.136 256 71.232v313.6z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center;--o-mobile:2"
      class="gN0xh-S5C6 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gwjLQiAub5">
    <div
      parentTag="Col"
        class="gwjLQiAub5 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:12px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-Poppins, 'Poppins'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggwjLQiAub5_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3uE6ISuT2">
    <div
      parentTag="Col"
        class="g3uE6ISuT2 "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#242424;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gg3uE6ISuT2_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g0d_1oSet9">
    <div
      parentTag="Col"
        class="g0d_1oSet9 "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#242424;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gg0d_1oSet9_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next gC09kb6v4r-{{section.id}} gp-carousel-arrow-gC09kb6v4r gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-relative  tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:flex;--d-tablet:none;--d-mobile:none;background-color:;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--w:22px;--w-tablet:22px;--w-mobile:20px;--h:22px;--h-tablet:22px;--h-mobile:20px"
  >
    <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M32 256a16 16 0 0 1 16 -16h377.376l-100.704 -100.672a16 16 0 0 1 22.656 -22.656l128 128a16 16 0 0 1 0 22.656l-128 128a16 16 0 0 1 -22.656 -22.656L425.376 272H48A16 16 0 0 1 32 256z" /></svg>
    </div>
      <style>
    .gp-carousel-arrow-gC09kb6v4r {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gC09kb6v4r::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gC09kb6v4r {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gC09kb6v4r::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gC09kb6v4r {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gC09kb6v4r::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gC09kb6v4r-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-absolute mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--mt:16px;--mt-tablet:16px;--bottom-mobile:16px;--d:flex;--d-tablet:flex;--d-mobile:flex"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfedA.woff) format('woff');
}
/* devanagari */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJbecnFHGPezSQ.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJnecnFHGPezSQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 6",
    "tag": "section",
    "class": "gps-557276193259651928 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=557276193259651928)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggdv9PTDKys_text","label":"ggdv9PTDKys_text","default":"<span style=\"color:#777777;\">Verified Customer</span>"},{"type":"html","id":"ggvkfdPFMGk_text","label":"ggvkfdPFMGk_text","default":"<p><span style=\"font-size:19px;\"><strong>Smoother Skin After Six Weeks—Worth the Effort!</strong></span></p>"},{"type":"html","id":"ggWTWapjIKv_text","label":"ggWTWapjIKv_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">I've been using this cellulite massager for six weeks now, and I can see a difference in my skin texture. The device has multiple settings and a heating function that helps with circulation. Battery life is decent, lasting 4-5 sessions between charges. While it takes consistent use to see results, my thighs and arms do look smoother. The massage can be a bit intense at first, but you get used to it. For the price, it's a solid option if you're looking to improve skin appearance as part of your regular routine.</span></p><p>&nbsp;</p><p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\"><strong>- Sarah M.</strong></span></p>"},{"type":"html","id":"gg0EojfYvZ6_text","label":"gg0EojfYvZ6_text","default":"<span style=\"color:#777777;\">Verified Customer</span>"},{"type":"html","id":"gg3RRJsZoac_text","label":"gg3RRJsZoac_text","default":"<p><span style=\"font-size:19px;\"><strong>Cellulite’s Losing, My Booty’s Winning</strong></span></p>"},{"type":"html","id":"ggbXfrdVy2p_text","label":"ggbXfrdVy2p_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">\"been using this regularly with my gym routine and surprised how much smoother my booty area looks now lol. heat setting is nice + helps after leg day. battery dies kinda quick but tbh the results are worth charging it more. definitely helps with cellulite if ur consistent with it\"</span></p>"},{"type":"html","id":"gggeeo5bF-k_text","label":"gggeeo5bF-k_text","default":"<p>- <span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">Jade S.</span></p>"},{"type":"html","id":"ggwjLQiAub5_text","label":"ggwjLQiAub5_text","default":"<span style=\"color:#777777;\">Verified Customer</span>"},{"type":"html","id":"gg3uE6ISuT2_text","label":"gg3uE6ISuT2_text","default":"<p><span style=\"font-size:19px;\"><strong>Leg Confidence Unlocked—Thank You, Elouris!</strong></span></p>"},{"type":"html","id":"gg0d_1oSet9_text","label":"gg0d_1oSet9_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">Anyone looking to buy this but are hesitant, just look at the difference after i used this consistently. This is not overnight results but this tool has changed my life and has made me feel so confident now in showing my legs off. I am so grateful to Elouris, thank you so much!</span></p><p>&nbsp;</p><p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\"><strong>- Lauren T.</strong></span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
