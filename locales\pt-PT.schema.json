/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Cores",
      "settings": {
        "colors_solid_button_labels": {
          "label": "Etiqueta do botão sólido",
          "info": "Utilizado como cor do primeiro plano em cores de destaque."
        },
        "colors_accent_1": {
          "label": "Destaque 1",
          "info": "Utilizado para fundo do botão sólido."
        },
        "colors_accent_2": {
          "label": "Destaque 2"
        },
        "header__1": {
          "content": "Cores primárias"
        },
        "header__2": {
          "content": "Cores secundárias"
        },
        "colors_text": {
          "label": "Texto",
          "info": "Utilizado como cor do primeiro plano em cores de fundo."
        },
        "colors_outline_button_labels": {
          "label": "Botão de contorno",
          "info": "Também utilizado para ligações de texto."
        },
        "colors_background_1": {
          "label": "Fundo 1"
        },
        "colors_background_2": {
          "label": "Fundo 2"
        },
        "gradient_accent_1": {
          "label": "Gradiente de destaque 1"
        },
        "gradient_accent_2": {
          "label": "Gradiente de destaque 2"
        },
        "gradient_background_1": {
          "label": "Gradiente de plano de fundo 1"
        },
        "gradient_background_2": {
          "label": "Gradiente de plano de fundo 2"
        }
      }
    },
    "typography": {
      "name": "Tipografia",
      "settings": {
        "type_header_font": {
          "label": "Tipo de letra",
          "info": "Selecionar um tipo de letra diferente pode afetar a velocidade da sua loja. [Saiba mais sobre tipos de letra do sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "header__1": {
          "content": "Títulos"
        },
        "header__2": {
          "content": "Corpo"
        },
        "type_body_font": {
          "label": "Tipo de letra",
          "info": "Selecionar um tipo de letra diferente pode afetar a velocidade da sua loja. [Saiba mais sobre tipos de letra do sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "heading_scale": {
          "label": "Escala de tamanho do tipo de letra"
        },
        "body_scale": {
          "label": "Escala de tamanho do tipo de letra"
        }
      }
    },
    "styles": {
      "name": "Ícones",
      "settings": {
        "accent_icons": {
          "options__3": {
            "label": "Botão de contorno"
          },
          "options__4": {
            "label": "Texto"
          },
          "label": "Cor"
        }
      }
    },
    "social-media": {
      "name": "Redes sociais",
      "settings": {
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https://twitter.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://twitter.com/shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "http://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Contas de redes sociais"
        }
      }
    },
    "currency_format": {
      "name": "Formato de moeda",
      "settings": {
        "content": "Códigos de moeda",
        "currency_code_enabled": {
          "label": "Mostrar códigos de moeda"
        },
        "paragraph": "Os preços de finalização da compra e carrinho mostram sempre os códigos de moeda. Exemplo: 1,00 USD."
      }
    },
    "layout": {
      "name": "Esquema",
      "settings": {
        "page_width": {
          "label": "Largura da página"
        },
        "spacing_sections": {
          "label": "Espaço entre as secções do modelo"
        },
        "header__grid": {
          "content": "Grelha"
        },
        "paragraph__grid": {
          "content": "Afeta áreas com várias colunas ou linhas."
        },
        "spacing_grid_horizontal": {
          "label": "Espaço horizontal"
        },
        "spacing_grid_vertical": {
          "label": "Espaço vertical"
        }
      }
    },
    "search_input": {
      "name": "Comportamento de pesquisa",
      "settings": {
        "header": {
          "content": "Sugestões de pesquisa"
        },
        "predictive_search_enabled": {
          "label": "Ativar as sugestões de pesquisa"
        },
        "predictive_search_show_vendor": {
          "label": "Mostrar o fornecedor do produto",
          "info": "Visível quando as sugestões de pesquisa estão ativas."
        },
        "predictive_search_show_price": {
          "label": "Mostrar o preço do produto",
          "info": "Visível quando as sugestões de pesquisa estão ativas."
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Limite"
        },
        "header__shadow": {
          "content": "Sombra"
        },
        "blur": {
          "label": "Desfocado"
        },
        "corner_radius": {
          "label": "Raio do canto"
        },
        "horizontal_offset": {
          "label": "Compensação horizontal"
        },
        "vertical_offset": {
          "label": "Compensação vertical"
        },
        "thickness": {
          "label": "Espessura"
        },
        "opacity": {
          "label": "Opacidade"
        },
        "image_padding": {
          "label": "Preenchimento da imagem"
        },
        "text_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do texto"
        }
      }
    },
    "badges": {
      "name": "Selos",
      "settings": {
        "position": {
          "options__1": {
            "label": "Canto inferior esquerdo"
          },
          "options__2": {
            "label": "Canto inferior direito"
          },
          "options__3": {
            "label": "Canto superior esquerdo"
          },
          "options__4": {
            "label": "Canto superior direito"
          },
          "label": "Posição nos cartões"
        },
        "sale_badge_color_scheme": {
          "label": "Esquema de cor do selo de venda"
        },
        "sold_out_badge_color_scheme": {
          "label": "Esquema de cor do selo de esgotado"
        }
      }
    },
    "buttons": {
      "name": "Botões"
    },
    "variant_pills": {
      "name": "Variantes com forma de comprimidos",
      "paragraph": "As variantes com forma de comprimidos são uma forma de apresentar as variantes do seu produto. [Saber mais](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"
    },
    "inputs": {
      "name": "Entradas"
    },
    "content_containers": {
      "name": "Contentores de conteúdo"
    },
    "popups": {
      "name": "Menus pendentes e pop-ups",
      "paragraph": "Afeta áreas como menus pendentes de navegação, pop-ups de modais e pop-ups de carrinho."
    },
    "media": {
      "name": "Conteúdo multimédia"
    },
    "drawers": {
      "name": "Gavetas"
    },
    "cart": {
      "name": "Carrinho",
      "settings": {
        "cart_type": {
          "label": "Tipo de carrinho",
          "drawer": {
            "label": "Gaveta"
          },
          "page": {
            "label": "Página"
          },
          "notification": {
            "label": "Notificação pop-up"
          }
        },
        "show_vendor": {
          "label": "Mostrar fornecedor"
        },
        "show_cart_note": {
          "label": "Ativar nota do carrinho"
        },
        "cart_drawer": {
          "header": "Painel deslizante do carrinho",
          "collection": {
            "label": "Coleção",
            "info": "Visível quando o painel deslizante do carrinho está vazio."
          }
        }
      }
    },
    "cards": {
      "name": "Cartões de produtos",
      "settings": {
        "style": {
          "options__1": {
            "label": "Padrão"
          },
          "options__2": {
            "label": "Cartão"
          },
          "label": "Estilo"
        }
      }
    },
    "collection_cards": {
      "name": "Cartões de coleção",
      "settings": {
        "style": {
          "options__1": {
            "label": "Padrão"
          },
          "options__2": {
            "label": "Cartão"
          },
          "label": "Estilo"
        }
      }
    },
    "blog_cards": {
      "name": "Cartões de blogue",
      "settings": {
        "style": {
          "options__1": {
            "label": "Padrão"
          },
          "options__2": {
            "label": "Cartão"
          },
          "label": "Estilo"
        }
      }
    },
    "logo": {
      "name": "Logótipo",
      "settings": {
        "logo_image": {
          "label": "Logótipo"
        },
        "logo_width": {
          "label": "Largura do logótipo em computador",
          "info": "A largura do logótipo é automaticamente otimizada para dispositivos móveis."
        },
        "favicon": {
          "label": "Imagem Favicon",
          "info": "Será reduzida para 32 x 32 px"
        }
      }
    },
    "brand_information": {
      "name": "Informação de marca",
      "settings": {
        "brand_headline": {
          "label": "Cabeçalho"
        },
        "brand_description": {
          "label": "Descrição"
        },
        "brand_image": {
          "label": "Imagem"
        },
        "brand_image_width": {
          "label": "Largura da imagem"
        },
        "paragraph": {
          "content": "Adicione uma descrição da marca ao rodapé da sua loja."
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Secção preenchimento",
        "padding_top": "Preenchimento superior",
        "padding_bottom": "Preenchimento inferior"
      },
      "spacing": "Espaçamento",
      "colors": {
        "accent_1": {
          "label": "Destaque 1"
        },
        "accent_2": {
          "label": "Destaque 2"
        },
        "background_1": {
          "label": "Fundo 1"
        },
        "background_2": {
          "label": "Fundo 2"
        },
        "inverse": {
          "label": "Invertido"
        },
        "label": "Esquema de cores",
        "has_cards_info": "Para alterar o esquema de cores do cartão, atualize as suas definições de tema."
      },
      "heading_size": {
        "label": "Tamanho do título",
        "options__1": {
          "label": "Pequeno"
        },
        "options__2": {
          "label": "Médio"
        },
        "options__3": {
          "label": "Grande"
        },
        "options__4": {
          "label": "Extra grande"
        }
      }
    },
    "announcement-bar": {
      "name": "Barra de comunicado",
      "blocks": {
        "announcement": {
          "settings": {
            "text": {
              "label": "Texto"
            },
            "text_alignment": {
              "label": "Alinhamento do texto",
              "options__1": {
                "label": "Esquerda"
              },
              "options__2": {
                "label": "Centro"
              },
              "options__3": {
                "label": "Direita"
              }
            },
            "link": {
              "label": "Ligação"
            }
          },
          "name": "Comunicado"
        }
      }
    },
    "collage": {
      "name": "Colagem",
      "settings": {
        "heading": {
          "label": "Título"
        },
        "desktop_layout": {
          "label": "Esquema do desktop",
          "options__1": {
            "label": "Bloco esquerdo grande"
          },
          "options__2": {
            "label": "Bloco direito grande"
          }
        },
        "mobile_layout": {
          "label": "Esquema móvel",
          "options__1": {
            "label": "Colagem"
          },
          "options__2": {
            "label": "Coluna"
          }
        },
        "card_styles": {
          "label": "Estilo do cartão",
          "info": "O produto, coleção e estilos do cartão de blogue podem ser atualizados nas definições do tema.",
          "options__1": {
            "label": "Utilizar estilos de cartão individuais"
          },
          "options__2": {
            "label": "Estilizar todos os cartões de produto"
          }
        }
      },
      "blocks": {
        "image": {
          "settings": {
            "image": {
              "label": "Imagem"
            }
          },
          "name": "Imagem"
        },
        "product": {
          "settings": {
            "product": {
              "label": "Produto"
            },
            "secondary_background": {
              "label": "Mostrar fundo secundário"
            },
            "second_image": {
              "label": "Mostrar a segunda imagem ao passar o rato"
            }
          },
          "name": "Produto"
        },
        "collection": {
          "settings": {
            "collection": {
              "label": "Coleção"
            }
          },
          "name": "Coleção"
        },
        "video": {
          "settings": {
            "cover_image": {
              "label": "Imagem de capa"
            },
            "video_url": {
              "label": "URL",
              "info": "O video será reproduzido numa janela pop-up se a secção contiver outros blocos.",
              "placeholder": "Usar um URL de YouTube ou Vimeo"
            },
            "description": {
              "label": "Texto alternativo do vídeo",
              "info": "Descreve o vídeo para que seja acessível a clientes que usam leitores de ecrã. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"
            }
          },
          "name": "Vídeo"
        }
      },
      "presets": {
        "name": "Colagem"
      }
    },
    "collection-list": {
      "name": "Lista de coleções",
      "settings": {
        "title": {
          "label": "Título"
        },
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Retrato"
          },
          "options__3": {
            "label": "Quadrado"
          },
          "info": "Adicione imagens ao editar as suas coleções. [Saber mais](https://help.shopify.com/manual/products/collections)"
        },
        "swipe_on_mobile": {
          "label": "Ativar leitura magnética no dispositivo móvel"
        },
        "show_view_all": {
          "label": "Ative o botão \"Ver tudo\" caso a lista possua mais coleções do que as apresentadas"
        },
        "columns_desktop": {
          "label": "Número de colunas no ambiente de trabalho"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        },
        "columns_mobile": {
          "label": "Número de colunas em dispositivo móvel",
          "options__1": {
            "label": "1 coluna"
          },
          "options__2": {
            "label": "2 colunas"
          }
        }
      },
      "blocks": {
        "featured_collection": {
          "settings": {
            "collection": {
              "label": "Coleção"
            }
          },
          "name": "Coleção"
        }
      },
      "presets": {
        "name": "Lista de coleções"
      }
    },
    "contact-form": {
      "name": "Formulário de contacto",
      "presets": {
        "name": "Formulário de contacto"
      }
    },
    "custom-liquid": {
      "name": "Liquid personalizado",
      "settings": {
        "custom_liquid": {
          "label": "Liquid personalizado",
          "info": "Adicione fragmentos de aplicação ou outro código Liquid para criar personalizações avançadas."
        }
      },
      "presets": {
        "name": "Liquid personalizado"
      }
    },
    "featured-blog": {
      "name": "Publicações no blogue",
      "settings": {
        "heading": {
          "label": "Título"
        },
        "blog": {
          "label": "Blogue"
        },
        "post_limit": {
          "label": "Número de publicações no blogue a mostrar"
        },
        "show_view_all": {
          "label": "Ative o botão \"Ver tudo\" caso o blogue possua mais publicações no blogue do que as apresentadas"
        },
        "show_image": {
          "label": "Mostrar imagem em destaque",
          "info": "Para obter os melhores resultados, use uma imagem com uma proporção de 3:2. [Saber mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "show_date": {
          "label": "Mostrar data"
        },
        "show_author": {
          "label": "Mostrar autor"
        },
        "columns_desktop": {
          "label": "Número de colunas no ambiente de trabalho"
        }
      },
      "presets": {
        "name": "Publicações no blogue"
      }
    },
    "featured-collection": {
      "name": "Coleção em destaque",
      "settings": {
        "title": {
          "label": "Título"
        },
        "collection": {
          "label": "Coleção"
        },
        "products_to_show": {
          "label": "Máximo de produtos a apresentar"
        },
        "show_view_all": {
          "label": "Ative a opção \"Ver tudo\" se a coleção tiver mais produtos do que os apresentados"
        },
        "header": {
          "content": "Cartão de produtos"
        },
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Retrato"
          },
          "options__3": {
            "label": "Quadrado"
          }
        },
        "show_secondary_image": {
          "label": "Mostrar a segunda imagem ao passar o rato"
        },
        "show_vendor": {
          "label": "Mostrar fornecedor"
        },
        "show_rating": {
          "label": "Mostrar classificação do produto",
          "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "enable_quick_buy": {
          "label": "Ativar botão para adicionar rapidamente",
          "info": "Ideal para o tipo de carrinho deslizante ou pop-up."
        },
        "columns_desktop": {
          "label": "Número de colunas no ambiente de trabalho"
        },
        "description": {
          "label": "Descrição"
        },
        "show_description": {
          "label": "Mostrar descrição da coleção do administrador"
        },
        "description_style": {
          "label": "Estilo da descrição",
          "options__1": {
            "label": "Corpo"
          },
          "options__2": {
            "label": "Legenda"
          },
          "options__3": {
            "label": "Maiúsculas"
          }
        },
        "view_all_style": {
          "options__1": {
            "label": "Ligação"
          },
          "options__2": {
            "label": "Botão de contorno"
          },
          "options__3": {
            "label": "Botão sólido"
          },
          "label": "Estilo \"ver tudo\""
        },
        "enable_desktop_slider": {
          "label": "Ativar carrossel em computador"
        },
        "full_width": {
          "label": "Tornar os produtos em largura total"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        },
        "columns_mobile": {
          "label": "Número de colunas em dispositivo móvel",
          "options__1": {
            "label": "1 coluna"
          },
          "options__2": {
            "label": "2 colunas"
          }
        },
        "swipe_on_mobile": {
          "label": "Ativar leitura magnética no dispositivo móvel"
        }
      },
      "presets": {
        "name": "Coleção em destaque"
      }
    },
    "footer": {
      "name": "Rodapé",
      "blocks": {
        "link_list": {
          "settings": {
            "heading": {
              "label": "Cabeçalho"
            },
            "menu": {
              "label": "Menu",
              "info": "Apresenta apenas itens de menu de nível superior."
            }
          },
          "name": "Menu"
        },
        "text": {
          "settings": {
            "heading": {
              "label": "Cabeçalho"
            },
            "subtext": {
              "label": "Subtexto"
            }
          },
          "name": "Texto"
        },
        "brand_information": {
          "name": "Informação de marca",
          "settings": {
            "paragraph": {
              "content": "Este bloco irá apresentar a sua informação de marca. [Editar informação de marca.](/editor?context=theme&category=brand%20information)"
            },
            "header__1": {
              "content": "Ícones de redes sociais"
            },
            "show_social": {
              "label": "Mostrar ícones de redes sociais",
              "info": "Para apresentar as suas contas de redes sociais, associe-as nas [definições do tema](/editor?context=theme&category=social%20media)."
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Mostrar registo de e-mail"
        },
        "newsletter_heading": {
          "label": "Cabeçalho"
        },
        "header__1": {
          "info": "Subscritores adicionados automaticamente à sua lista de clientes que \"aceitam marketing\". [Saber mais](https://help.shopify.com/manual/customers/manage-customers)",
          "content": "Registo de e-mail"
        },
        "header__2": {
          "content": "Ícones de redes sociais",
          "info": "Para apresentar as suas contas de redes sociais, associe-as nas [definições do tema](/editor?context=theme&category=social%20media)."
        },
        "show_social": {
          "label": "Mostrar ícones de redes sociais"
        },
        "header__3": {
          "content": "Seletor de país/região"
        },
        "header__4": {
          "info": "Para adicionar um país/região, vá a [definições de mercado.](/admin/settings/markets)"
        },
        "enable_country_selector": {
          "label": "Ativar seletor de país/região"
        },
        "header__5": {
          "content": "Seletor de idioma"
        },
        "header__6": {
          "info": "Para adicionar um idioma, vá a [definições de idioma.](/admin/settings/languages)"
        },
        "enable_language_selector": {
          "label": "Ativar seletor de idioma"
        },
        "header__7": {
          "content": "Métodos de pagamento"
        },
        "payment_enable": {
          "label": "Mostrar ícones de pagamento"
        },
        "margin_top": {
          "label": "Margem superior"
        },
        "header__8": {
          "content": "Ligações das políticas",
          "info": "Para adicionar políticas de loja, aceda às suas [definições de políticas](/admin/settings/legal)."
        },
        "show_policy": {
          "label": "Mostrar ligações das políticas"
        },
        "header__9": {
          "content": "Seguir no Shop",
          "info": "Apresente um botão para seguir na sua frente de loja na aplicação Shop. [Saber mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        },
        "enable_follow_on_shop": {
          "label": "Ativar Seguir no Shop"
        }
      }
    },
    "header": {
      "name": "Cabeçalho",
      "settings": {
        "logo_position": {
          "label": "Posição do logótipo de desktop",
          "options__1": {
            "label": "Intermédio à esquerda"
          },
          "options__2": {
            "label": "Canto superior esquerdo"
          },
          "options__3": {
            "label": "Superior centro"
          },
          "options__4": {
            "label": "Intermédio ao centro"
          }
        },
        "menu": {
          "label": "Menu"
        },
        "show_line_separator": {
          "label": "Mostrar linha do separador"
        },
        "margin_bottom": {
          "label": "Margem inferior"
        },
        "menu_type_desktop": {
          "label": "Tipo de menu do ambiente de trabalho",
          "info": "O tipo de menu é automaticamente otimizado para dispositivos móveis.",
          "options__1": {
            "label": "Menu pendente"
          },
          "options__2": {
            "label": "Mega menu"
          }
        },
        "mobile_layout": {
          "content": "Esquema móvel"
        },
        "mobile_logo_position": {
          "label": "Posição móvel do logótipo",
          "options__1": {
            "label": "Centro"
          },
          "options__2": {
            "label": "Esquerda"
          }
        },
        "logo_header": {
          "content": "Logótipo"
        },
        "logo_help": {
          "content": "Edite o seu logótipo em [definições de tema](/editor?context=theme&category=logo)."
        },
        "sticky_header_type": {
          "label": "Cabeçalho fixo",
          "options__1": {
            "label": "Nenhum"
          },
          "options__2": {
            "label": "Ao rodar a roda do rato"
          },
          "options__3": {
            "label": "Sempre"
          },
          "options__4": {
            "label": "Sempre, reduzir o tamanho do logótipo"
          }
        }
      }
    },
    "image-banner": {
      "name": "Faixa de imagem",
      "settings": {
        "image": {
          "label": "Primeira imagem"
        },
        "image_2": {
          "label": "Segunda imagem"
        },
        "color_scheme": {
          "info": "Visível quando o recetor é exibido."
        },
        "stack_images_on_mobile": {
          "label": "Empilhar imagens em dispositivos móveis"
        },
        "adapt_height_first_image": {
          "label": "Adaptar altura da secção ao tamanho da primeira imagem",
          "info": "Quando marcada, substitui a definição de altura do banner da imagem."
        },
        "show_text_box": {
          "label": "Mostrar recetores no desktop"
        },
        "image_overlay_opacity": {
          "label": "Opacidade da sobreposição da imagem"
        },
        "header": {
          "content": "Esquema para dispositivo móvel"
        },
        "show_text_below": {
          "label": "Mostrar recetor em dispositivo móvel"
        },
        "image_height": {
          "label": "Altura da faixa",
          "options__1": {
            "label": "Adaptar à primeira imagem"
          },
          "options__2": {
            "label": "Pequeno"
          },
          "options__3": {
            "label": "Médio"
          },
          "info": "Para obter os melhores resultados, use uma imagem com uma proporção de 3:2. [Saber mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
          "options__4": {
            "label": "Grande"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Canto superior esquerdo"
          },
          "options__2": {
            "label": "Superior centro"
          },
          "options__3": {
            "label": "Canto superior direito"
          },
          "options__4": {
            "label": "Intermédio à esquerda"
          },
          "options__5": {
            "label": "Intermédio ao centro"
          },
          "options__6": {
            "label": "Intermédio à direita"
          },
          "options__7": {
            "label": "Canto inferior esquerdo"
          },
          "options__8": {
            "label": "Inferior centro"
          },
          "options__9": {
            "label": "Canto inferior direito"
          },
          "label": "Posição do conteúdo em computadores"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em computadores"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em dispositivos móveis"
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Título"
            }
          },
          "name": "Cabeçalho"
        },
        "text": {
          "settings": {
            "text": {
              "label": "Descrição"
            },
            "text_style": {
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              },
              "options__3": {
                "label": "Maiúsculas"
              },
              "label": "Estilo de texto"
            }
          },
          "name": "Texto"
        },
        "buttons": {
          "settings": {
            "button_label_1": {
              "label": "Primeira etiqueta do botão",
              "info": "Deixe a etiqueta em branco para ocultar o botão."
            },
            "button_link_1": {
              "label": "Primeira ligação do botão"
            },
            "button_style_secondary_1": {
              "label": "Utilizar estilo de botão de contorno"
            },
            "button_label_2": {
              "label": "Segunda etiqueta do botão",
              "info": "Deixe a etiqueta em branco para ocultar o botão."
            },
            "button_link_2": {
              "label": "Segunda ligação do botão"
            },
            "button_style_secondary_2": {
              "label": "Utilizar estilo de botão de contorno"
            }
          },
          "name": "Botões"
        }
      },
      "presets": {
        "name": "Faixa de imagem"
      }
    },
    "image-with-text": {
      "name": "Imagem com texto",
      "settings": {
        "image": {
          "label": "Imagem"
        },
        "height": {
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Pequeno"
          },
          "options__3": {
            "label": "Médio"
          },
          "label": "Altura da imagem",
          "options__4": {
            "label": "Grande"
          }
        },
        "layout": {
          "options__1": {
            "label": "Imagem primeiro"
          },
          "options__2": {
            "label": "Segunda imagem"
          },
          "label": "Posicionamento da imagem em desktop",
          "info": "Imagem primeiro é o esquema predefinido para dispositivos móveis."
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Pequeno"
          },
          "options__2": {
            "label": "Médio"
          },
          "options__3": {
            "label": "Grande"
          },
          "label": "Largura da imagem em desktop",
          "info": "A imagem é otimizada automaticamente em dispositivos móveis."
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em desktop",
          "options__2": {
            "label": "Ao centro"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Em cima"
          },
          "options__2": {
            "label": "Meio"
          },
          "options__3": {
            "label": "Em baixo"
          },
          "label": "Posição do conteúdo em desktop"
        },
        "content_layout": {
          "options__1": {
            "label": "Sem sobreposição"
          },
          "options__2": {
            "label": "Sobreposição"
          },
          "label": "Esquema do conteúdo"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em dispositivos móveis",
          "options__2": {
            "label": "Ao centro"
          }
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Título"
            }
          },
          "name": "Cabeçalho"
        },
        "text": {
          "settings": {
            "text": {
              "label": "Conteúdo"
            },
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              }
            }
          },
          "name": "Texto"
        },
        "button": {
          "settings": {
            "button_label": {
              "label": "Etiqueta do botão",
              "info": "Deixe a etiqueta em branco para ocultar o botão."
            },
            "button_link": {
              "label": "Ligação do botão"
            }
          },
          "name": "Botão"
        },
        "caption": {
          "name": "Legenda",
          "settings": {
            "text": {
              "label": "Texto"
            },
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Legenda"
              },
              "options__2": {
                "label": "Maiúsculas"
              }
            },
            "caption_size": {
              "label": "Tamanho do texto",
              "options__1": {
                "label": "Pequeno"
              },
              "options__2": {
                "label": "Médio"
              },
              "options__3": {
                "label": "Grande"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Imagem com texto"
      }
    },
    "main-article": {
      "name": "Publicação no blogue",
      "blocks": {
        "featured_image": {
          "settings": {
            "image_height": {
              "label": "Altura da imagem em destaque",
              "options__1": {
                "label": "Adaptar à imagem"
              },
              "options__2": {
                "label": "Pequeno"
              },
              "options__3": {
                "label": "Médio"
              },
              "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [Saber mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
              "options__4": {
                "label": "Grande"
              }
            }
          },
          "name": "Imagem em destaque"
        },
        "title": {
          "settings": {
            "blog_show_date": {
              "label": "Mostrar data"
            },
            "blog_show_author": {
              "label": "Mostrar autor"
            }
          },
          "name": "Título"
        },
        "content": {
          "name": "Conteúdo"
        },
        "share": {
          "name": "Partilhar",
          "settings": {
            "featured_image_info": {
              "content": "Se incluir uma ligação nas publicações das redes sociais, a imagem em destaque da página será demonstrada como a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "É incluído um título de loja e descrição com a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Texto"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Publicações no blogue",
      "settings": {
        "header": {
          "content": "Cartão de publicação no blogue"
        },
        "show_image": {
          "label": "Mostrar imagem em destaque"
        },
        "paragraph": {
          "content": "Altere excertos ao editar as suas publicações no blogue. [Saber mais](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"
        },
        "show_date": {
          "label": "Mostrar data"
        },
        "show_author": {
          "label": "Mostrar autor"
        },
        "layout": {
          "label": "Esquema de desktop",
          "options__1": {
            "label": "Grelha"
          },
          "options__2": {
            "label": "Colagem"
          },
          "info": "Publicações empilhadas em dispositivo móvel."
        },
        "image_height": {
          "label": "Altura da imagem em destaque",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Pequeno"
          },
          "options__3": {
            "label": "Médio"
          },
          "options__4": {
            "label": "Grande"
          },
          "info": "Para obter os melhores resultados, use uma imagem com uma proporção de 3:2. [Saber mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-cart-footer": {
      "name": "Subtotal",
      "blocks": {
        "subtotal": {
          "name": "Subtotal"
        },
        "buttons": {
          "name": "Botão de finalização da compra"
        }
      }
    },
    "main-cart-items": {
      "name": "Itens"
    },
    "main-collection-banner": {
      "name": "Faixa de coleção",
      "settings": {
        "paragraph": {
          "content": "Adicione uma descrição ou imagem ao editar a sua coleção. [Saber mais](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Mostrar descrição da coleção"
        },
        "show_collection_image": {
          "label": "Mostrar imagem da coleção",
          "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [Saber mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Grelha de produtos",
      "settings": {
        "products_per_page": {
          "label": "Produtos por página"
        },
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Retrato"
          },
          "options__3": {
            "label": "Quadrado"
          }
        },
        "show_secondary_image": {
          "label": "Mostrar a segunda imagem ao passar o rato"
        },
        "show_vendor": {
          "label": "Mostrar fornecedor"
        },
        "header__1": {
          "content": "Filtragem e ordenação"
        },
        "enable_tags": {
          "label": "Ativar filtragem",
          "info": "Personalize filtros com a aplicação Search & Discovery. [Saber mais](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_filtering": {
          "label": "Ativar filtragem",
          "info": "Personalize filtros com a aplicação Search & Discovery. [Saber mais](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Ativar ordenação"
        },
        "header__3": {
          "content": "Cartão de produtos"
        },
        "show_rating": {
          "label": "Mostrar classificação do produto",
          "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"
        },
        "enable_quick_buy": {
          "label": "Ativar botão para adicionar rapidamente",
          "info": "Ideal para o tipo de carrinho deslizante ou pop-up."
        },
        "columns_desktop": {
          "label": "Número de colunas no ambiente de trabalho"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        },
        "columns_mobile": {
          "label": "Número de colunas em dispositivo móvel",
          "options__1": {
            "label": "1 coluna"
          },
          "options__2": {
            "label": "2 colunas"
          }
        },
        "filter_type": {
          "label": "Esquema de filtro para desktop",
          "options__1": {
            "label": "Horizontal"
          },
          "options__2": {
            "label": "Vertical"
          },
          "options__3": {
            "label": "Gaveta"
          },
          "info": "A gaveta é o esquema padrão para dispositivos móveis."
        }
      }
    },
    "main-list-collections": {
      "name": "Página da lista de coleções",
      "settings": {
        "title": {
          "label": "Título"
        },
        "sort": {
          "label": "Ordenar coleções por:",
          "options__1": {
            "label": "Alfabeticamente, A-Z"
          },
          "options__2": {
            "label": "Alfabeticamente, Z-A"
          },
          "options__3": {
            "label": "Data, mais recentes"
          },
          "options__4": {
            "label": "Data, mais antigos"
          },
          "options__5": {
            "label": "Contagem de produtos, alta para baixa"
          },
          "options__6": {
            "label": "Contagem de produtos, baixa para alta"
          }
        },
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Retrato"
          },
          "options__3": {
            "label": "Quadrado"
          },
          "info": "Adicione imagens ao editar as suas coleções. [Saber mais](https://help.shopify.com/manual/products/collections)"
        },
        "columns_desktop": {
          "label": "Número de colunas no ambiente de trabalho"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        },
        "columns_mobile": {
          "label": "Número de colunas em dispositivo móvel",
          "options__1": {
            "label": "1 coluna"
          },
          "options__2": {
            "label": "2 colunas"
          }
        }
      }
    },
    "main-page": {
      "name": "Página"
    },
    "main-password-footer": {
      "name": "Rodapé de palavra-passe"
    },
    "main-password-header": {
      "name": "Cabeçalho de palavra-passe",
      "settings": {
        "logo_header": {
          "content": "Logótipo"
        },
        "logo_help": {
          "content": "Edite o seu logótipo nas definições de tema."
        }
      }
    },
    "main-product": {
      "name": "Informações do produto",
      "blocks": {
        "text": {
          "settings": {
            "text": {
              "label": "Texto"
            },
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              },
              "options__3": {
                "label": "Maiúsculas"
              }
            }
          },
          "name": "Texto"
        },
        "variant_picker": {
          "settings": {
            "picker_type": {
              "label": "Tipo",
              "options__1": {
                "label": "Pendente"
              },
              "options__2": {
                "label": "Comprimidos"
              }
            }
          },
          "name": "Seletor de variante"
        },
        "buy_buttons": {
          "settings": {
            "show_dynamic_checkout": {
              "label": "Mostrar botões dinâmicos de finalização da compra",
              "info": "Utilizando os métodos de pagamento disponíveis na sua loja, os clientes poderão ver a sua opção preferida, como o PayPal ou Apple Pay. [Saiba mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          },
          "name": "Botão de compra"
        },
        "share": {
          "settings": {
            "featured_image_info": {
              "content": "Se incluir uma ligação nas publicações das redes sociais, a imagem em destaque da página será demonstrada como a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "É incluído um título de loja e descrição com a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Texto"
            }
          },
          "name": "Partilhar"
        },
        "collapsible_tab": {
          "settings": {
            "heading": {
              "info": "Inclua um título que explique o conteúdo.",
              "label": "Título"
            },
            "content": {
              "label": "Conteúdo da linha"
            },
            "page": {
              "label": "Conteúdo da linha da página"
            },
            "icon": {
              "label": "Ícone",
              "options__1": {
                "label": "Nenhum"
              },
              "options__2": {
                "label": "Maçã"
              },
              "options__3": {
                "label": "Banana"
              },
              "options__4": {
                "label": "Garrafa"
              },
              "options__5": {
                "label": "Caixa"
              },
              "options__6": {
                "label": "Cenoura"
              },
              "options__7": {
                "label": "Bolha de conversa"
              },
              "options__8": {
                "label": "Marca de verificação"
              },
              "options__9": {
                "label": "Prancheta"
              },
              "options__10": {
                "label": "Produtos lácteos"
              },
              "options__11": {
                "label": "Sem produtos lácteos"
              },
              "options__12": {
                "label": "Secador"
              },
              "options__13": {
                "label": "Olho"
              },
              "options__14": {
                "label": "Fogo"
              },
              "options__15": {
                "label": "Sem glúten"
              },
              "options__16": {
                "label": "Coração"
              },
              "options__17": {
                "label": "Ferro"
              },
              "options__18": {
                "label": "Folha"
              },
              "options__19": {
                "label": "Couro"
              },
              "options__20": {
                "label": "Relâmpago"
              },
              "options__21": {
                "label": "Batom"
              },
              "options__22": {
                "label": "Cadeado"
              },
              "options__23": {
                "label": "Marcador de mapa"
              },
              "options__24": {
                "label": "Sem frutos de casca rija"
              },
              "options__25": {
                "label": "Calças"
              },
              "options__26": {
                "label": "Marca de pata"
              },
              "options__27": {
                "label": "Pimenta"
              },
              "options__28": {
                "label": "Perfume"
              },
              "options__29": {
                "label": "Avião"
              },
              "options__30": {
                "label": "Planta"
              },
              "options__31": {
                "label": "Etiqueta de preço"
              },
              "options__32": {
                "label": "Ponto de interrogação"
              },
              "options__33": {
                "label": "Reciclar"
              },
              "options__34": {
                "label": "Devolução"
              },
              "options__35": {
                "label": "Régua"
              },
              "options__36": {
                "label": "Prato"
              },
              "options__37": {
                "label": "Camisa"
              },
              "options__38": {
                "label": "Sapato"
              },
              "options__39": {
                "label": "Silhueta"
              },
              "options__40": {
                "label": "Floco de neve"
              },
              "options__41": {
                "label": "Estrela"
              },
              "options__42": {
                "label": "Cronómetro"
              },
              "options__43": {
                "label": "Camião"
              },
              "options__44": {
                "label": "Lavar"
              }
            }
          },
          "name": "Linha recolhível"
        },
        "popup": {
          "settings": {
            "link_label": {
              "label": "Etiqueta de ligação"
            },
            "page": {
              "label": "Página"
            }
          },
          "name": "Pop-up"
        },
        "title": {
          "name": "Título"
        },
        "price": {
          "name": "Preço"
        },
        "quantity_selector": {
          "name": "Seletor de quantidade"
        },
        "pickup_availability": {
          "name": "Disponibilidade de recolha"
        },
        "description": {
          "name": "Descrição"
        },
        "custom_liquid": {
          "name": "Liquid personalizado",
          "settings": {
            "custom_liquid": {
              "label": "Liquid personalizado",
              "info": "Adicione fragmentos de aplicação ou outro código Liquid para criar personalizações avançadas."
            }
          }
        },
        "rating": {
          "name": "Classificação do produto",
          "settings": {
            "paragraph": {
              "content": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Produtos complementares",
          "settings": {
            "paragraph": {
              "content": "Para selecionar produtos complementares, adicione a aplicação Search & Discovery. [Saber mais](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Título"
            },
            "make_collapsible_row": {
              "label": "Mostrar como linha recolhível"
            },
            "icon": {
              "info": "Visível quando é mostrada a linha recolhível."
            },
            "product_list_limit": {
              "label": "Máximo de produtos a apresentar"
            },
            "products_per_page": {
              "label": "Número de produtos por página"
            },
            "pagination_style": {
              "label": "Estilo de paginação",
              "options": {
                "option_1": "Pontos",
                "option_2": "Contador",
                "option_3": "Números"
              }
            },
            "product_card": {
              "heading": "Cartão de produtos"
            },
            "image_ratio": {
              "label": "Proporção de imagem",
              "options": {
                "option_1": "Retrato",
                "option_2": "Quadrado"
              }
            },
            "enable_quick_add": {
              "label": "Ativar botão para adicionar rapidamente"
            }
          }
        },
        "icon_with_text": {
          "name": "Ícone com texto",
          "settings": {
            "layout": {
              "label": "Esquema",
              "options__1": {
                "label": "Horizontal"
              },
              "options__2": {
                "label": "Vertical"
              }
            },
            "content": {
              "label": "Conteúdo",
              "info": "Escolha um ícone ou adicione uma imagem para cada coluna ou linha."
            },
            "heading": {
              "info": "Deixe a etiqueta do título em branco para ocultar a coluna do ícone."
            },
            "icon_1": {
              "label": "Primeiro ícone"
            },
            "image_1": {
              "label": "Primeira imagem"
            },
            "heading_1": {
              "label": "Primeiro título"
            },
            "icon_2": {
              "label": "Segundo ícone"
            },
            "image_2": {
              "label": "Segunda imagem"
            },
            "heading_2": {
              "label": "Segundo título"
            },
            "icon_3": {
              "label": "Terceiro ícone"
            },
            "image_3": {
              "label": "Terceira imagem"
            },
            "heading_3": {
              "label": "Terceiro título"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              },
              "options__3": {
                "label": "Maiúsculas"
              }
            }
          }
        },
        "inventory": {
          "name": "Estado do inventário",
          "settings": {
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              },
              "options__3": {
                "label": "Maiúsculas"
              }
            },
            "inventory_threshold": {
              "label": "Limite do inventário baixo",
              "info": "Escolha 0 para mostrar sempre em stock, se disponível."
            },
            "show_inventory_quantity": {
              "label": "Mostrar contagem de inventário"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Conteúdo multimédia",
          "info": "Saiba mais sobre [tipos de média.](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Ativar ciclo de vídeo"
        },
        "enable_sticky_info": {
          "label": "Ativar conteúdo fixo no desktop"
        },
        "hide_variants": {
          "label": "Ocultar outro conteúdo multimédia das variantes após selecionar uma variante"
        },
        "gallery_layout": {
          "label": "Esquema de desktop",
          "options__1": {
            "label": "Empilhado"
          },
          "options__2": {
            "label": "Duas colunas"
          },
          "options__3": {
            "label": "Miniaturas"
          },
          "options__4": {
            "label": "Carrossel de miniaturas"
          }
        },
        "media_size": {
          "label": "Largura do conteúdo multimédia para computador",
          "options__1": {
            "label": "Pequeno"
          },
          "options__2": {
            "label": "Médio"
          },
          "options__3": {
            "label": "Grande"
          },
          "info": "Os conteúdos multimédia são automaticamente otimizados para dispositivos móveis."
        },
        "mobile_thumbnails": {
          "label": "Esquema móvel",
          "options__1": {
            "label": "Duas colunas"
          },
          "options__2": {
            "label": "Mostrar miniaturas"
          },
          "options__3": {
            "label": "Ocultar miniaturas"
          }
        },
        "media_position": {
          "label": "Posição do conteúdo multimédia no ambiente de trabalho",
          "info": "A posição é automaticamente otimizada para dispositivos móveis.",
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Direita"
          }
        },
        "image_zoom": {
          "label": "Zoom da imagem",
          "info": "Clique e passe o cursor para abrir a janela modal (lightbox) no telemóvel.",
          "options__1": {
            "label": "Abrir janela modal (lightbox)"
          },
          "options__2": {
            "label": "Clicar e passar o cursor"
          },
          "options__3": {
            "label": "Sem zoom"
          }
        },
        "constrain_to_viewport": {
          "label": "Ajustar conteúdo multimédia ao ecrã"
        },
        "media_fit": {
          "label": "Ajuste do conteúdo multimédia",
          "options__1": {
            "label": "Original"
          },
          "options__2": {
            "label": "Preencher"
          }
        }
      }
    },
    "main-search": {
      "name": "Resultados da pesquisa",
      "settings": {
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Retrato"
          },
          "options__3": {
            "label": "Quadrado"
          }
        },
        "show_secondary_image": {
          "label": "Mostrar a segunda imagem ao passar o rato"
        },
        "show_vendor": {
          "label": "Mostrar fornecedor"
        },
        "header__1": {
          "content": "Cartão de produtos"
        },
        "header__2": {
          "content": "Cartão de blogue",
          "info": "Os estilos de cartões de blogue são também aplicáveis aos cartões de páginas nos resultados de pesquisa. Para alterar os estilos de cartões, atualize as suas definições do tema."
        },
        "article_show_date": {
          "label": "Mostrar data"
        },
        "article_show_author": {
          "label": "Mostrar autor"
        },
        "show_rating": {
          "label": "Mostrar classificação do produto",
          "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"
        },
        "columns_desktop": {
          "label": "Número de colunas no ambiente de trabalho"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        },
        "columns_mobile": {
          "label": "Número de colunas em dispositivo móvel",
          "options__1": {
            "label": "1 coluna"
          },
          "options__2": {
            "label": "2 colunas"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Várias colunas",
      "settings": {
        "title": {
          "label": "Título"
        },
        "image_width": {
          "label": "Largura da imagem",
          "options__1": {
            "label": "Um terço da largura da coluna"
          },
          "options__2": {
            "label": "Metade da largura da coluna"
          },
          "options__3": {
            "label": "Largura total da coluna"
          }
        },
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Retrato"
          },
          "options__3": {
            "label": "Quadrado"
          },
          "options__4": {
            "label": "Círculo"
          }
        },
        "column_alignment": {
          "label": "Alinhamento de colunas",
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Centro"
          }
        },
        "background_style": {
          "label": "Fundo secundário",
          "options__1": {
            "label": "Nenhum"
          },
          "options__2": {
            "label": "Mostrar como fundo da coluna"
          }
        },
        "button_label": {
          "label": "Etiqueta do botão"
        },
        "button_link": {
          "label": "Ligação do botão"
        },
        "swipe_on_mobile": {
          "label": "Ativar leitura magnética no dispositivo móvel"
        },
        "columns_desktop": {
          "label": "Número de colunas no ambiente de trabalho"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        },
        "columns_mobile": {
          "label": "Número de colunas em dispositivo móvel",
          "options__1": {
            "label": "1 coluna"
          },
          "options__2": {
            "label": "2 colunas"
          }
        }
      },
      "blocks": {
        "column": {
          "settings": {
            "image": {
              "label": "Imagem"
            },
            "title": {
              "label": "Título"
            },
            "text": {
              "label": "Descrição"
            },
            "link_label": {
              "label": "Etiqueta de ligação"
            },
            "link": {
              "label": "Ligação"
            }
          },
          "name": "Coluna"
        }
      },
      "presets": {
        "name": "Várias colunas"
      }
    },
    "newsletter": {
      "name": "Registo de e-mail",
      "settings": {
        "full_width": {
          "label": "Tornar a secção em largura total"
        },
        "paragraph": {
          "content": "Cada subscrição de e-mail cria uma conta de cliente. [Saber mais](https://help.shopify.com/manual/customers)"
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Título"
            }
          },
          "name": "Cabeçalho"
        },
        "paragraph": {
          "settings": {
            "paragraph": {
              "label": "Descrição"
            }
          },
          "name": "Subtítulo"
        },
        "email_form": {
          "name": "Formulário de e-mail"
        }
      },
      "presets": {
        "name": "Registo de e-mail"
      }
    },
    "page": {
      "name": "Página",
      "settings": {
        "page": {
          "label": "Página"
        }
      },
      "presets": {
        "name": "Página"
      }
    },
    "rich-text": {
      "name": "Texto formatado",
      "settings": {
        "full_width": {
          "label": "Tornar a secção em largura total"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Posição do conteúdo para computador",
          "info": "A posição é automaticamente otimizada para dispositivos móveis."
        },
        "content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento de conteúdo"
        }
      },
      "blocks": {
        "heading": {
          "settings": {
            "heading": {
              "label": "Título"
            }
          },
          "name": "Cabeçalho"
        },
        "text": {
          "settings": {
            "text": {
              "label": "Descrição"
            }
          },
          "name": "Texto"
        },
        "buttons": {
          "settings": {
            "button_label_1": {
              "label": "Primeira etiqueta do botão",
              "info": "Deixe a etiqueta em branco para ocultar o botão."
            },
            "button_link_1": {
              "label": "Primeira ligação do botão"
            },
            "button_style_secondary_1": {
              "label": "Utilizar estilo de botão de contorno"
            },
            "button_label_2": {
              "label": "Segunda etiqueta do botão",
              "info": "Deixe a etiqueta em branco para ocultar o botão."
            },
            "button_link_2": {
              "label": "Segunda ligação do botão"
            },
            "button_style_secondary_2": {
              "label": "Utilizar estilo de botão de contorno"
            }
          },
          "name": "Botões"
        },
        "caption": {
          "name": "Legenda",
          "settings": {
            "text": {
              "label": "Texto"
            },
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Legenda"
              },
              "options__2": {
                "label": "Maiúsculas"
              }
            },
            "caption_size": {
              "label": "Tamanho do texto",
              "options__1": {
                "label": "Pequeno"
              },
              "options__2": {
                "label": "Médio"
              },
              "options__3": {
                "label": "Grande"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Texto formatado"
      }
    },
    "apps": {
      "name": "Aplicações",
      "settings": {
        "include_margins": {
          "label": "Faça as margens de secção as mesmas que o tema"
        }
      },
      "presets": {
        "name": "Aplicações"
      }
    },
    "video": {
      "name": "Vídeo",
      "settings": {
        "heading": {
          "label": "Cabeçalho"
        },
        "cover_image": {
          "label": "Imagem de capa"
        },
        "video_url": {
          "label": "URL",
          "placeholder": "Usar um URL de YouTube ou Vimeo",
          "info": "O vídeo é reproduzido na página."
        },
        "description": {
          "label": "Texto alternativo do vídeo",
          "info": "Descreve o vídeo para que seja acessível a clientes que usam leitores de ecrã. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"
        },
        "image_padding": {
          "label": "Adicionar preenchimento de imagem",
          "info": "Selecione um preenchimento de imagem se não quer que a sua imagem de capa seja cortada."
        },
        "full_width": {
          "label": "Tornar a secção em largura total"
        }
      },
      "presets": {
        "name": "Vídeo"
      }
    },
    "featured-product": {
      "name": "Produto em destaque",
      "blocks": {
        "text": {
          "name": "Texto",
          "settings": {
            "text": {
              "label": "Texto"
            },
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              },
              "options__3": {
                "label": "Maiúsculas"
              }
            }
          }
        },
        "title": {
          "name": "Título"
        },
        "price": {
          "name": "Preço"
        },
        "quantity_selector": {
          "name": "Seletor de quantidade"
        },
        "variant_picker": {
          "name": "Seletor de variante",
          "settings": {
            "picker_type": {
              "label": "Tipo",
              "options__1": {
                "label": "Pendente"
              },
              "options__2": {
                "label": "Forma de comprimidos"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Botão de compra",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Mostrar botões dinâmicos de finalização da compra",
              "info": "Utilizando os métodos de pagamento disponíveis na sua loja, os clientes poderão ver a sua opção preferida, como o PayPal ou Apple Pay. [Saber mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Descrição"
        },
        "share": {
          "name": "Partilhar",
          "settings": {
            "featured_image_info": {
              "content": "Se incluir uma ligação nas publicações das redes sociais, a imagem em destaque da página será demonstrada como a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "É incluído um título de loja e descrição com a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Texto"
            }
          }
        },
        "custom_liquid": {
          "name": "Liquid personalizado",
          "settings": {
            "custom_liquid": {
              "label": "Liquid personalizado"
            }
          }
        },
        "rating": {
          "name": "Classificação do produto",
          "settings": {
            "paragraph": {
              "content": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Estilo de texto",
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              },
              "options__3": {
                "label": "Maiúsculas"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Produto"
        },
        "secondary_background": {
          "label": "Mostrar fundo secundário"
        },
        "header": {
          "content": "Conteúdo multimédia",
          "info": "Saiba mais sobre [tipos de conteúdo multimédia](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Ativar ciclo de vídeo"
        },
        "hide_variants": {
          "label": "Ocultar conteúdo multimédia das variantes não selecionadas no desktop"
        },
        "media_position": {
          "label": "Posição do conteúdo multimédia no ambiente de trabalho",
          "info": "A posição é automaticamente otimizada para dispositivos móveis.",
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Direita"
          }
        }
      },
      "presets": {
        "name": "Produto em destaque"
      }
    },
    "email-signup-banner": {
      "name": "Faixa de registo de e-mail",
      "settings": {
        "paragraph": {
          "content": "Cada subscrição de e-mail cria uma conta de cliente. [Saber mais](https://help.shopify.com/manual/customers)"
        },
        "image": {
          "label": "Imagem de fundo"
        },
        "show_background_image": {
          "label": "Mostrar imagem de fundo"
        },
        "show_text_box": {
          "label": "Mostrar recetores no desktop"
        },
        "image_overlay_opacity": {
          "label": "Opacidade da sobreposição da imagem"
        },
        "color_scheme": {
          "info": "Visível quando o recetor é exibido."
        },
        "show_text_below": {
          "label": "Mostrar conteúdo por baixo da imagem em dispositivos móveis",
          "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [Saber mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "image_height": {
          "label": "Altura da faixa",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Pequeno"
          },
          "options__3": {
            "label": "Médio"
          },
          "options__4": {
            "label": "Grande"
          },
          "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [Saber mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Canto superior esquerdo"
          },
          "options__2": {
            "label": "Superior centro"
          },
          "options__3": {
            "label": "Canto superior direito"
          },
          "options__4": {
            "label": "Intermédio à esquerda"
          },
          "options__5": {
            "label": "Intermédio ao centro"
          },
          "options__6": {
            "label": "Intermédio à direita"
          },
          "options__7": {
            "label": "Canto inferior esquerdo"
          },
          "options__8": {
            "label": "Inferior centro"
          },
          "options__9": {
            "label": "Canto inferior direito"
          },
          "label": "Posição do conteúdo em computadores"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em desktop"
        },
        "header": {
          "content": "Esquema para dispositivo móvel"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em dispositivos móveis"
        }
      },
      "blocks": {
        "heading": {
          "name": "Cabeçalho",
          "settings": {
            "heading": {
              "label": "Cabeçalho"
            }
          }
        },
        "paragraph": {
          "name": "Parágrafo",
          "settings": {
            "paragraph": {
              "label": "Descrição"
            },
            "text_style": {
              "options__1": {
                "label": "Corpo"
              },
              "options__2": {
                "label": "Legenda"
              },
              "label": "Estilo de texto"
            }
          }
        },
        "email_form": {
          "name": "Formulário de e-mail"
        }
      },
      "presets": {
        "name": "Faixa de registo de e-mail"
      }
    },
    "slideshow": {
      "name": "Apresentação de diapositivos",
      "settings": {
        "layout": {
          "label": "Esquema",
          "options__1": {
            "label": "Largura total"
          },
          "options__2": {
            "label": "Grelha"
          }
        },
        "slide_height": {
          "label": "Altura do diapositivo",
          "options__1": {
            "label": "Adaptar à primeira imagem"
          },
          "options__2": {
            "label": "Pequeno"
          },
          "options__3": {
            "label": "Médio"
          },
          "options__4": {
            "label": "Grande"
          }
        },
        "slider_visual": {
          "label": "Estilo de paginação",
          "options__1": {
            "label": "Contador"
          },
          "options__2": {
            "label": "Pontos"
          },
          "options__3": {
            "label": "Números"
          }
        },
        "auto_rotate": {
          "label": "Rotação automática de diapositivos"
        },
        "change_slides_speed": {
          "label": "Mudar diapositivos a cada"
        },
        "mobile": {
          "content": "Esquema móvel"
        },
        "show_text_below": {
          "label": "Mostrar conteúdo abaixo das imagens em dispositivos móveis"
        },
        "accessibility": {
          "content": "Acessibilidade",
          "label": "Descrição da apresentação de diapositivos",
          "info": "Descreve a apresentação de diapositivos para que seja acessível a clientes que usam leitores de ecrã."
        }
      },
      "blocks": {
        "slide": {
          "name": "Diapositivo",
          "settings": {
            "image": {
              "label": "Imagem"
            },
            "heading": {
              "label": "Cabeçalho"
            },
            "subheading": {
              "label": "Subtítulo"
            },
            "button_label": {
              "label": "Etiqueta do botão",
              "info": "Deixe a etiqueta em branco para ocultar o botão."
            },
            "link": {
              "label": "Ligação do botão"
            },
            "secondary_style": {
              "label": "Utilizar estilo de botão de contorno"
            },
            "box_align": {
              "label": "Posição do conteúdo em desktop",
              "options__1": {
                "label": "Canto superior esquerdo"
              },
              "options__2": {
                "label": "Superior centro"
              },
              "options__3": {
                "label": "Canto superior direito"
              },
              "options__4": {
                "label": "Intermédio à esquerda"
              },
              "options__5": {
                "label": "Intermédio ao centro"
              },
              "options__6": {
                "label": "Intermédio à direita"
              },
              "options__7": {
                "label": "Canto inferior esquerdo"
              },
              "options__8": {
                "label": "Inferior centro"
              },
              "options__9": {
                "label": "Canto inferior direito"
              },
              "info": "Posição é automaticamente otimizada para dispositivos móveis."
            },
            "show_text_box": {
              "label": "Mostrar recetores no desktop"
            },
            "text_alignment": {
              "label": "Alinhamento do conteúdo em desktop",
              "option_1": {
                "label": "Esquerda"
              },
              "option_2": {
                "label": "Ao centro"
              },
              "option_3": {
                "label": "Direita"
              }
            },
            "image_overlay_opacity": {
              "label": "Opacidade da sobreposição da imagem"
            },
            "color_scheme": {
              "info": "Visível quando o recetor é exibido."
            },
            "text_alignment_mobile": {
              "label": "Alinhamento do conteúdo em dispositivos móveis",
              "options__1": {
                "label": "Esquerda"
              },
              "options__2": {
                "label": "Ao centro"
              },
              "options__3": {
                "label": "Direita"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Apresentação de diapositivos"
      }
    },
    "collapsible_content": {
      "name": "Conteúdo recolhível",
      "settings": {
        "caption": {
          "label": "Legenda"
        },
        "heading": {
          "label": "Cabeçalho"
        },
        "heading_alignment": {
          "label": "Alinhamento do título",
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Ao centro"
          },
          "options__3": {
            "label": "Direita"
          }
        },
        "layout": {
          "label": "Esquema",
          "options__1": {
            "label": "Nenhum contentor"
          },
          "options__2": {
            "label": "Contentor de linha"
          },
          "options__3": {
            "label": "Contentor de secção"
          }
        },
        "container_color_scheme": {
          "label": "Esquema de cores do contentor",
          "info": "Visível quando o esquema está definido para contentor de linha ou de secção."
        },
        "open_first_collapsible_row": {
          "label": "Abrir primeira linha recolhível"
        },
        "header": {
          "content": "Esquema de imagem"
        },
        "image": {
          "label": "Imagem"
        },
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Pequeno"
          },
          "options__3": {
            "label": "Grande"
          }
        },
        "desktop_layout": {
          "label": "Esquema de desktop",
          "options__1": {
            "label": "Primeira imagem"
          },
          "options__2": {
            "label": "Segunda imagem"
          },
          "info": "A imagem sempre aparece sempre primeiro nos dispositivos móveis."
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Linha recolhível",
          "settings": {
            "heading": {
              "info": "Inclua um título que explique o conteúdo.",
              "label": "Cabeçalho"
            },
            "row_content": {
              "label": "Conteúdo da linha"
            },
            "page": {
              "label": "Conteúdo da linha da página"
            },
            "icon": {
              "label": "Ícone",
              "options__1": {
                "label": "Nenhum"
              },
              "options__2": {
                "label": "Maçã"
              },
              "options__3": {
                "label": "Banana"
              },
              "options__4": {
                "label": "Garrafa"
              },
              "options__5": {
                "label": "Caixa"
              },
              "options__6": {
                "label": "Cenoura"
              },
              "options__7": {
                "label": "Bolha de conversa"
              },
              "options__8": {
                "label": "Marca de verificação"
              },
              "options__9": {
                "label": "Prancheta"
              },
              "options__10": {
                "label": "Produtos lácteos"
              },
              "options__11": {
                "label": "Sem produtos lácteos"
              },
              "options__12": {
                "label": "Secador"
              },
              "options__13": {
                "label": "Olho"
              },
              "options__14": {
                "label": "Fogo"
              },
              "options__15": {
                "label": "Sem glúten"
              },
              "options__16": {
                "label": "Coração"
              },
              "options__17": {
                "label": "Ferro"
              },
              "options__18": {
                "label": "Folha"
              },
              "options__19": {
                "label": "Couro"
              },
              "options__20": {
                "label": "Relâmpago"
              },
              "options__21": {
                "label": "Batom"
              },
              "options__22": {
                "label": "Cadeado"
              },
              "options__23": {
                "label": "Marcador de mapa"
              },
              "options__24": {
                "label": "Sem frutos de casca rija"
              },
              "options__25": {
                "label": "Calças"
              },
              "options__26": {
                "label": "Marca de pata"
              },
              "options__27": {
                "label": "Pimenta"
              },
              "options__28": {
                "label": "Perfume"
              },
              "options__29": {
                "label": "Avião"
              },
              "options__30": {
                "label": "Planta"
              },
              "options__31": {
                "label": "Etiqueta de preço"
              },
              "options__32": {
                "label": "Ponto de interrogação"
              },
              "options__33": {
                "label": "Reciclar"
              },
              "options__34": {
                "label": "Devolução"
              },
              "options__35": {
                "label": "Régua"
              },
              "options__36": {
                "label": "Prato"
              },
              "options__37": {
                "label": "Camisa"
              },
              "options__38": {
                "label": "Sapato"
              },
              "options__39": {
                "label": "Silhueta"
              },
              "options__40": {
                "label": "Floco de neve"
              },
              "options__41": {
                "label": "Estrela"
              },
              "options__42": {
                "label": "Cronómetro"
              },
              "options__43": {
                "label": "Camião"
              },
              "options__44": {
                "label": "Lavar"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Conteúdo recolhível"
      }
    },
    "main-account": {
      "name": "Conta"
    },
    "main-activate-account": {
      "name": "Ativação de conta"
    },
    "main-addresses": {
      "name": "Endereços"
    },
    "main-login": {
      "name": "Iniciar sessão"
    },
    "main-order": {
      "name": "Encomenda"
    },
    "main-register": {
      "name": "Registo"
    },
    "main-reset-password": {
      "name": "Redefinição de palavra-passe"
    },
    "related-products": {
      "name": "Produtos relacionados",
      "settings": {
        "heading": {
          "label": "Título"
        },
        "products_to_show": {
          "label": "Máximo de produtos a apresentar"
        },
        "columns_desktop": {
          "label": "Número de colunas no computador"
        },
        "paragraph__1": {
          "content": "As recomendações dinâmicas utilizam informações de encomenda e de produto para mudar e melhorar ao longo do tempo. [Saber mais](https://help.shopify.com/themes/development/recommended-products)"
        },
        "header__2": {
          "content": "Cartão de produtos"
        },
        "image_ratio": {
          "label": "Proporção de imagem",
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Retrato"
          },
          "options__3": {
            "label": "Quadrado"
          }
        },
        "show_secondary_image": {
          "label": "Mostrar a segunda imagem ao passar o rato"
        },
        "show_vendor": {
          "label": "Mostrar fornecedor"
        },
        "show_rating": {
          "label": "Mostrar classificação do produto",
          "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [Saber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        },
        "columns_mobile": {
          "label": "Número de colunas em dispositivo móvel",
          "options__1": {
            "label": "1 coluna"
          },
          "options__2": {
            "label": "2 colunas"
          }
        }
      }
    },
    "multirow": {
      "name": "Várias linhas",
      "settings": {
        "image": {
          "label": "Imagem"
        },
        "image_height": {
          "options__1": {
            "label": "Adaptar à imagem"
          },
          "options__2": {
            "label": "Pequeno"
          },
          "options__3": {
            "label": "Médio"
          },
          "options__4": {
            "label": "Grande"
          },
          "label": "Altura da imagem"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Pequeno"
          },
          "options__2": {
            "label": "Médio"
          },
          "options__3": {
            "label": "Grande"
          },
          "label": "Largura da imagem em computador",
          "info": "A imagem é otimizada automaticamente em dispositivos móveis."
        },
        "heading_size": {
          "options__1": {
            "label": "Pequeno"
          },
          "options__2": {
            "label": "Médio"
          },
          "options__3": {
            "label": "Grande"
          },
          "label": "Tamanho do título"
        },
        "text_style": {
          "options__1": {
            "label": "Corpo"
          },
          "options__2": {
            "label": "Legenda"
          },
          "label": "Estilo de texto"
        },
        "button_style": {
          "options__1": {
            "label": "Botão sólido"
          },
          "options__2": {
            "label": "Botão de contorno"
          },
          "label": "Estilo do botão"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em computador"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Em cima"
          },
          "options__2": {
            "label": "Ao meio"
          },
          "options__3": {
            "label": "Em baixo"
          },
          "label": "Posição do conteúdo para computador",
          "info": "A posição é automaticamente otimizada para dispositivos móveis."
        },
        "image_layout": {
          "options__1": {
            "label": "Alternar da esquerda"
          },
          "options__2": {
            "label": "Alternar da direita"
          },
          "options__3": {
            "label": "Alinhado à esquerda"
          },
          "options__4": {
            "label": "Alinhado à direita"
          },
          "label": "Posicionamento da imagem em computador",
          "info": "O posicionamento é otimizado automaticamente para dispositivos móveis."
        },
        "container_color_scheme": {
          "label": "Esquema de cores do contentor"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Esquerda"
          },
          "options__2": {
            "label": "Centro"
          },
          "options__3": {
            "label": "Direita"
          },
          "label": "Alinhamento do conteúdo em dispositivos móveis"
        },
        "header_mobile": {
          "content": "Esquema para dispositivo móvel"
        }
      },
      "blocks": {
        "row": {
          "name": "Linha",
          "settings": {
            "image": {
              "label": "Imagem"
            },
            "caption": {
              "label": "Legenda"
            },
            "heading": {
              "label": "Título"
            },
            "text": {
              "label": "Texto"
            },
            "button_label": {
              "label": "Etiqueta do botão"
            },
            "button_link": {
              "label": "Ligação do botão"
            }
          }
        }
      },
      "presets": {
        "name": "Várias linhas"
      }
    }
  }
}
