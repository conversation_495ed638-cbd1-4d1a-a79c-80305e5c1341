{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  
  .horizontal-ticker-{{ section.id }} .horizontal-ticker__container {
    animation: mobileHorTicker{{ section.id }} {{ 150 | divided_by: section.settings.speed }}s linear infinite {{ section.settings.direction }} forwards;
    column-gap: {{ section.settings.mobile_spacing }}px;
  }

  {% if section.settings.stop_on_hover %}
    .horizontal-ticker-{{ section.id }}:hover .horizontal-ticker__container {
      animation-play-state: paused;
    }
  {% endif %}
    
  .horizontal-ticker-{{ section.id }} .review-item {
    width: {{ section.settings.mobile_reviews_width }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.desktop_padding_top }}px;
      padding-bottom: {{ section.settings.desktop_padding_bottom }}px;
    }

    .horizontal-ticker-{{ section.id }} .horizontal-ticker__container {
      column-gap: {{ section.settings.desktop_spacing }}px;
      animation-name: desktopHorTicker{{ section.id }};
    }
    
    .horizontal-ticker-{{ section.id }} .review-item {
      width: {{ section.settings.desktop_reviews_width }}px;
    }
  }

  .horizontal-ticker-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  
  @keyframes mobileHorTicker{{ section.id }} {
    to {
      transform: translateX(calc(-50% - {{ section.settings.mobile_spacing | divided_by: 2.0}}px));
    }
  }
  @keyframes desktopHorTicker{{ section.id }} {
    to {
      transform: translateX(calc(-50% - {{ section.settings.desktop_spacing | divided_by: 2.0}}px));
    }
  }
{%- endstyle -%}

{% liquid
  assign displayed = true
  if section.settings.hidden_products != blank
    assign hidden_handles = section.settings.hidden_products | split: ','
    for hidden_handle in hidden_handles
      assign handle_strip = hidden_handle | strip
      if handle_strip == product.handle
        assign displayed = false
        break
      endif
    endfor
  endif
  if section.settings.enable_specific_display and section.settings.displayed_products != blank
    assign displayed_handles = section.settings.displayed_products | split: ','
    assign displayed = false
    for displayed_handle in displayed_handles
      assign handle_strip = displayed_handle | strip
      if handle_strip == product.handle
        assign displayed = true
        break
      endif
    endfor
  endif
%}

{% if displayed %}
  <div class="horizontal-ticker horizontal-ticker-{{ section.id }} color-{{ section.settings.color_scheme }} gradient section-{{ section.id }}-padding {{ section.settings.visibility }}" style='--mobile-text-size: {{ section.settings.mobile_text_size | divided_by: 10.0 }}rem;--desktop-text-size: {{ section.settings.desktop_text_size | divided_by: 10.0 }}rem;--mobile-image-height: {{ section.settings.mobile_image_height | divided_by: 10.0 }}rem;--desktop-image-height: {{ section.settings.desktop_image_height | divided_by: 10.0 }}rem;'>
    <div class="horizontal-ticker__container">
      {% assign unique_blocks_total = section.blocks | size %}
      {% assign target_multiply = 20 | divided_by: unique_blocks_total | floor %}
      {% assign target_total = unique_blocks_total | times: target_multiply | times: 2 %}
      {% assign block_counter = 0 %}
  
      {%- for block in section.blocks -%}
        {%- assign block_counter = block_counter | plus: 1 -%}
        {% case block.type %}
          {% when 'text' %}
            <p class="custom-font-size horizontal-ticker__item{% if section.settings.italic_text %} horizontal-ticker__item--italic{% endif %}{% if section.settings.uppercase_text %} horizontal-ticker__item--uppercase{% endif %}{% if section.settings.bold_text %} horizontal-ticker__item--bold{% endif %}">
              {{ block.settings.title }}
            </p>
          {% when 'image' %}
            <div
              class='horizontal-ticker__image media media--transparent media--fit-contain custom-image-height custom-image-width'
              style="--mobile-width: {{ section.settings.mobile_image_height | times: block.settings.image.aspect_ratio | divided_by: 10.0 }}rem;--desktop-width: {{ section.settings.desktop_image_height | times: block.settings.image.aspect_ratio | divided_by: 10.0 }}rem;"
            >
              <img
                src="{{ block.settings.image | image_url }}"
                {% if block.settings.image.alt != blank %}
                  alt="{{ block.settings.image.alt | escape }}"
                {% else %}
                  role="presentation"
                {% endif %}
                height="auto"
                width="auto"
                loading="lazy"
              >
            </div>
          {% when 'reviews' %}
            {% liquid
              assign force_padding = false
              if section.settings.color_scheme != block.settings.color_scheme
                assign force_padding = true
              endif
            %}
            {%- render 'reviews', block: block, force_padding: force_padding -%}
        {% endcase %}
      {%- endfor -%}
  
      {%- if block_counter < target_total -%}
        {% for i in (1..15) %}
          {% for block in section.blocks %}
            {% case block.type %}
              {% when 'text' %}
                <p class="custom-font-size horizontal-ticker__item{% if section.settings.italic_text %} horizontal-ticker__item--italic{% endif %}{% if section.settings.uppercase_text %} horizontal-ticker__item--uppercase{% endif %}{% if section.settings.bold_text %} horizontal-ticker__item--bold{% endif %}">
                  {{ block.settings.title }}
                </p>
              {% when 'image' %}
                <div
                  class='horizontal-ticker__image media media--transparent media--fit-contain custom-image-height custom-image-width'
                  style="--mobile-width: {{ section.settings.mobile_image_height | times: block.settings.image.aspect_ratio | divided_by: 10.0 }}rem;--desktop-width: {{ section.settings.desktop_image_height | times: block.settings.image.aspect_ratio | divided_by: 10.0 }}rem;"
                >
                  <img
                    src="{{ block.settings.image | image_url }}"
                    {% if block.settings.image.alt != blank %}
                      alt="{{ block.settings.image.alt | escape }}"
                    {% else %}
                      role="presentation"
                    {% endif %}
                    height="auto"
                    width="auto"
                    loading="lazy"
                  >
                </div>
              {% when 'reviews' %}
                {% liquid
                  assign force_padding = false
                  if section.settings.color_scheme != block.settings.color_scheme
                    assign force_padding = true
                  endif
                %}
                {%- render 'reviews', block: block, force_padding: force_padding -%}
            {% endcase %}
            
            {% assign block_counter = block_counter | plus: 1 %}
            {% if block_counter >= target_total %}
              {% break %}
            {% endif %}
          {% endfor %}
          {% if block_counter >= target_total %}
            {% break %}
          {% endif %}
        {% endfor %}
      {%- endif -%}
    </div>
  </div>
{% endif %}

{% schema %}
{
  "name": "Horizontal Ticker",
  "settings": [
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Ticker"
    },
    {
      "type": "range",
      "id": "speed",
      "label": "Ticker speed",
      "min": 1,
      "max": 5,
      "default": 3,
      "step": 0.5
    },
    {
      "type": "select",
      "id": "direction",
      "options": [
        {
          "value": "normal",
          "label": "Right to left"
        },
        {
          "value": "reverse",
          "label": "Left to right"
        }
      ],
      "label": "Animation direction",
      "default": "normal"
    },
    {
      "type": "checkbox",
      "id": "stop_on_hover",
      "label": "Pause animation on hover/touch",
      "default": false
    },
    {
      "type": "number",
      "id": "mobile_spacing",
      "label": "Mobile spacing (px)",
      "default": 60
    },
    {
      "type": "number",
      "id": "desktop_spacing",
      "label": "Desktop spacing (px)",
      "default": 100
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "range",
      "id": "mobile_text_size",
      "min": 8,
      "max": 30,
      "step": 1,
      "unit": "px",
      "default": 16,
      "label": "Mobile text size"
    },
    {
      "type": "range",
      "id": "desktop_text_size",
      "min": 10,
      "max": 40,
      "step": 1,
      "unit": "px",
      "default": 18,
      "label": "Desktop text size"
    },
    {
      "type": "checkbox",
      "id": "italic_text",
      "label": "Italic text",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "uppercase_text",
      "label": "Uppercase text",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "bold_text",
      "label": "Bold text",
      "default": false
    },
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "number",
      "id": "mobile_image_height",
      "default": 26,
      "label": "Mobile image height"
    },
    {
      "type": "number",
      "id": "desktop_image_height",
      "default": 40,
      "label": "Desktop image height"
    },
    {
      "type": "header",
      "content": "Reviews"
    },
    {
      "type": "number",
      "id": "mobile_reviews_width",
      "default": 300,
      "label": "Mobile reviews width"
    },
    {
      "type": "number",
      "id": "desktop_reviews_width",
      "default": 400,
      "label": "Desktop reviews width"
    },
    {
      "type": "header",
      "content": "Visibility"
    },
    {
      "type": "paragraph",
      "content": "Use these options if you want to display different tickers in the Header group across different product pages."
    },
    {
      "type": "text",
      "id": "hidden_products",
      "label": "Product pages to hide the block on",
      "info": "Handles of products to hide the block on, split with a comma."
    },
    {
      "type": "checkbox",
      "id": "enable_specific_display",
      "label": "Display the block on specific product pages only",
      "default": false
    },
    {
      "type": "text",
      "id": "displayed_products",
      "label": "Specific product pages to display the block on",
      "info": "Applied if the checkbox above is checked. Handles of products to only display the block on, split with a comma."
    },
    {
      "type": "header",
      "content": "Mobile padding"
    },
    {
      "type": "range",
      "id": "mobile_padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 12
    },
    {
      "type": "range",
      "id": "mobile_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 12
    },
    {
      "type": "header",
      "content": "Desktop padding"
    },
    {
      "type": "range",
      "id": "desktop_padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 16
    },
    {
      "type": "range",
      "id": "desktop_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 16
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "Shrine Theme",
          "label": "Text"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    },
    {
      "type": "reviews",
      "name": "Review",
      "settings": [
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            { 
              "value": "inverse", 
              "label": "t:sections.all.colors.inverse.label" 
            }
          ],
          "default": "accent-1",
          "label": "Color scheme"
        },
        {
          "type": "checkbox",
          "id": "show_custom_bg",
          "label": "Show custom background",
          "default": false
        },
        {
          "type": "color",
          "id": "custom_bg_color",
          "label": "Custom background color",
          "default": "#F2F2F2"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 12
        },
        {
          "type": "range",
          "id": "border_width",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Border thickness",
          "unit": "px",
          "default": 0
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#B7B7B7"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "select",
          "id": "avatar_alignment",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "middle",
              "label": "Middle"
            }
          ],
          "label": "Avatar alignment",
          "default": "top"
        },
        {
          "type": "range",
          "id": "avatar_corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Avatar corner radius",
          "default": 40
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "range",
          "id": "stars_translate",
          "min": -10,
          "max": 20,
          "step": 1,
          "unit": "%",
          "default": 0,
          "label": "Stars vertical position adjustment",
          "info": "Move the stars up/down to adjust them to different fonts. Negative values move the stars up."
        },
        {
          "type": "color",
          "id": "checkmark_color",
          "default": "#6D388B",
          "label": "Checkmark background color"
        },
        {
          "type": "color",
          "id": "checkmark_icon_color",
          "default": "#FFFFFF",
          "label": "Checkmark icon color"
        },
        {
          "type": "header",
          "content": "Review #1"
        },
        {
          "type": "inline_richtext",
          "id": "author_1",
          "label": "Author name",
          "default": "<em>Author</em> [stars]",
          "info": "Use [stars] to display review stars and [checkmark] to display the verified checkmark icon."
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Author image"
        },
        {
          "type": "richtext",
          "id": "text_1",
          "label": "Text",
          "default": "<p>Share positive thoughts and feedback from your customer.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Horizontal Ticker",
      "blocks": [
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        }
      ]
    }
  ]
}
{% endschema %}
