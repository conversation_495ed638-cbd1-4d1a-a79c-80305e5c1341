{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  media-gallery {
    --mobile-media-border-radius: {{ section.settings.mobile_media_corner_radius }}px;
  }
{%- endstyle -%}

{%- liquid
  assign product = section.settings.product

  if section.settings.media_size == 'large'
    assign media_width = 0.65
  elsif section.settings.media_size == 'medium'
    assign media_width = 0.55
  elsif section.settings.media_size == 'small'
    assign media_width = 0.45
  endif
-%}

{% comment %} TODO: assign `product.selected_or_first_available_variant` to variable and replace usage to reduce verbosity {% endcomment %}

{%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
{%- if first_3d_model -%}
  {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
  <link
    id="ModelViewerStyle"
    rel="stylesheet"
    href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
    media="print"
    onload="this.media='all'"
  >
  <link
    id="ModelViewerOverride"
    rel="stylesheet"
    href="{{ 'component-model-viewer-ui.css' | asset_url }}"
    media="print"
    onload="this.media='all'"
  >
{%- endif -%}

<section class="color-{{ section.settings.color_scheme }} {% if section.settings.secondary_background %}background-secondary{% else %}gradient{% endif %} content-for-grouping {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class="section-id-btn button" data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width section-{{ section.id }}-padding{% if section.settings.secondary_background %} isolate{% endif %}">
    <div class="featured-product product product--{{ section.settings.media_size }} grid grid--1-col gradient color-{{ section.settings.color_scheme }} product--{{ section.settings.media_position }}{% if section.settings.secondary_background == false %} isolate{% endif %} {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %}">
      <div class="grid__item product__media-wrapper{% if section.settings.media_position == 'right' %} medium-hide large-up-hide{% endif %}">
        <media-gallery
          id="MediaGallery-{{ section.id }}"
          data-section="{{ section.id }}"
          role="region"
          aria-label="{{ 'products.product.media.gallery_viewer' | t }}"
          data-desktop-layout="stacked"
        >
          <div
            id="GalleryViewer-{{ section.id }}"
            class="product__media-list{% if section.settings.full_media_width %} product__media-list--full-mobile-width{% endif %}"
          >
            {%- if product.selected_or_first_available_variant.featured_media != null -%}
              {%- assign media = product.selected_or_first_available_variant.featured_media -%}
              <div class="product__media-item" data-media-id="{{ section.id }}-{{ media.id }}">
                {% render 'product-thumbnail',
                  media: media,
                  position: 'featured',
                  loop: section.settings.enable_video_looping,
                  modal_id: section.id,
                  xr_button: false,
                  media_width: media_width,
                  media_fit: section.settings.media_fit,
                  constrain_to_viewport: section.settings.constrain_to_viewport
                %}
              </div>
            {%- endif -%}
            {%- liquid
              assign media_to_render = product.featured_media.id
              for variant in product.variants
                assign media_to_render = media_to_render | append: variant.featured_media.id | append: ' '
              endfor
            -%}
            {%- for media in product.media -%}
              {%- if media_to_render contains media.id
                and media.id != product.selected_or_first_available_variant.featured_media.id
              -%}
                <div class="product__media-item" data-media-id="{{ section.id }}-{{ media.id }}">
                  {% render 'product-thumbnail',
                    media: media,
                    position: forloop.index,
                    loop: section.settings.enable_video_looping,
                    modal_id: section.id,
                    xr_button: false,
                    media_width: media_width,
                    media_fit: section.settings.media_fit,
                    constrain_to_viewport: section.settings.constrain_to_viewport
                  %}
                </div>
              {%- endif -%}
            {%- endfor -%}
          </div>
          {%- if first_3d_model -%}
            <button
              class="button button--full-width product__xr-button"
              type="button"
              aria-label="{{ 'products.product.xr_button_label' | t }}"
              data-shopify-xr
              data-shopify-model3d-id="{{ first_3d_model.id }}"
              data-shopify-title="{{ product.title | escape }}"
              data-shopify-xr-hidden
            >
              {% render 'icon-3d-model' %}
              {{ 'products.product.xr_button' | t }}
            </button>
          {%- endif -%}
        </media-gallery>
      </div>
      <div class="product__info-wrapper product__info-wrapper--top-padding grid__item">
        <product-info
          id="ProductInfo-{{ section.id }}"
          class="product__info-container"
          data-section="{{ section.id }}"
          data-url="{{ product.url }}"
        >
          {%- assign product_form_id = 'product-form-' | append: section.id -%}

          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when '@app' -%}
                {% render block %}
              {%- when 'text' -%}
                {% render 'text-with-icon-block', block: block, margins: true, block_attributes: true %}
              {%- when 'title' -%}
                <div
                  class="product__title"
                  {{ block.shopify_attributes }}
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                >
                  <h1 class="{{ block.settings.text_size }}{% if block.settings.uppercase_title %} product-title--uppercase{% endif %}{% if block.settings.title_alignment == 'center' %} center{% endif %}">
                    {{ product.title }}
                  </h1>
                  <a href="{{ product.url }}" class="product__title">
                    <h2 class="h1">
                      {{ product.title }}
                    </h2>
                  </a>
                </div>
              {% when 'rating_stars' %}
                {% render 'rating-stars-block', block: block, margins: true, block_attributes: true %}
              {% when 'trustpilot_stars' %}
                {% render 'trustpilot-stars-block', block: block, margins: true, block_attributes: true %}
              {%- when 'price' -%}
                <div
                  class="no-js-hidden product-page-price"
                  id="price-{{ section.id }}"
                  role="status"
                  {{ block.shopify_attributes }}
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                >
                  {% liquid
                    assign price_second = false
                    if block.settings.layout == 'price_second'
                      assign price_second = true
                    endif
                  %}
                  {%- render 'price',
                    product: product,
                    block: block,
                    use_variant: true,
                    show_badges: true,
                    price_class: 'price--large',
                    price_second: price_second,
                    main_price: true
                  -%}
                </div>
                <div {{ block.shopify_attributes }} class="product-form-installment-block">
                  {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                  {%- form 'product', product, id: product_form_installment_id, class: 'installment caption-large' -%}
                    <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                    {{ form | payment_terms }}
                  {%- endform -%}
                </div>
              {%- when 'sku' -%}
                <p
                  class="product__sku no-js-hidden{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}"
                  id="Sku-{{ section.id }}"
                  role="status"
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                  {{ block.shopify_attributes }}
                >
                  <span class="visually-hidden">{{ 'products.product.sku' | t }}:</span>
                  {{- product.selected_or_first_available_variant.sku -}}
                </p>
              {%- when 'inventory' -%}
                <p
                  class="product__inventory no-js-hidden{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                  {{ block.shopify_attributes }}
                  id="Inventory-{{ section.id }}"
                  role="status"
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                >
                  {%- if product.selected_or_first_available_variant.inventory_quantity > 0 -%}
                    {%- if product.selected_or_first_available_variant.inventory_quantity <= block.settings.inventory_threshold -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(238,148,65, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(238,148,65)"/>
                      </svg>
                      {%- if block.settings.show_inventory_quantity -%}
                        {{- 'products.product.inventory_low_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                      {%- else -%}
                        {{- 'products.product.inventory_low_stock' | t -}}
                      {%- endif -%}
                    {%- else -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(62,214,96, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(62,214,96)"/>
                      </svg>
                      {%- if block.settings.show_inventory_quantity -%}
                        {{- 'products.product.inventory_in_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                      {%- else -%}
                          {{- 'products.product.inventory_in_stock' | t -}}
                      {%- endif -%}
                    {%- endif -%}
                  {%- else -%}
                    {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(62,214,96, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(62,214,96)"/>
                      </svg>
                      {{- 'products.product.inventory_out_of_stock_continue_selling' | t -}}
                    {%- else -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(200,200,200, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(200,200,200)"/>
                      </svg>
                      {{- 'products.product.inventory_out_of_stock' | t -}}
                    {%- endif -%}
                  {%- endif -%}
                </p>
              {% when 'emoji_benefits' %}
                <div class='emoji-benefits-container' {{ block.shopify_attributes }} style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                  {{ block.settings.benefits }}
                </div>
              {%- when 'description' -%}
                {%- if product.description != blank -%}
                  <div class="product__description rte quick-add-hidden" {{ block.shopify_attributes }} style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                    {{ product.description }}
                  </div>
                {%- endif -%}
              {%- when 'collapsible_tab' -%}
                <div class="product__accordion accordion accordion--{{ block.settings.heading_size }}{% if block.settings.display_top_border %} accordion--top-border{% endif %} quick-add-hidden" style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;" {{ block.shopify_attributes }}>
                  <details class='accordion__details'{% if block.settings.open %} open{% endif %}>
                    <summary class="accordion__summary">
                      <div class="summary__title">
                        {% if block.settings.custom_icon != blank %}
                          <img
                            src="{{ block.settings.custom_icon | image_url }}"
                            {% if block.settings.custom_icon.alt != blank %}
                              alt="{{ block.settings.custom_icon.alt | escape }}"
                            {% else %}
                              role="presentation"
                            {% endif %}
                            height="auto"
                            width="auto"
                            loading="lazy"
                          >
                        {% else %}
                          {% render 'material-icon', icon: block.settings.icon, filled: block.settings.filled_icon %}
                        {% endif %}
                        <h2 class="h4 accordion__title">
                          {{ block.settings.heading | default: block.settings.page.title }}
                        </h2>
                      </div>
                      {% if block.settings.collapse_icon == 'carret' %}
                        {% render 'icon-caret' %}
                      {% else %}
                        {% render 'icon-plus' %}
                      {% endif %}
                    </summary>
                  </details>
                  <div class='accordion__content-wrapper'>
                    <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                      {{ block.settings.content | replace: '[description]', product.description }}
                      {{ block.settings.page.content }}
                    </div>
                  </div>
                </div>
              {%- when 'quantity_selector' -%}
                {%- if block.settings.enable_quantity_discounts -%}
                  {% render 'quantity-breaks', block: block, product: product, product_form_id: product_form_id %}
                {%- else -%}
                  {% liquid
                    assign quantity_atc_append =  block.settings.atc_append
                    assign quantity_atc_append_heights =  block.settings.atc_append_heights
                  %}
                  {% capture quantity_selector_html %}
                    <div
                      id="Quantity-Form-{{ section.id }}"
                      class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
                      style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                      {{ block.shopify_attributes }}
                    >
                      {% comment %} TODO: enable theme-check once `item_count_for_variant` is accepted as valid filter {% endcomment %}
                      {% # theme-check-disable %}
                      {%- assign cart_qty = cart
                        | item_count_for_variant: product.selected_or_first_available_variant.id
                      -%}
                      {% # theme-check-enable %}
                      <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                        {{ 'products.product.quantity.label' | t }}
                        <span class="quantity__rules-cart no-js-hidden{% if cart_qty == 0 %} hidden{% endif %}">
                          <span class="loading-overlay hidden">
                            <span class="loading-overlay__spinner">
                              <svg
                                aria-hidden="true"
                                focusable="false"
                                class="spinner"
                                viewBox="0 0 66 66"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                              </svg>
                            </span>
                          </span>
                          <span>({{- 'products.product.quantity.in_cart_html' | t: quantity: cart_qty -}})</span>
                        </span>
                      </label>
                      <quantity-input class="quantity main-quantity{% if block.settings.full_width_classic and quantity_atc_append == 'none' %} quantity--full{% endif %} no-background color-{{ settings.quantity_color_scheme }} accent-color-{{ settings.quantity_overlay_color }} accent-2-color-{{ settings.quantity_text_color }}" data-section="{{ section.id }}">
                        <button class="quantity__button no-js-hidden" name="minus" type="button">
                          <span class="visually-hidden">
                            {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                          </span>
                          {% render 'icon-minus' %}
                        </button>
                        <input
                          class="quantity__input"
                          type="number"
                          name="quantity"
                          id="Quantity-{{ section.id }}"
                          data-cart-quantity="{{ cart_qty }}"
                          data-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                          min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                          {% if product.selected_or_first_available_variant.quantity_rule.max != null %}
                            data-max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                            max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                          {% endif %}
                          step="{{ product.selected_or_first_available_variant.quantity_rule.increment }}"
                          value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                          form="{{ product_form_id }}"
                        >
                        <button class="quantity__button no-js-hidden" name="plus" type="button">
                          <span class="visually-hidden">
                            {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                          </span>
                          {% render 'icon-plus' %}
                        </button>
                      </quantity-input>
                      <div class="quantity__rules caption no-js-hidden">
                        {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.multiples_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment
                            -}}
                          </span>
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.minimum_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.min
                            -}}
                          </span>
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.maximum_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.max
                            -}}
                          </span>
                        {%- endif -%}
                      </div>
                    </div>
                  {% endcapture %}
                  
                  {% if quantity_atc_append == 'none' %}
                    {{ quantity_selector_html }}
                  {% endif %}
                {%- endif -%}

              {%- when 'share' -%}
                {% assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin %}
                {% render 'share-button', block: block, share_link: share_url %}
              {% when 'sizing_chart' %}
                {% capture sizing_chart_html %}
                  <modal-opener
                    class="product-popup-modal__opener sizing-chart no-js-hidden quick-add-hidden flex-justify-{{ block.settings.button_alignment }}"
                    data-modal="#PopupModal-{{ block.id }}"
                    data-section="{{ section.id }}"
                    data-append="{{ block.settings.append }}"
                    style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                  >
                    <button
                      id="ProductPopup-{{ block.id }}"
                      class="product-popup-modal__button sizing-chart__button{% if block.settings.button_underline %} sizing-chart__button--underline{% endif %} flex-center" 
                      style='--font-size:{{ block.settings.text_size | divided_by: 10.0 }}rem;'
                      type="button"
                      aria-haspopup="dialog"
                    >
                      {% if block.settings.button_custom_icon != blank %}
                        <img
                          src="{{ block.settings.button_custom_icon | image_url: width: 150 }}"
                          alt=""
                          height="auto"
                          width="auto"
                          loading="lazy"
                        >
                      {% elsif block.settings.button_icon != blank %}
                        {% render 'material-icon', icon: block.settings.button_icon, filled: block.settings.button_filled_icon %}
                      {% endif %}
                      <span class="sizing-chart__button__text">{{ block.settings.button_label }}</span>
                    </button>
                  </modal-opener>
                  <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                    {{- block.settings.text -}}
                  </a>
                {% endcapture %}
                {% assign sizing_chart_append = block.settings.append %}
                {% if block.settings.append == 'none' %}
                  {{ sizing_chart_html }}
                {% endif %}
              {%- when 'variant_picker' -%}
                {% render 'product-variant-picker',
                  product: product,
                  block: block,
                  product_form_id: product_form_id,
                  update_url: false, 
                  sizing_chart_append: sizing_chart_append, 
                  sizing_chart_html: sizing_chart_html
                %}
              {%- when 'buy_buttons' -%}
                {%- render 'buy-buttons',
                  block: block,
                  product: product,
                  product_form_id: product_form_id,
                  section_id: section.id,
                  main_product: false,
                  quantity_atc_append: quantity_atc_append,
                  quantity_selector_html: quantity_selector_html,
                  quantity_atc_append_heights: quantity_atc_append_heights
                -%}
              {%- when 'custom_liquid' -%}
                {{ block.settings.custom_liquid }}
              {%- when 'payment_badges' -%}
                <div class='payment-badges-block' style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                  <ul class="payment-badges" role="list">
                    {% assign enabled_payment_types = shop.enabled_payment_types %}
                    {% if block.settings.enabled_payment_types != blank %}
                      {% assign enabled_payment_types = block.settings.enabled_payment_types | remove: ' ' | split: ',' %}
                    {% endif %}
              
                    {%- for type in enabled_payment_types -%}
                      {% assign payment_type = type | strip %}
                      <li class="list-payment__item">
                        {{ payment_type | payment_type_svg_tag: class: 'icon icon--full-color' }}
                      </li>
                    {%- endfor -%}
                  </ul>
                </div>
              {% when 'estimated_shipping' %}
                {% render 'estimated-shipping', block: block %}
              {% when 'shipping_checkpoints' %}
                {% render 'shipping-checkpoints', block: block %}
              {% when 'custom_product_field' %}
                {% render 'custom-product-field', block: block, not_main: true %}
              {%- when 'reviews' -%}
                {%- render 'reviews', block: block -%}
              {% when 'image' %}
                <div 
                  class="product-info__image-block{% if block.settings.Full_mobile_width %} product-info__image-block--mobile-full{% endif %}" 
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                  {{ block.shopify_attributes }}
                >
                  {% if block.settings.image != blank %}
                    <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%">
                      {%- capture sizes -%}
                        (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                        (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px))
                      {%- endcapture -%}
                      {{
                        block.settings.image
                        | image_url: width: 1500
                        | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
                      }}
                    </div>
                  {% else %}
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                  {% endif %}
                </div>
              {% when 'video' %}
                <div 
                  class="product-info__image-block{% if block.settings.Full_mobile_width %} product-info__image-block--mobile-full{% endif %}" 
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                  {{ block.shopify_attributes }}
                >
                  {% if block.settings.video != blank %}
                    <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: block.settings.video.aspect_ratio | times: 100 }}%">
                      {% render 'video-player', block: block %}
                    </div>
                  {% else %}
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                  {% endif %}
                </div>
              {% when 'product_upsell' %}
                {% render 'upsell-block', block: block, type: 'product-info' %}
              {%- when 'popup' -%}
                <modal-opener
                  class="product-popup-modal__opener no-js-hidden quick-add-hidden"
                  data-modal="#PopupModal-{{ block.id }}"
                  {{ block.shopify_attributes }}
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                >
                  <button
                    id="ProductPopup-{{ block.id }}"
                    class="product-popup-modal__button link"
                    type="button"
                    aria-haspopup="dialog"
                  >
                    {{ block.settings.text | default: block.settings.page.title }}
                  </button>
                </modal-opener>
                <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                  {{- block.settings.text -}}
                </a>
              {%- when 'rating' -%}
                {%- if product.metafields.reviews.rating.value != blank -%}
                  {% liquid
                    assign rating_decimal = 0
                    assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                    if decimal >= 0.3 and decimal <= 0.7
                      assign rating_decimal = 0.5
                    elsif decimal > 0.7
                      assign rating_decimal = 1
                    endif
                  %}
                  <div
                    class="rating"
                    role="img"
                    aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                  >
                    <span
                      aria-hidden="true"
                      class="rating-star color-icon-{{ settings.accent_icons }}"
                      style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                    ></span>
                  </div>
                  <p class="rating-text caption">
                    <span aria-hidden="true">
                      {{- product.metafields.reviews.rating.value }} /
                      {{ product.metafields.reviews.rating.value.scale_max -}}
                    </span>
                  </p>
                  <p class="rating-count caption">
                    <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                    <span class="visually-hidden">
                      {{- product.metafields.reviews.rating_count }}
                      {{ 'accessibility.total_reviews' | t -}}
                    </span>
                  </p>
                {%- endif -%}
              {%- when 'icon_with_text' -%}
                {% render 'icon-with-text', block: block %}
              {%- when 'button' -%}
                <div
                  class="buttons-container"
                  style="--alignment:{{ block.settings.alignment }};--mobile-alignment:{{ block.settings.mobile_alignment }};--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                >
                  <a
                    {% if block.settings.button_link == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}{% if block.settings.full_width %} button--full-width{% endif %}{% if block.settings.full_width %} button--large-text{% endif %}"
                  >
                    {{- block.settings.button_label -}}
                  </a>
                </div>
            {%- endcase -%}
          {%- endfor -%}
          <a
            {% if product == blank %}
              role="link" aria-disabled="true"
            {% else %}
              href="{{ product.url }}"
            {% endif %}
            class="link product__view-details animate-arrow"
          >
            {{ 'products.product.view_full_details' | t }}
            {% render 'icon-arrow' %}
          </a>
        </product-info>
      </div>
      {%- if section.settings.media_position == 'right' -%}
        <div class="grid__item product__media-wrapper small-hide">
          <media-gallery
            id="MediaGallery-{{ section.id }}-right"
            data-section="{{ section.id }}"
            role="region"
            aria-label="{{ 'products.product.media.gallery_viewer' | t }}"
            data-desktop-layout="stacked"
          >
            <div id="GalleryViewer-{{ section.id }}-right" class="product__media-list">
              {%- if product.selected_or_first_available_variant.featured_media != null -%}
                {%- assign media = product.selected_or_first_available_variant.featured_media -%}
                <div class="product__media-item" data-media-id="{{ section.id }}-{{ media.id }}">
                  {% render 'product-thumbnail',
                    media: media,
                    position: 'featured',
                    loop: section.settings.enable_video_looping,
                    modal_id: section.id,
                    xr_button: false,
                    media_width: media_width,
                    media_fit: section.settings.media_fit,
                    constrain_to_viewport: section.settings.constrain_to_viewport
                  %}
                </div>
              {%- endif -%}
              {%- for media in product.media -%}
                {%- if media_to_render contains media.id
                  and media.id != product.selected_or_first_available_variant.featured_media.id
                -%}
                  <div class="product__media-item" data-media-id="{{ section.id }}-{{ media.id }}">
                    {% render 'product-thumbnail',
                      media: media,
                      position: forloop.index,
                      loop: section.settings.enable_video_looping,
                      modal_id: section.id,
                      xr_button: false,
                      media_width: media_width,
                      media_fit: section.settings.media_fit,
                      constrain_to_viewport: section.settings.constrain_to_viewport
                    %}
                  </div>
                {%- endif -%}
              {%- endfor -%}
            </div>
            {%- if first_3d_model -%}
              <button
                class="button button--full-width product__xr-button"
                type="button"
                aria-label="{{ 'products.product.xr_button_label' | t }}"
                data-shopify-xr
                data-shopify-model3d-id="{{ first_3d_model.id }}"
                data-shopify-title="{{ product.title | escape }}"
                data-shopify-xr-hidden
              >
                {% render 'icon-3d-model' %}
                {{ 'products.product.xr_button' | t }}
              </button>
            {%- endif -%}
          </media-gallery>
        </div>
      {%- endif -%}
    </div>
    {% render 'product-media-modal', product: product, variant_images: media_to_render %}
  </div>

  {% assign popups = section.blocks | where: 'type', 'popup' %}
  {%- for block in popups -%}
    <modal-dialog id="PopupModal-{{ block.id }}" class="product-popup-modal" {{ block.shopify_attributes }}>
      <div
        role="dialog"
        aria-label="{{ block.settings.text }}"
        aria-modal="true"
        class="product-popup-modal__content"
        tabindex="-1"
      >
        <button
          id="ModalClose-{{ block.id }}"
          type="button"
          class="product-popup-modal__toggle product-popup-modal__toggle--{{ block.settings.close_button_style }}"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          {% render 'icon-close' %}
        </button>
        <div class="product-popup-modal__content-info">
          <h1 class="h2">{{ block.settings.page.title }}</h1>
          {{ block.settings.page.content }}
        </div>
      </div>
    </modal-dialog>
  {%- endfor -%}
                                
  {% assign sizing_chart = section.blocks | where: 'type', 'sizing_chart' %}
  {%- for block in sizing_chart -%}
    <modal-dialog id="PopupModal-{{ block.id }}" class="product-popup-modal" {{ block.shopify_attributes }}{% if block.settings.test_mode %} open{% endif %}>
      <div
        role="dialog"
        aria-label="{{ block.settings.text }}"
        aria-modal="true"
        class="product-popup-modal__content product-popup-modal__content--centered sizing-chart__modal-container"
        tabindex="-1"
      >
        <button
          id="ModalClose-{{ block.id }}"
          type="button"
          class="product-popup-modal__toggle product-popup-modal__toggle--{{ block.settings.close_button_style }}"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          {% render 'icon-close' %}
        </button>
        <div class="product-popup-modal__content-info sizing-chart__modal content-rte color-{{ block.settings.color_scheme }}">
          {% if block.settings.headline != blank %}
            <h3 class="sizing-chart__title center {{ block.settings.heading_size }}">
              {{ block.settings.headline }}
            </h3>
          {% endif %}
          {% if block.settings.top_image != blank %}
            <div class="sizing-chart__top-image">
              {%- capture sizes -%}
                (min-width: 750px) 800px / 2), calc((100vw - 50px))
              {%- endcapture -%}
              {{
                block.settings.top_image
                | image_url: width: 1500
                | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
              }}
            </div>
          {% endif %}
          {% if block.settings.table_content != blank %}
            <table class="sizing-chart-table">
              {% if block.settings.table_header_content != blank %}
                {% assign columns = block.settings.table_header_content | split: ',' %}
                <thead>
                  <tr>
                    {% for column in columns %}
                      <th class="sizing-chart-table__th color-{{ block.settings.table_header_color_scheme }}" align="center">
                        {{ column | strip }}
                      </th>
                    {% endfor %}
                  </tr>
                </thead>
              {% endif %}
              <tbody>
                {% assign rows = block.settings.table_content | split: '</p><p>' %}
                {% for row in rows %}
                  <tr>
                    {% assign columns = row | remove: '<p>' | remove: '</p>' | split: ',' %}
                    {% for column in columns %}
                      <td class="sizing-chart-table__td" align="center">
                        {{ column | strip }}
                      </td>
                    {% endfor %}
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% endif %}
          {% if block.settings.caption_text != blank %}
            <div
              class="sizing-chart__caption {{ block.settings.caption_alignment }} rte"
              style="--text-size:{{ block.settings.caption_size }};--text-color:{{ block.settings.caption_color }};"
            >
              {{ block.settings.caption_text }}
            </div>
          {% endif %}
          {% if block.settings.bottom_image != blank %}
            <div class="sizing-chart__top-image">
              {%- capture sizes -%}
                (min-width: 750px) 800px / 2), calc((100vw - 50px))
              {%- endcapture -%}
              {{
                block.settings.bottom_image
                | image_url: width: 1500
                | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
              }}
            </div>
          {% endif %}
        </div>
      </div>
    </modal-dialog>
  {%- endfor -%}                 
</section>

{%- if section.settings.image_zoom == 'hover' -%}
  <script id="EnableZoomOnHover-featured" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
{%- endif %}
{%- if request.design_mode -%}
  <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if first_3d_model -%}
  <script type="application/json" id="ProductJSON-{{ product.id }}">
    {{ product.media | where: 'media_type', 'model' | json }}
  </script>
  <script src="{{ 'product-model.js' | asset_url }}" defer></script>
{%- endif -%}

{%- liquid
  if product.selected_or_first_available_variant.featured_media
    assign seo_media = product.selected_or_first_available_variant.featured_media
  else
    assign seo_media = product.featured_media
  endif
-%}

<script type="application/ld+json">
  {
    "@context": "http://schema.org/",
    "@type": "Product",
    "name": {{ product.title | json }},
    "url": {{ request.origin | append: product.url | json }},
    {% if seo_media -%}
      "image": [
        {{ seo_media | image_url: width: 1920 | prepend: "https:" | json }}
      ],
    {%- endif %}
    "description": {{ product.description | strip_html | json }},
    {% if product.selected_or_first_available_variant.sku != blank -%}
      "sku": {{ product.selected_or_first_available_variant.sku | json }},
    {%- endif %}
    "brand": {
      "@type": "Brand",
      "name": {{ product.vendor | json }}
    },
    "offers": [
      {%- for variant in product.variants -%}
        {
          "@type" : "Offer",
          {%- if variant.sku != blank -%}
            "sku": {{ variant.sku | json }},
          {%- endif -%}
          {%- if variant.barcode.size == 12 -%}
              "gtin12": {{ variant.barcode }},
          {%- endif -%}
          {%- if variant.barcode.size == 13 -%}
            "gtin13": {{ variant.barcode }},
          {%- endif -%}
          {%- if variant.barcode.size == 14 -%}
            "gtin14": {{ variant.barcode }},
          {%- endif -%}
          "availability" : "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
          "price" : {{ variant.price | divided_by: 100.00 | json }},
          "priceCurrency" : {{ cart.currency.iso_code | json }},
          "url" : {{ request.origin | append: variant.url | json }}
        }{% unless forloop.last %},{% endunless %}
      {%- endfor -%}
    ]
  }
</script>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    function isIE() {
      const ua = window.navigator.userAgent;
      const msie = ua.indexOf('MSIE ');
      const trident = ua.indexOf('Trident/');

      return msie > 0 || trident > 0;
    }

    if (!isIE()) return;
    const hiddenInput = document.querySelector('#{{ product_form_id }} input[name="id"]');
    const noScriptInputWrapper = document.createElement('div');
    const variantSwitcher =
      document.querySelector('variant-radios[data-section="{{ section.id }}"]') ||
      document.querySelector('variant-selects[data-section="{{ section.id }}"]');
    noScriptInputWrapper.innerHTML = document.querySelector(
      '.product-form__noscript-wrapper-{{ section.id }}'
    ).textContent;
    variantSwitcher.outerHTML = noScriptInputWrapper.outerHTML;

    document.querySelector('#Variants-{{ section.id }}').addEventListener('change', function (event) {
      hiddenInput.value = event.currentTarget.value;
    });
  });
</script>

{% if product.media.size > 0 %}
  <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
{% endif %}

{% schema %}
{
  "name": "t:sections.featured-product.name",
  "tag": "section",
  "class": "section section-featured-product",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "Text with icon",
      "settings": [
        {
          "type": "header",
          "content": "Text"
        },
        {
          "type": "range",
          "id": "mobile_text_size",
          "min": 8,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 14,
          "label": "Mobile text size"
        },
        {
          "type": "range",
          "id": "desktop_text_size",
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Desktop text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "color",
          "id": "text_color",
          "default": "#121212",
          "label": "Text color"
        },
        {
          "type": "inline_richtext",
          "id": "text_1",
          "default": "Text with icon",
          "label": "Text #1"
        },
        {
          "type": "inline_richtext",
          "id": "text_2",
          "label": "Text #2"
        },
        {
          "type": "inline_richtext",
          "id": "text_3",
          "label": "Text #3"
        },
        {
          "type": "header",
          "content": "Icons"
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 100,
          "max": 200,
          "step": 5,
          "default": 120,
          "unit": "%",
          "label": "Icon scale",
          "info": "Relative to text size"
        },
        {
          "type": "color",
          "id": "icon_color",
          "default": "#121212",
          "label": "Icons color"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "check_circle",
          "label": "Icon #1",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "Filled icon #1"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon #1"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "check_circle",
          "label": "Icon #2",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "Filled icon #2"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon #2"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "check_circle",
          "label": "Icon #3",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_3",
          "default": false,
          "label": "Filled icon #3"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_3",
          "label": "Custom icon #3"
        },
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "width",
          "options": [
            {
              "value": "fit-content",
              "label": "Fit text"
            },
            {
              "value": "100%",
              "label": "Full"
            }
          ],
          "default": "100%",
          "label": "Width"
        },
        {
          "type": "select",
          "id": "direction",
          "options": [
            {
              "value": "horizontal",
              "label": "Horizontal"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "default": "horizontal",
          "label": "Stacking direction",
          "info": "Applied when multiple texts are added."
        },
        {
          "type": "range",
          "id": "column_gap",
          "min": 0,
          "max": 6,
          "step": 0.5,
          "label": "Stacking spacing",
          "default": 3
        },
        {
          "type": "checkbox",
          "id": "enable_bg",
          "default": false,
          "label": "Enable background",
          "info": "The following settings are applied when this option is enabled."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background color",
          "default": "#F3F3F3"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "t:settings_schema.global.settings.corner_radius.label",
          "default": 40,
          "info": "Relative to font size"
        },
        {
          "type": "range",
          "id": "padding",
          "min": 1,
          "max": 5,
          "step": 0.5,
          "label": "Padding",
          "default": 3
        },
        {
          "type": "range",
          "id": "border_size",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Border thickness",
          "unit": "px",
          "default": 0
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#B7B7B7"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "label": "Title size",
          "default": "h1"
        },
        {
          "type": "select",
          "id": "title_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            }
          ],
          "default": "left",
          "label": "Title alignment"
        },
        {
          "type": "checkbox",
          "id": "uppercase_title",
          "label": "Uppercase title",
          "default": false
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 0
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "rating_stars",
      "name": "Rating stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 4.8
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "select",
          "id": "bg_stars_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "full",
              "label": "Full"
            }
          ],
          "label": "Background stars style",
          "default": "full"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#ececec",
          "label": "Background stars color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "trustpilot_stars",
      "name": "Trustpilot stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 5
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#00b67a",
          "label": "Active stars container color"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#c8c8c8",
          "label": "Background stars container color"
        },
        {
          "type": "color",
          "id": "star_symbol_color",
          "default": "#fff",
          "label": "Stars inside symbol color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "price",
      "name": "t:sections.main-product.blocks.price.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "label": "Layout",
          "options": [
            {
              "value": "price_first",
              "label": "Price first"
            },
            {
              "value": "price_second",
              "label": "Compare price first"
            }
          ],
          "default": "price_first"
        },
        {
          "type": "select",
          "id": "price_color",
          "label": "Price color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "select",
          "id": "compare_price_color",
          "label": "Compare price color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "text"
        },
        {
          "type": "header",
          "content": "Badge"
        },
        {
          "type": "select",
          "id": "displayed_badge",
          "label": "Displayed badge",
          "options": [
            {
              "value": "none",
              "label": "Hidden"
            },
            {
              "value": "sale",
              "label": "Sale badge"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "sale",
          "info": "If the variant is sold out, the sold out badge will be displayed instead. \"Sale badge\" displays the classci sale badge from Theme settings > Badges if product has a comapre price. \"Custom\" displays a custom badge that's configured below. The custom badge is displayed even if the product doesn't have a compare price."
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 100,
          "max": 200,
          "step": 5,
          "default": 140,
          "unit": "%",
          "label": "Icon scale",
          "info": "Relative to text size"
        },
        {
          "type": "text",
          "id": "custom_badge_text",
          "label": "Custom badge text",
          "default": "[icon_1] SPECIAL OFFER",
          "info": "Dynamic values that can be used: [icon_1] dispalys the Icon #1. [icon_2] dispalys the Icon #1. [percentage_saved] displays the percentage saved from the product compare price. [amount_saved] displays the money amount saved from the comapre price."
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "redeem",
          "label": "Icon #1",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "Filled icon #1"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon #1"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "check_circle",
          "label": "Icon #2",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "Filled icon #2"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon #2"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "sku",
      "name": "t:sections.featured-product.blocks.sku.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.featured-product.blocks.sku.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.featured-product.blocks.sku.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.featured-product.blocks.sku.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.featured-product.blocks.sku.settings.text_style.label"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Classic quantity selector"
        },
        {
          "type": "paragraph",
          "content": "To customize the appearance of the classic quantity selector (not quantity breaks), navigate to Theme settings > Quantity selector."
        },
        {
          "type": "checkbox",
          "id": "full_width_classic",
          "label": "Full width"
        },
        {
          "type": "select",
          "id": "atc_append",
          "options": [
            {
              "value": "none",
              "label": "Normal"
            },
            {
              "value": "left",
              "label": "Left side of the ATC button"
            },
            {
              "value": "right",
              "label": "Right side of the ATC button"
            }
          ],
          "default": "none",
          "label": "Position",
          "info": "IMPORTANT: When displaying the selector next to the ATC button, the Quantity selector block must be placed ABOVE the Buy buttons block."
        },
        {
          "type": "select",
          "id": "atc_append_heights",
          "options": [
            {
              "value": "original",
              "label": "Keep original heights"
            },
            {
              "value": "stretch-quantity",
              "label": "Make quantity selector same height as ATC button"
            },
            {
              "value": "shrink-atc",
              "label": "Make ATC button same height as quantity selector"
            }
          ],
          "default": "stretch-quantity",
          "label": "Heights of the quantity selector & ATC button when together",
          "info": "By default, quantity selector & ATC button are not the same height. When you display the quantity selector next to the ATC button, this setting controls how's that handled."
        },
        {
          "type": "header",
          "content": "Quantity Discounts"
        },
        {
          "type": "paragraph",
          "content": "Make sure to set up an [automatic discount](https://help.shopify.com/en/manual/discounts/automatic-discounts) to allow items to be discounted at certain quantity levels."
        },
        {
          "type": "checkbox",
          "id": "enable_quantity_discounts",
          "label": "Enable Quantity Discounts"
        },
        {
          "type": "paragraph",
          "content": "[Tutorial on how to set up](https://dashboard.shrinesolutions.com/customer/help-center?category=Blocks&element=Quantity+Breaks)"
        },
        {
          "type": "paragraph",
          "content": "DYNAMIC VALUES: Use these auto-calculated values based on variant/quantity: [quantity] - bundle quantity, [price] - bundle total, [compare_price] - bundle compare total (adjustable calculation for each option), [price_each] - item average price, [compare_price_each] - item average compare price, [amount_saved] - difference between total & compare price, [amount_saved_rounded] - rounded up difference (e.g., 19.95 > 20)."
        },
        {
          "type": "paragraph",
          "content": "NOTE: Dynamic values are available in ALL text boxes"
        },
        {
          "type": "select",
          "id": "style",
          "options": [
            {
              "value": "normal",
              "label": "Normal"
            },
            {
              "value": "compact",
              "label": "Compact"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "label": "Style",
          "default": "normal"
        },
        {
          "type": "text",
          "id": "headline",
          "label": "Heading",
          "default": "BUNDLE & SAVE"
        },
        {
          "type": "select",
          "id": "preselected",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "option_1",
              "label": "Option #1"
            },
            {
              "value": "option_2",
              "label": "Option #2"
            },
            {
              "value": "option_3",
              "label": "Option #3"
            },
            {
              "value": "option_4",
              "label": "Option #4"
            }
          ],
          "default": "option_1",
          "label": "Preselected option"
        },
        {
          "type": "checkbox",
          "id": "display_selected_indicator",
          "label": "Display selected indicator in normal & vertical style",
          "default": true
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 10
        },
        {
          "type": "range",
          "id": "border_width",
          "min": 1,
          "max": 5,
          "step": 1,
          "unit": "px",
          "label": "Border width",
          "default": 2
        },
        {
          "type": "select",
          "id": "color_scheme",
          "label": "Color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "checkbox",
          "id": "enable_variant_selectors",
          "label": "Enable variant selectors",
          "default": true,
          "info": "If the product has multiple variants, a variant picker will be displayed for each quantity"
        },
        {
          "type": "checkbox",
          "id": "enable_variant_selectors_on_quantity_of_1",
          "label": "Enable variant selectors on single quantity",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "update_prices",
          "label": "Enable variant price updates",
          "default": false,
          "info": "This option will dynamically change the displayed bundle prices based on the selected variant. ATTENTION: This option might NOT work with currency converters."
        },
        {
          "type": "checkbox",
          "id": "skip_unavailable",
          "label": "Hide & automatically skip sold out variants",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "full_width_pickers",
          "label": "Make variant pickers full width",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "hide_pickers_overlay",
          "label": "Disable variant pickers color overlay",
          "default": true
        },
        {
          "type": "inline_richtext",
          "id": "pickers_label",
          "label": "Variant pickers label",
          "info": "Displayed above the variant pickers."
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Images width",
          "default": 70,
          "info": "Width of images that can be added with normal & vertical styles."
        },
        {
          "type": "checkbox",
          "id": "space_images",
          "label": "Space images from top in vertical style",
          "default": true,
          "info": "Applied if style is set to Vertical and the option below is set to Top."
        },
        {
          "type": "select",
          "id": "vertical_images_position",
          "label": "Image position in vertical style",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "vertical_prices_layout",
          "label": "Prices layout in vertical style",
          "options": [
            {
              "value": "vertical",
              "label": "Vertical"
            },
            {
              "value": "horizontal",
              "label": "Horizontal"
            }
          ],
          "default": "vertical",
          "info": "Difference is visible if a bundle has a compare price."
        },
        {
          "type": "header",
          "content": "Quantity option #1"
        },
        {
          "type": "range",
          "id": "option_1_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #1 Quantity",
          "default": 1,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_1_badge",
          "label": "Option #1 Badge text"
        },
        {
          "type": "select",
          "id": "option_1_badge_style",
          "label": "Option #1 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_1_badge_color",
          "label": "Option #1 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_1_image",
          "label": "Option #1 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_1_label",
          "label": "Option #1 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_1_benefit",
          "label": "Option #1 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_1_benefit_position",
          "label": "Option #1 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_1_benefit_style",
          "label": "Option #1 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_1_benefit_color",
          "label": "Option #1 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_1_caption",
          "label": "Option #1 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_1_percentage_off_text",
          "default": "0",
          "label": "Option #1 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_1_fixed_amount_off",
          "label": "Option #1 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_1_price_text",
          "label": "Option #1 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_1_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #1 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_1_compare_price_text",
          "label": "Option #1 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Quantity option #2"
        },
        {
          "type": "range",
          "id": "option_2_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #2 Quantity",
          "default": 2,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_2_badge",
          "label": "Option #2 Badge text"
        },
        {
          "type": "select",
          "id": "option_2_badge_style",
          "label": "Option #2 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_2_badge_color",
          "label": "Option #2 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_2_image",
          "label": "Option #2 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_2_label",
          "label": "Option #2 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_2_benefit",
          "label": "Option #2 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_2_benefit_position",
          "label": "Option #2 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_2_benefit_style",
          "label": "Option #2 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_2_benefit_color",
          "label": "Option #2 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_2_caption",
          "label": "Option #2 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_2_percentage_off_text",
          "default": "0",
          "label": "Option #2 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_2_fixed_amount_off",
          "label": "Option #2 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_2_price_text",
          "label": "Option #2 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_2_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #2 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_2_compare_price_text",
          "label": "Option #2 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Quantity option #3"
        },
        {
          "type": "range",
          "id": "option_3_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #3 Quantity",
          "default": 3,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_3_badge",
          "label": "Option #3 Badge text"
        },
        {
          "type": "select",
          "id": "option_3_badge_style",
          "label": "Option #3 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_3_badge_color",
          "label": "Option #3 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_3_image",
          "label": "Option #3 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_3_label",
          "label": "Option #3 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_3_benefit",
          "label": "Option #3 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_3_benefit_position",
          "label": "Option #3 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_3_benefit_style",
          "label": "Option #3 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_3_benefit_color",
          "label": "Option #3 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_3_caption",
          "label": "Option #3 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_3_percentage_off_text",
          "default": "0",
          "label": "Option #3 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_3_fixed_amount_off",
          "label": "Option #3 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_3_price_text",
          "label": "Option #3 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_3_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #3 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_3_compare_price_text",
          "label": "Option #3 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Quantity option #4"
        },
        {
          "type": "range",
          "id": "option_4_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #4 Quantity",
          "default": 4,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_4_badge",
          "label": "Option #4 Badge text"
        },
        {
          "type": "select",
          "id": "option_4_badge_style",
          "label": "Option #4 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_4_badge_color",
          "label": "Option #4 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_4_image",
          "label": "Option #4 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_4_label",
          "label": "Option #4 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_4_benefit",
          "label": "Option #4 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_4_benefit_position",
          "label": "Option #4 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_4_benefit_style",
          "label": "Option #4 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_4_benefit_color",
          "label": "Option #4 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_4_caption",
          "label": "Option #4 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_4_percentage_off_text",
          "default": "0",
          "label": "Option #4 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_4_fixed_amount_off",
          "label": "Option #4 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_4_price_text",
          "label": "Option #4 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_4_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #4 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_4_compare_price_text",
          "label": "Option #4 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "text",
          "id": "picker_types",
          "label": "Type for each option",
          "default": "pills, pills, pills",
          "info": "Choose picker type for each option individually by splitting them with a comma. Available options are \"pills\", \"dropdown\", \"swatches\", \"quantity breaks\" and \"hidden\". Example: \"swatches, dropdown, quantity breaks\"."
        },
        {
          "type": "text",
          "id": "custom_labels",
          "label": "Custom labels",
          "info": "If empty, the option name will be displayed. Available dynamic values: [count] - option index, [name] - option name, [name_lowercase] - option name in lowercase, [name_uppercase] - option name in uppercase, [selected] - selected option. Not applied to \"Quantity breaks\" picker heading."
        },
        {
          "type": "checkbox",
          "id": "disable_prepend",
          "label": "Disable prepend of the new active media",
          "info": "If disabled, the new active variant media will be moved to the first place in the slider.",
          "default": false
        },
        {
          "type": "header",
          "content": "Color swatches"
        },
        {
          "type": "select",
          "id": "swatches_size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            },
            {
              "value": "extra-large",
              "label": "Extra large"
            }
          ],
          "label": "Swatches size",
          "default": "medium"
        },
        {
          "type": "range",
          "id": "swatches_border_radius",
          "min": 0,
          "max": 100,
          "step": 4,
          "unit": "%",
          "label": "Corner radius",
          "default": 100
        },
        {
          "type": "select",
          "id": "swatches_custom_colors",
          "options": [
            {
              "value": "disabled",
              "label": "Disabled"
            },
            {
              "value": "image_alt",
              "label": "Variant images alt text"
            },
            {
              "value": "custom",
              "label": "Custom colors list"
            }
          ],
          "label": "Swatches custom colors",
          "default": "disabled",
          "info": "Disabled displays the variant images in swatches. Variant images alt text displays CSS values from each image's alt text. Custom colors list takes the list of the custom CSS values from the setting bellow. Watch the tutorial for CSS values [here](https:\/\/youtu.be\/Xo_ZFzRCk3o)"
        },
        {
          "type": "text",
          "id": "swatches_custom_colors_list",
          "label": "Custom colors list)",
          "info": "CSS values (hex/gradient) split by a comma. Example: \"#000000, #FFFFFF\"",
          "default": "#000000, #FFFFFF"
        },
        {
          "type": "header",
          "content": "Quantity breaks"
        },
        {
          "type": "paragraph",
          "content": "DYNAMIC VALUES: Use these auto-calculated values based on variant: [name] - option name, [price] - option price, [compare_price] - option compare price, [amount_saved] - difference between price & compare price, [amount_saved_rounded] - rounded up difference (e.g., 19.95 > 20)."
        },
        {
          "type": "paragraph",
          "content": "NOTE: Dynamic values are available in ALL text boxes."
        },
        {
          "type": "select",
          "id": "breaks_style",
          "options": [
            {
              "value": "normal",
              "label": "Normal"
            },
            {
              "value": "compact",
              "label": "Compact"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "label": "Style",
          "default": "normal"
        },
        {
          "type": "text",
          "id": "breaks_headline",
          "label": "Heading",
          "default": "BUNDLE & SAVE"
        },
        {
          "type": "select",
          "id": "breaks_color_scheme",
          "label": "Color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "paragraph",
          "content": "HOW TO CUSTOMIZE TEXT: Every text field bellow is used to configure text of all options together. Split the texts for each option WITH A COMMA. Use [empty] to skip an option. Example: \"Caption 1, [empty], Caption 3\". All dynamic values are available."
        },
        {
          "type": "text",
          "id": "breaks_badges",
          "label": "Badges",
          "default": "[empty], Most popular, [empty]"
        },
        {
          "type": "select",
          "id": "breaks_displayed_images",
          "label": "Displayed images",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "variant_images",
              "label": "Variant images"
            },
            {
              "value": "custom",
              "label": "Custom images"
            }
          ],
          "default": "variant_images",
          "info": "Choose which images you want to display when the Style is set to Vertical"
        },
        {
          "type": "text",
          "id": "breaks_custom_images",
          "label": "Custom image URLs",
          "info": "To upload images, go to your Shopify admin > Content > Files > Upload an image > copy its link"
        },
        {
          "type": "range",
          "id": "breaks_image_width",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Images width",
          "default": 70,
          "info": "Width of images (relative to the container) that can be added when Style is set to Vertical"
        },
        {
          "type": "checkbox",
          "id": "breaks_space_images",
          "label": "Space images from top",
          "default": true
        },
        {
          "type": "text",
          "id": "breaks_labels",
          "label": "Labels",
          "default": "[name], [name], [name]"
        },
        {
          "type": "text",
          "id": "breaks_benefits",
          "label": "Benefits",
          "default": "[empty], Free Shipping, Free Shipping"
        },
        {
          "type": "text",
          "id": "breaks_captions",
          "label": "Captions",
          "default": "Variant 1 caption, Variant 2 caption, Variant 3 caption"
        },
        {
          "type": "text",
          "id": "breaks_price_texts",
          "label": "Price texts",
          "default": "[price], [price], [price]"
        },
        {
          "type": "text",
          "id": "breaks_compare_price_texts",
          "label": "Compare price texts",
          "default": "[compare_price], [compare_price], [compare_price]"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.main-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "paragraph",
          "content": "IMPORTANT: Variant selectors in Quantity breaks, gifts & preselected upsells do NOT work with dynamic checkout buttons."
        },
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": false,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "checkbox",
          "id": "skip_cart",
          "label": "Skip cart",
          "default": false,
          "info": "Your customers will be sent directly to checkout after click the Add to Cart button."
        },
        {
          "type": "header",
          "content": "Buttons & icons"
        },
        {
          "type": "checkbox",
          "id": "uppercase_text",
          "label": "Uppercase button labels",
          "default": true
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 80,
          "max": 180,
          "step": 5,
          "unit": "%",
          "label": "Icons scale",
          "default": 120,
          "info": "Related to button label font size"
        },
        {
          "type": "range",
          "id": "icon_spacing",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Icons spacing",
          "default": 10,
          "info": "Empty space between the button label & the icons"
        },
        {
          "type": "header",
          "content": "Main button"
        },
        {
          "type": "checkbox",
          "id": "display_price",
          "label": "Display price inside the button",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "enable_custom_color",
          "label": "Enable custom button color",
          "default": false
        },
        {
          "type": "color",
          "id": "custom_color",
          "default": "#dd1d1d",
          "label": "Button custom color",
          "info": "Applied when Enable custom button color is checked."
        },
        {
          "type": "image_picker",
          "id": "prefix_icon",
          "label": "Label prefix icon"
        },
        {
          "type": "image_picker",
          "id": "suffix_icon",
          "label": "Label suffix icon"
        },
        {
          "type": "header",
          "content": "Secondary Skip cart ATC button"
        },
        {
          "type": "paragraph",
          "content": "This is a secondary add to cart button which goes straight to checkout. It's a replica of the unbranded \"Buy It Now\" button."
        },
        {
          "type": "paragraph",
          "content": "Since dynamic checkout buttons don't work with multiple variants, this is meant to be a substitute for it."
        },
        {
          "type": "checkbox",
          "id": "enable_secondary_btn",
          "label": "Display secondary Skip cart ATC button",
          "default": false
        },
        {
          "type": "text",
          "id": "secondary_btn_label",
          "label": "Button label",
          "default": "Buy It Now"
        },
        {
          "type": "checkbox",
          "id": "secondary_btn_enable_custom_color",
          "label": "Enable custom button color",
          "default": false
        },
        {
          "type": "color",
          "id": "secondary_btn_custom_color",
          "default": "#dd1d1d",
          "label": "Button custom color",
          "info": "Applied when Enable custom button color is checked."
        },
        {
          "type": "image_picker",
          "id": "secondary_btn_prefix_icon",
          "label": "Label prefix icon"
        },
        {
          "type": "image_picker",
          "id": "secondary_btn_suffix_icon",
          "label": "Label suffix icon"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "reviews",
      "name": "Reviews",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            { 
              "value": "inverse", 
              "label": "t:sections.all.colors.inverse.label" 
            }
          ],
          "default": "background-1",
          "label": "Color scheme"
        },
        {
          "type": "checkbox",
          "id": "show_custom_bg",
          "label": "Show custom background",
          "default": false
        },
        {
          "type": "color",
          "id": "custom_bg_color",
          "label": "Custom background color",
          "default": "#F2F2F2"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 12
        },
        {
          "type": "range",
          "id": "border_width",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Border thickness",
          "unit": "px",
          "default": 0
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#B7B7B7"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "select",
          "id": "avatar_alignment",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "middle",
              "label": "Middle"
            }
          ],
          "label": "Avatar alignment",
          "default": "top"
        },
        {
          "type": "range",
          "id": "avatar_corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Avatar corner radius",
          "default": 40
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "range",
          "id": "stars_translate",
          "min": -10,
          "max": 20,
          "step": 1,
          "unit": "%",
          "default": 0,
          "label": "Stars vertical position adjustment",
          "info": "Move the stars up/down to adjust them to different fonts. Negative values move the stars up."
        },
        {
          "type": "color",
          "id": "checkmark_color",
          "default": "#6D388B",
          "label": "Checkmark background color"
        },
        {
          "type": "color",
          "id": "checkmark_icon_color",
          "default": "#FFFFFF",
          "label": "Checkmark icon color"
        },
        {
          "type": "header",
          "content": "Slider"
        },
        {
          "type": "select",
          "id": "slider_type",
          "options": [
            {
              "value": "slide",
              "label": "Classic"
            },
            {
              "value": "loop",
              "label": "Infinite"
            },
            {
              "value": "fade",
              "label": "Fade"
            }
          ],
          "default": "slide",
          "label": "Type"
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "Enable autoplay",
          "default": false
        },
        {
          "type": "range",
          "id": "autoplay_speed",
          "min": 1,
          "max": 15,
          "step": 0.5,
          "default": 5,
          "unit": "sec",
          "label": "Autoplay speed"
        },
        {
          "type": "checkbox",
          "id": "display_arrows",
          "label": "Display arrows",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "display_dots",
          "label": "Display dots",
          "default": true
        },
        {
          "type": "header",
          "content": "Review #1"
        },
        {
          "type": "inline_richtext",
          "id": "author_1",
          "label": "Review #1 Author name",
          "default": "<em>Author</em> [stars]",
          "info": "Use [stars] to display review stars and [checkmark] to display the verified checkmark icon."
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Review #1 Author image"
        },
        {
          "type": "richtext",
          "id": "text_1",
          "label": "Review #1 Text",
          "default": "<p>Share positive thoughts and feedback from your customer.</p>"
        },
        {
          "type": "header",
          "content": "Review #2"
        },
        {
          "type": "inline_richtext",
          "id": "author_2",
          "label": "Review #2 Author name",
          "default": "<em>Author</em> [stars]",
          "info": "Use [stars] to display review stars and [checkmark] to display the verified checkmark icon."
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Review #2 Author image"
        },
        {
          "type": "richtext",
          "id": "text_2",
          "label": "Review #2 Text",
          "default": "<p>Share positive thoughts and feedback from your customer.</p>"
        },
        {
          "type": "header",
          "content": "Review #3"
        },
        {
          "type": "inline_richtext",
          "id": "author_3",
          "label": "Review #3 Author name",
          "default": "<em>Author</em> [stars]",
          "info": "Use [stars] to display review stars and [checkmark] to display the verified checkmark icon."
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "Review #3 Author image"
        },
        {
          "type": "richtext",
          "id": "text_3",
          "label": "Review #3 Text",
          "default": "<p>Share positive thoughts and feedback from your customer.</p>"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "description",
      "name": "t:sections.main-product.blocks.description.name",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "share",
      "name": "t:sections.featured-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.featured-product.blocks.share.settings.text.label",
          "default": "Share"
        },
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.share.settings.title_info.content"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 0
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 0
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.featured-product.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.featured-product.blocks.custom_liquid.settings.custom_liquid.label"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.main-product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "Heading size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium"
        },
        {
          "type": "text",
          "id": "icon",
          "default": "check_box",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon"
        },
        {
          "type": "select",
          "id": "collapse_icon",
          "label": "Collapse icon",
          "options": [
            {
              "value": "carret",
              "label": "Carret"
            },
            {
              "value": "plus",
              "label": "Plus"
            }
          ],
          "default": "carret"
        },
        {
          "type": "checkbox",
          "id": "display_top_border",
          "label": "Display top border",
          "default": true,
          "info": "This option is automatically optimized for stacked rows."
        },
        {
          "type": "checkbox",
          "id": "open",
          "label": "Open row by default",
          "default": false
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label",
          "info": "Use [description] to automatically display product description."
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 0
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Pop-up link text",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        },
        {
          "type": "select",
          "id": "close_button_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "minimalistic",
              "label": "Minimalistic"
            }
          ],
          "default": "minimalistic",
          "label": "Close button style"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 12
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 12
        }
      ]
    },
    {
      "type": "sizing_chart",
      "name": "Sizing chart",
      "settings": [
        {
          "type": "checkbox",
          "id": "test_mode",
          "label": "Test mode",
          "info": "Keeps the popup open for faster customization.",
          "default": false
        },
        {
          "type": "header",
          "content": "Open button"
        },
        {
          "type": "paragraph",
          "content": "IMPORTANT: If using the append, the Sizing blcok has to be placed ABOVE the Variant picker block."
        },
        {
          "type": "select",
          "id": "append",
          "options": [
            {
              "value": "none",
              "label": "Don't append"
            },
            {
              "value": "option_1_label_container",
              "label": "Option #1 label"
            },
            {
              "value": "option_1_under_container",
              "label": "Under option #1 picker"
            },
            {
              "value": "option_2_label_container",
              "label": "Option #2 label"
            },
            {
              "value": "option_2_under_container",
              "label": "Under option #2 picker"
            },
            {
              "value": "option_3_label_container",
              "label": "Option #3 label"
            },
            {
              "value": "option_3_under_container",
              "label": "Under option #3 picker"
            }
          ],
          "default": "none",
          "label": "Append the open button into the variant picker"
        },
        {
          "type": "inline_richtext",
          "id": "button_label",
          "label": "Button label",
          "default": "Sizing chart"
        },
        {
          "type": "text",
          "id": "button_icon",
          "default": "straighten",
          "label": "Button icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "button_filled_icon",
          "default": false,
          "label": "Button filled icon"
        },
        {
          "type": "image_picker",
          "id": "button_custom_icon",
          "label": "Button custom icon"
        },
        {
          "type": "range",
          "id": "text_size",
          "min": 12,
          "max": 22,
          "step": 1,
          "unit": "px",
          "default": 17,
          "label": "Button text size"
        },
        {
          "type": "select",
          "id": "button_alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ],
          "default": "start",
          "label": "Button alignment"
        },
        {
          "type": "checkbox",
          "id": "button_underline",
          "default": false,
          "label": "Button underlined text"
        },
        {
          "type": "header",
          "content": "Popup"
        },
        {
          "type": "text",
          "id": "headline",
          "default": "Sizing Chart",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h3",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "close_button_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "minimalistic",
              "label": "Minimalistic"
            }
          ],
          "default": "minimalistic",
          "label": "Close button style"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            { 
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "t:sections.all.colors.label"
        },
        {
          "type": "header",
          "content": "Table"
        },
        {
          "type": "inline_richtext",
          "id": "table_header_content",
          "label": "Header content",
          "default": "Size, Chest, Length, Shoulder",
          "info": "Split the words with a comma to create columns. If you wish to leave a cell empty, use [empty]. Avoid styling the comma with bold,italic etc. to avoid unexpected content styling."
        },
        {
          "type": "select",
          "id": "table_header_color_scheme",
          "options": [
            { 
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "inverse",
          "label": "Header color scheme"
        },
        {
          "type": "richtext",
          "id": "table_content",
          "label": "Body content",
          "default": "<p>S, 95cm, 61cm, 44cm</p><p>M, 102cm, 66cm, 46cm</p><p>L, 106cm, 71cm, 48cm</p><p>XL, 111cm, 74cm, 51cm</p>",
          "info": "Go into a new line to create rows. Split the words with a comma to create columns. If you wish to leave a cell empty, use [empty]. Avoid styling the comma with bold,italic etc. to avoid unexpected content styling."
        },
        {
          "type": "header",
          "content": "Caption"
        },
        {
          "type": "richtext",
          "id": "caption_text",
          "label": "Caption",
          "default": "<p>Give your customers further information about choosing the correct size.</p>"
        },
        {
          "type": "select",
          "id": "caption_size",
          "options": [
            {
              "value": "1.2rem",
              "label": "Small"
            },
            {
              "value": "1.45rem",
              "label": "Medium"
            },
            {
              "value": "1.7rem",
              "label": "Large"
            }
          ],
          "default": "1.45rem",
          "label": "Caption size"
        },
        {
          "type": "color",
          "id": "caption_color",
          "label": "Caption color",
          "default": "#121212"
        },
        {
          "type": "select",
          "id": "caption_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Caption alignment"
        },
        {
          "type": "header",
          "content": "Images"
        },
        {
          "type": "image_picker",
          "id": "top_image",
          "label": "Top image",
          "info": "Displayed above the table."
        },
        {
          "type": "image_picker",
          "id": "bottom_image",
          "label": "Bottom image",
          "info": "Displayed bellow the table and the caption."
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "product_upsell",
      "name": "Product upsells",
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "style",
          "options": [
            {
              "value": "toggle_switch",
              "label": "Toggle switch"
            },
            {
              "value": "checkbox_1",
              "label": "Checkbox style 1"
            },
            {
              "value": "checkbox_2",
              "label": "Checkbox style 2"
            },
            {
              "value": "plus_button",
              "label": "Plus button"
            },
            {
              "value": "add_button",
              "label": "Classic add button"
            }
          ],
          "default": "toggle_switch",
          "label": "Toggle button style"
        },
        {
          "type": "select",
          "id": "btn_position",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "right",
          "label": "Toggle button position"
        },
        {
          "type": "select",
          "id": "toggle_element",
          "options": [
            {
              "value": "button",
              "label": "Toggle button"
            },
            {
              "value": "container",
              "label": "Whole container"
            }
          ],
          "default": "button",
          "label": "Toggle product selection by clicking on:"
        },
        {
          "type": "inline_richtext",
          "id": "add_btn_label",
          "label": "Classic add button label",
          "default":  "<strong>Add</strong>"
        },
        {
          "type": "select",
          "id": "stacking",
          "options": [
            {
              "value": "row",
              "label": "Under each other"
            },
            {
              "value": "column",
              "label": "Next to each other"
            },
            {
              "value": "slider",
              "label": "Slider"
            }
          ],
          "default": "row",
          "label": "Multiple products stacking",
          "info": "If Next to each other is selected, featured images with transparent backgrounds are recommended."
        },
        {
          "type": "select",
          "id": "accent_color",
          "label": "Accent color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            { 
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Products"
        },
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Products",
          "limit": 3,
          "info": "Up to 3 products per block are supported."
        },
        {
          "type": "header",
          "content": "Product #1"
        },
        {
          "type": "checkbox",
          "id": "prdouct_1_preselected",
          "label": "Product #1 Preselected",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "product_1_image",
          "label": "Product #1 Featured image"
        },
        {
          "type": "inline_richtext",
          "id": "product_1_desc",
          "label": "Product #1 Description"
        },
        {
          "type": "text",
          "id": "product_1_percentage_discount",
          "label": "Product #1 Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_1_fixed_amount_discount",
          "label": "Product #1 Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "header",
          "content": "Product #2"
        },
        {
          "type": "checkbox",
          "id": "prdouct_2_preselected",
          "label": "Product #2 Preselected",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "product_2_image",
          "label": "Product #2 Featured image"
        },
        {
          "type": "inline_richtext",
          "id": "product_2_desc",
          "label": "Product #2 Description"
        },
        {
          "type": "text",
          "id": "product_2_percentage_discount",
          "label": "Product #2 Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_2_fixed_amount_discount",
          "label": "Product #2 Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "header",
          "content": "Product #3"
        },
        {
          "type": "checkbox",
          "id": "prdouct_3_preselected",
          "label": "Product #3 Preselected",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "product_3_image",
          "label": "Product #3 Featured image"
        },
        {
          "type": "inline_richtext",
          "id": "product_3_desc",
          "label": "Product #3 Description"
        },
        {
          "type": "text",
          "id": "product_3_percentage_discount",
          "label": "Product #3 Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_3_fixed_amount_discount",
          "label": "Product #3 Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "checkbox",
          "id": "show_images",
          "label": "Show product images",
          "default": true
        },
        {
          "type": "select",
          "id": "images_size",
          "options": [
            {
              "value": "3.5",
              "label": "Extra small"
            },
            {
              "value": "4.25",
              "label": "Small"
            },
            {
              "value": "5",
              "label": "Medium"
            },
            {
              "value": "5.75",
              "label": "Large"
            },
            {
              "value": "6.5",
              "label": "Extra large"
            }
          ],
          "default": "5",
          "label": "Images size"
        },
        {
          "type": "range",
          "id": "images_corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Images corner radius",
          "default": 0
        },
        {
          "type": "select",
          "id": "title_size",
          "options": [
            {
              "value": "1",
              "label": "Extra small"
            },
            {
              "value": "1.2",
              "label": "Small"
            },
            {
              "value": "1.4",
              "label": "Medium"
            },
            {
              "value": "1.6",
              "label": "Large"
            },
            {
              "value": "1.8",
              "label": "Extra large"
            }
          ],
          "default": "1.4",
          "label": "Product title font size"
        },
        {
          "type": "checkbox",
          "id": "title_link",
          "label": "Make title a product page link",
          "default": false
        },
        {
          "type": "select",
          "id": "desc_size",
          "options": [
            {
              "value": "0.9",
              "label": "Extra small"
            },
            {
              "value": "1.05",
              "label": "Small"
            },
            {
              "value": "1.2",
              "label": "Medium"
            },
            {
              "value": "1.35",
              "label": "Large"
            },
            {
              "value": "1.5",
              "label": "Extra large"
            }
          ],
          "default": "1.2",
          "label": "Description font size"
        },
        {
          "type": "select",
          "id": "price_position",
          "options": [
            {
              "value": "next_to_title",
              "label": "Next to title"
            },
            {
              "value": "separate",
              "label": "Separate"
            },
            {
              "value": "hidden",
              "label": "Hidden"
            }
          ],
          "default": "next_to_title",
          "label": "Price position"
        },
        {
          "type": "select",
          "id": "price_size",
          "options": [
            {
              "value": "1",
              "label": "Extra small"
            },
            {
              "value": "1.2",
              "label": "Small"
            },
            {
              "value": "1.4",
              "label": "Medium"
            },
            {
              "value": "1.6",
              "label": "Large"
            },
            {
              "value": "1.8",
              "label": "Extra large"
            }
          ],
          "default": "1.4",
          "label": "Price font size"
        },
        {
          "type": "checkbox",
          "id": "update_prices",
          "label": "Enable variant price updates",
          "default": false,
          "info": "This option will dynamically change the displayed price based on the selected variant. ATTENTION: This option might NOT work with currency converters."
        },
        {
          "type": "checkbox",
          "id": "hide_compare_price",
          "label": "Hide compare price",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_variant_picker",
          "label": "Show variant picker",
          "default": true
        },
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "t:settings_schema.global.settings.corner_radius.label",
          "default": 0
        },
        {
          "type": "checkbox",
          "id": "show_box_shadow",
          "label": "Show drop shadow",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_border",
          "label": "Show border",
          "default": false
        },
        {
          "type": "color",
          "id": "regular_border_color",
          "label": "Border color",
          "default": "#E6E6E6"
        },
        {
          "type": "color",
          "id": "selected_border_color",
          "label": "Selected border color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "border_width",
          "options": [
            {
              "value": "0.1",
              "label": "Thin"
            },
            {
              "value": "0.2",
              "label": "Normal"
            }
          ],
          "default": "0.2",
          "label": "Border thickness"
        },
        {
          "type": "checkbox",
          "id": "show_custom_bg",
          "label": "Show custom background",
          "default": false
        },
        {
          "type": "color",
          "id": "regular_bg_color",
          "label": "Background color",
          "default": "#F2F2F2"
        },
        {
          "type": "color",
          "id": "selected_bg_color",
          "label": "Selected background color",
          "default": "#F2F2F2"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 21
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 21
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Image width",
          "default": 100
        },
        {
          "type": "checkbox",
          "id": "Full_mobile_width",
          "label": "Full container width on mobile",
          "default": false,
          "info": "If enabled & Width is set to 100%, the image will be edge-to-edge on mobile."
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Image alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 0
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "image_picker",
          "id": "thumbnail",
          "label": "Video Thumbnail",
          "info": "If empty, the first frame of the video will be displayed"
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Video looping",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "muted_autoplay",
          "label": "Muted autoplay",
          "default": true,
          "info": "Use this instead of GIFs & animated WEBPs."
        },
        {
          "type": "checkbox",
          "id": "display_play_btn",
          "label": "Enable play & pause on click",
          "info": "Automatically enabled if autoplay is disabled.",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "btn_animation",
          "label": "Enable button ripple animation",
          "default": false
        },
        {
          "type": "select",
          "id": "btn_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "accent-1",
          "label": "Play button color scheme"
        },
        {
          "type": "checkbox",
          "id": "display_sound_btn",
          "label": "Display mute/unmute button",
          "default": false
        },
        {
          "type": "select",
          "id": "sound_btn_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "inverse",
          "label": "Sound button color scheme"
        },
        {
          "type": "checkbox",
          "id": "display_timeline",
          "label": "Display timeline",
          "default": false
        },
        {
          "type": "select",
          "id": "timeline_color",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "accent-1",
          "label": "Timeline color"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Video width",
          "default": 100
        },
        {
          "type": "checkbox",
          "id": "Full_mobile_width",
          "label": "Full container width on mobile",
          "default": false,
          "info": "If enabled & Width is set to 100%, the video will be edge-to-edge on mobile."
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Video alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 0
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "emoji_benefits",
      "name": "Emoji benefits",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "benefits",
          "label": "Benefits",
          "default": "<p>🙌 Benefit</p><p>💪 Benefit</p><p>🌟 Benefit</p>"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "payment_badges",
      "name": "Payment badges",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "enabled_payment_types",
          "label": "Custom payment icons to show",
          "info": "List of payments you want to show, split with a comma. Options are: afterpay, american_express, apple_pay, bitcoin, dankort, diners_club, discover, dogecoin, dwolla, facebook_pay, forbrugsforeningen, google_pay, ideal, jcb, klarna, klarna-pay-later, litecoin, maestro, master, paypal, shopify_pay, sofort, unionpay, visa"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "estimated_shipping",
      "name": "Estimated shipping",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "icon",
          "default": "local_shipping",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon"
        },
        {
          "type": "select",
          "id": "icon_size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium",
          "label": "Icon size"
        },
        {
          "type": "select",
          "id": "icon_alignment",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "middle",
              "label": "Middle"
            }
          ],
          "default": "top",
          "label": "Icon alignment"
        },
        {
          "type": "richtext",
          "id": "message",
          "label": "Text",
          "default": "<p>Get it between <strong>[start_date]</strong> and <strong>[end_date]</strong>.</p>",
          "info": "Use [start_date] to display the earliest date the package can arrive and [end_date] to display the latest date the package can arrive."
        },
        {
          "type": "number",
          "id": "min_shipping_days",
          "label": "Minimum shipping days",
          "default": 7
        },
        {
          "type": "number",
          "id": "max_shipping_days",
          "label": "Maximum shipping days",
          "default": 15
        },
        {
          "type": "select",
          "id": "date_format",
          "label": "Date format",
          "options": [
            {
              "value": "day_mm_dd",
              "label": "Monday, February 1st"
            },
            {
              "value": "day_dd_mm",
              "label": "Monday, 1. February"
            },
            {
              "value": "mm_dd",
              "label": "February 1st"
            },
            {
              "value": "dd_mm",
              "label": "1. February"
            },
            {
              "value": "dd_mm_no_dot",
              "label": "1 February"
            },
            {
              "value": "day_dd_mm_numeric",
              "label": "Monday, 01. 02."
            },
            {
              "value": "dd_mm_numeric",
              "label": "01. 02."
            }
          ],
          "default": "day_mm_dd"
        },
        {
          "type": "text",
          "id": "days_labels",
          "label": "Day labels",
          "default": "Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday"
        },
        {
          "type": "text",
          "id": "months_labels",
          "label": "Month labels",
          "default": "January, February, March, April, May, June, July, August, September, October, November, December"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "shipping_checkpoints",
      "name": "Shipping checkpoints",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "How to use"
        },
        {
          "type": "paragraph",
          "content": "Use [start_date} and {end_date} to automatically display the dates calculated based on the required days needed you enter."
        },
        {
          "type": "header",
          "content": "Checkpoint #1"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "add_shopping_cart",
          "label": "#1 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "#1 Filled icon"
        },
        {
          "type": "image_picker",
          "id": "icon_1_image",
          "label": "#1 Custom icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_1",
          "label": "#1 Top Text",
          "default": "<strong>[start_date]</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_1",
          "label": "#1 Bottom Text",
          "default": "Ordered"
        },
        {
          "type": "number",
          "id": "min_days_1",
          "label": "#1 Minimum required days",
          "default": 0
        },
        {
          "type": "number",
          "id": "max_days_1",
          "label": "#1 Maximum required days",
          "default": 0
        },
        {
          "type": "header",
          "content": "Checkpoint #2"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "local_shipping",
          "label": "#2 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "#2 Filled icon"
        },
        {
          "type": "image_picker",
          "id": "icon_2_image",
          "label": "#2 Custom icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_2",
          "label": "#2 Top Text",
          "default": "<strong>[start_date] - [end_date]</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_2",
          "label": "#2 Bottom Text",
          "default": "Order Ready"
        },
        {
          "type": "number",
          "id": "min_days_2",
          "label": "#2 Minimum required days",
          "default": 1
        },
        {
          "type": "number",
          "id": "max_days_2",
          "label": "#2 Maximum required days",
          "default": 2
        },
        {
          "type": "header",
          "content": "Checkpoint #3"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "redeem",
          "label": "#3 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_3",
          "default": false,
          "label": "#3 Filled icon"
        },
        {
          "type": "image_picker",
          "id": "icon_3_image",
          "label": "#3 Custom icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_3",
          "label": "#3 Top Text",
          "default": "<strong>[start_date] - [end_date]</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_3",
          "label": "#3 Bottom Text",
          "default": "Delivered"
        },
        {
          "type": "number",
          "id": "min_days_3",
          "label": "#3 Minimum required days",
          "default": 10
        },
        {
          "type": "number",
          "id": "max_days_3",
          "label": "#3 Maximum required days",
          "default": 12
        },
        {
          "type": "header",
          "content": "Checkpoint #4"
        },
        {
          "type": "text",
          "id": "icon_4",
          "label": "#4 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_4",
          "default": false,
          "label": "#4 Filled icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_4",
          "label": "#4 Top Text"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_4",
          "label": "#4 Bottom Text"
        },
        {
          "type": "number",
          "id": "min_days_4",
          "label": "#4 Minimum required days"
        },
        {
          "type": "number",
          "id": "max_days_4",
          "label": "#4 Maximum required days"
        },
        {
          "type": "header",
          "content": "Date formating"
        },
        {
          "type": "select",
          "id": "date_format",
          "label": "Date format",
          "options": [
            {
              "value": "day_mm_dd",
              "label": "Monday, February 1st"
            },
            {
              "value": "day_dd_mm",
              "label": "Monday, 1. February"
            },
            {
              "value": "mm_dd",
              "label": "February 1st"
            },
            {
              "value": "dd_mm",
              "label": "1. February"
            },
            {
              "value": "dd_mm_no_dot",
              "label": "1 February"
            },
            {
              "value": "day_dd_mm_numeric",
              "label": "Monday, 01. 02."
            },
            {
              "value": "dd_mm_numeric",
              "label": "01. 02."
            }
          ],
          "default": "mm_dd"
        },
        {
          "type": "text",
          "id": "days_labels",
          "label": "Day labels",
          "default": "Mon, Tue, Wed, Thu, Fri, Sat, Sun"
        },
        {
          "type": "text",
          "id": "months_labels",
          "label": "Month labels",
          "default": "Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec"
        },
        {
          "type": "header",
          "content": "Color"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "inverse",
          "label": "Bar color scheme"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "custom_product_field",
      "name": "Custom product field",
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "type",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "number",
              "label": "Number"
            },
            {
              "value": "textarea",
              "label": "Textarea"
            },
            {
              "value": "pills",
              "label": "Pills"
            },
            {
              "value": "select",
              "label": "Dropdown"
            }
          ],
          "default": "text",
          "label": "Field type"
        },
        {
          "type": "text",
          "id": "field_name",
          "label": "Field name",
          "default": "Name",
          "info": "Custom property name that's sent to order information."
        },
        {
          "type": "text",
          "id": "field_label",
          "label": "Field label",
          "default": "Your name"
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "Full width input & dropdown",
          "default": false
        },
        {
          "type": "header",
          "content": "Validation"
        },
        {
          "type": "checkbox",
          "id": "required",
          "default": false,
          "label": "Required field",
          "info": "Disables all Add to Cart buttons unless all required fields are filled out & displays an error message."
        },
        {
          "type": "text",
          "id": "atc_error_message",
          "default": "Please enter your name",
          "label": "ATC button error message",
          "info": "Displayed inside the Add to Cart button if a required field is empty. If multiple required fields exist, the message from the last one will be applied."
        },
        {
          "type": "text",
          "id": "field_error_message",
          "default": "This field is required!",
          "label": "Field error message",
          "info": "Displayed if a required field value is changed to empty."
        },
        {
          "type": "header",
          "content": "Text input"
        },
        {
          "type": "text",
          "id": "text_placeholder",
          "label": "Placeholder",
          "default": "Your name"
        },
        {
          "type": "number",
          "id": "text_max_characters",
          "label": "Maximum characters"
        },
        {
          "type": "header",
          "content": "Number input"
        },
        {
          "type": "text",
          "id": "number_placeholder",
          "label": "Placeholder"
        },
        {
          "type": "number",
          "id": "number_min",
          "label": "Minimum value"
        },
        {
          "type": "number",
          "id": "number_max",
          "label": "Maximum value"
        },
        {
          "type": "header",
          "content": "Textarea input"
        },
        {
          "type": "text",
          "id": "textarea_placeholder",
          "label": "Placeholder"
        },
        {
          "type": "select",
          "id": "textarea_height",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium",
          "label": "Height"
        },
        {
          "type": "header",
          "content": "Pills & Dropdown input"
        },
        {
          "type": "text",
          "id": "select_options",
          "label": "Options separated by comma",
          "default": "Option 1,Option 2,Option 3"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.featured-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.rating.settings.paragraph.content"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "button",
      "name": "Link button",
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "Button label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "default": false,
          "label": "Use outline button style"
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "default": false,
          "label": "Full buttons width"
        },
        {
          "type": "checkbox",
          "id": "large_font_size",
          "default": false,
          "label": "Use larger font size"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start",
          "label": "Desktop alignment"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Mobile alignment"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 21
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 21
        }
      ]
    },
    {
      "type": "icon_with_text",
      "name": "Icons with text",
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "default": "horizontal",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.label"
        },
        {
          "type": "select",
          "id": "icon_color",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "outline-button",
              "label": "t:settings_schema.styles.settings.accent_icons.options__3.label"
            },
            {
              "value": "text",
              "label": "t:settings_schema.styles.settings.accent_icons.options__4.label"
            }
          ],
          "default": "accent-1",
          "label": "Icon color"
        },
        {
          "type": "header",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "desktop_icon_size",
          "min": 16,
          "max": 72,
          "step": 4,
          "unit": "px",
          "default": 48,
          "label": "Icon size"
        },
        {
          "type": "range",
          "id": "desktop_spacing",
          "min": 2,
          "max": 24,
          "step": 2,
          "unit": "px",
          "default": 12,
          "label": "Icon & text spacing"
        },
        {
          "type": "range",
          "id": "desktop_text_size",
          "min": 12,
          "max": 34,
          "step": 2,
          "unit": "px",
          "default": 18,
          "label": "Text size"
        },
        {
          "type": "header",
          "content": "Mobile"
        },
        {
          "type": "range",
          "id": "mobile_icon_size",
          "min": 12,
          "max": 60,
          "step": 4,
          "unit": "px",
          "default": 40,
          "label": "Icon size"
        },
        {
          "type": "range",
          "id": "mobile_spacing",
          "min": 2,
          "max": 24,
          "step": 2,
          "unit": "px",
          "default": 10,
          "label": "Icon & text spacing"
        },
        {
          "type": "range",
          "id": "mobile_text_size",
          "min": 8,
          "max": 26,
          "step": 2,
          "unit": "px",
          "default": 14,
          "label": "Text size"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.content.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.content.info"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "favorite",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_1.label",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "icon_1_fill",
          "default": false,
          "label": "First icon fill"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_1.label"
        },
        {
          "type": "text",
          "id": "heading_1",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "undo",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_2.label",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "icon_2_fill",
          "default": false,
          "label": "Second icon fill"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_2.label"
        },
        {
          "type": "text",
          "id": "heading_2",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "local_shipping",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_3.label",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "icon_3_fill",
          "default": false,
          "label": "Third icon fill"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_3.label"
        },
        {
          "type": "text",
          "id": "heading_3",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "product",
      "id": "product",
      "label": "t:sections.featured-product.settings.product.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "checkbox",
      "id": "secondary_background",
      "default": false,
      "label": "t:sections.featured-product.settings.secondary_background.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-product.settings.header.content",
      "info": "t:sections.featured-product.settings.header.info"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.main-product.settings.media_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.media_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-product.settings.media_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.main-product.settings.media_size.label",
      "info": "t:sections.main-product.settings.media_size.info"
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "t:sections.main-product.settings.constrain_to_viewport.label"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.media_fit.options__1.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.media_fit.options__2.label"
        }
      ],
      "default": "contain",
      "label": "t:sections.main-product.settings.media_fit.label"
    },
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.featured-product.settings.media_position.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.featured-product.settings.media_position.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.featured-product.settings.media_position.label",
      "info": "t:sections.featured-product.settings.media_position.info"
    },
    {
      "type": "select",
      "id": "image_zoom",
      "options": [
        {
          "value": "lightbox",
          "label": "t:sections.main-product.settings.image_zoom.options__1.label"
        },
        {
          "value": "none",
          "label": "t:sections.main-product.settings.image_zoom.options__3.label"
        }
      ],
      "default": "lightbox",
      "label": "t:sections.main-product.settings.image_zoom.label"
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "t:sections.main-product.settings.hide_variants.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.featured-product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "Mobile media"
    },
    {
      "type": "range",
      "id": "mobile_media_corner_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "t:settings_schema.global.settings.corner_radius.label",
      "default": 12
    },
    {
      "type": "checkbox",
      "id": "full_media_width",
      "default": false,
      "label": "Use full page width"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-product.presets.name",
      "blocks": [
        {
          "type": "title"
        },
        {
          "type": "price"
        },
        {
          "type": "variant_picker"
        },
        {
          "type": "quantity_selector"
        },
        {
          "type": "buy_buttons"
        }
      ]
    }
  ]
}
{% endschema %}
