

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557276193259389784.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-557276193259389784.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557276193259389784.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557276193259389784.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557276193259389784.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557276193259389784.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557276193259389784.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557276193259389784.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557276193259389784.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557276193259389784.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-557276193259389784.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557276193259389784.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-557276193259389784.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-557276193259389784.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-557276193259389784.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557276193259389784.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-557276193259389784.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-557276193259389784.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-557276193259389784.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557276193259389784.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557276193259389784.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557276193259389784.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557276193259389784.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-557276193259389784.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557276193259389784.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557276193259389784.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-557276193259389784.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557276193259389784.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-557276193259389784.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557276193259389784.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-557276193259389784.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-557276193259389784.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557276193259389784.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557276193259389784.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-557276193259389784.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-557276193259389784.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557276193259389784.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557276193259389784.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557276193259389784.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557276193259389784.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557276193259389784.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557276193259389784.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557276193259389784.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557276193259389784.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557276193259389784.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-557276193259389784.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557276193259389784.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-557276193259389784.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-557276193259389784.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-557276193259389784.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557276193259389784.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-557276193259389784.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-557276193259389784.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-557276193259389784.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557276193259389784.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557276193259389784.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-557276193259389784.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-557276193259389784.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-557276193259389784.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-557276193259389784.gps.gpsil [style*="--bga-mobile:"]{background-attachment:var(--bga-mobile)}.gps-557276193259389784.gps.gpsil [style*="--bgc-mobile:"]{background-color:var(--bgc-mobile)}.gps-557276193259389784.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-557276193259389784.gps.gpsil [style*="--bgp-mobile:"]{background-position:var(--bgp-mobile)}.gps-557276193259389784.gps.gpsil [style*="--bgr-mobile:"]{background-repeat:var(--bgr-mobile)}.gps-557276193259389784.gps.gpsil [style*="--bgs-mobile:"]{background-size:var(--bgs-mobile)}.gps-557276193259389784.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-557276193259389784.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557276193259389784.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557276193259389784.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-557276193259389784.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-557276193259389784.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-557276193259389784.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-557276193259389784.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-557276193259389784.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557276193259389784.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557276193259389784.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557276193259389784.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557276193259389784.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-557276193259389784.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-557276193259389784.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-557276193259389784 .gp-relative{position:relative}.gps-557276193259389784 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557276193259389784 .gp-mb-0{margin-bottom:0}.gps-557276193259389784 .gp-flex{display:flex}.gps-557276193259389784 .gp-grid{display:grid}.gps-557276193259389784 .gp-contents{display:contents}.gps-557276193259389784 .\!gp-hidden{display:none!important}.gps-557276193259389784 .gp-hidden{display:none}.gps-557276193259389784 .gp-h-auto{height:auto}.gps-557276193259389784 .gp-h-full{height:100%}.gps-557276193259389784 .gp-w-full{width:100%}.gps-557276193259389784 .gp-max-w-full{max-width:100%}.gps-557276193259389784 .gp-flex-none{flex:none}.gps-557276193259389784 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557276193259389784 .gp-flex-col{flex-direction:column}.gps-557276193259389784 .gp-gap-y-0{row-gap:0}.gps-557276193259389784 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259389784 .gp-duration-200{transition-duration:.2s}.gps-557276193259389784 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-557276193259389784 .tablet\:\!gp-hidden{display:none!important}.gps-557276193259389784 .tablet\:gp-hidden{display:none}.gps-557276193259389784 .tablet\:gp-h-auto{height:auto}.gps-557276193259389784 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-557276193259389784 .mobile\:\!gp-hidden{display:none!important}.gps-557276193259389784 .mobile\:gp-hidden{display:none}.gps-557276193259389784 .mobile\:gp-h-auto{height:auto}.gps-557276193259389784 .mobile\:gp-flex-none{flex:none}}.gps-557276193259389784 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557276193259389784 .\[\&_p\]\:gp-inline p{display:inline}.gps-557276193259389784 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557276193259389784 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gns7Ie7axo" data-id="gns7Ie7axo"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:15px;--pb:auto;--pr:15px;--mt-mobile:-16px;--pt-mobile:14px;--pl-mobile:15px;--pb-mobile:32px;--pr-mobile:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:linear-gradient(1deg, #439D8F 0%, #FFFFFF 71%);--bgc-mobile:linear-gradient(173deg, #439D8F 0%, #FFFFFF 71%);--bgi:linear-gradient(1deg, #439D8F 0%, #FFFFFF 71%);--bgi-mobile:linear-gradient(173deg, #439D8F 0%, #FFFFFF 71%);--bgp-mobile:;--bgs-mobile:;--bgr-mobile:;--bga-mobile:"
        class="gns7Ie7axo gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gGd2ZwCmiU gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g_Ons18bn4" data-id="g_Ons18bn4"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:-70px;--pb:26px;--mb-mobile:0px;--pb-mobile:26px;--cg:30px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:1200%;--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g_Ons18bn4 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start;--o:1;--o-mobile:1"
      class="gpxol17XTB gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIuAGtgMGY">
    <div
      parentTag="Col"
        class="gIuAGtgMGY "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%;--mb:48px;--pt:48px;--mb-mobile:42px;--pt-mobile:0px;--mb-tablet:48px;--pt-tablet:0px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ff:var(--g-font-Poppins, 'Poppins'), var(--g-font-heading, heading);--size:46px;--size-tablet:46px;--size-mobile:41px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggIuAGtgMGY_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="ghh5YcrKpT" data-id="ghh5YcrKpT"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:24px;--cg:16px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="ghh5YcrKpT gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gJo9kqtKr3 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="grJpG5qNcV">
    <div
      parentTag="Col"
        class="grJpG5qNcV "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#FFFFFF;word-break:break-word;--bgc:#439D8F;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pl:24px;--pr:24px;--pt:4px;--pb:4px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:4px;--pb-mobile:4px;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;overflow:hidden"
        >{{ section.settings.ggrJpG5qNcV_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-grJpG5qNcV">
        .grJpG5qNcV {

}
.grJpG5qNcV p {

}
      </style>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gHEcNi_9gS gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjMWlqjlqn">
    <div
      parentTag="Col"
        class="gjMWlqjlqn "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggjMWlqjlqn_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gjMWlqjlqn">
        .gjMWlqjlqn {

}
.gjMWlqjlqn p {

}
      </style>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gsYUAnQPbR" data-id="gsYUAnQPbR"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:24px;--cg:16px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gsYUAnQPbR gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gsrcDradJU gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gfKogPvOTu">
    <div
      parentTag="Col"
        class="gfKogPvOTu "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#FFFFFF;word-break:break-word;--bgc:#439d8f;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pl:24px;--pr:24px;--pt:4px;--pb:4px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:4px;--pb-mobile:4px;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;overflow:hidden"
        >{{ section.settings.ggfKogPvOTu_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gfKogPvOTu">
        .gfKogPvOTu {

}
.gfKogPvOTu p {

}
      </style>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gsJXQvHrpr gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggFOIIx2ux">
    <div
      parentTag="Col"
        class="ggFOIIx2ux "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gggFOIIx2ux_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-ggFOIIx2ux">
        .ggFOIIx2ux {

}
.ggFOIIx2ux p {

}
      </style>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gzZEH7JtO6" data-id="gzZEH7JtO6"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:24px;--cg:16px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gzZEH7JtO6 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gU0KnlkQM5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gsosAGH94T">
    <div
      parentTag="Col"
        class="gsosAGH94T "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#FFFFFF;word-break:break-word;--bgc:#439d8f;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pl:24px;--pr:24px;--pt:4px;--pb:4px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:4px;--pb-mobile:4px;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;overflow:hidden"
        >{{ section.settings.ggsosAGH94T_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gsosAGH94T">
        .gsosAGH94T {

}
.gsosAGH94T p {

}
      </style>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gBkcn1wIyp gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="guCEgJJ5i-">
    <div
      parentTag="Col"
        class="guCEgJJ5i- "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gguCEgJJ5i-_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-guCEgJJ5i-">
        .guCEgJJ5i- {

}
.guCEgJJ5i- p {

}
      </style>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gjhw43FAMt" data-id="gjhw43FAMt"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:24px;--cg:16px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gjhw43FAMt gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gqp3SnuriX gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gGIi0Sw2WY">
    <div
      parentTag="Col"
        class="gGIi0Sw2WY "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#FFFFFF;word-break:break-word;--bgc:#439d8f;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pl:24px;--pr:24px;--pt:4px;--pb:4px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:4px;--pb-mobile:4px;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;overflow:hidden"
        >{{ section.settings.ggGIi0Sw2WY_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gGIi0Sw2WY">
        .gGIi0Sw2WY {

}
.gGIi0Sw2WY p {

}
      </style>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gUpBeD7cOz gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gzLqecWDNO">
    <div
      parentTag="Col"
        class="gzLqecWDNO "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggzLqecWDNO_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gzLqecWDNO">
        .gzLqecWDNO {

}
.gzLqecWDNO p {

}
      </style>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gLZgtx8grr" data-id="gLZgtx8grr"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:24px;--cg:16px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gLZgtx8grr gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gYuvQ3D8mr gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gJjcqhoSJ9">
    <div
      parentTag="Col"
        class="gJjcqhoSJ9 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#FFFFFF;word-break:break-word;--bgc:#439d8f;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pl:24px;--pr:24px;--pt:4px;--pb:4px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:4px;--pb-mobile:4px;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;overflow:hidden"
        >{{ section.settings.ggJjcqhoSJ9_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gJjcqhoSJ9">
        .gJjcqhoSJ9 {

}
.gJjcqhoSJ9 p {

}
      </style>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gmh_HBwb-6 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gxWLcZW6xu">
    <div
      parentTag="Col"
        class="gxWLcZW6xu "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggxWLcZW6xu_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gxWLcZW6xu">
        .gxWLcZW6xu {

}
.gxWLcZW6xu p {

}
      </style>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gNkYX6IGlh" data-id="gNkYX6IGlh"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--cg:16px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gNkYX6IGlh gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gjlWLkiUlR gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gYHJwBfp3l">
    <div
      parentTag="Col"
        class="gYHJwBfp3l "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#FFFFFF;word-break:break-word;--bgc:#439d8f;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pl:24px;--pr:24px;--pt:4px;--pb:4px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:4px;--pb-mobile:4px;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;overflow:hidden"
        >{{ section.settings.ggYHJwBfp3l_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gYHJwBfp3l">
        .gYHJwBfp3l {

}
.gYHJwBfp3l p {

}
      </style>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gRa1AXgAdo gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gsj03HtT_4">
    <div
      parentTag="Col"
        class="gsj03HtT_4 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggsj03HtT_4_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gsj03HtT_4">
        .gsj03HtT_4 {

}
.gsj03HtT_4 p {

}
      </style>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--o:0;--o-mobile:0"
      class="g_o1SdIeWO gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gGQ-jEA0gN">
    <div
      parentTag="Col"
        class="gGQ-jEA0gN "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:none;--d-mobile:block;--d-tablet:none;--op:100%;--mb:48px;--pt:48px;--mb-mobile:0px;--pt-mobile:0px;--mb-tablet:48px;--pt-tablet:0px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:center;--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:33px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggGQ-jEA0gN_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    <div
    
     data-id="grtWcAhrLt"
    role="presentation"
    class="gp-group/image grtWcAhrLt gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:42px;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_523685320072364842-4a7729e3-a521-48b7-a768-2dab5cd8b6c2.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_523685320072364842-4a7729e3-a521-48b7-a768-2dab5cd8b6c2.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_523685320072364842-4a7729e3-a521-48b7-a768-2dab5cd8b6c2.png" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
      <style id="custom-css-grtWcAhrLt">
        .grtWcAhrLt {

}
.grtWcAhrLt > figure > img {

}
      </style>
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfedA.woff) format('woff');
}
/* devanagari */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJbecnFHGPezSQ.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJnecnFHGPezSQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 7",
    "tag": "section",
    "class": "gps-557276193259389784 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=557276193259389784)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggIuAGtgMGY_text","label":"ggIuAGtgMGY_text","default":"Functions &amp; Features"},{"type":"html","id":"ggrJpG5qNcV_text","label":"ggrJpG5qNcV_text","default":"<p>1</p>"},{"type":"html","id":"ggjMWlqjlqn_text","label":"ggjMWlqjlqn_text","default":"<p>Screen with Timer&nbsp;</p>"},{"type":"html","id":"ggfKogPvOTu_text","label":"ggfKogPvOTu_text","default":"<p>2</p>"},{"type":"html","id":"gggFOIIx2ux_text","label":"gggFOIIx2ux_text","default":"<p>On/Off Button</p>"},{"type":"html","id":"ggsosAGH94T_text","label":"ggsosAGH94T_text","default":"<p>3</p>"},{"type":"html","id":"gguCEgJJ5i-_text","label":"gguCEgJJ5i-_text","default":"<p>Increase Vacuum Strength</p>"},{"type":"html","id":"ggGIi0Sw2WY_text","label":"ggGIi0Sw2WY_text","default":"<p>4</p>"},{"type":"html","id":"ggzLqecWDNO_text","label":"ggzLqecWDNO_text","default":"<p>Decrease Vacuum Strength &nbsp;</p>"},{"type":"html","id":"ggJjcqhoSJ9_text","label":"ggJjcqhoSJ9_text","default":"<p>5</p>"},{"type":"html","id":"ggxWLcZW6xu_text","label":"ggxWLcZW6xu_text","default":"<p>Suction Release&nbsp;</p>"},{"type":"html","id":"ggYHJwBfp3l_text","label":"ggYHJwBfp3l_text","default":"<p>6</p>"},{"type":"html","id":"ggsj03HtT_4_text","label":"ggsj03HtT_4_text","default":"<p>Thermal Heat Setting</p>"},{"type":"html","id":"ggGQ-jEA0gN_text","label":"ggGQ-jEA0gN_text","default":"Functions &amp; Features"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
