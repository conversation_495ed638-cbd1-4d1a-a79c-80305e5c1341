{% case settings.cart_icon %}
  {% when 'cart_1' %}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 53.98 45.23"
      class="icon icon-cart"
      aria-hidden="true"
      focusable="false"
      fill="none"
    >
      <polyline stroke="currentColor" points="1.5 1.5 10.04 2.06 15.09 27.94 45.39 27.94 52.48 6.79 18.25 6.65" style="fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px"/>
      <circle stroke="currentColor" cx="20.58" cy="39.74" r="3.99" style="fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px"/>
      <circle stroke="currentColor" cx="40.02" cy="39.74" r="3.99" style="fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px"/>
      <polyline stroke="currentColor" points="45.42 35.75 14.49 35.75 17.21 27.94" style="fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px"/>
    </svg>
  {% when 'cart_2' %}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 53.98 45.23" class="icon icon-cart" aria-hidden="true" focusable="false">
      <polyline style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" points="1.5 1.5 10.04 2.06 15.09 27.94 45.39 27.94 52.48 6.79 10.97 6.84"/>
      <circle style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" cx="20.58" cy="39.74" r="3.99"/>
      <circle style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" cx="40.02" cy="39.74" r="3.99"/>
      <polyline style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" points="45.42 35.75 14.49 35.75 17.21 27.94"/>
      <line style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" x1="13.09" y1="17.69" x2="48.83" y2="17.69"/>
      <line style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" x1="29.95" y1="6.82" x2="29.95" y2="27.94"/>
      <line style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" x1="40.91" y1="6.82" x2="37.57" y2="27.94"/>
      <line style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:3px;" x1="19.63" y1="6.82" x2="22.1" y2="27.94"/>
    </svg>
  {% when 'basket_1' %}
    <svg class="icon icon-cart" aria-hidden="true" focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 53.4 41.05" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" style='transform:translateY(-2px)'>
      <line x1="1.5" y1="14.55" x2="51.9" y2="14.55"/>
      <line x1="20.61" y1="1.5" x2="12.52" y2="14.55"/>
      <line x1="33.01" y1="1.5" x2="41.06" y2="14.55"/>
      <path d="M4.28,14.69l8.67,25H40q5-12.49,10-25"/>
    </svg>
  {% when 'basket_2' %}
    <svg class="icon icon-cart" aria-hidden="true" focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 53.4 43.82" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" style='transform:translateY(-1px)'>
      <line x1="1.5" y1="14.55" x2="51.9" y2="14.55"/>
      <line x1="19.46" y1="1.5" x2="11.36" y2="14.55"/>
      <line x1="34.13" y1="1.5" x2="42.18" y2="14.55"/>
      <line x1="26.7" y1="22.9" x2="26.7" y2="34.13"/>
      <line x1="36.73" y1="22.9" x2="36.73" y2="34.13"/>
      <line x1="16.71" y1="22.9" x2="16.71" y2="34.13"/>
      <path d="M4.28,14.69,8.53,37.91a5.41,5.41,0,0,0,5.28,4.43L40,42.46a5.45,5.45,0,0,0,5.37-4.39L50,14.69"/>
    </svg>
  {% when 'bag_1' %}
    <svg class="icon icon-cart" aria-hidden="true" focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36.52 45.01" style='transform: scale(0.9);'>
      <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M12.88,10.4H41.23l2.5,27.78a5,5,0,0,1-5,5.47H15.25a5,5,0,0,1-5-5.49Z" transform="translate(-8.73 -0.14)"/>
      <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M19.62,15.51V9.1a7.46,7.46,0,0,1,14.92,0v6.41" transform="translate(-8.73 -0.14)"/>
    </svg>
  {% when 'bag_2' %}
    <svg class="icon icon-cart" aria-hidden="true" focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 35.53 44.83" style='transform: scale(0.9);'>
      <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M12.36,10.93l29.38-.08q.72,16.41,1.45,32.8H10.66Z" transform="translate(-9.16 -0.32)"/>
      <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M19.39,10.85V9.52a7.7,7.7,0,1,1,15.39,0v1.33" transform="translate(-9.16 -0.32)"/>
      <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M19.12,15.46a7.7,7.7,0,0,0,15.08,0" transform="translate(-9.16 -0.32)"/>
    </svg>
{% endcase %}