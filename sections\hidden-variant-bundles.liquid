{{ 'section-hidden-variant-bundles.css' | asset_url | stylesheet_tag }}
<script src="{{ 'hidden-variant-bundles.js' | asset_url }}" defer="defer"></script>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --accent-color: {{ section.settings.custom_colors_accent.red }}, {{ section.settings.custom_colors_accent.green }}, {{ section.settings.custom_colors_accent.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
  }
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  
  <div class="page-width section-{{ section.id }}-padding">
    {% assign content_index = 0 %}
    
    {%- unless section.settings.title == blank -%}
      {% assign content_index = 1 %}
      <div class="title-wrapper--no-top-margin animate-item animate-item--child index-0">
        <h2 class="title {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
          {{ section.settings.title }}
        </h2>
        {% if section.settings.description != blank %}
          <div class="text-under-title">
            {{ section.settings.description }}
          </div>
        {% endif %}
      </div>
    {%- endunless -%}

    <hidden-variant-bundles
      id="hidden-variant-bundles-{{ section.id }}"
      class="hidden-variant-bundles"
      data-section="{{ section.id }}"
    >
      {%- if section.blocks.size > 0 -%}
        <!-- Bundle Tabs Navigation -->
        <div class="bundle-tabs animate-item animate-item--child index-{{ content_index }}">
          {%- for block in section.blocks -%}
            {%- if block.settings.collection != blank -%}
              <button 
                class="bundle-tab{% if forloop.first %} bundle-tab--active{% endif %}"
                data-tab="{{ forloop.index0 }}"
                data-collection="{{ block.settings.collection.handle }}"
                {{ block.shopify_attributes }}
              >
                {{ block.settings.tab_title | default: block.settings.collection.title }}
              </button>
            {%- endif -%}
          {%- endfor -%}
        </div>

        <!-- Bundle Content Areas -->
        <div class="bundle-content animate-item animate-item--child index-{{ content_index | plus: 1 }}">
          {%- for block in section.blocks -%}
            {%- if block.settings.collection != blank -%}
              <div 
                class="bundle-tab-content{% if forloop.first %} bundle-tab-content--active{% endif %}"
                data-tab-content="{{ forloop.index0 }}"
                data-collection="{{ block.settings.collection.handle }}"
              >
                <div class="bundle-products">
                  {%- for product in block.settings.collection.products limit: 10 -%}
                    <div class="bundle-product" data-product-handle="{{ product.handle }}">
                      <div class="bundle-product__image">
                        {%- if product.featured_image -%}
                          <img 
                            src="{{ product.featured_image | image_url: width: 300 }}"
                            alt="{{ product.featured_image.alt | escape }}"
                            loading="lazy"
                            width="150"
                            height="150"
                          >
                        {%- else -%}
                          {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                        {%- endif -%}
                      </div>
                      <div class="bundle-product__info">
                        <h3 class="bundle-product__title">{{ product.title }}</h3>
                        <div class="bundle-product__price">
                          {%- comment -%}
                            Display the Regular variant price (this will be replaced by Bundle variant price via JavaScript)
                          {%- endcomment -%}
                          <span class="bundle-product__price-regular">
                            {{ product.selected_or_first_available_variant.price | money }}
                          </span>
                          <span class="bundle-product__price-bundle" style="display: none;"></span>
                        </div>
                      </div>
                    </div>
                  {%- endfor -%}
                </div>

                <div class="bundle-summary">
                  <div class="bundle-summary__total">
                    <span class="bundle-summary__label">{{ section.settings.total_label | default: 'Bundle Total:' }}</span>
                    <span class="bundle-summary__price">£0.00</span>
                  </div>
                  
                  {%- liquid
                    # Build dynamic product handles list from collection
                    assign product_handles = ''
                    for product in block.settings.collection.products limit: 10
                      if product_handles != blank
                        assign product_handles = product_handles | append: ','
                      endif
                      assign product_handles = product_handles | append: product.handle
                    endfor

                    # Create unique bundle ID
                    assign bundle_id = 'bundle-' | append: forloop.index0 | append: '-' | append: section.id
                    assign bundle_name = block.settings.tab_title | default: block.settings.collection.title
                  -%}

                  <button
                    type="button"
                    class="bundle-add-to-cart button button--primary button--full-width"
                    data-bundle-products="{{ product_handles }}"
                    data-bundle-id="{{ bundle_id }}"
                    data-bundle-name="{{ bundle_name }}"
                    data-collection-handle="{{ block.settings.collection.handle }}"
                    disabled
                  >
                    <span class="bundle-add-to-cart__text">
                      {{ section.settings.button_text | default: 'Add Bundle to Cart' }}
                    </span>
                    <div class="loading-overlay__spinner hidden">
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        class="spinner"
                        viewBox="0 0 66 66"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                      </svg>
                    </div>
                  </button>
                </div>
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
      {%- else -%}
        <div class="bundle-empty animate-item animate-item--child index-{{ content_index }}">
          <p>{{ 'sections.hidden_variant_bundles.empty' | t | default: 'Add collection blocks to create bundle options.' }}</p>
        </div>
      {%- endif -%}
    </hidden-variant-bundles>
  </div>
</div>

{% schema %}
{
  "name": "Hidden Variant Bundles",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Bundle Deals",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "text",
      "id": "total_label",
      "label": "Total price label",
      "default": "Bundle Total:"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Add to cart button text",
      "default": "Add Bundle to Cart"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_accent",
      "default": "#dd1d1d",
      "label": "Accents"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    }
  ],
  "blocks": [
    {
      "type": "bundle_tab",
      "name": "Bundle Tab",
      "limit": 3,
      "settings": [
        {
          "type": "text",
          "id": "tab_title",
          "label": "Tab title",
          "info": "Leave blank to use collection title"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection",
          "info": "Select a collection containing products for this bundle. Products should have 'Purchase Type' variants with 'Regular' and 'Bundle' options."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Hidden Variant Bundles",
      "blocks": [
        {
          "type": "bundle_tab"
        }
      ]
    }
  ]
}
{% endschema %}
