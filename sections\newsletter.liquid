{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="newsletter center {% if section.settings.full_width == false %}newsletter--narrow page-width{% endif %} content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="newsletter__wrapper color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-container isolate{% if section.settings.full_width %} content-container--full-width{% endif %} section-{{ section.id }}-padding">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when '@app' -%}
          {% render block %}
        {%- when 'heading' -%}
          <h2 class="{{ block.settings.heading_size }} title-with-highlight animate-item animate-item--child" {{ block.shopify_attributes }} style="--hightlight-color:{{ block.settings.title_highlight_color }};--index:{{ forloop.index }};">
            {{ block.settings.title }}
          </h2>
        {%- when 'paragraph' -%}
          <div class="newsletter__subheading rte animate-item animate-item--child" {{ block.shopify_attributes }} style="--index:{{ forloop.index }};">{{ block.settings.text }}</div>
        {%- when 'email_form' -%}
          <div class='animate-item animate-item--child' {{ block.shopify_attributes }} style="--index:{{ forloop.index }};">
            {% form 'customer', class: 'newsletter-form' %}
              <input type="hidden" name="contact[tags]" value="newsletter">
              <div class="newsletter-form__field-wrapper">
                <div class="field">
                  <input
                    id="NewsletterForm--{{ section.id }}"
                    type="email"
                    name="contact[email]"
                    class="field__input"
                    value="{{ form.email }}"
                    aria-required="true"
                    autocorrect="off"
                    autocapitalize="off"
                    autocomplete="email"
                    {% if form.errors %}
                      autofocus
                      aria-invalid="true"
                      aria-describedby="Newsletter-error--{{ section.id }}"
                    {% elsif form.posted_successfully? %}
                      aria-describedby="Newsletter-success--{{ section.id }}"
                    {% endif %}
                    placeholder="{{ 'newsletter.label' | t }}"
                    required
                  >
                  <label class="field__label" for="NewsletterForm--{{ section.id }}">
                    {{ 'newsletter.label' | t }}
                  </label>
                  {% if section.settings.button_type == 'arrow' %}
                    <button
                      type="submit"
                      class="newsletter-form__button field__button"
                      name="commit"
                      id="Subscribe"
                      aria-label="{{ 'newsletter.button_label' | t }}"
                    >
                      {% render 'icon-arrow' %}
                    </button>
                  {% endif %}
                </div>
                {% if section.settings.button_type == 'solid' %}
                  <button
                    type="submit"
                    class="button newsletter__solid-btn button--full-width{% if section.settings.button_style_secondary %} button--secondary{% endif %}"
                    name="commit"
                    id="Subscribe"
                    aria-label="{{ 'newsletter.button_label' | t }}"
                  >
                    {{ section.settings.button_label }}
                  </button>
                {% endif %}
                {%- if form.errors -%}
                  <small class="newsletter-form__message form__message" id="Newsletter-error--{{ section.id }}">
                    {%- render 'icon-error' -%}
                    {{- form.errors.translated_fields.email | capitalize }}
                    {{ form.errors.messages.email -}}
                  </small>
                {%- endif -%}
              </div>
              {%- if form.posted_successfully? -%}
                <h3
                  class="newsletter-form__message newsletter-form__message--success form__message"
                  id="Newsletter-success--{{ section.id }}"
                  tabindex="-1"
                  autofocus
                >
                  {% render 'icon-success' -%}
                  {{- 'newsletter.success' | t }}
                </h3>
              {%- endif -%}
            {% endform %}
          </div>
      {%- endcase -%}
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.newsletter.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "select",
      "id": "button_type",
      "options": [
        {
          "value": "arrow",
          "label": "Arrow button"
        },
        {
          "value": "solid",
          "label": "Solid button"
        }
      ],
      "default": "solid",
      "label": "Submit button type"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Solid button label",
      "default": "Sign up"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
      "default": false
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "t:sections.newsletter.settings.full_width.label"
    },
    {
      "type": "paragraph",
      "content": "t:sections.newsletter.settings.paragraph.content"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 52
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.newsletter.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Subscribe to our emails",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        }
      ]
    },
    {
      "type": "paragraph",
      "name": "t:sections.newsletter.blocks.paragraph.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Be the first to know about new collections and exclusive offers.</p>",
          "label": "t:sections.newsletter.blocks.paragraph.settings.paragraph.label"
        }
      ]
    },
    {
      "type": "email_form",
      "name": "t:sections.newsletter.blocks.email_form.name",
      "limit": 1
    },
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.newsletter.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "paragraph"
        },
        {
          "type": "email_form"
        }
      ]
    }
  ]
}
{% endschema %}
