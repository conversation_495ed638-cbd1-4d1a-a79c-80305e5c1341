/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Värit",
      "settings": {
        "colors_solid_button_labels": {
          "label": "Peittävä tekstipainike",
          "info": "Käytetään korostusvärien edustavärinä."
        },
        "colors_accent_1": {
          "label": "Korostus 1",
          "info": "Käytetään peittävän painikkeen taustana."
        },
        "colors_accent_2": {
          "label": "Korostus 2"
        },
        "header__1": {
          "content": "Ensisijaiset värit"
        },
        "header__2": {
          "content": "Toissijaiset värit"
        },
        "colors_text": {
          "label": "Teksti",
          "info": "Käytetään taustavärien edustavärinä."
        },
        "colors_outline_button_labels": {
          "label": "Kehyspainike",
          "info": "Käytetään myös tekstilinkeissä."
        },
        "colors_background_1": {
          "label": "Tausta 1"
        },
        "colors_background_2": {
          "label": "Tausta 2"
        },
        "gradient_accent_1": {
          "label": "Korostus 1 liukuväri"
        },
        "gradient_accent_2": {
          "label": "Korostus 2 liukuväri"
        },
        "gradient_background_1": {
          "label": "Tausta 1 liukuväri"
        },
        "gradient_background_2": {
          "label": "Tausta 2 liukuväri"
        }
      }
    },
    "typography": {
      "name": "Typografia",
      "settings": {
        "type_header_font": {
          "label": "Fontti",
          "info": "Muun fontin valitseminen voi vaikuttaa kauppasi nopeuteen. [Lue lisää järjestelmäfonteista.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "header__1": {
          "content": "Otsikot"
        },
        "header__2": {
          "content": "Leipäteksti"
        },
        "type_body_font": {
          "label": "Fontti",
          "info": "Muun fontin valitseminen voi vaikuttaa kauppasi nopeuteen. [Lue lisää järjestelmäfonteista.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "heading_scale": {
          "label": "Fonttikoon skaala"
        },
        "body_scale": {
          "label": "Fonttikoon skaala"
        }
      }
    },
    "styles": {
      "name": "Kuvakkeet",
      "settings": {
        "accent_icons": {
          "options__3": {
            "label": "Kehyspainike"
          },
          "options__4": {
            "label": "Teksti"
          },
          "label": "Väri"
        }
      }
    },
    "social-media": {
      "name": "Sosiaalinen media",
      "settings": {
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https://twitter.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://facebook.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "https://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://twitter.com/shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "http://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://facebook.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Sosiaalisen median tilit"
        }
      }
    },
    "currency_format": {
      "name": "Valuutan muoto",
      "settings": {
        "content": "Valuuttakoodit",
        "currency_code_enabled": {
          "label": "Näytä valuuttakoodit"
        },
        "paragraph": "Ostoskorin ja kassan hinnat näyttävät aina valuuttakoodit. Esimerkki: $1.00 USD."
      }
    },
    "layout": {
      "name": "Asettelu",
      "settings": {
        "page_width": {
          "label": "Sivun leveys"
        },
        "spacing_sections": {
          "label": "Tila malliosioiden välissä"
        },
        "header__grid": {
          "content": "Ruudukko"
        },
        "paragraph__grid": {
          "content": "Vaikuttaa alueisiin, joissa on monta saraketta tai riviä."
        },
        "spacing_grid_horizontal": {
          "label": "Vaakasuuntainen tila"
        },
        "spacing_grid_vertical": {
          "label": "Pystysuuntainen tila"
        }
      }
    },
    "search_input": {
      "name": "Hakukäyttäytyminen",
      "settings": {
        "header": {
          "content": "Hakuehdotukset"
        },
        "predictive_search_enabled": {
          "label": "Ota hakuehdotukset käyttöön"
        },
        "predictive_search_show_vendor": {
          "label": "Näytä tuotteen myyjä",
          "info": "Näkyy, kun hakuehdotukset ovat käytössä."
        },
        "predictive_search_show_price": {
          "label": "Näytä tuotteen nimi",
          "info": "Näkyy, kun hakuehdotukset ovat käytössä."
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Reuna"
        },
        "header__shadow": {
          "content": "Varjo"
        },
        "blur": {
          "label": "Sumeus"
        },
        "corner_radius": {
          "label": "Kulman säde"
        },
        "horizontal_offset": {
          "label": "Vaakasuuntainen siirtymä"
        },
        "vertical_offset": {
          "label": "Pystysuuntainen siirtymä"
        },
        "thickness": {
          "label": "Paksuus"
        },
        "opacity": {
          "label": "Sameus"
        },
        "image_padding": {
          "label": "Kuvan täyttäminen"
        },
        "text_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Tekstin tasaus"
        }
      }
    },
    "cards": {
      "name": "Tuotekortit",
      "settings": {
        "style": {
          "options__1": {
            "label": "Vakiomuotoinen"
          },
          "options__2": {
            "label": "Kortti"
          },
          "label": "Tyyli"
        }
      }
    },
    "badges": {
      "name": "Tunnukset",
      "settings": {
        "position": {
          "options__1": {
            "label": "Alhaalla vasemmalla"
          },
          "options__2": {
            "label": "Alhaalla oikealla"
          },
          "options__3": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__4": {
            "label": "Ylhäällä oikealla"
          },
          "label": "Sijainti korteilla"
        },
        "sale_badge_color_scheme": {
          "label": "Alennusmyynti-tunnuksen värimalli"
        },
        "sold_out_badge_color_scheme": {
          "label": "Loppuunmyyty-tunnuksen värimalli"
        }
      }
    },
    "buttons": {
      "name": "Painikkeet"
    },
    "variant_pills": {
      "name": "Versioiden kuvakkeet",
      "paragraph": "Versiopillerit ovat yksi tapa näyttää tuoteversiosi asiakkaille. [Lisätietoja](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"
    },
    "inputs": {
      "name": "Syötteet"
    },
    "content_containers": {
      "name": "Sisältösäiliöt"
    },
    "popups": {
      "name": "Alasvetovalikot ja ponnahdusikkunat",
      "paragraph": "Vaikuttaa navigoinnin alasvetovalikoiden, modaalisiin ponnahdusikkunoiden ja ostoskorin kaltaisiin alueisiin."
    },
    "media": {
      "name": "Tietovälineet"
    },
    "drawers": {
      "name": "Vetolaatikot"
    },
    "cart": {
      "name": "Ostoskori",
      "settings": {
        "cart_type": {
          "label": "Ostoskorin tyyppi",
          "drawer": {
            "label": "Laatikko"
          },
          "page": {
            "label": "Sivu"
          },
          "notification": {
            "label": "Ponnahdusikkunailmoitukset"
          }
        },
        "show_vendor": {
          "label": "Näytä myyjä"
        },
        "show_cart_note": {
          "label": "Ota tilauskommentit käyttöön"
        },
        "cart_drawer": {
          "header": "Veto-ostoskori",
          "collection": {
            "label": "Kokoelma",
            "info": "Näkyy, kun veto-ostoskori on tyhjä."
          }
        }
      }
    },
    "collection_cards": {
      "name": "Kokoelman kortit",
      "settings": {
        "style": {
          "options__1": {
            "label": "Vakio"
          },
          "options__2": {
            "label": "Kortti"
          },
          "label": "Tyyli"
        }
      }
    },
    "blog_cards": {
      "name": "Blogi-kortit",
      "settings": {
        "style": {
          "options__1": {
            "label": "Vakio"
          },
          "options__2": {
            "label": "Kortti"
          },
          "label": "Tyyli"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Työpöytälogon leveys",
          "info": "Logon leveys optimoidaan automaattisesti mobiililaitteille."
        },
        "favicon": {
          "label": "Favicon-kuva",
          "info": "Skaalataan 32 x 32 pikseliin"
        }
      }
    },
    "brand_information": {
      "name": "Bränditiedot",
      "settings": {
        "brand_headline": {
          "label": "Otsikko"
        },
        "brand_description": {
          "label": "Oikeuksien kuvaus"
        },
        "brand_image": {
          "label": "Kuva"
        },
        "brand_image_width": {
          "label": "Kuvan leveys"
        },
        "paragraph": {
          "content": "Lisää brändin kuvaus kauppasi alatunnisteeseen."
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Osion täyttö",
        "padding_top": "Yläosan täyttö",
        "padding_bottom": "Alaosan täyttö"
      },
      "spacing": "Väli",
      "colors": {
        "accent_1": {
          "label": "Korostus 1"
        },
        "accent_2": {
          "label": "Korostus 2"
        },
        "background_1": {
          "label": "Tausta 1"
        },
        "background_2": {
          "label": "Tausta 2"
        },
        "inverse": {
          "label": "Käänteinen"
        },
        "label": "Värimalli",
        "has_cards_info": "Voit muuttaa kortin väriskeemaa päivittämällä teema-asetuksia."
      },
      "heading_size": {
        "label": "Otsikon koko",
        "options__1": {
          "label": "Pieni"
        },
        "options__2": {
          "label": "Keskisuuri"
        },
        "options__3": {
          "label": "Suuri"
        },
        "options__4": {
          "label": "Erittäin suuri"
        }
      }
    },
    "announcement-bar": {
      "name": "Ilmoituspalkki",
      "blocks": {
        "announcement": {
          "name": "Ilmoitus",
          "settings": {
            "text": {
              "label": "Teksti"
            },
            "text_alignment": {
              "label": "Tekstin tasaus",
              "options__1": {
                "label": "Vasen"
              },
              "options__2": {
                "label": "Keskitetty"
              },
              "options__3": {
                "label": "Oikea"
              }
            },
            "link": {
              "label": "Linkki"
            }
          }
        }
      }
    },
    "collage": {
      "name": "Kollaasi",
      "settings": {
        "heading": {
          "label": "Otsikko"
        },
        "desktop_layout": {
          "label": "Työpöytäasettelu",
          "options__1": {
            "label": "Vasemmanpuoleinen suuri lohko"
          },
          "options__2": {
            "label": "Oikeanpuoleinen suuri lohko"
          }
        },
        "mobile_layout": {
          "label": "Mobiiliasettelu",
          "options__1": {
            "label": "Kollaasi"
          },
          "options__2": {
            "label": "Sarake"
          }
        },
        "card_styles": {
          "label": "Kortin tyyli",
          "info": "Voit muuttaa tuote‑, kokoelma‑ ja blogikorttien tyylejä teema-asetuksista.",
          "options__1": {
            "label": "Käytä yksittäisiä korttityylejä"
          },
          "options__2": {
            "label": "Muuta kaikki tuotekorteiksi"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Kuva",
          "settings": {
            "image": {
              "label": "Kuva"
            }
          }
        },
        "product": {
          "name": "Tuote",
          "settings": {
            "product": {
              "label": "Tuote"
            },
            "secondary_background": {
              "label": "Näytä toissijainen tausta"
            },
            "second_image": {
              "label": "Näytä toinen kuva osoittaessa"
            }
          }
        },
        "collection": {
          "name": "Kokoelma",
          "settings": {
            "collection": {
              "label": "Kokoelma"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Kansikuva"
            },
            "video_url": {
              "label": "URL-osoite",
              "info": "Video toistetaan ponnahdusikkunassa, jos osio sisältää muita lohkoja.",
              "placeholder": "Käytä YouTube- tai Vimeo-linkkiä"
            },
            "description": {
              "label": "Videon vaihtoehtoinen teksti",
              "info": "Kuvaile videota näytönlukijoita käyttäviä asiakkaita varten. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"
            }
          }
        }
      },
      "presets": {
        "name": "Kollaasi"
      }
    },
    "collection-list": {
      "name": "Kokoelmaluettelo",
      "settings": {
        "title": {
          "label": "Otsikko"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          },
          "info": "Lisää kuvia kokoelmiasi muokkaamalla. [Lisätietoja](https://help.shopify.com/manual/products/collections)"
        },
        "swipe_on_mobile": {
          "label": "Ota pyyhkäisy käyttöön mobiililaitteessa"
        },
        "show_view_all": {
          "label": "Ota Näytä kaikki ‑painike käyttöön, jos luettelo sisältää useampia kokoelmia kuin mitä on näkyvissä"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        },
        "columns_mobile": {
          "label": "Sarakkeiden määrä mobiilissa",
          "options__1": {
            "label": "1 sarake"
          },
          "options__2": {
            "label": "2 saraketta"
          }
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Kokoelma",
          "settings": {
            "collection": {
              "label": "Kokoelma"
            }
          }
        }
      },
      "presets": {
        "name": "Kokoelmaluettelo"
      }
    },
    "contact-form": {
      "name": "Yhteydenottolomake",
      "presets": {
        "name": "Yhteydenottolomake"
      }
    },
    "custom-liquid": {
      "name": "Mukautettu Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Mukautettu Liquid",
          "info": "Luo vaativampia mukautuksia lisäämällä sovelluksen koodinpätkiä tai muita Liquid-koodeja."
        }
      },
      "presets": {
        "name": "Mukautettu Liquid"
      }
    },
    "featured-blog": {
      "name": "Blogipostaukset",
      "settings": {
        "heading": {
          "label": "Otsikko"
        },
        "blog": {
          "label": "Blogi"
        },
        "post_limit": {
          "label": "Näytettävien blogipostausten määrä"
        },
        "show_view_all": {
          "label": "Ota Näytä kaikki ‑painike käyttöön, jos blogi sisältää useampia blogipostauksia kuin mitä on näkyvissä"
        },
        "show_image": {
          "label": "Näytä esittelykuva",
          "info": "Saat parhaat tulokset käyttämällä kuvaa, jonka kuvasuhde on 3:2. [Lisätietoja](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "show_date": {
          "label": "Näytä päivämäärä"
        },
        "show_author": {
          "label": "Näytä tekijä"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        }
      },
      "presets": {
        "name": "Blogipostaukset"
      }
    },
    "featured-collection": {
      "name": "Esittelyssä oleva kokoelma",
      "settings": {
        "title": {
          "label": "Otsikko"
        },
        "collection": {
          "label": "Kokoelma"
        },
        "products_to_show": {
          "label": "Näytettävien tuotteiden enimmäismäärä"
        },
        "show_view_all": {
          "label": "Ota ”Katso kaikki” käyttöön, jos kokoelma sisältää enemmän kuin näkyvissä olevat tuotteet"
        },
        "header": {
          "content": "Tuotekortti"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Näytä myyjä"
        },
        "show_rating": {
          "label": "Näytä tuotteen luokitus",
          "info": "Näytä luokitus lisäämällä tuotearviointisovellus. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        },
        "description": {
          "label": "Kuvaus"
        },
        "show_description": {
          "label": "Näytä kokoelman kuvaus adminissa"
        },
        "description_style": {
          "label": "Kuvauksen tyyli",
          "options__1": {
            "label": "Leipäteksti"
          },
          "options__2": {
            "label": "Alaotsikko"
          },
          "options__3": {
            "label": "Isot kirjaimet"
          }
        },
        "view_all_style": {
          "label": "”Katso kaikki” tyylit",
          "options__1": {
            "label": "Linkki"
          },
          "options__2": {
            "label": "Kehyspainike"
          },
          "options__3": {
            "label": "Peittävä painike"
          }
        },
        "enable_desktop_slider": {
          "label": "Ota karuselli käyttöön työpöydällä"
        },
        "full_width": {
          "label": "Tee tuotteista täysleveitä"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        },
        "columns_mobile": {
          "label": "Sarakkeiden määrä mobiilissa",
          "options__1": {
            "label": "1 sarake"
          },
          "options__2": {
            "label": "2 saraketta"
          }
        },
        "swipe_on_mobile": {
          "label": "Ota pyyhkäisy käyttöön mobiililaitteessa"
        },
        "enable_quick_buy": {
          "label": "Ota pikalisäyspainike käyttöön",
          "info": "Ihanteellinen ponnahdusikkuna- tai laatikkotyyppisten ostokorien kanssa."
        }
      },
      "presets": {
        "name": "Esittelyssä oleva kokoelma"
      }
    },
    "footer": {
      "name": "Alatunniste",
      "blocks": {
        "link_list": {
          "name": "Valikko",
          "settings": {
            "heading": {
              "label": "Otsikko"
            },
            "menu": {
              "label": "Valikko",
              "info": "Näytetään vain ylimmän tason valikkokohdat."
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "heading": {
              "label": "Otsikko"
            },
            "subtext": {
              "label": "Alateksti"
            }
          }
        },
        "brand_information": {
          "name": "Bränditiedot",
          "settings": {
            "paragraph": {
              "content": "Bränditietosi näkyvät tässä lohkossa. [Muokkaa bränditietoja.](/editor?context=theme&category=brand%20information)"
            },
            "header__1": {
              "content": "Some-kuvakkeet"
            },
            "show_social": {
              "label": "Näytä some-kuvakkeet",
              "info": "Jos haluat näyttää sosiaalisen median tilisi, linkitä ne [teema-asetuksistasi](/editor?context=theme&category=social%20media)."
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Näytä sähköpostirekisteröityminen"
        },
        "newsletter_heading": {
          "label": "Otsikko"
        },
        "header__1": {
          "content": "Sähköpostitilaaja",
          "info": "Tilaajat lisättiin automaattisesti \"markkinoinnin hyväksyneiden\" asiakasluetteloon. [Lisätietoja](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "header__2": {
          "content": "Some-kuvakkeet",
          "info": "Jos haluat näyttää sosiaalisen median tilisi, linkitä ne [teema-asetuksistasi](/editor?context=theme&category=social%20media)."
        },
        "show_social": {
          "label": "Näytä some-kuvakkeet"
        },
        "header__3": {
          "content": "Maa-/aluevalitsin"
        },
        "header__4": {
          "info": "Lisää maa/alue siirtymällä [markkina-asetuksiisi.](/admin/settings/markets)"
        },
        "enable_country_selector": {
          "label": "Näytä maa-/aluevalitsin"
        },
        "header__5": {
          "content": "Kielivalitsin"
        },
        "header__6": {
          "info": "Lisää kieli siirtymällä [kieliasetuksiisi.](/admin/settings/languages)"
        },
        "enable_language_selector": {
          "label": "Ota kielivalitsin käyttöön"
        },
        "header__7": {
          "content": "Maksutavat"
        },
        "payment_enable": {
          "label": "Näytä maksukuvakkeet"
        },
        "margin_top": {
          "label": "Yläreunus"
        },
        "header__8": {
          "content": "Käytäntöjen linkit",
          "info": "Jos haluat lisätä kaupan käytännöt, siirry [käytäntöjen asetuksiin](/admin/settings/legal)."
        },
        "show_policy": {
          "label": "Näytä käytäntöjen linkit"
        },
        "header__9": {
          "content": "Seuraa Shopissa",
          "info": "Näytä myyntipaikkasi Seuraa-painike Shop-sovelluksessa. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        },
        "enable_follow_on_shop": {
          "label": "Ota Seuraa Shopissa käyttöön"
        }
      }
    },
    "header": {
      "name": "Ylätunniste",
      "settings": {
        "logo_position": {
          "label": "Logon sijainti tietokoneella",
          "options__1": {
            "label": "Keskellä vasemmalla"
          },
          "options__2": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__3": {
            "label": "Keskellä ylhäällä"
          },
          "options__4": {
            "label": "Keskellä"
          }
        },
        "menu": {
          "label": "Valikko"
        },
        "show_line_separator": {
          "label": "Näytä erotinviiva"
        },
        "margin_bottom": {
          "label": "Alareunus"
        },
        "menu_type_desktop": {
          "label": "Pöytäkoneen valikkotyyppi",
          "info": "Valikkotyyppi optimoidaan automaattisesti mobiililaitteilla.",
          "options__1": {
            "label": "Pudotusvalikko"
          },
          "options__2": {
            "label": "Mega-valikko"
          }
        },
        "mobile_layout": {
          "content": "Mobiiliasettelu"
        },
        "mobile_logo_position": {
          "label": "Logon asettelu mobiilissa",
          "options__1": {
            "label": "Keskitetty"
          },
          "options__2": {
            "label": "Vasen"
          }
        },
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Muokkaa logoa [teeman asetuksissa](/editor?context=theme&category=logo)."
        },
        "sticky_header_type": {
          "label": "Paikallaan pysyvä ylätunniste",
          "options__1": {
            "label": "Ei mitään"
          },
          "options__2": {
            "label": "Ylöspäin vieritettäessä"
          },
          "options__3": {
            "label": "Aina"
          },
          "options__4": {
            "label": "Aina, pienennä logon kokoa"
          }
        }
      }
    },
    "image-banner": {
      "name": "Kuvabanneri",
      "settings": {
        "image": {
          "label": "Ensimmäinen kuva"
        },
        "image_2": {
          "label": "Toinen kuva"
        },
        "color_scheme": {
          "info": "Näkyvillä, kun säilö on esillä."
        },
        "stack_images_on_mobile": {
          "label": "Pinoa kuvat mobiilissa"
        },
        "adapt_height_first_image": {
          "label": "Mukauta osion korkeus ensimmäisen kuvan kokoon",
          "info": "Korvaa kuvabannerin korkeusasetuksen"
        },
        "show_text_box": {
          "label": "Näytä säilö pöytäkoneella"
        },
        "image_overlay_opacity": {
          "label": "Peittokuvan läpikuultavuus"
        },
        "header": {
          "content": "Mobiiliasettelu"
        },
        "show_text_below": {
          "label": "Näytä säilö mobiililaitteessa"
        },
        "image_height": {
          "label": "Bannerin korkeus",
          "options__1": {
            "label": "Mukauta ensimmäisen kuvan mukaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "info": "Saat parhaat tulokset käyttämällä kuvaa, jonka kuvasuhde on 3:2. [Lisätietoja](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
          "options__4": {
            "label": "Suuri"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__2": {
            "label": "Keskellä ylhäällä"
          },
          "options__3": {
            "label": "Ylhäällä oikealla"
          },
          "options__4": {
            "label": "Keskellä vasemmalla"
          },
          "options__5": {
            "label": "Keskitetty keskelle"
          },
          "options__6": {
            "label": "Keskellä oikealla"
          },
          "options__7": {
            "label": "Alhaalla vasemmalla"
          },
          "options__8": {
            "label": "Keskellä alhaalla"
          },
          "options__9": {
            "label": "Alhaalla oikealla"
          },
          "label": "Työpöytäsisällön sijainti"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Työpöydän sisällön kohdistus"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Mobiilisisällön tasaus"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko"
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Kuvaus"
            },
            "text_style": {
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              },
              "label": "Tekstityyli"
            }
          }
        },
        "buttons": {
          "name": "Painikkeet",
          "settings": {
            "button_label_1": {
              "label": "Ensimmäinen tekstipainike",
              "info": "Jos haluat piilottaa painikkeen, jätä painikkeen teksti tyhjäksi."
            },
            "button_link_1": {
              "label": "Ensimmäinen painikelinkki"
            },
            "button_style_secondary_1": {
              "label": "Käytä ääriviivallista painiketyyliä"
            },
            "button_label_2": {
              "label": "Toinen tekstipainike",
              "info": "Jos haluat piilottaa painikkeen, jätä painikkeen teksti tyhjäksi."
            },
            "button_link_2": {
              "label": "Toinen painikelinkki"
            },
            "button_style_secondary_2": {
              "label": "Käytä ääriviivallista painiketyyliä"
            }
          }
        }
      },
      "presets": {
        "name": "Kuvabanneri"
      }
    },
    "image-with-text": {
      "name": "Kuva tekstillä",
      "settings": {
        "image": {
          "label": "Kuva"
        },
        "height": {
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "label": "Kuvan korkeus",
          "options__4": {
            "label": "Suuri"
          }
        },
        "layout": {
          "options__1": {
            "label": "Kuva ensin"
          },
          "options__2": {
            "label": "Toinen kuva"
          },
          "label": "Työpöytäkuvan sijoitus",
          "info": "Kuva ensin on oletusarvoinen asettelu mobiilissa."
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Pieni"
          },
          "options__2": {
            "label": "Keskisuuri"
          },
          "options__3": {
            "label": "Suuri"
          },
          "label": "Työpöytäkuvan leveys",
          "info": "Kuva optimoidaan automaattisesti mobiililaitteille."
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Työpöydän sisällön kohdistus"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Ylös"
          },
          "options__2": {
            "label": "Keskelle"
          },
          "options__3": {
            "label": "Alas"
          },
          "label": "Työpöytäsisällön sijainti"
        },
        "content_layout": {
          "options__1": {
            "label": "Ei päällekkäisyyksiä"
          },
          "options__2": {
            "label": "Päällekkäisyys"
          },
          "label": "Sisällön pohja"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Mobiilisisällön tasaus"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko"
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Sisältö"
            },
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              }
            }
          }
        },
        "button": {
          "name": "Painike",
          "settings": {
            "button_label": {
              "label": "Tekstipainike",
              "info": "Jos haluat piilottaa painikkeen, jätä painikkeen teksti tyhjäksi."
            },
            "button_link": {
              "label": "Painikelinkki"
            }
          }
        },
        "caption": {
          "name": "Kuvateksti",
          "settings": {
            "text": {
              "label": "Teksti"
            },
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Alaotsikko"
              },
              "options__2": {
                "label": "Isot kirjaimet"
              }
            },
            "caption_size": {
              "label": "Tekstin koko",
              "options__1": {
                "label": "Pieni"
              },
              "options__2": {
                "label": "Keskisuuri"
              },
              "options__3": {
                "label": "Suuri"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Kuva tekstillä"
      }
    },
    "main-article": {
      "name": "Blogipostaus",
      "blocks": {
        "featured_image": {
          "name": "Esittelykuva",
          "settings": {
            "image_height": {
              "label": "esittelykuvan korkeus",
              "options__1": {
                "label": "Sovita kuvaan"
              },
              "options__2": {
                "label": "Pieni"
              },
              "options__3": {
                "label": "Keskisuuri"
              },
              "info": "Saat parhaat tulokset käyttämällä kuvaa, jonka kuvasuhde on 16:9. [Lisätietoja](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
              "options__4": {
                "label": "Suuri"
              }
            }
          }
        },
        "title": {
          "name": "Otsikko",
          "settings": {
            "blog_show_date": {
              "label": "Näytä päivämäärä"
            },
            "blog_show_author": {
              "label": "Näytä tekijä"
            }
          }
        },
        "content": {
          "name": "Sisältö"
        },
        "share": {
          "name": "Jaa",
          "settings": {
            "featured_image_info": {
              "content": "Jos lisäät sosiaalisen median julkaisuihin linkkejä, esikatselukuvana näkyy sivun esittelykuva. [Lisätietoja](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Esikatselukuvassa näkyy kaupan nimi ja kuvaus. [Lisätietoja](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Teksti"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blogipostaukset",
      "settings": {
        "header": {
          "content": "blogipostauskortti"
        },
        "show_image": {
          "label": "Näytä esittelykuva"
        },
        "paragraph": {
          "content": "Muuta otteita blogipostauksiasi muokkaamalla. [Lisätietoja](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"
        },
        "show_date": {
          "label": "Näytä päivämäärä"
        },
        "show_author": {
          "label": "Näytä tekijä"
        },
        "layout": {
          "label": "Työpöytäasettelu",
          "options__1": {
            "label": "Ruudukko"
          },
          "options__2": {
            "label": "Kollaasi"
          },
          "info": "Julkaisut asetetaan päällekkäin mobiilissa."
        },
        "image_height": {
          "label": "Esittelykuvan korkeus",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          },
          "info": "Saat parhaat tulokset käyttämällä kuvaa, jonka kuvasuhde on 3:2. [Lisätietoja](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-cart-footer": {
      "name": "Välisumma",
      "blocks": {
        "subtotal": {
          "name": "Välisumma yhteensä"
        },
        "buttons": {
          "name": "Kassapainike"
        }
      }
    },
    "main-cart-items": {
      "name": "Tuotteet"
    },
    "main-collection-banner": {
      "name": "Kokoelmabanneri",
      "settings": {
        "paragraph": {
          "content": "Lisää kuvaus tai kuva kokoelmiasi muokkaamalla. [Lisätietoja](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Näytä kokoelman kuvaus"
        },
        "show_collection_image": {
          "label": "Näytä kokoelman kuva",
          "info": "Saat parhaat tulokset käyttämällä kuvaa, jonka kuvasuhde on 16:9. [Lisätietoja](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Tuoteruudukko",
      "settings": {
        "products_per_page": {
          "label": "Tuotteita sivulla"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Näytä myyjä"
        },
        "enable_tags": {
          "label": "Ota suodatus käyttöön",
          "info": "Mukauta suodattimia Search & Discovery -sovelluksen avulla. [Lisätietoja](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_filtering": {
          "label": "Ota suodatus käyttöön",
          "info": "Mukauta suodattimia Search & Discovery -sovelluksen avulla. [Lisätietoja](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Ota lajittelu käyttöön"
        },
        "header__1": {
          "content": "Suodatus ja lajittelu"
        },
        "header__3": {
          "content": "Tuotekortti"
        },
        "show_rating": {
          "label": "Näytä tuotteen luokitus",
          "info": "Näytä luokitus lisäämällä tuotearviointisovellus. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        },
        "columns_mobile": {
          "label": "Sarakkeiden määrä mobiilissa",
          "options__1": {
            "label": "1 sarake"
          },
          "options__2": {
            "label": "2 saraketta"
          }
        },
        "enable_quick_buy": {
          "label": "Ota pikalisäyspainike käyttöön",
          "info": "Ihanteellinen ponnahdusikkuna- tai laatikkotyyppisten ostokorien kanssa."
        },
        "filter_type": {
          "label": "Suodatinasettelu tietokoneella",
          "options__1": {
            "label": "Vaaka"
          },
          "options__2": {
            "label": "Pysty"
          },
          "options__3": {
            "label": "Laatikko"
          },
          "info": "Laatikko on oletusarvoinen asettelu mobiilissa."
        }
      }
    },
    "main-list-collections": {
      "name": "Kokoelmaluettelosivu",
      "settings": {
        "title": {
          "label": "Otsikko"
        },
        "sort": {
          "label": "Lajittele kokoelmat seuraavasti:",
          "options__1": {
            "label": "Aakkosjärjestyksessä A–Z"
          },
          "options__2": {
            "label": "Aakkosjärjestyksessä Z–A"
          },
          "options__3": {
            "label": "Päivämäärä uusimmasta vanhimpaan"
          },
          "options__4": {
            "label": "Päivämäärä vanhimmasta uusimpaan"
          },
          "options__5": {
            "label": "Tuotteiden määrä suurimmasta pienimpään"
          },
          "options__6": {
            "label": "Tuotteiden määrä pienimmästä suurimpaan"
          }
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          },
          "info": "Lisää kuvia kokoelmiasi muokkaamalla. [Lisätietoja](https://help.shopify.com/manual/products/collections)"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        },
        "columns_mobile": {
          "label": "Sarakkeiden määrä mobiililaitteessa",
          "options__1": {
            "label": "1 sarake"
          },
          "options__2": {
            "label": "2 saraketta"
          }
        }
      }
    },
    "main-page": {
      "name": "Sivu"
    },
    "main-password-footer": {
      "name": "Salasana-alatunniste"
    },
    "main-password-header": {
      "name": "Salasanaylätunniste",
      "settings": {
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Muokkaa logoa teeman asetuksissa."
        }
      }
    },
    "main-product": {
      "blocks": {
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Teksti"
            },
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        },
        "title": {
          "name": "Otsikko"
        },
        "price": {
          "name": "Hinta"
        },
        "quantity_selector": {
          "name": "Määrän valitsin"
        },
        "variant_picker": {
          "name": "Versionvalitsin",
          "settings": {
            "picker_type": {
              "label": "Tyyppi",
              "options__1": {
                "label": "Pudotusvalikko"
              },
              "options__2": {
                "label": "Pillerit"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Osta-painikkeet",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Näytä dynaamiset kassapainikkeet",
              "info": "Kun käytät kaupallesi saatavilla olevia maksutapoja, asiakkaat näkevät ensisijaisen vaihtoehtonsa, kuten PayPalin tai Apple Payn. [Lisätietoja](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "pickup_availability": {
          "name": "Noudon saatavuus"
        },
        "description": {
          "name": "Kuvaus"
        },
        "share": {
          "name": "Jaa",
          "settings": {
            "featured_image_info": {
              "content": "Jos lisäät sosiaalisen median julkaisuihin linkkejä, esikatselukuvana näkyy sivun esittelykuva. [Lisätietoja](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Esikatselukuvassa näkyy kaupan nimi ja kuvaus. [Lisätietoja](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Teksti"
            }
          }
        },
        "collapsible_tab": {
          "name": "Pienenettävä rivi",
          "settings": {
            "heading": {
              "info": "Lisää sisältöä kuvaava otsikko.",
              "label": "Otsikko"
            },
            "content": {
              "label": "Rivin sisältö"
            },
            "page": {
              "label": "Rivin sisältö sivulta"
            },
            "icon": {
              "options__1": {
                "label": "Ei mitään"
              },
              "options__2": {
                "label": "Omena"
              },
              "options__3": {
                "label": "Banaani"
              },
              "options__4": {
                "label": "Pullo"
              },
              "options__5": {
                "label": "Laatikko"
              },
              "options__6": {
                "label": "Porkkana"
              },
              "options__7": {
                "label": "Keskustelukupla"
              },
              "options__8": {
                "label": "Valintamerkki"
              },
              "options__9": {
                "label": "Leikepöytä"
              },
              "options__10": {
                "label": "Maitotuote"
              },
              "options__11": {
                "label": "Maidoton"
              },
              "options__12": {
                "label": "Kuivain"
              },
              "options__13": {
                "label": "Silmä"
              },
              "options__14": {
                "label": "Tuli"
              },
              "options__15": {
                "label": "Gluteeniton"
              },
              "options__16": {
                "label": "Sydän"
              },
              "options__17": {
                "label": "Silitysrauta"
              },
              "options__18": {
                "label": "Lehti"
              },
              "options__19": {
                "label": "Nahka"
              },
              "options__20": {
                "label": "Salama"
              },
              "options__21": {
                "label": "Huulipuna"
              },
              "options__22": {
                "label": "Lukko"
              },
              "options__23": {
                "label": "Karttamerkki"
              },
              "options__24": {
                "label": "Pähkinätön"
              },
              "label": "Kuvake",
              "options__25": {
                "label": "Housut"
              },
              "options__26": {
                "label": "Tassun jälki"
              },
              "options__27": {
                "label": "Pippuri"
              },
              "options__28": {
                "label": "Tuoksu"
              },
              "options__29": {
                "label": "Lentokone"
              },
              "options__30": {
                "label": "Kasvi"
              },
              "options__31": {
                "label": "Hintalappu"
              },
              "options__32": {
                "label": "Kysymysmerkki"
              },
              "options__33": {
                "label": "Kierrätys"
              },
              "options__34": {
                "label": "Palaa"
              },
              "options__35": {
                "label": "Viivain"
              },
              "options__36": {
                "label": "Tarjoiluastia"
              },
              "options__37": {
                "label": "Paita"
              },
              "options__38": {
                "label": "Kenkä"
              },
              "options__39": {
                "label": "Siluetti"
              },
              "options__40": {
                "label": "Lumihiutale"
              },
              "options__41": {
                "label": "Tähti"
              },
              "options__42": {
                "label": "Sekuntikello"
              },
              "options__43": {
                "label": "Kuljetusajoneuvo"
              },
              "options__44": {
                "label": "Pesu"
              }
            }
          }
        },
        "popup": {
          "name": "Ponnahdusikkuna",
          "settings": {
            "link_label": {
              "label": "Linkin teksti"
            },
            "page": {
              "label": "Sivu"
            }
          }
        },
        "custom_liquid": {
          "name": "Mukautettu liquid",
          "settings": {
            "custom_liquid": {
              "label": "Mukautettu liquid",
              "info": "Luo vaativampia mukautuksia lisäämällä sovelluksen koodinpätkiä tai muita Liquid-koodeja."
            }
          }
        },
        "rating": {
          "name": "Tuotearvio",
          "settings": {
            "paragraph": {
              "content": "Näytä luokitus lisäämällä tuotearviointisovellus. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Täydentävät tuotteet",
          "settings": {
            "paragraph": {
              "content": "Lisää Search & Discovery -sovellus, jotta voit valita täydentäviä tuotteita. [Lisätietoja](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Otsikko"
            },
            "make_collapsible_row": {
              "label": "Näytä pienennettävänä rivinä"
            },
            "icon": {
              "info": "Näkyvissä, kun pienennettävä rivi on esillä."
            },
            "product_list_limit": {
              "label": "Näytettävien tuotteiden enimmäismäärä"
            },
            "products_per_page": {
              "label": "Tuotteiden määrä sivua kohden"
            },
            "pagination_style": {
              "label": "Sivunumerointityyli",
              "options": {
                "option_1": "Pisteet",
                "option_2": "Laskuri",
                "option_3": "Numerot"
              }
            },
            "product_card": {
              "heading": "Tuotekortti"
            },
            "image_ratio": {
              "label": "Kuvasuhde",
              "options": {
                "option_1": "Muotokuva",
                "option_2": "Neliö"
              }
            },
            "enable_quick_add": {
              "label": "Ota pikalisäyspainike käyttöön"
            }
          }
        },
        "icon_with_text": {
          "name": "Kuvake johon liittyy teksti",
          "settings": {
            "layout": {
              "label": "Asettelu",
              "options__1": {
                "label": "Vaaka"
              },
              "options__2": {
                "label": "Pysty"
              }
            },
            "content": {
              "label": "Sisältö",
              "info": "Valitse kuvake tai lisää kuva jokaiselle sarakkeelle tai riville."
            },
            "heading": {
              "info": "Piilota kuvakkeen sarake jättämällä sarakkeen teksti tyhjäksi."
            },
            "icon_1": {
              "label": "Ensimmäinen kuvake"
            },
            "image_1": {
              "label": "Ensimmäinen kuva"
            },
            "heading_1": {
              "label": "Ensimmäinen otsikko"
            },
            "icon_2": {
              "label": "Toinen kuvake"
            },
            "image_2": {
              "label": "Toinen kuva"
            },
            "heading_2": {
              "label": "Toinen otsikko"
            },
            "icon_3": {
              "label": "Kolmas kuvake"
            },
            "image_3": {
              "label": "Kolmas kuva"
            },
            "heading_3": {
              "label": "Kolmas otsikko"
            }
          }
        },
        "sku": {
          "name": "SKU-koodi",
          "settings": {
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        },
        "inventory": {
          "name": "Varaston tila",
          "settings": {
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            },
            "inventory_threshold": {
              "label": "Vähäisen varaston kynnysarvo",
              "info": "Valitse 0, jos haluat näyttää aina varastossa, jos saatavilla."
            },
            "show_inventory_quantity": {
              "label": "Näytä varastomäärä"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Media",
          "info": "Lisätietoja [mediatyypeistä.](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Ota videosilmukka käyttöön"
        },
        "enable_sticky_info": {
          "label": "Ota käyttöön kiinnitettävä sisältö tietokoneella"
        },
        "hide_variants": {
          "label": "Piilota muiden versioiden aineisto, kun versio on valittu"
        },
        "gallery_layout": {
          "label": "Työpöytäasettelu",
          "options__1": {
            "label": "Päällekkäin"
          },
          "options__2": {
            "label": "2 saraketta"
          },
          "options__3": {
            "label": "Pikkukuvat"
          },
          "options__4": {
            "label": "Pikkukuvien karuselli"
          }
        },
        "media_size": {
          "label": "Median leveys tietokoneella",
          "options__1": {
            "label": "Pieni"
          },
          "options__2": {
            "label": "Keskisuuri"
          },
          "options__3": {
            "label": "Suuri"
          },
          "info": "Media optimoidaan automaattisesti mobiililaitteille."
        },
        "mobile_thumbnails": {
          "label": "Mobiiliasettelu",
          "options__1": {
            "label": "2 saraketta"
          },
          "options__2": {
            "label": "Näytä pikkukuvat"
          },
          "options__3": {
            "label": "Piilota pikkukuvat"
          }
        },
        "media_position": {
          "label": "Työpöytämedian sijainti",
          "info": "Sijainti optimoidaan automaattisesti mobiililaitteille.",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Oikea"
          }
        },
        "image_zoom": {
          "label": "Kuvan zoomaus",
          "info": "Avaa lightbox-ikkuna mobiililaitteessa klikkaamalla oletusarvoja ja viemällä osoitin niiden päälle.",
          "options__1": {
            "label": "Avaa lightbox-ikkuna"
          },
          "options__2": {
            "label": "Klikkaa ja vie osoitin päälle"
          },
          "options__3": {
            "label": "Ei zoomausta"
          }
        },
        "constrain_to_viewport": {
          "label": "Sovita media näytön korkeuteen"
        },
        "media_fit": {
          "label": "Median sovitus",
          "options__1": {
            "label": "Alkuperäinen"
          },
          "options__2": {
            "label": "Täyttö"
          }
        }
      },
      "name": "Tuotetiedot"
    },
    "main-search": {
      "name": "Hakutulokset",
      "settings": {
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Näytä myyjä"
        },
        "header__1": {
          "content": "Tuotekortti"
        },
        "header__2": {
          "content": "Blogikortti",
          "info": "Blogikortin tyylejä sovelletaan myös sivukortteihin hakutuloksissa. Voit muuttaa korttien tyylejä päivittämällä teema-asetuksesi."
        },
        "article_show_date": {
          "label": "Näytä päivämäärä"
        },
        "article_show_author": {
          "label": "Näytä tekijä"
        },
        "show_rating": {
          "label": "Näytä tuotteen luokitus",
          "info": "Näytä luokitus lisäämällä tuotearviointisovellus. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        },
        "columns_mobile": {
          "label": "Sarakkeiden määrä mobiilissa",
          "options__1": {
            "label": "1 sarake"
          },
          "options__2": {
            "label": "2 saraketta"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Monisarakkeinen",
      "settings": {
        "title": {
          "label": "Otsikko"
        },
        "image_width": {
          "label": "Kuvan leveys",
          "options__1": {
            "label": "Kolmasosa sarakkeen leveydestä"
          },
          "options__2": {
            "label": "Puolet sarakkeen leveydestä"
          },
          "options__3": {
            "label": "Sarakkeen levyinen"
          }
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          },
          "options__4": {
            "label": "Ympyrä"
          }
        },
        "column_alignment": {
          "label": "Sarakkeen tasaus",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          }
        },
        "background_style": {
          "label": "Toissijainen tausta",
          "options__1": {
            "label": "Ei mitään"
          },
          "options__2": {
            "label": "Näytä sarakkeen taustana"
          }
        },
        "button_label": {
          "label": "Tekstipainike"
        },
        "button_link": {
          "label": "Painikelinkki"
        },
        "swipe_on_mobile": {
          "label": "Ota pyyhkäisy käyttöön mobiililaitteessa"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        },
        "columns_mobile": {
          "label": "Sarakkeiden määrä mobiilissa",
          "options__1": {
            "label": "1 sarake"
          },
          "options__2": {
            "label": "2 saraketta"
          }
        }
      },
      "blocks": {
        "column": {
          "name": "Sarake",
          "settings": {
            "image": {
              "label": "Kuva"
            },
            "title": {
              "label": "Otsikko"
            },
            "text": {
              "label": "Kuvaus"
            },
            "link_label": {
              "label": "Linkin teksti"
            },
            "link": {
              "label": "Linkki"
            }
          }
        }
      },
      "presets": {
        "name": "Monisarakkeinen"
      }
    },
    "newsletter": {
      "name": "Sähköpostirekisteröityminen",
      "settings": {
        "full_width": {
          "label": "Tee osiosta täysleveä"
        },
        "paragraph": {
          "content": "Sähköpostitilaus luo asiakastilin. [Lisätietoja](https://help.shopify.com/manual/customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko"
            }
          }
        },
        "paragraph": {
          "name": "Alaotsikko",
          "settings": {
            "paragraph": {
              "label": "Kuvaus"
            }
          }
        },
        "email_form": {
          "name": "Sähköpostilomake"
        }
      },
      "presets": {
        "name": "Sähköpostirekisteröityminen"
      }
    },
    "page": {
      "name": "Sivu",
      "settings": {
        "page": {
          "label": "Sivu"
        }
      },
      "presets": {
        "name": "Sivu"
      }
    },
    "rich-text": {
      "name": "Rich text",
      "settings": {
        "full_width": {
          "label": "Tee osiosta täysleveä"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Työpöytäsisällön sijainti",
          "info": "Sijainti optimoidaan automaattisesti mobiililaitteille."
        },
        "content_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Sisällön kohdistus"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko"
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Kuvaus"
            }
          }
        },
        "buttons": {
          "name": "Painikkeet",
          "settings": {
            "button_label_1": {
              "label": "Ensimmäinen tekstipainike",
              "info": "Jos haluat piilottaa painikkeen, jätä painikkeen teksti tyhjäksi."
            },
            "button_link_1": {
              "label": "Ensimmäinen painikelinkki"
            },
            "button_style_secondary_1": {
              "label": "Käytä ääriviivallista painiketyyliä"
            },
            "button_label_2": {
              "label": "Toinen tekstipainike",
              "info": "Jos haluat piilottaa painikkeen, jätä painikkeen teksti tyhjäksi."
            },
            "button_link_2": {
              "label": "Toinen painikelinkki"
            },
            "button_style_secondary_2": {
              "label": "Käytä ääriviivallista painiketyyliä"
            }
          }
        },
        "caption": {
          "name": "Kuvateksti",
          "settings": {
            "text": {
              "label": "Teksti"
            },
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Alaotsikko"
              },
              "options__2": {
                "label": "Isot kirjaimet"
              }
            },
            "caption_size": {
              "label": "Tekstin koko",
              "options__1": {
                "label": "Pieni"
              },
              "options__2": {
                "label": "Keskisuuri"
              },
              "options__3": {
                "label": "Suuri"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Rich text"
      }
    },
    "apps": {
      "name": "Sovellukset",
      "settings": {
        "include_margins": {
          "label": "Tee osioiden reunuksista sama kuin teema"
        }
      },
      "presets": {
        "name": "Sovellukset"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Otsikko"
        },
        "cover_image": {
          "label": "Kansikuva"
        },
        "video_url": {
          "label": "URL-osoite",
          "placeholder": "Käytä YouTube- tai Vimeo-linkkiä",
          "info": "Video toistetaan sivulla."
        },
        "description": {
          "label": "Videon vaihtoehtoinen teksti",
          "info": "Kuvaile videota näytönlukijoita käyttäviä asiakkaita varten. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"
        },
        "image_padding": {
          "label": "Lisää kuvan täyttö",
          "info": "Valitse kuvan täyttäminen, jos et halua, että kansikuvaasi rajataan."
        },
        "full_width": {
          "label": "Tee osiosta täysleveä"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Esittelyssä oleva tuote",
      "blocks": {
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Teksti"
            },
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        },
        "title": {
          "name": "Otsikko"
        },
        "price": {
          "name": "Hinta"
        },
        "quantity_selector": {
          "name": "Määrän valitsin"
        },
        "variant_picker": {
          "name": "Versionvalitsin",
          "settings": {
            "picker_type": {
              "label": "Tyyppi",
              "options__1": {
                "label": "Pudotusvalikko"
              },
              "options__2": {
                "label": "Kuvakkeet"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Osta-painikkeet",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Näytä dynaamiset kassapainikkeet",
              "info": "Kun käytät kaupallesi saatavilla olevia maksutapoja, asiakkaat näkevät ensisijaisen vaihtoehtonsa, kuten PayPalin tai Apple Payn. [Lisätietoja](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Kuvaus"
        },
        "share": {
          "name": "Jaa",
          "settings": {
            "featured_image_info": {
              "content": "Jos lisäät sosiaalisen median julkaisuihin linkkejä, esikatselukuvana näkyy sivun esittelykuva. [Lisätietoja](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Esikatselukuvassa näkyy kaupan nimi ja kuvaus. [Lisätietoja](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Teksti"
            }
          }
        },
        "custom_liquid": {
          "name": "Mukautettu liquid",
          "settings": {
            "custom_liquid": {
              "label": "Mukautettu liquid"
            }
          }
        },
        "rating": {
          "name": "Tuotearvio",
          "settings": {
            "paragraph": {
              "content": "Näytä luokitus lisäämällä tuotearviointisovellus. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU-koodi",
          "settings": {
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Tuote"
        },
        "secondary_background": {
          "label": "Näytä toissijainen tausta"
        },
        "header": {
          "content": "Media",
          "info": "Lisätietoja [mediatyypeistä](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Ota videosilmukka käyttöön"
        },
        "hide_variants": {
          "label": "Piilota valitsemattomien versioiden media tietokoneversiossa"
        },
        "media_position": {
          "label": "Työpöytämedian sijainti",
          "info": "Sijainti optimoidaan automaattisesti mobiililaitteille.",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Oikea"
          }
        }
      },
      "presets": {
        "name": "Esittelyssä oleva tuote"
      }
    },
    "email-signup-banner": {
      "name": "Sähköpostirekisteröitymisen banneri",
      "settings": {
        "paragraph": {
          "content": "Sähköpostitilaus luo asiakastilin. [Lisätietoja](https://help.shopify.com/manual/customers)"
        },
        "image": {
          "label": "Taustakuva"
        },
        "show_background_image": {
          "label": "Näytä taustakuva"
        },
        "show_text_box": {
          "label": "Näytä säilö pöytäkoneella"
        },
        "image_overlay_opacity": {
          "label": "Peittokuvan läpikuultavuus"
        },
        "color_scheme": {
          "info": "Näkyvissä, kun säilö on esillä."
        },
        "show_text_below": {
          "label": "Näytä kuvien alla oleva sisältö mobiililaitteessa",
          "info": "Saat parhaat tulokset käyttämällä kuvaa, jonka kuvasuhde on 16:9. [Lisätietoja](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "image_height": {
          "label": "Bannerin korkeus",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          },
          "info": "Saat parhaat tulokset käyttämällä kuvaa, jonka kuvasuhde on 16:9. [Lisätietoja](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "desktop_content_position": {
          "options__4": {
            "label": "Keskellä vasemmalla"
          },
          "options__5": {
            "label": "Keskitetty keskelle"
          },
          "options__6": {
            "label": "Keskellä oikealla"
          },
          "options__7": {
            "label": "Alhaalla vasemmalla"
          },
          "options__8": {
            "label": "Keskellä alhaalla"
          },
          "options__9": {
            "label": "Alhaalla oikealla"
          },
          "options__1": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__2": {
            "label": "Keskellä ylhäällä"
          },
          "options__3": {
            "label": "Ylhäällä oikealla"
          },
          "label": "Työpöytäsisällön sijainti"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Työpöydän sisällön kohdistus"
        },
        "header": {
          "content": "Mobiiliasettelu"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Mobiilisisällön tasaus"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko"
            }
          }
        },
        "paragraph": {
          "name": "Kohta",
          "settings": {
            "paragraph": {
              "label": "Kuvaus"
            },
            "text_style": {
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "label": "Tekstityyli"
            }
          }
        },
        "email_form": {
          "name": "Sähköpostilomake"
        }
      },
      "presets": {
        "name": "Sähköpostirekisteröitymisen banneri"
      }
    },
    "slideshow": {
      "name": "Diaesitys",
      "settings": {
        "layout": {
          "label": "Asettelu",
          "options__1": {
            "label": "Täysi leveys"
          },
          "options__2": {
            "label": "Ruudukko"
          }
        },
        "slide_height": {
          "label": "Dian korkeus",
          "options__1": {
            "label": "Mukauta ensimmäisen kuvan mukaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          }
        },
        "slider_visual": {
          "label": "Sivunumerointityyli",
          "options__1": {
            "label": "Laskuri"
          },
          "options__2": {
            "label": "Pisteet"
          },
          "options__3": {
            "label": "Numerot"
          }
        },
        "auto_rotate": {
          "label": "Käännä diat automaattisesti"
        },
        "change_slides_speed": {
          "label": "Vaihda diat joka"
        },
        "show_text_below": {
          "label": "Näytä kuvan alla oleva sisältö mobiililaitteessa"
        },
        "mobile": {
          "content": "Mobiiliasettelu"
        },
        "accessibility": {
          "content": "Käytettävyys",
          "label": "Diaesityksen kuvaus",
          "info": "Kuvaile diaesitystä näytönlukijoita käyttäviä asiakkaita varten."
        }
      },
      "blocks": {
        "slide": {
          "name": "Dia",
          "settings": {
            "image": {
              "label": "Kuva"
            },
            "heading": {
              "label": "Otsikko"
            },
            "subheading": {
              "label": "Alaotsikko"
            },
            "button_label": {
              "label": "Tekstipainike",
              "info": "Jos haluat piilottaa painikkeen, jätä painikkeen teksti tyhjäksi."
            },
            "link": {
              "label": "Painikelinkki"
            },
            "secondary_style": {
              "label": "Käytä ääriviivallista painiketyyliä"
            },
            "box_align": {
              "label": "Työpöytäsisällön sijainti",
              "options__1": {
                "label": "Ylhäällä vasemmalla"
              },
              "options__2": {
                "label": "Keskellä ylhäällä"
              },
              "options__3": {
                "label": "Ylhäällä oikealla"
              },
              "options__4": {
                "label": "Keskellä vasemmalla"
              },
              "options__5": {
                "label": "Keskellä"
              },
              "options__6": {
                "label": "Keskellä oikealla"
              },
              "options__7": {
                "label": "Alhaalla vasemmalla"
              },
              "options__8": {
                "label": "Keskellä alhaalla"
              },
              "options__9": {
                "label": "Alhaalla oikealla"
              },
              "info": "Sijainti optimoidaan automaattisesti mobiililaitteille."
            },
            "show_text_box": {
              "label": "Näytä säilö pöytäkoneella"
            },
            "text_alignment": {
              "label": "Työpöydän sisällön kohdistus",
              "option_1": {
                "label": "Vasemmalla"
              },
              "option_2": {
                "label": "Keskitetty"
              },
              "option_3": {
                "label": "Oikealla"
              }
            },
            "image_overlay_opacity": {
              "label": "Peittokuvan läpikuultavuus"
            },
            "color_scheme": {
              "info": "Näkyvillä, kun säilö on esillä."
            },
            "text_alignment_mobile": {
              "label": "Mobiilisisällön tasaus",
              "options__1": {
                "label": "Vasemmalla"
              },
              "options__2": {
                "label": "Keskitetty"
              },
              "options__3": {
                "label": "Oikealla"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Diaesitys"
      }
    },
    "collapsible_content": {
      "name": "Pienenettävä sisältö",
      "settings": {
        "caption": {
          "label": "Kuvateksti"
        },
        "heading": {
          "label": "Otsikko"
        },
        "heading_alignment": {
          "label": "Otsikon tasaus",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          }
        },
        "layout": {
          "label": "Asettelu",
          "options__1": {
            "label": "Ei säiliötä"
          },
          "options__2": {
            "label": "Rivisäiliö"
          },
          "options__3": {
            "label": "Osiosäiliö"
          }
        },
        "open_first_collapsible_row": {
          "label": "Avaa ensimmäinen pienenettävä rivi"
        },
        "header": {
          "content": "Kuvan asettelu"
        },
        "image": {
          "label": "Kuva"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Suuri"
          }
        },
        "desktop_layout": {
          "label": "Pöytäkoneen asettelu",
          "options__1": {
            "label": "Ensimmäinen kuva"
          },
          "options__2": {
            "label": "Toinen kuva"
          },
          "info": "Kuva on aina ensisijainen mobiilissa."
        },
        "container_color_scheme": {
          "label": "Säiliön värimalli",
          "info": "Näkyy, kun asetteluna on rivi- tai osiosäiliö."
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Pienenettävä rivi",
          "settings": {
            "heading": {
              "info": "Lisää sisältöä kuvaava otsikko.",
              "label": "Otsikko"
            },
            "row_content": {
              "label": "Rivin sisältö"
            },
            "page": {
              "label": "Rivin sisältö sivulta"
            },
            "icon": {
              "label": "Kuvake",
              "options__1": {
                "label": "Ei yhtään"
              },
              "options__2": {
                "label": "Omena"
              },
              "options__3": {
                "label": "Banaani"
              },
              "options__4": {
                "label": "Pullo"
              },
              "options__5": {
                "label": "Laatikko"
              },
              "options__6": {
                "label": "Porkkana"
              },
              "options__7": {
                "label": "Keskustelukupla"
              },
              "options__8": {
                "label": "Valintamerkki"
              },
              "options__9": {
                "label": "Leikepöytä"
              },
              "options__10": {
                "label": "Maitotuote"
              },
              "options__11": {
                "label": "Maidoton"
              },
              "options__12": {
                "label": "Kuivain"
              },
              "options__13": {
                "label": "Silmä"
              },
              "options__14": {
                "label": "Tuli"
              },
              "options__15": {
                "label": "Gluteeniton"
              },
              "options__16": {
                "label": "Sydän"
              },
              "options__17": {
                "label": "Silitysrauta"
              },
              "options__18": {
                "label": "Lehti"
              },
              "options__19": {
                "label": "Nahka"
              },
              "options__20": {
                "label": "Salama"
              },
              "options__21": {
                "label": "Huulipuna"
              },
              "options__22": {
                "label": "Lukko"
              },
              "options__23": {
                "label": "Karttamerkki"
              },
              "options__24": {
                "label": "Pähkinätön"
              },
              "options__25": {
                "label": "Housut"
              },
              "options__26": {
                "label": "Tassun jälki"
              },
              "options__27": {
                "label": "Pippuri"
              },
              "options__28": {
                "label": "Tuoksu"
              },
              "options__29": {
                "label": "Lentokone"
              },
              "options__30": {
                "label": "Kasvi"
              },
              "options__31": {
                "label": "Hintalappu"
              },
              "options__32": {
                "label": "Kysymysmerkki"
              },
              "options__33": {
                "label": "Kierrätys"
              },
              "options__34": {
                "label": "Palaa"
              },
              "options__35": {
                "label": "Viivain"
              },
              "options__36": {
                "label": "Tarjoiluastia"
              },
              "options__37": {
                "label": "Paita"
              },
              "options__38": {
                "label": "Kenkä"
              },
              "options__39": {
                "label": "Siluetti"
              },
              "options__40": {
                "label": "Lumihiutale"
              },
              "options__41": {
                "label": "Tähti"
              },
              "options__42": {
                "label": "Sekuntikello"
              },
              "options__43": {
                "label": "Kuljetusajoneuvo"
              },
              "options__44": {
                "label": "Pesu"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Pienenettävä sisältö"
      }
    },
    "main-account": {
      "name": "Tili"
    },
    "main-activate-account": {
      "name": "Tilin aktivointi"
    },
    "main-addresses": {
      "name": "Osoitteet"
    },
    "main-login": {
      "name": "Kirjautuminen"
    },
    "main-order": {
      "name": "Tilaus"
    },
    "main-register": {
      "name": "Rekisteröinti"
    },
    "main-reset-password": {
      "name": "Salasanan nollaus"
    },
    "related-products": {
      "name": "Liittyvät tuotteet",
      "settings": {
        "heading": {
          "label": "Otsikko"
        },
        "products_to_show": {
          "label": "Näytettävien tuotteiden enimmäismäärä"
        },
        "columns_desktop": {
          "label": "Sarakkeiden määrä työpöydällä"
        },
        "paragraph__1": {
          "content": "Dynaamisissa suosituksissa käytetään tilaus- ja tuotetietoja, jotta suositukset muuttuvat ja paranevat ajan myötä. [Lisätietoja](https://help.shopify.com/themes/development/recommended-products)"
        },
        "header__2": {
          "content": "Tuotekortti"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Näytä myyjä"
        },
        "show_rating": {
          "label": "Näytä tuotteen luokitus",
          "info": "Näytä luokitus lisäämällä tuotearviointisovellus. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        },
        "columns_mobile": {
          "label": "Sarakkeiden määrä mobiililaitteessa",
          "options__1": {
            "label": "1 sarake"
          },
          "options__2": {
            "label": "2 saraketta"
          }
        }
      }
    },
    "multirow": {
      "name": "Monirivinen",
      "settings": {
        "image": {
          "label": "Kuva"
        },
        "image_height": {
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          },
          "label": "Kuvan korkeus"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Pieni"
          },
          "options__2": {
            "label": "Keskisuuri"
          },
          "options__3": {
            "label": "Suuri"
          },
          "label": "Työpöytäkuvan leveys",
          "info": "Kuva optimoidaan automaattisesti mobiililaitteille."
        },
        "heading_size": {
          "options__1": {
            "label": "Pieni"
          },
          "options__2": {
            "label": "Keskisuuri"
          },
          "options__3": {
            "label": "Suuri"
          },
          "label": "Otsikon koko"
        },
        "text_style": {
          "options__1": {
            "label": "Leipäteksti"
          },
          "options__2": {
            "label": "Alaotsikko"
          },
          "label": "Tekstityyli"
        },
        "button_style": {
          "options__1": {
            "label": "Yhtenäinen painike"
          },
          "options__2": {
            "label": "Kehyspainike"
          },
          "label": "Painikkeen tyyli"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Työpöytäsisällön tasaus"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Ylös"
          },
          "options__2": {
            "label": "Keskelle"
          },
          "options__3": {
            "label": "Alas"
          },
          "label": "Työpöytäsisällön sijainti",
          "info": "Sijainti optimoidaan automaattisesti mobiililaitteille."
        },
        "image_layout": {
          "options__1": {
            "label": "Vuorottelu vasemmalta"
          },
          "options__2": {
            "label": "Vuorottelu oikealta"
          },
          "options__3": {
            "label": "Tasattu vasemmalle"
          },
          "options__4": {
            "label": "Tasattu oikealle"
          },
          "label": "Työpöytäkuvan sijoitus",
          "info": "Sijoitus optimoidaan automaattisesti mobiililaitteille."
        },
        "container_color_scheme": {
          "label": "Säiliön värimalli"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Mobiilisisällön tasaus"
        },
        "header_mobile": {
          "content": "Mobiilipohja"
        }
      },
      "blocks": {
        "row": {
          "name": "Rivi",
          "settings": {
            "image": {
              "label": "Kuva"
            },
            "caption": {
              "label": "Kuvateksti"
            },
            "heading": {
              "label": "Otsikko"
            },
            "text": {
              "label": "Teksti"
            },
            "button_label": {
              "label": "Tekstipainike"
            },
            "button_link": {
              "label": "Painikelinkki"
            }
          }
        }
      },
      "presets": {
        "name": "Monirivinen"
      }
    }
  }
}
