{{ 'template-collection.css' | asset_url | stylesheet_tag }}

{%- if section.settings.enable_quick_add -%}
  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="section-{{ section.id }}-padding animate-section animate--hidden">
  {% comment %} Sort is the first tabbable element when filter type is vertical {% endcomment %}
  {%- if section.settings.enable_sorting and section.settings.filter_type == 'vertical' -%}
    <facet-filters-form class="facets facets-vertical-sort page-width small-hide no-js-hidden">
      <form class="facets-vertical-form" id="FacetSortForm">
        <div class="facet-filters sorting caption">
          <div class="facet-filters__field">
            <h2 class="facet-filters__label caption-large text-body">
              <label for="SortBy">{{ 'products.facets.sort_by_label' | t }}</label>
            </h2>
            <div class="select">
              {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
              <select
                name="sort_by"
                class="facet-filters__sort select__select caption-large"
                id="SortBy"
                aria-describedby="a11y-refresh-page-message"
              >
                {%- for option in collection.sort_options -%}
                  <option
                    value="{{ option.value | escape }}"
                    {% if option.value == sort_by %}
                      selected="selected"
                    {% endif %}
                  >
                    {{ option.name | escape }}
                  </option>
                {%- endfor -%}
              </select>
              {% render 'icon-caret' %}
            </div>
          </div>
          <noscript>
            <button type="submit" class="facets__button-no-js button button--secondary">
              {{ 'products.facets.sort_button' | t }}
            </button>
          </noscript>
        </div>

        <div class="product-count-vertical light" role="status">
          <h2 class="product-count__text text-body">
            <span id="ProductCountDesktop">
              {%- if collection.results_count -%}
                {{
                  'templates.search.results_with_count'
                  | t: terms: collection.terms, count: collection.results_count
                }}
              {%- elsif collection.products_count == collection.all_products_count -%}
                {{ 'products.facets.product_count_simple' | t: count: collection.products_count }}
              {%- else -%}
                {{
                  'products.facets.product_count'
                  | t: product_count: collection.products_count, count: collection.all_products_count
                }}
              {%- endif -%}
            </span>
          </h2>
          <div class="loading-overlay__spinner">
            <svg
              aria-hidden="true"
              focusable="false"
              class="spinner"
              viewBox="0 0 66 66"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
            </svg>
          </div>
        </div>
      </form>
    </facet-filters-form>
  {%- endif -%}

  <div class="{% if section.settings.filter_type == 'vertical' %} facets-vertical page-width{% endif %}">
    {{ 'component-facets.css' | asset_url | stylesheet_tag }}
    <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
    {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
      <aside
        aria-labelledby="verticalTitle"
        class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
        id="main-collection-filters"
        data-id="{{ section.id }}"
      >
        {% render 'facets',
          results: collection,
          enable_filtering: section.settings.enable_filtering,
          enable_sorting: section.settings.enable_sorting,
          filter_type: section.settings.filter_type
        %}
      </aside>
    {%- endif -%}

    <div class="product-grid-container" id="ProductGridContainer">
      {%- paginate collection.products by section.settings.products_per_page -%}
        {%- if collection.products.size == 0 -%}
          <div class="collection collection--empty page-width" id="product-grid" data-id="{{ section.id }}">
            <div class="loading-overlay gradient"></div>
            <div class="title-wrapper center">
              <h2 class="title title--primary">
                {{ 'sections.collection_template.empty' | t -}}
                <br>
                {{
                  'sections.collection_template.use_fewer_filters_html'
                  | t: link: collection.url, class: 'underlined-link link'
                }}
              </h2>
            </div>
          </div>
        {%- else -%}
          <div class="collection{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}">
            <div class="loading-overlay gradient"></div>

            <ul
              id="product-grid"
              data-id="{{ section.id }}"
              class="
                grid product-grid grid--{{ section.settings.columns_mobile }}-col-tablet-down{% if section.settings.stretch_cards %} grid--stretch{% endif %}
                grid--{{ section.settings.columns_desktop }}-col-desktop
              "
            >
              {%- for product in collection.products -%}
                {% liquid
                  assign lazy_load = false
                  if forloop.index > 2
                    assign lazy_load = true
                  endif
                  
                  assign hidden = false
                  if product.tags contains 'collection-hidden'
                    assign hidden = true
                  endif
                %}
                {% unless hidden %}
                  <li class="grid__item animate-item animate-item--child" style='--index: {{ forloop.index0 }};'>
                    {% render 'card-product',
                      card_product: product,
                      media_aspect_ratio: section.settings.image_ratio,
                      show_secondary_image: section.settings.show_secondary_image,
                      badges: section.settings.badges,
                      show_vendor: section.settings.show_vendor,
                      show_rating: section.settings.show_rating,
                      lazy_load: lazy_load,
                      show_quick_add: section.settings.enable_quick_add,
                      section_id: section.id
                    %}
                  </li>
                {% endunless %}
              {%- endfor -%}
            </ul>

            {%- if paginate.pages > 1 -%}
              {% render 'pagination', paginate: paginate, anchor: '' %}
            {%- endif -%}
          </div>
        {%- endif -%}
      {%- endpaginate -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-collection-product-grid.name",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "products_per_page",
      "min": 8,
      "max": 48,
      "step": 4,
      "default": 16,
      "label": "t:sections.main-collection-product-grid.settings.products_per_page.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "t:sections.main-collection-product-grid.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "stretch_cards",
      "default": false,
      "label": "Make all cards same height"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__3.content"
    },
    {
      "type": "paragraph",
      "content": "Add a \"collection-hidden\" tag to the products you want to hide from the list."
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
    },
    {
      "type": "select",
      "id": "badges",
      "options": [
        {
          "value": "disabled",
          "label": "Disabled"
        },
        {
          "value": "regular",
          "label": "Regular sale"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "regular",
      "label": "Badges",
      "info": "Adjust custom badges in Theme settings > Product cards."
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_rating.label",
      "info": "t:sections.main-collection-product-grid.settings.show_rating.info"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_add",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.enable_quick_buy.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__1.label"
        },
        {
          "value": "vertical",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__3.label"
        }
      ],
      "default": "horizontal",
      "label": "t:sections.main-collection-product-grid.settings.filter_type.label",
      "info": "t:sections.main-collection-product-grid.settings.filter_type.info"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.main-collection-product-grid.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
