<section
  id="MainProduct-{{ section.id }}"
  class="page-width section-{{ section.id }}-padding"
  data-section="{{ section.id }}"
  data-product-tags="{{ product.tags | join: ',' }}"
>
  {%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.mobile_padding_top }}px;
      padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.desktop_padding_top }}px;
        padding-bottom: {{ section.settings.desktop_padding_bottom }}px;
      }
    }

    #MediaGallery-{{ section.id }} {
      --desktop-thumbnails: {{ section.settings.desktop_thumbnails_count }};
      --mobile-thumbnails: {{ section.settings.mobile_thumbnails_count }};
    }
    @media screen and (max-width: 749px) {
      #MediaGallery-{{ section.id }} {
        --media-radius: {{ section.settings.mobile_media_corner_radius }}px;
        --grid-mobile-horizontal-spacing: {{ section.settings.mobile_spacing_pixels }}px;
        --mobile-scroll-padding: calc({{ section.settings.mobile_scroll_padding_percentage }}% + {{ section.settings.mobile_scroll_padding_pixels }}px);
        --slide-container-percentage-width: {{ section.settings.mobile_slides_container_width }}%;
        --slide-inner-percentage-width: {{ section.settings.mobile_slides_inner_width }}%;
      }
    }
  {%- endstyle -%}

  {% if section.settings.image_zoom == 'hover' %}
    <script id="EnableZoomOnHover-main" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
  {% endif %}
  {%- if request.design_mode -%}
    <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
  {%- if first_3d_model -%}
    {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
    <link
      id="ModelViewerStyle"
      rel="stylesheet"
      href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
      media="print"
      onload="this.media='all'"
    >
    <link
      id="ModelViewerOverride"
      rel="stylesheet"
      href="{{ 'component-model-viewer-ui.css' | asset_url }}"
      media="print"
      onload="this.media='all'"
    >
  {%- endif -%}

  {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}

  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}

  {%- liquid
    assign has_filtering = false
    if section.settings.variant_image_filtering != 'none'
      assign has_filtering = true
    endif
  
    assign hide_variants_enabled = false
    if section.settings.hide_variants and has_filtering != true
      assign hide_variants_enabled = true
    endif
  
    if hide_variants_enabled and variant_images.size == product.media.size
      assign single_media_visible = true
    endif
  
    assign media_count = product.media.size
    if hide_variants_enabled and media_count > 1 and variant_images.size > 0
      assign media_count = media_count | minus: variant_images.size | plus: 1
    endif
  
    if media_count == 1 or single_media_visible
      assign single_media_visible_mobile = true
    endif
  
    if media_count == 0 or single_media_visible_mobile or section.settings.mobile_thumbnails == 'show' and media_count < 3
      assign hide_mobile_slider = true
    endif
  -%}
  <div class="product product--{{ section.settings.media_size }} product--{{ section.settings.media_position }} product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %}">
    <div class="grid__item product__media-wrapper{% if section.settings.media_position == 'right' %} medium-hide large-up-hide{% endif %}{% if section.settings.enable_sticky_info %} product__column-sticky{% endif %}">
      {% render 'product-media-gallery', variant_images: variant_images, has_filtering: has_filtering, section %}
    </div>
    <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}{% if section.settings.enable_sticky_info %} product__column-sticky{% endif %}{% if section.settings.mobile_pagination == 'dots_overlay' or hide_mobile_slider %} product__info-wrapper--top-padding{% endif %}">
      <product-info
        id="ProductInfo-{{ section.id }}"
        data-section="{{ section.id }}"
        data-url="{{ product.url }}"
        class="product__info-container main-product__info-container"
      >
        {%- assign product_form_id = 'product-form-' | append: section.id -%}

        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when '@app' -%}
              {% render block %}
            {%- when 'text' -%}
              {% render 'text-with-icon-block', block: block, margins: true, block_attributes: true %}
            {%- when 'title' -%}
              <div class="product__title" {{ block.shopify_attributes }} style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                <h1 class="{{ block.settings.text_size }}{% if block.settings.uppercase_title %} product-title--uppercase{% endif %}{% if block.settings.title_alignment == 'center' %} center{% endif %}">
                  {{ product.title }}
                </h1>
                <a href="{{ product.url }}" class="product__title">
                  <h2 class="h1">
                    {{ product.title }}
                  </h2>
                </a>
              </div>
            {% when 'rating_stars' %}
              {% render 'rating-stars-block', block: block, margins: true, block_attributes: true %}
            {% when 'trustpilot_stars' %}
              {% render 'trustpilot-stars-block', block: block, margins: true, block_attributes: true %}
            {%- when 'price' -%}
              <div class="no-js-hidden product-page-price" id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }} style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                {% liquid 
                  assign price_second = false
                  if block.settings.layout == 'price_second'
                    assign price_second = true
                  endif
                %}
                {%- render 'price',
                  product: product,
                  block: block,
                  use_variant: true,
                  show_badges: true,
                  hide_sale_badge: block.settings.hide_sale_badge,
                  price_class: 'price--large',
                  price_second: price_second,
                  main_price: true
                -%}
              </div>
              <div {{ block.shopify_attributes }}>
                {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                {%- form 'product', product, id: product_form_installment_id, class: 'installment caption-large' -%}
                  <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                  {{ form | payment_terms }}
                {%- endform -%}
              </div>
            {% when 'clickable_discount' %}
              <div style='--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;' {{ block.shopify_attributes }}>
                {% if block.settings.discount_code != blank %}
                  <clickable-discount class='clickable-discount' data-applied='false' data-code="{{ block.settings.discount_code }}" data-loading="false" data-error='false' style='--mobile-text-size: {{ block.settings.mobile_text_size | divided_by: 10.0 }}rem;--desktop-text-size: {{ block.settings.desktop_text_size | divided_by: 10.0 }}rem;--padding:{{ block.settings.padding | divided_by: 7.0 }}em;--corner-radius:{{ block.settings.corner_radius | divided_by: 100.0 }}em;--alignment:{{ block.settings.alignment }};'>
                    <button class='clickable-discount__btn'>
                      <div class='clickable-discount__btn__text-1 product__text-container product__text-container--{{ block.settings.alignment }}{% if block.settings.enable_bg %} product__text-container--background{% endif %}'  style='--bg-color:{{ block.settings.bg_color_1 }};--icon-scale:{{ block.settings.icon_scale | divided_by: 100.0 }};' >
                        <p class="product__text product__text-{{ block.settings.alignment }}" style="--text-color:{{ block.settings.text_color_1 }};--icon-color:{{ block.settings.icon_color_1 }};">
                          {% if block.settings.custom_icon_1 != blank %}
                            <img
                              src="{{ block.settings.custom_icon_1 | image_url }}"
                              alt="{{ block.settings.custom_icon_1.alt }}"
                              width='auto'
                              height='auto'
                              loading='lazy'
                            >
                          {% elsif block.settings.icon_1 != blank %}
                            {% render 'material-icon', icon: block.settings.icon_1, filled: block.settings.filled_icon_1 %}
                          {% endif %}
                          <span class='product__text__text'>{{- block.settings.text_1 -}}</span>
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            class="spinner"
                            viewBox="0 0 66 66"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                          </svg>
                        </p>
                      </div>
                      <div class='clickable-discount__btn__text-2 product__text-container product__text-container--{{ block.settings.alignment }}{% if block.settings.enable_bg %} product__text-container--background{% endif %}'  style='--bg-color:{{ block.settings.bg_color_2 }};--icon-scale:{{ block.settings.icon_scale | divided_by: 100.0 }};' >
                        <p class="product__text product__text-{{ block.settings.alignment }}" style="--text-color:{{ block.settings.text_color_2 }};--icon-color:{{ block.settings.icon_color_2 }};">
                          {% if block.settings.custom_icon_2 != blank %}
                            <img
                              src="{{ block.settings.custom_icon_2 | image_url }}"
                              alt="{{ block.settings.custom_icon_2.alt }}"
                              width='auto'
                              height='auto'
                              loading='lazy'
                            >
                          {% elsif block.settings.icon_2 != blank %}
                            {% render 'material-icon', icon: block.settings.icon_2, filled: block.settings.filled_icon_2 %}
                          {% endif %}
                          <span class='product__text__text'>{{- block.settings.text_2 -}}</span>
                        </p>
                      </div>
                    </button>
                  </clickable-discount>
                  <p class='clickable-discount__error'>An error has occured. Please try again.</p>
                {% else %}
                  <h4>Enter a discount code in block settings</h4>
                {% endif %}
              </div>
            {%- when 'inventory' -%}
              <p
                class="product__inventory no-js-hidden{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                {{ block.shopify_attributes }}
                id="Inventory-{{ section.id }}"
                role="status"
                style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
              >
                {%- if product.selected_or_first_available_variant.inventory_quantity > 0 -%}
                  {%- if product.selected_or_first_available_variant.inventory_quantity <= block.settings.inventory_threshold -%}
                    <svg width="15" height="15" aria-hidden="true">
                      <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(238,148,65, 0.3)"/>
                      <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(238,148,65)"/>
                    </svg>
                    {%- if block.settings.show_inventory_quantity -%}
                      {{- 'products.product.inventory_low_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                    {%- else -%}
                      {{- 'products.product.inventory_low_stock' | t -}}
                    {%- endif -%}
                  {%- else -%}
                    <svg width="15" height="15" aria-hidden="true">
                      <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(62,214,96, 0.3)"/>
                      <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(62,214,96)"/>
                    </svg>
                    {%- if block.settings.show_inventory_quantity -%}
                      {{- 'products.product.inventory_in_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                    {%- else -%}
                        {{- 'products.product.inventory_in_stock' | t -}}
                    {%- endif -%}
                  {%- endif -%}
                {%- else -%}
                  {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' -%}
                    <svg width="15" height="15" aria-hidden="true">
                      <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(62,214,96, 0.3)"/>
                      <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(62,214,96)"/>
                    </svg>
                    {{- 'products.product.inventory_out_of_stock_continue_selling' | t -}}
                  {%- else -%}
                    <svg width="15" height="15" aria-hidden="true">
                      <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(200,200,200, 0.3)"/>
                      <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(200,200,200)"/>
                    </svg>
                    {{- 'products.product.inventory_out_of_stock' | t -}}
                  {%- endif -%}
                {%- endif -%}
              </p>
            {%- when 'description' -%}
              {%- if product.description != blank -%}
                <div class="product__description rte quick-add-hidden" {{ block.shopify_attributes }} style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                  {{ product.description }}
                </div>
              {%- endif -%}
            {%- when 'sku' -%}
              <p
                class="product__sku no-js-hidden{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}"
                id="Sku-{{ section.id }}"
                role="status"
                {{ block.shopify_attributes }}
                style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
              >
                <span class="visually-hidden">{{ 'products.product.sku' | t }}:</span> {{- product.selected_or_first_available_variant.sku -}}
              </p>
            {%- when 'custom_liquid' -%}
              {{ block.settings.custom_liquid }}
            {%- when 'collapsible_tab' -%}
              <div class="product__accordion accordion accordion--{{ block.settings.heading_size }}{% if block.settings.display_top_border %} accordion--top-border{% endif %} quick-add-hidden" style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;" {{ block.shopify_attributes }}>
                <details class='accordion__details'{% if block.settings.open %} open{% endif %}>
                  <summary class="accordion__summary">
                    <div class="summary__title">
                      {% if block.settings.custom_icon != blank %}
                        <img
                          src="{{ block.settings.custom_icon | image_url }}"
                          {% if block.settings.custom_icon.alt != blank %}
                            alt="{{ block.settings.custom_icon.alt | escape }}"
                          {% else %}
                            role="presentation"
                          {% endif %}
                          height="auto"
                          width="auto"
                          loading="lazy"
                        >
                      {% else %}
                        {% render 'material-icon', icon: block.settings.icon, filled: block.settings.filled_icon %}
                      {% endif %}
                      <h2 class="h4 accordion__title">
                        {{ block.settings.heading | default: block.settings.page.title }}
                      </h2>
                    </div>
                    {% if block.settings.collapse_icon == 'carret' %}
                      {% render 'icon-caret' %}
                    {% else %}
                      {% render 'icon-plus' %}
                    {% endif %}
                  </summary>
                </details>
                <div class='accordion__content-wrapper'>
                  <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                    {{ block.settings.content | replace: '[description]', product.description }}
                    {{ block.settings.page.content }}
                  </div>
                </div>
              </div>
            {%- when 'quantity_selector' -%}
              {%- if block.settings.enable_quantity_discounts -%}
                {% render 'quantity-breaks', block: block, product: product, product_form_id: product_form_id %}
              {%- else -%}
                {% liquid
                  assign quantity_atc_append =  block.settings.atc_append
                  assign quantity_atc_append_heights =  block.settings.atc_append_heights
                %}
                {% capture quantity_selector_html %}
                  <div
                    id="Quantity-Form-{{ section.id }}"
                    class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
                    {{ block.shopify_attributes }}
                    style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                  >
                    {% comment %} TODO: enable theme-check once `item_count_for_variant` is accepted as valid filter {% endcomment %}
                    {% # theme-check-disable %}
                    {%- assign cart_qty = cart | item_count_for_variant: product.selected_or_first_available_variant.id -%}
                    {% # theme-check-enable %}
                    <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                      {{ 'products.product.quantity.label' | t }}
                      <span class="quantity__rules-cart no-js-hidden{% if cart_qty == 0 %} hidden{% endif %}">
                        <span class="loading-overlay hidden">
                          <span class="loading-overlay__spinner">
                            <svg
                            aria-hidden="true"
                            focusable="false"
                            class="spinner"
                            viewBox="0 0 66 66"
                            xmlns="http://www.w3.org/2000/svg"
                            >
                            <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                            </svg>
                          </span>
                        </span>
                        <span>({{- 'products.product.quantity.in_cart_html' | t: quantity: cart_qty -}})</span>
                      </span>
                    </label>
                    <quantity-input class="quantity main-quantity{% if block.settings.full_width_classic and quantity_atc_append == 'none' %} quantity--full{% endif %} no-background color-{{ settings.quantity_color_scheme }} accent-color-{{ settings.quantity_overlay_color }} accent-2-color-{{ settings.quantity_text_color }}" data-section="{{ section.id }}">
                      <button class="quantity__button no-js-hidden" name="minus" type="button">
                        <span class="visually-hidden">
                        {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                        </span>
                        {% render 'icon-minus' %}
                      </button>
                      <input
                        class="quantity__input"
                        type="number"
                        name="quantity"
                        id="Quantity-{{ section.id }}"
                        data-cart-quantity="{{ cart_qty }}"
                        data-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                        min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                        {% if product.selected_or_first_available_variant.quantity_rule.max != null %}
                        data-max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                        max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                        {% endif %}
                        step="{{ product.selected_or_first_available_variant.quantity_rule.increment }}"
                        value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                        form="{{ product_form_id }}"
                      />
                      <button class="quantity__button no-js-hidden" name="plus" type="button">
                        <span class="visually-hidden">
                        {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                        </span>
                        {% render 'icon-plus' %}
                      </button>
                    </quantity-input>
                    <div class="quantity__rules caption no-js-hidden">
                      {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                        <span class="divider">
                          {{-
                            'products.product.quantity.multiples_of'
                            | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment
                          -}}
                        </span>
                      {%- endif -%}
                      {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                        <span class="divider">
                          {{-
                            'products.product.quantity.minimum_of'
                            | t: quantity: product.selected_or_first_available_variant.quantity_rule.min
                          -}}
                        </span>
                      {%- endif -%}
                      {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                        <span class="divider">
                          {{-
                            'products.product.quantity.maximum_of'
                            | t: quantity: product.selected_or_first_available_variant.quantity_rule.max
                          -}}
                        </span>
                      {%- endif -%}
                    </div>
                  </div>
                {% endcapture %}
                {% if quantity_atc_append == 'none' %}
                  {{ quantity_selector_html }}
                {% endif %}
              {%- endif -%}
            {%- when 'sticky_atc' -%}
              {% if block.settings.enable_custom_btn_color %}
                {% style %}
                  #sticky-atc-{{ section.id }} .button {
                    --color-button: {{ block.settings.custom_btn_color.red }}, {{ block.settings.custom_btn_color.green }}, {{ block.settings.custom_btn_color.blue }};
                  }
                {% endstyle %}
              {% endif %}
              <sticky-atc 
                id="sticky-atc-{{ section.id }}"
                class="sticky-atc color-{{ block.settings.color_scheme }}{% unless block.settings.mobile_show_sale_badge %} sticky-atc--mobile-no-badge{% endunless %}{% unless block.settings.desktop_show_sale_badge %} sticky-atc--desktop-no-badge{% endunless %}{% if block.settings.mobile_transparent_bg %} sticky-atc--mobile-transparent{% endif %}{% if block.settings.desktop_transparent_bg %} sticky-atc--desktop-transparent{% endif %}{% if block.settings.mobile_show_title or block.settings.mobile_show_price %} sticky-atc--small-mobile-select{% endif %}" 
                data-section="{{ section.id }}"
                {%- if block.settings.display_when == 'after_scroll' -%}
                  data-after-scroll="true"
                {%- endif -%}
                {%- if block.settings.function != 'add_to_cart' -%}
                  data-scroll-btn="true"
                  data-scroll-destination="{{ block.settings.function | remove: 'scroll_' }}"
                {%- endif -%}
                {{ block.shopify_attributes }}
              >
                <div class='sticky-atc-container page-width{% if block.settings.desktop_full_button_width %} sticky-atc--desktop-btn-full{% endif %}{% if block.settings.mobile_full_button_width %} sticky-atc--mobile-btn-full{% endif %}'>
                  {%- if block.settings.mobile_show_image or block.settings.desktop_show_image -%}
                    <div class='sticky-atc__image{% unless block.settings.mobile_show_image %} mobile-hidden{% endunless %}{% unless block.settings.desktop_show_image %} desktop-hidden{% endunless %}'>
                      <img
                        id="sticky-atc-image-{{ section.id }}"
                        src="{{ product.featured_image | image_url: width: 500 }}"
                        alt="{{ product.featured_image.alt | escape }}"
                        loading="lazy"
                        width="auto"
                        height="auto"
                      >
                    </div>
                  {%- endif -%}
                  <div class='sticky-atc__left'>
                    <div class='sticky-atc__left__content'>
                      {% if block.settings.mobile_show_title or block.settings.desktop_show_title %}
                        <h4 class='sticky-atc__title{% unless block.settings.mobile_show_title %} mobile-hidden{% endunless %}{% unless block.settings.desktop_show_title %} desktop-hidden{% endunless %}'>
                          {{ product.title }}
                        </h4>
                      {% endif %}
                      {% if block.settings.mobile_rating_stars or block.settings.desktop_rating_stars %}
                        <div class='rating-stars-and-text font-size--desktop-auto flex-center{% unless block.settings.mobile_rating_stars %} mobile-hidden{% endunless %}{% unless block.settings.desktop_rating_stars %} desktop-hidden{% endunless %}' style='--bg-star-color:{{ block.settings.star_color }};--font-size:1.3rem;--alignment:flex-start;'>
                          <div class='rating-stars__container rating-stars__container--underlay'>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" fill='currentColor'>
                              <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" fill='currentColor'>
                              <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" fill='currentColor'>
                              <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" fill='currentColor'>
                              <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" fill='currentColor'>
                              <path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/>
                            </svg>
                          </div>
                          <span class="rating-stars__label">&nbsp;{{ block.settings.stars_label }}</span>
                        </div>
                      {% endif %}
                      {%- if block.settings.mobile_show_price or block.settings.desktop_show_price -%}
                        <div class='sticky-atc__price{% unless block.settings.mobile_show_price %} mobile-hidden{% endunless %}{% unless block.settings.desktop_show_price %} desktop-hidden{% endunless %}' id="sticky-atc-separate-price-{{ section.id }}">
                          {%- render 'price',
                            product: product,
                            use_variant: true,
                            show_badges: true,
                            price_class: '',
                            hide_currency_code: true
                          -%}
                        </div>
                      {%- endif -%}
                    </div>
                    {%- if block.settings.mobile_variant_picker or block.settings.desktop_variant_picker -%}
                      {% unless product.has_only_default_variant %}
                        <secondary-variant-select{% if block.settings.picker_type == 'separate' %}-separate{% endif %}
                          id="StickyAtcVariantPicker-{{ section.id }}"
                          class="sticky-atc__picker--{{ block.settings.picker_type }} no-js-hidden{% unless block.settings.mobile_variant_picker %} mobile-hidden{% endunless %}{% unless block.settings.desktop_variant_picker %} desktop-hidden{% endunless %}"
                          data-section="{{ section.id }}"
                          data-url="{{ product.url }}"
                          data-update-url="true"
                          style='--options-count: {{ product.options.size }}'
                        >
                          {% if block.settings.picker_type == 'combined' %}
                            <div class="product-form__input">
                              <div class="select no-background color-{{ settings.pickers_color_scheme }} accent-color-{{ settings.pickers_overlay_color }} accent-2-color-{{ settings.pickers_text_color }}">
                                <select class="sticky-atc__variant-select select__select variant-dropdown">
                                  {% for variant in product.variants %}
                                    {%- comment -%}
                                      Hide Bundle variants in sticky add-to-cart (case-insensitive)
                                    {%- endcomment -%}
                                    {%- assign option1_clean = variant.option1 | default: '' | strip | downcase -%}
                                    {%- assign option2_clean = variant.option2 | default: '' | strip | downcase -%}
                                    {%- assign option3_clean = variant.option3 | default: '' | strip | downcase -%}
                                    {%- unless option1_clean == 'bundle' or option2_clean == 'bundle' or option3_clean == 'bundle' -%}
                                      <option
                                        value='{{ variant.options | join: ',' | escape }}'
                                        data-options='{{ variant.options | join: ',' }}'
                                        {% if product.selected_or_first_available_variant.id == variant.id %}
                                          selected
                                        {% endif %}
                                        {% unless variant.available %}
                                          disabled
                                        {% endunless %}
                                      >
                                        {{ variant.title }}
                                      </option>
                                    {%- endunless -%}
                                  {% endfor %}
                                </select>
                                {% render 'icon-caret' %}
                              </div>
                            </div>
                          {% else %}
                            {%- for option in product.options_with_values -%}
                              {%- comment -%}
                                Hide "Purchase Type" option in sticky add-to-cart separate picker
                                Case-insensitive matching for better compatibility
                              {%- endcomment -%}
                              {%- assign option_name_clean = option.name | strip | downcase -%}
                              {%- unless option_name_clean == 'purchase type' -%}
                                <div
                                  class="product-form__input product-form__input--dropdown"
                                >
                                <div class="visually-hidden product-form__input__type" data-type="dropdown">&nbsp</div>
                                <div class="select no-background color-{{ settings.pickers_color_scheme }} accent-color-{{ settings.pickers_overlay_color }} accent-2-color-{{ settings.pickers_text_color }}">
                                  <select
                                    id="Option-{{ section.id }}-{{ forloop.index0 }}"
                                    class="select__select"
                                    name="options[{{ option.name | escape }}]"
                                    form="{{ product_form_id }}"
                                  >
                                    {%- liquid
                                      assign variants_available_arr = product.variants | map: 'available'
                                      assign variants_option1_arr = product.variants | map: 'option1'
                                      assign variants_option2_arr = product.variants | map: 'option2'
                                      assign variants_option3_arr = product.variants | map: 'option3'
                                    -%}
                                    {%- for value in option.values -%}
                                      {%- liquid
                                        assign option_disabled = true
                                        assign currentVariant = nil
                                    
                                        for variant in product.variants
                                          case option.position
                                            when 1
                                              if variant.option1 == value
                                                assign currentVariant = variant
                                                if variant.available
                                                  assign option_disabled = false
                                                endif
                                                break
                                              endif
                                            when 2
                                              if variant.option1 == product.selected_or_first_available_variant.option1 and variant.option2 == value
                                                assign currentVariant = variant
                                                if variant.available
                                                  assign option_disabled = false
                                                endif
                                                break
                                              endif
                                            when 3
                                              if variant.option1 == product.selected_or_first_available_variant.option1 and variant.option2 == product.selected_or_first_available_variant.option2 and variant.option3 == value
                                                assign currentVariant = variant
                                                if variant.available
                                                  assign option_disabled = false
                                                endif
                                                break
                                              endif
                                          endcase
                                        endfor
                                      -%}
                                      <option
                                        value="{{ value | escape }}"
                                        {% if option.selected_value == value %}
                                          selected="selected"
                                        {% endif %}
                                      >
                                        {% if option_disabled -%}
                                          {{- 'products.product.value_unavailable' | t: option_value: value -}}
                                        {%- else -%}
                                          {{- value -}}
                                        {%- endif %}
                                      </option>
                                    {%- endfor -%}
                                  </select>
                                  {% render 'icon-caret' %}
                                </div>
                              </div>
                              {%- endunless -%}
                            {%- endfor -%}
                          {% endif %}
                          <script type="application/json">
                            {{ product.variants | json }}
                          </script>
                        </secondary-variant-select{% if block.settings.picker_type == 'separate' %}-separate{% endif %}>
                      {% endunless %}
                    {%- endif -%}
                  </div>
                  <div class='sticky-atc__button'>
                    {%- if block.settings.function == 'add_to_cart' -%}
                      {%- liquid
                        assign check_against_inventory = true
                        if product.selected_or_first_available_variant.inventory_management != 'shopify' or  product.selected_or_first_available_variant.inventory_policy == 'continue'
                        assign check_against_inventory = false
                        endif
                        if product.selected_or_first_available_variant.quantity_rule.min > product.selected_or_first_available_variant.inventory_quantity and check_against_inventory
                        assign quantity_rule_soldout = true
                        endif
                      -%}
                      <button
                        id='SectionAtcBtn-{{ section.id }}' 
                        type="button"
                        class="button main-product-atc button--has-spinner"
                        form="{{ product_form_id }}"
                        {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                          disabled
                        {% endif %}
                      >
                        <span class="sticky-atc__error" style="display:none;"></span>
                        <span class="sticky-atc__label">
                          {%- if product.selected_or_first_available_variant.available == false or quantity_rule_soldout -%}
                            {{ 'products.product.sold_out' | t }}
                          {%- else -%}
                            {% if block.settings.mobile_show_price_in_button or block.settings.desktop_show_price_in_button %}
                              <span id="sticky-atc-price-{{ section.id }}" class="{% unless block.settings.mobile_show_price_in_button %} mobile-hidden{% endunless %}{% unless block.settings.desktop_show_price_in_button %} desktop-hidden{% endunless %}">
                                {{ product.selected_or_first_available_variant.price | money }} •
                              </span>
                            {% endif %}
                            <span class="sticky-atc__label__text">{{ block.settings.button_label }}</span>
                          {%- endif -%}
                        </span>
                        <div class="loading-overlay__spinner">
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            class="spinner"
                            viewBox="0 0 66 66"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                          </svg>
                        </div>
                      </button>
                    {% else %}
                      <button class='button sticky-atc__scroll-btn'>
                        <span>
                          {% if block.settings.mobile_show_price_in_button or block.settings.desktop_show_price_in_button %}
                            <span id="sticky-atc-price-{{ section.id }}" class="{% unless block.settings.mobile_show_price_in_button %} mobile-hidden{% endunless %}{% unless block.settings.desktop_show_price_in_button %} desktop-hidden{% endunless %}">
                              {{ product.selected_or_first_available_variant.price | money }} •
                            </span>
                          {% endif %}
                          <span class="sticky-atc__label__text">{{ block.settings.button_label }}</span>
                        </span>
                      </button>
                    {% endif %}
                  </div>
                </div>
              </sticky-atc>
            {%- when 'popup' -%}
              <modal-opener
                class="product-popup-modal__opener no-js-hidden quick-add-hidden"
                data-modal="#PopupModal-{{ block.id }}"
                {{ block.shopify_attributes }}
                style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
              >
                <button
                  id="ProductPopup-{{ block.id }}"
                  class="product-popup-modal__button link"
                  type="button"
                  aria-haspopup="dialog"
                >
                  {{ block.settings.text | default: block.settings.page.title }}
                </button>
              </modal-opener>
              <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                {{- block.settings.text -}}
              </a>
            {%- when 'share' -%}
              {% assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin %}
              {% render 'share-button',
                block: block,
                share_link: share_url
              %}

            {%- when 'variant_picker' -%}
              {% render 'product-variant-picker', 
                product: product, 
                block: block, 
                product_form_id: product_form_id, 
                sizing_chart_append: sizing_chart_append, 
                sizing_chart_html: sizing_chart_html,
                has_filtering: has_filtering
              %}
            {%- when 'buy_buttons' -%}
              {%- render 'buy-buttons',
                 block: block,
                 product: product,
                 product_form_id: product_form_id,
                 section_id: section.id,
                 show_pickup_availability: true,
                 main_product: true,
                 quantity_atc_append: quantity_atc_append,
                 quantity_selector_html: quantity_selector_html,
                 quantity_atc_append_heights: quantity_atc_append_heights
              -%}
            {%- when 'bundle_deals' -%}
              {% render 'bundle-deals-block', block: block, product: product, section_id: section.id %}
            {%- when 'reviews' -%}
              {%- render 'reviews', block: block -%}
            {%- when 'rating' -%}
              {%- if product.metafields.reviews.rating.value != blank -%}
                {% liquid
                  assign rating_decimal = 0
                  assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                  if decimal >= 0.3 and decimal <= 0.7
                    assign rating_decimal = 0.5
                  elsif decimal > 0.7
                    assign rating_decimal = 1
                  endif
                %}
                <div
                  class="rating"
                  role="img"
                  aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                >
                  <span
                    aria-hidden="true"
                    class="rating-star color-icon-{{ settings.accent_icons }}"
                    style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                  ></span>
                </div>
                <p class="rating-text caption">
                  <span aria-hidden="true">
                    {{- product.metafields.reviews.rating.value }} /
                    {{ product.metafields.reviews.rating.value.scale_max -}}
                  </span>
                </p>
                <p class="rating-count caption">
                  <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                  <span class="visually-hidden">
                    {{- product.metafields.reviews.rating_count }}
                    {{ 'accessibility.total_reviews' | t -}}
                  </span>
                </p>
              {%- endif -%}
            {%- when 'payment_badges' -%}
              <div class='payment-badges-block' style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                <ul class="payment-badges" role="list">
                  {% assign enabled_payment_types = shop.enabled_payment_types %}
                  {% if block.settings.enabled_payment_types != blank %}
                    {% assign enabled_payment_types = block.settings.enabled_payment_types | remove: ' ' | split: ',' %}
                  {% endif %}
            
                  {%- for type in enabled_payment_types -%}
                    {% assign payment_type = type | strip %}
                    <li class="list-payment__item">
                      {{ payment_type | payment_type_svg_tag: class: 'icon icon--full-color' }}
                    </li>
                  {%- endfor -%}
                </ul>
              </div>
            {% when 'urgency' %}
              <p class="urgency-text color-{{ block.settings.color_scheme }}" {{ block.shopify_attributes }} style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                <strong>{{ block.settings.urgency }}</strong>
              </p>
            {% when 'emoji_benefits' %}
              <div class='emoji-benefits-container' {{ block.shopify_attributes }} style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                {{ block.settings.benefits }}
              </div>
            {% when 'quantity_gifts' %}
              {% render 'quantity-gifts', block: block %}
            {% when 'bundle_offer' %}
              {% render 'bundle-offer', block: block %}
            {% when 'estimated_shipping' %}
              {% render 'estimated-shipping', block: block %}
            {% when 'shipping_checkpoints' %}
              {% render 'shipping-checkpoints', block: block %}
            {% when 'custom_product_field' %}
              {% render 'custom-product-field', block: block %}
            {% when 'sizing_chart' %}
              {% capture sizing_chart_html %}
                <modal-opener
                  class="product-popup-modal__opener sizing-chart no-js-hidden quick-add-hidden flex-justify-{{ block.settings.button_alignment }}"
                  data-modal="#PopupModal-{{ block.id }}"
                  data-section="{{ section.id }}"
                  data-append="{{ block.settings.append }}"
                  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
                >
                  <button
                    id="ProductPopup-{{ block.id }}"
                    class="product-popup-modal__button sizing-chart__button{% if block.settings.button_underline %} sizing-chart__button--underline{% endif %} flex-center" 
                    style='--font-size:{{ block.settings.text_size | divided_by: 10.0 }}rem;'
                    type="button"
                    aria-haspopup="dialog"
                  >
                    {% if block.settings.button_custom_icon != blank %}
                      <img
                        src="{{ block.settings.button_custom_icon | image_url: width: 150 }}"
                        alt=""
                        height="auto"
                        width="auto"
                        loading="lazy"
                      >
                    {% elsif block.settings.button_icon != blank %}
                      {% render 'material-icon', icon: block.settings.button_icon, filled: block.settings.button_filled_icon %}
                    {% endif %}
                    <span class="sizing-chart__button__text">{{ block.settings.button_label }}</span>
                  </button>
                </modal-opener>
                <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                  {{- block.settings.text -}}
                </a>
              {% endcapture %}
              {% assign sizing_chart_append = block.settings.append %}
              {% if block.settings.append == 'none' %}
                {{ sizing_chart_html }}
              {% endif %}
            {% when 'product_upsell' %}
              {% render 'upsell-block', block: block, type: 'product-info' %}
            {% when 'image' %}
              <div 
                class="product-info__image-block{% if block.settings.Full_mobile_width %} product-info__image-block--mobile-full{% endif %}" 
                style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                {{ block.shopify_attributes }}
              >
                {% if block.settings.image != blank %}
                  <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%">
                    {%- capture sizes -%}
                      (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                      (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px))
                    {%- endcapture -%}
                    {{
                      block.settings.image
                      | image_url: width: 1500
                      | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
                    }}
                  </div>
                {% else %}
                  {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                {% endif %}
              </div>
            {% when 'video' %}
              <div 
                class="product-info__image-block{% if block.settings.Full_mobile_width %} product-info__image-block--mobile-full{% endif %}" 
                style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;--image-width:{{ block.settings.width }}%;--image-alignment:{{ block.settings.alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                {{ block.shopify_attributes }}
              >
                {% if block.settings.video != blank %}
                  <div class="media media--transparent ratio" style="--ratio-percent: {{ 1 | divided_by: block.settings.video.aspect_ratio | times: 100 }}%">
                    {% render 'video-player', block: block %}
                  </div>
                {% else %}
                  {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                {% endif %}
              </div>
            {% when 'divider' %}
              <div 
                class="product-info__image-block{% if block.settings.Full_mobile_width %} product-info__image-block--mobile-full{% endif %}" 
                style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;--image-alignment:{{ block.settings.alignment }};--border-radius:{{ block.settings.border_radius | divided_by: 10.0 }}rem;"
                {{ block.shopify_attributes }}
              >
                <div class='product-info__divider' style="height:{{ block.settings.height }}px;width:{{ block.settings.width }}%;background:{{ block.settings.color }};border-radius:{{ block.settings.border_radius }}px;display:block;">&nbsp</div>
              </div>
            {%- when 'button' -%}
              <div
                class="buttons-container"
                style="--alignment:{{ block.settings.alignment }};--mobile-alignment:{{ block.settings.mobile_alignment }};--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
              >
                <a
                  {% if block.settings.button_link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.button_link }}"
                  {% endif %}
                  class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}{% if block.settings.full_width %} button--full-width{% endif %}{% if block.settings.full_width %} button--large-text{% endif %}"
                >
                  {{- block.settings.button_label -}}
                </a>
              </div>
            {%- when 'complementary' -%}
              <product-recommendations class="complementary-products quick-add-hidden no-js-hidden{% if block.settings.make_collapsible_row %} is-accordion{% endif %}{% if block.settings.enable_quick_add %} complementary-products-contains-quick-add{% endif %}" data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ block.settings.product_list_limit }}&intent=complementary" style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;">
                {%- if recommendations.performed and recommendations.products_count > 0 -%}
                  <aside aria-label="{{ 'accessibility.complementary_products' | t }}" {{ block.shopify_attributes }}{% if block.settings.make_collapsible_row %} class="product__accordion accordion"{% endif %}>
                    <div class="complementary-products__container">
                      {%- if block.settings.make_collapsible_row -%}
                        <details id="Details-{{ block.id }}-{{ section.id }}" open>
                          <summary>
                      {%- endif %}
                      <div class="summary__title">
                        {%- if block.settings.make_collapsible_row -%}
                          {% render 'icon-accordion', icon: block.settings.icon %}
                          <h2 class="h4 accordion__title">{{ block.settings.block_heading }}</h2>
                        {%- else -%}
                          <h2 class="h3 accordion__title">{{ block.settings.block_heading }}</h2>
                        {%- endif -%}
                      </div>
                      {%- if block.settings.make_collapsible_row -%}
                          {% render 'icon-caret' %}
                        </summary>
                      {%- endif -%}
                      <slideshow-component class="slider-mobile-gutter">
                        {%- assign number_of_slides = recommendations.products_count | plus: 0.0 | divided_by: block.settings.products_per_page | ceil -%}
                        <div id="Slider-{{ block.id }}" class="contains-card contains-card--product complementary-slider grid grid--1-col slider slider--everywhere" role="list"{% if number_of_slides > 1 %} aria-label="{{ 'general.slider.name' | t }}"{% endif %}>
                          {%- for i in (1..number_of_slides) -%}
                            <div id="Slide-{{ block.id }}-{{ forloop.index }}" class="complementary-slide complementary-slide--{{ settings.card_style }} grid__item slider__slide slideshow__slide" tabindex="-1" role="group"{% if number_of_slides > 1 %} aria-roledescription="{{ 'sections.slideshow.slide' | t }}" aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"{% endif %}>
                              <ul class="list-unstyled" role="list">
                                {%- for product in recommendations.products limit: block.settings.products_per_page offset: continue -%}
                                  <li>
                                    {% render 'card-product',
                                      card_product: product,
                                      media_aspect_ratio: block.settings.image_ratio,
                                      show_secondary_image: false,
                                      lazy_load: false,
                                      show_quick_add: block.settings.enable_quick_add,
                                      section_id: section.id,
                                      horizontal_class: true,
                                      horizontal_quick_add: true
                                    %}
                                  </li>
                                {%- endfor -%}
                              </ul>
                            </div>
                          {%- endfor -%}
                        </div>
                        {%- if number_of_slides > 1 -%}
                          <div class="slider-buttons no-js-hidden">
                            <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-caret' %}</button>
                            <div class="slider-counter slider-counter--{{ block.settings.pagination_style }}{% if block.settings.pagination_style == 'counter' or block.settings.pagination_style == 'numbers' %} caption{% endif %}">
                              {%- if block.settings.pagination_style == 'counter' -%}
                                <span class="slider-counter--current">1</span>
                                <span aria-hidden="true"> / </span>
                                <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                                <span class="slider-counter--total">{{ number_of_slides }}</span>
                              {%- else -%}
                                <div class="slideshow__control-wrapper">
                                  {%- for i in (1..number_of_slides) -%}
                                    <button class="slider-counter__link slider-counter__link--{{ block.settings.pagination_style }} link" aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}" aria-controls="Slider-{{ block.id }}">
                                      {%- if block.settings.pagination_style == 'numbers' -%}{{ forloop.index }}{% else %}<span class="dot"></span>{%- endif -%}
                                    </button>
                                  {%- endfor -%}
                                </div>
                              {%- endif -%}
                            </div>
                            <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-caret' %}</button>
                          </div>
                        {%- endif -%}
                      </slideshow-component>
                      {%- if block.settings.make_collapsible_row -%}
                        </details>
                      {%- endif -%}
                    </div>
                  </aside>
                {%- endif -%}
                {{ 'component-complementary-products.css' | asset_url | stylesheet_tag }}
                {%- if block.settings.enable_quick_add -%}
                  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
                  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
                {%- endif -%}
              </product-recommendations>
            {%- when 'icon_with_text' -%}
              {% render 'icon-with-text',
                block: block
              %}
          {%- endcase -%}
        {%- endfor -%}
        <a href="{{ product.url }}" class="link product__view-details animate-arrow">
          {{ 'products.product.view_full_details' | t }}
          {% render 'icon-arrow' %}
        </a>
      </product-info>
    </div>
    {%- if section.settings.media_position == 'right' -%}
      {% comment %} Duplicate gallery to display after product content on tablet/desktop breakpoint {% endcomment %}
      <div class="grid__item product__media-wrapper small-hide">
        {% render 'product-media-gallery', variant_images: variant_images, is_duplicate: true %}
      </div>
    {%- endif -%}
  </div>

  {% render 'product-media-modal' variant_images: variant_images %}

  {% assign popups = section.blocks | where: 'type', 'popup' %}
  {%- for block in popups -%}
    <modal-dialog id="PopupModal-{{ block.id }}" class="product-popup-modal" {{ block.shopify_attributes }}>
      <div
        role="dialog"
        aria-label="{{ block.settings.text }}"
        aria-modal="true"
        class="product-popup-modal__content"
        tabindex="-1"
      >
        <button
          id="ModalClose-{{ block.id }}"
          type="button"
          class="product-popup-modal__toggle product-popup-modal__toggle--{{ block.settings.close_button_style }}"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          {% render 'icon-close' %}
        </button>
        <div class="product-popup-modal__content-info">
          <h1 class="h2">{{ block.settings.page.title }}</h1>
          {{ block.settings.page.content }}
        </div>
      </div>
    </modal-dialog>
  {%- endfor -%}
                                
  {% assign sizing_chart = section.blocks | where: 'type', 'sizing_chart' %}
  {%- for block in sizing_chart -%}
    <modal-dialog id="PopupModal-{{ block.id }}" class="product-popup-modal" {{ block.shopify_attributes }}{% if block.settings.test_mode %} open{% endif %}>
      <div
        role="dialog"
        aria-label="{{ block.settings.text }}"
        aria-modal="true"
        class="product-popup-modal__content product-popup-modal__content--centered sizing-chart__modal-container"
        tabindex="-1"
      >
        <button
          id="ModalClose-{{ block.id }}"
          type="button"
          class="product-popup-modal__toggle product-popup-modal__toggle--{{ block.settings.close_button_style }}"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          {% render 'icon-close' %}
        </button>
        <div class="product-popup-modal__content-info sizing-chart__modal content-rte color-{{ block.settings.color_scheme }}">
          {% if block.settings.headline != blank %}
            <h3 class="sizing-chart__title center {{ block.settings.heading_size }}">
              {{ block.settings.headline }}
            </h3>
          {% endif %}
          {% if block.settings.top_image != blank %}
            <div class="sizing-chart__top-image">
              {%- capture sizes -%}
                (min-width: 750px) 800px / 2), calc((100vw - 50px))
              {%- endcapture -%}
              {{
                block.settings.top_image
                | image_url: width: 1500
                | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
              }}
            </div>
          {% endif %}
          {% if block.settings.table_content != blank %}
            <table class="sizing-chart-table">
              {% if block.settings.table_header_content != blank %}
                {% assign columns = block.settings.table_header_content | split: ',' %}
                <thead>
                  <tr>
                    {% for column in columns %}
                      <th class="sizing-chart-table__th color-{{ block.settings.table_header_color_scheme }}" align="center">
                        {{ column | strip }}
                      </th>
                    {% endfor %}
                  </tr>
                </thead>
              {% endif %}
              <tbody>
                {% assign rows = block.settings.table_content | split: '</p><p>' %}
                {% for row in rows %}
                  <tr>
                    {% assign columns = row | remove: '<p>' | remove: '</p>' | split: ',' %}
                    {% for column in columns %}
                      <td class="sizing-chart-table__td" align="center">
                        {{ column | strip }}
                      </td>
                    {% endfor %}
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% endif %}
          {% if block.settings.caption_text != blank %}
            <div
              class="sizing-chart__caption {{ block.settings.caption_alignment }} rte"
              style="--text-size:{{ block.settings.caption_size }};--text-color:{{ block.settings.caption_color }};"
            >
              {{ block.settings.caption_text }}
            </div>
          {% endif %}
          {% if block.settings.bottom_image != blank %}
            <div class="sizing-chart__top-image">
              {%- capture sizes -%}
                (min-width: 750px) 800px / 2), calc((100vw - 50px))
              {%- endcapture -%}
              {{
                block.settings.bottom_image
                | image_url: width: 1500
                | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
              }}
            </div>
          {% endif %}
        </div>
      </div>
    </modal-dialog>
  {%- endfor -%}

  {%- if product.media.size > 0 -%}
    <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {%- if first_3d_model -%}
    <script type="application/json" id="ProductJSON-{{ product.id }}">
      {{ product.media | where: 'media_type', 'model' | json }}
    </script>
    <script src="{{ 'product-model.js' | asset_url }}" defer></script>
  {%- endif -%}

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      function isIE() {
        const ua = window.navigator.userAgent;
        const msie = ua.indexOf('MSIE ');
        const trident = ua.indexOf('Trident/');

        return msie > 0 || trident > 0;
      }

      if (!isIE()) return;
      const hiddenInput = document.querySelector('#{{ product_form_id }} input[name="id"]');
      const noScriptInputWrapper = document.createElement('div');
      const variantSwitcher =
        document.querySelector('variant-radios[data-section="{{ section.id }}"]') ||
        document.querySelector('variant-selects[data-section="{{ section.id }}"]');
      noScriptInputWrapper.innerHTML = document.querySelector(
        '.product-form__noscript-wrapper-{{ section.id }}'
      ).textContent;
      variantSwitcher.outerHTML = noScriptInputWrapper.outerHTML;

      document.querySelector('#Variants-{{ section.id }}').addEventListener('change', function (event) {
        hiddenInput.value = event.currentTarget.value;
      });
    });
  </script>

  {%- liquid
    if product.selected_or_first_available_variant.featured_media
      assign seo_media = product.selected_or_first_available_variant.featured_media
    else
      assign seo_media = product.featured_media
    endif
  -%}

  <script type="application/ld+json">
    {
      "@context": "http://schema.org/",
      "@type": "Product",
      "name": {{ product.title | json }},
      "url": {{ request.origin | append: product.url | json }},
      {% if seo_media -%}
        "image": [
          {{ seo_media | image_url: width: 1920 | prepend: "https:" | json }}
        ],
      {%- endif %}
      "description": {{ product.description | strip_html | json }},
      {% if product.selected_or_first_available_variant.sku != blank -%}
        "sku": {{ product.selected_or_first_available_variant.sku | json }},
      {%- endif %}
      "brand": {
        "@type": "Brand",
        "name": {{ product.vendor | json }}
      },
      "offers": [
        {%- for variant in product.variants -%}
          {
            "@type" : "Offer",
            {%- if variant.sku != blank -%}
              "sku": {{ variant.sku | json }},
            {%- endif -%}
            {%- if variant.barcode.size == 12 -%}
              "gtin12": {{ variant.barcode }},
            {%- endif -%}
            {%- if variant.barcode.size == 13 -%}
              "gtin13": {{ variant.barcode }},
            {%- endif -%}
            {%- if variant.barcode.size == 14 -%}
              "gtin14": {{ variant.barcode }},
            {%- endif -%}
            "availability" : "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
            "price" : {{ variant.price | divided_by: 100.00 | json }},
            "priceCurrency" : {{ cart.currency.iso_code | json }},
            "url" : {{ request.origin | append: variant.url | json }}
          }{% unless forloop.last %},{% endunless %}
        {%- endfor -%}
      ]
    }
  </script>
</section>

{% schema %}
{
  "name": "t:sections.main-product.name",
  "tag": "section",
  "class": "section",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "Text with icon",
      "settings": [
        {
          "type": "header",
          "content": "Text"
        },
        {
          "type": "range",
          "id": "mobile_text_size",
          "min": 8,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 14,
          "label": "Mobile text size"
        },
        {
          "type": "range",
          "id": "desktop_text_size",
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Desktop text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "color",
          "id": "text_color",
          "default": "#121212",
          "label": "Text color"
        },
        {
          "type": "inline_richtext",
          "id": "text_1",
          "default": "Text with icon",
          "label": "Text #1"
        },
        {
          "type": "inline_richtext",
          "id": "text_2",
          "label": "Text #2"
        },
        {
          "type": "inline_richtext",
          "id": "text_3",
          "label": "Text #3"
        },
        {
          "type": "header",
          "content": "Icons"
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 100,
          "max": 200,
          "step": 5,
          "default": 120,
          "unit": "%",
          "label": "Icon scale",
          "info": "Relative to text size"
        },
        {
          "type": "color",
          "id": "icon_color",
          "default": "#121212",
          "label": "Icons color"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "check_circle",
          "label": "Icon #1",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "Filled icon #1"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon #1"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "check_circle",
          "label": "Icon #2",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "Filled icon #2"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon #2"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "check_circle",
          "label": "Icon #3",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_3",
          "default": false,
          "label": "Filled icon #3"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_3",
          "label": "Custom icon #3"
        },
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "width",
          "options": [
            {
              "value": "fit-content",
              "label": "Fit text"
            },
            {
              "value": "100%",
              "label": "Full"
            }
          ],
          "default": "100%",
          "label": "Width"
        },
        {
          "type": "select",
          "id": "direction",
          "options": [
            {
              "value": "horizontal",
              "label": "Horizontal"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "default": "horizontal",
          "label": "Stacking direction",
          "info": "Applied when multiple texts are added."
        },
        {
          "type": "range",
          "id": "column_gap",
          "min": 0,
          "max": 6,
          "step": 0.5,
          "label": "Stacking spacing",
          "default": 3
        },
        {
          "type": "checkbox",
          "id": "enable_bg",
          "default": false,
          "label": "Enable background",
          "info": "The following settings are applied when this option is enabled."
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background color",
          "default": "#F3F3F3"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "t:settings_schema.global.settings.corner_radius.label",
          "default": 40,
          "info": "Relative to font size"
        },
        {
          "type": "range",
          "id": "padding",
          "min": 1,
          "max": 5,
          "step": 0.5,
          "label": "Padding",
          "default": 3
        },
        {
          "type": "range",
          "id": "border_size",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Border thickness",
          "unit": "px",
          "default": 0
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#B7B7B7"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "label": "Title size",
          "default": "h1"
        },
        {
          "type": "select",
          "id": "title_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            }
          ],
          "default": "left",
          "label": "Title alignment"
        },
        {
          "type": "checkbox",
          "id": "uppercase_title",
          "label": "Uppercase title",
          "default": false
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 0
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "rating_stars",
      "name": "Rating stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 4.8
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "select",
          "id": "bg_stars_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "full",
              "label": "Full"
            }
          ],
          "label": "Background stars style",
          "default": "full"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#ececec",
          "label": "Background stars color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "trustpilot_stars",
      "name": "Trustpilot stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 5
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#00b67a",
          "label": "Active stars container color"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#c8c8c8",
          "label": "Background stars container color"
        },
        {
          "type": "color",
          "id": "star_symbol_color",
          "default": "#fff",
          "label": "Stars inside symbol color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "price",
      "name": "t:sections.main-product.blocks.price.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "label": "Layout",
          "options": [
            {
              "value": "price_first",
              "label": "Price first"
            },
            {
              "value": "price_second",
              "label": "Compare price first"
            }
          ],
          "default": "price_first"
        },
        {
          "type": "select",
          "id": "price_color",
          "label": "Price color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "select",
          "id": "compare_price_color",
          "label": "Compare price color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "text"
        },
        {
          "type": "header",
          "content": "Badge"
        },
        {
          "type": "select",
          "id": "displayed_badge",
          "label": "Displayed badge",
          "options": [
            {
              "value": "none",
              "label": "Hidden"
            },
            {
              "value": "sale",
              "label": "Sale badge"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "sale",
          "info": "If the variant is sold out, the sold out badge will be displayed instead. \"Sale badge\" displays the classci sale badge from Theme settings > Badges if product has a comapre price. \"Custom\" displays a custom badge that's configured below. The custom badge is displayed even if the product doesn't have a compare price."
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 100,
          "max": 200,
          "step": 5,
          "default": 140,
          "unit": "%",
          "label": "Icon scale",
          "info": "Relative to text size"
        },
        {
          "type": "text",
          "id": "custom_badge_text",
          "label": "Custom badge text",
          "default": "[icon_1] SPECIAL OFFER",
          "info": "Dynamic values that can be used: [icon_1] dispalys the Icon #1. [icon_2] dispalys the Icon #1. [percentage_saved] displays the percentage saved from the product compare price. [amount_saved] displays the money amount saved from the comapre price."
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "redeem",
          "label": "Icon #1",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "Filled icon #1"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon #1"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "check_circle",
          "label": "Icon #2",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "Filled icon #2"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon #2"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "clickable_discount",
      "name": "Clickable discount",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Important notes"
        },
        {
          "type": "paragraph",
          "content": "Click on the button automatically applies a discount code in the checkout."
        },
        {
          "type": "paragraph",
          "content": "Shopify only allows ONE discount code to be applied (the one with a largest discout). Use this block only if you don't have any other discount (automatic/code) that will conflict."
        },
        {
          "type": "paragraph",
          "content": "If you use quantity breaks with automatic discounts, ask your supplier for new SKUs with quantity and use the Variant picker with Quantity breaks style. This will allow you to use this block with quantity breaks."
        },
        {
          "type": "paragraph",
          "content": "If the customer applied the code and went back to the page, the code will stay applied."
        },
        {
          "type": "header",
          "content": "Discount code"
        },
        {
          "type": "text",
          "id": "discount_code",
          "label": "Discount code"
        },
        {
          "type": "header",
          "content": "Button settings"
        },
        {
          "type": "range",
          "id": "mobile_text_size",
          "min": 8,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 14,
          "label": "Mobile text size"
        },
        {
          "type": "range",
          "id": "desktop_text_size",
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Desktop text size"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 100,
          "max": 200,
          "step": 5,
          "default": 110,
          "unit": "%",
          "label": "Icon scale",
          "info": "Relative to text size"
        },
        {
          "type": "checkbox",
          "id": "enable_bg",
          "default": false,
          "label": "Enable background",
          "info": "The following settings are applied when this option is enabled."
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "t:settings_schema.global.settings.corner_radius.label",
          "default": 40,
          "info": "Relative to font size"
        },
        {
          "type": "range",
          "id": "padding",
          "min": 1,
          "max": 5,
          "step": 0.5,
          "label": "Padding",
          "default": 3
        },
        {
          "type": "header",
          "content": "Non-applied content",
          "info": "Shown before the customer clicks to apply a discount."
        },
        {
          "type": "inline_richtext",
          "id": "text_1",
          "default": "<strong>Click to apply a discount</strong>",
          "label": "Text"
        },
        {
          "type": "color",
          "id": "text_color_1",
          "default": "#449502",
          "label": "Text color"
        },
        {
          "type": "text",
          "id": "icon_1",
          "label": "Icon",
          "default": "sell",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": true,
          "label": "Filled icon"
        },
        {
          "type": "color",
          "id": "icon_color_1",
          "default": "#449502",
          "label": "Icon color"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_1",
          "label": "Custom icon"
        },
        {
          "type": "color",
          "id": "bg_color_1",
          "label": "Background color",
          "default": "#F3F3F3",
          "info": "Applied if Enable background is checked."
        },
        {
          "type": "header",
          "content": "Applied content",
          "info": "Shown after the discount has been applied"
        },
        {
          "type": "inline_richtext",
          "id": "text_2",
          "default": "<strong>A XX% discount will be applied at checkout!</strong>",
          "label": "Text"
        },
        {
          "type": "color",
          "id": "text_color_2",
          "default": "#449502",
          "label": "Text color"
        },
        {
          "type": "text",
          "id": "icon_2",
          "label": "Icon",
          "default": "sell",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": true,
          "label": "Filled icon"
        },
        {
          "type": "color",
          "id": "icon_color_2",
          "default": "#449502",
          "label": "Icon color"
        },
        {
          "type": "image_picker",
          "id": "custom_icon_2",
          "label": "Custom icon"
        },
        {
          "type": "color",
          "id": "bg_color_2",
          "label": "Background color",
          "default": "#F3F3F3",
          "info": "Applied if Enable background is checked."
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "sku",
      "name": "t:sections.main-product.blocks.sku.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.sku.settings.text_style.label"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "inventory",
      "name": "t:sections.main-product.blocks.inventory.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.inventory.settings.text_style.label"
        },
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.label",
          "min": 0,
          "max": 100,
          "step": 1,
          "info": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.info",
          "default": 10
        },
        {
          "type": "checkbox",
          "id": "show_inventory_quantity",
          "label": "t:sections.main-product.blocks.inventory.settings.show_inventory_quantity.label",
          "default": true
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Classic quantity selector"
        },
        {
          "type": "paragraph",
          "content": "To customize the appearance of the classic quantity selector (not quantity breaks), navigate to Theme settings > Quantity selector."
        },
        {
          "type": "checkbox",
          "id": "full_width_classic",
          "label": "Full width"
        },
        {
          "type": "select",
          "id": "atc_append",
          "options": [
            {
              "value": "none",
              "label": "Normal"
            },
            {
              "value": "left",
              "label": "Left side of the ATC button"
            },
            {
              "value": "right",
              "label": "Right side of the ATC button"
            }
          ],
          "default": "none",
          "label": "Position",
          "info": "IMPORTANT: When displaying the selector next to the ATC button, the Quantity selector block must be placed ABOVE the Buy buttons block."
        },
        {
          "type": "select",
          "id": "atc_append_heights",
          "options": [
            {
              "value": "original",
              "label": "Keep original heights"
            },
            {
              "value": "stretch-quantity",
              "label": "Make quantity selector same height as ATC button"
            },
            {
              "value": "shrink-atc",
              "label": "Make ATC button same height as quantity selector"
            }
          ],
          "default": "stretch-quantity",
          "label": "Heights of the quantity selector & ATC button when together",
          "info": "By default, quantity selector & ATC button are not the same height. When you display the quantity selector next to the ATC button, this setting controls how's that handled."
        },
        {
          "type": "header",
          "content": "Quantity Discounts"
        },
        {
          "type": "paragraph",
          "content": "Make sure to set up an [automatic discount](https://help.shopify.com/en/manual/discounts/automatic-discounts) to allow items to be discounted at certain quantity levels."
        },
        {
          "type": "checkbox",
          "id": "enable_quantity_discounts",
          "label": "Enable Quantity Discounts"
        },
        {
          "type": "paragraph",
          "content": "[Tutorial on how to set up](https://dashboard.shrinesolutions.com/customer/help-center?category=Blocks&element=Quantity+Breaks)"
        },
        {
          "type": "paragraph",
          "content": "DYNAMIC VALUES: Use these auto-calculated values based on variant/quantity: [quantity] - bundle quantity, [price] - bundle total, [compare_price] - bundle compare total (adjustable calculation for each option), [price_each] - item average price, [compare_price_each] - item average compare price, [amount_saved] - difference between total & compare price, [amount_saved_rounded] - rounded up difference (e.g., 19.95 > 20)."
        },
        {
          "type": "paragraph",
          "content": "NOTE: Dynamic values are available in ALL text boxes"
        },
        {
          "type": "select",
          "id": "style",
          "options": [
            {
              "value": "normal",
              "label": "Normal"
            },
            {
              "value": "compact",
              "label": "Compact"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "label": "Style",
          "default": "normal"
        },
        {
          "type": "text",
          "id": "headline",
          "label": "Heading",
          "default": "BUNDLE & SAVE"
        },
        {
          "type": "select",
          "id": "preselected",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "option_1",
              "label": "Option #1"
            },
            {
              "value": "option_2",
              "label": "Option #2"
            },
            {
              "value": "option_3",
              "label": "Option #3"
            },
            {
              "value": "option_4",
              "label": "Option #4"
            }
          ],
          "default": "option_1",
          "label": "Preselected option"
        },
        {
          "type": "checkbox",
          "id": "display_selected_indicator",
          "label": "Display selected indicator in normal & vertical style",
          "default": true
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 10
        },
        {
          "type": "range",
          "id": "border_width",
          "min": 1,
          "max": 5,
          "step": 1,
          "unit": "px",
          "label": "Border width",
          "default": 2
        },
        {
          "type": "select",
          "id": "color_scheme",
          "label": "Color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "checkbox",
          "id": "enable_variant_selectors",
          "label": "Enable variant selectors",
          "default": true,
          "info": "If the product has multiple variants, a variant picker will be displayed for each quantity"
        },
        {
          "type": "checkbox",
          "id": "enable_variant_selectors_on_quantity_of_1",
          "label": "Enable variant selectors on single quantity",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "update_prices",
          "label": "Enable variant price updates",
          "default": false,
          "info": "This option will dynamically change the displayed bundle prices based on the selected variant. ATTENTION: This option might NOT work with currency converters."
        },
        {
          "type": "checkbox",
          "id": "skip_unavailable",
          "label": "Hide & automatically skip sold out variants",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "full_width_pickers",
          "label": "Make variant pickers full width",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "hide_pickers_overlay",
          "label": "Disable variant pickers color overlay",
          "default": true
        },
        {
          "type": "inline_richtext",
          "id": "pickers_label",
          "label": "Variant pickers label",
          "info": "Displayed above the variant pickers."
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Images width",
          "default": 70,
          "info": "Width of images that can be added with normal & vertical styles."
        },
        {
          "type": "checkbox",
          "id": "space_images",
          "label": "Space images from top in vertical style",
          "default": true,
          "info": "Applied if style is set to Vertical and the option below is set to Top."
        },
        {
          "type": "select",
          "id": "vertical_images_position",
          "label": "Image position in vertical style",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "vertical_prices_layout",
          "label": "Prices layout in vertical style",
          "options": [
            {
              "value": "vertical",
              "label": "Vertical"
            },
            {
              "value": "horizontal",
              "label": "Horizontal"
            }
          ],
          "default": "vertical",
          "info": "Difference is visible if a bundle has a compare price."
        },
        {
          "type": "header",
          "content": "Quantity option #1"
        },
        {
          "type": "range",
          "id": "option_1_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #1 Quantity",
          "default": 1,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_1_badge",
          "label": "Option #1 Badge text"
        },
        {
          "type": "select",
          "id": "option_1_badge_style",
          "label": "Option #1 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_1_badge_color",
          "label": "Option #1 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_1_image",
          "label": "Option #1 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_1_label",
          "label": "Option #1 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_1_benefit",
          "label": "Option #1 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_1_benefit_position",
          "label": "Option #1 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_1_benefit_style",
          "label": "Option #1 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_1_benefit_color",
          "label": "Option #1 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_1_caption",
          "label": "Option #1 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_1_percentage_off_text",
          "default": "0",
          "label": "Option #1 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_1_fixed_amount_off",
          "label": "Option #1 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_1_price_text",
          "label": "Option #1 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_1_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #1 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_1_compare_price_text",
          "label": "Option #1 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Quantity option #2"
        },
        {
          "type": "range",
          "id": "option_2_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #2 Quantity",
          "default": 2,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_2_badge",
          "label": "Option #2 Badge text"
        },
        {
          "type": "select",
          "id": "option_2_badge_style",
          "label": "Option #2 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_2_badge_color",
          "label": "Option #2 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_2_image",
          "label": "Option #2 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_2_label",
          "label": "Option #2 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_2_benefit",
          "label": "Option #2 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_2_benefit_position",
          "label": "Option #2 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_2_benefit_style",
          "label": "Option #2 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_2_benefit_color",
          "label": "Option #2 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_2_caption",
          "label": "Option #2 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_2_percentage_off_text",
          "default": "0",
          "label": "Option #2 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_2_fixed_amount_off",
          "label": "Option #2 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_2_price_text",
          "label": "Option #2 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_2_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #2 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_2_compare_price_text",
          "label": "Option #2 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Quantity option #3"
        },
        {
          "type": "range",
          "id": "option_3_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #3 Quantity",
          "default": 3,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_3_badge",
          "label": "Option #3 Badge text"
        },
        {
          "type": "select",
          "id": "option_3_badge_style",
          "label": "Option #3 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_3_badge_color",
          "label": "Option #3 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_3_image",
          "label": "Option #3 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_3_label",
          "label": "Option #3 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_3_benefit",
          "label": "Option #3 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_3_benefit_position",
          "label": "Option #3 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_3_benefit_style",
          "label": "Option #3 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_3_benefit_color",
          "label": "Option #3 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_3_caption",
          "label": "Option #3 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_3_percentage_off_text",
          "default": "0",
          "label": "Option #3 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_3_fixed_amount_off",
          "label": "Option #3 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_3_price_text",
          "label": "Option #3 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_3_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #3 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_3_compare_price_text",
          "label": "Option #3 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Quantity option #4"
        },
        {
          "type": "range",
          "id": "option_4_quantity",
          "min": 0,
          "max": 20,
          "step": 1,
          "unit": "qty",
          "label": "Option #4 Quantity",
          "default": 4,
          "info": "Set to 0 to disable this option"
        },
        {
          "type": "text",
          "id": "option_4_badge",
          "label": "Option #4 Badge text"
        },
        {
          "type": "select",
          "id": "option_4_badge_style",
          "label": "Option #4 Badge style",
          "options": [
            {
              "value": "1",
              "label": "Style 1"
            },
            {
              "value": "2",
              "label": "Style 2"
            },
            {
              "value": "3",
              "label": "Style 3"
            }
          ],
          "default": "1",
          "info": "NOTE: This setting is NOT applied if Style is set to Compact."
        },
        {
          "type": "select",
          "id": "option_4_badge_color",
          "label": "Option #4 Badge color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "image_picker",
          "id": "option_4_image",
          "label": "Option #4 Image",
          "info": "Not displayed if Style is set to Compact."
        },
        {
          "type": "text",
          "id": "option_4_label",
          "label": "Option #4 Label",
          "default": "Buy [quantity]"
        },
        {
          "type": "text",
          "id": "option_4_benefit",
          "label": "Option #4 Benefit",
          "info": "Display an additional benefit of the bundle"
        },
        {
          "type": "select",
          "id": "option_4_benefit_position",
          "label": "Option #4 Benefit position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top"
        },
        {
          "type": "select",
          "id": "option_4_benefit_style",
          "label": "Option #4 Benefit style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "solid",
              "label": "Solid"
            }
          ],
          "default": "outlined"
        },
        {
          "type": "select",
          "id": "option_4_benefit_color",
          "label": "Option #4 Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "option_4_caption",
          "label": "Option #4 Caption",
          "default": "You save [amount_saved]"
        },
        {
          "type": "text",
          "id": "option_4_percentage_off_text",
          "default": "0",
          "label": "Option #4 Percentage off",
          "info": "A percentage that will be discounted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_4_fixed_amount_off",
          "label": "Option #4 Fixed amount off",
          "default": "0",
          "info": "A fixed amount of money (WITHOUT the currency symbol/code) that will be subtracted from the quantity's total price"
        },
        {
          "type": "text",
          "id": "option_4_price_text",
          "label": "Option #4 Price text",
          "default": "[price]"
        },
        {
          "type": "select",
          "id": "option_4_compare_price",
          "options": [
            {
              "value": "price",
              "label": "Regular price"
            },
            {
              "value": "compare_price",
              "label": "Compare price"
            }
          ],
          "default": "compare_price",
          "label": "Option #4 Compare price based on",
          "info": "Choose if you want bundle compare price to be calculated based on product price or compare price (selected value multiplied by quantity)"
        },
        {
          "type": "text",
          "id": "option_4_compare_price_text",
          "label": "Option #4 Compare price text",
          "default": "[compare_price]"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "text",
          "id": "picker_types",
          "label": "Type for each option",
          "default": "pills, pills, pills",
          "info": "Choose picker type for each option individually by splitting them with a comma. Available options are \"pills\", \"dropdown\", \"swatches\", \"quantity breaks\" and \"hidden\". Example: \"swatches, dropdown, quantity breaks\"."
        },
        {
          "type": "text",
          "id": "custom_labels",
          "label": "Custom labels",
          "info": "If empty, the option name will be displayed. Available dynamic values: [count] - option index, [name] - option name, [name_lowercase] - option name in lowercase, [name_uppercase] - option name in uppercase, [selected] - selected option. Not applied to \"Quantity breaks\" picker heading."
        },
        {
          "type": "checkbox",
          "id": "skip_unavailable",
          "label": "Hide & automatically skip sold out variants",
          "default": false
        },
        {
          "type": "header",
          "content": "Color swatches"
        },
        {
          "type": "paragraph",
          "content": "Customize the appeare of swatches in Theme settings > Color swatches."
        },
        {
          "type": "select",
          "id": "swatches_size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            },
            {
              "value": "extra-large",
              "label": "Extra large"
            }
          ],
          "label": "Swatches size",
          "default": "medium"
        },
        {
          "type": "select",
          "id": "swatches_custom_colors",
          "options": [
            {
              "value": "disabled",
              "label": "Disabled"
            },
            {
              "value": "image_alt",
              "label": "Variant images alt text"
            },
            {
              "value": "custom",
              "label": "Custom colors list"
            },
            {
              "value": "predefined",
              "label": "Global predefined colors list"
            }
          ],
          "label": "Swatches custom colors",
          "default": "disabled",
          "info": "Disabled displays the variant images in swatches. Varaint images alt text displays CSS values from each image's alt text. Custom colors list takes the list of the custom CSS values from the setting bellow. Watch the tutorial for CSS values [here](https:\/\/youtu.be\/Xo_ZFzRCk3o)"
        },
        {
          "type": "text",
          "id": "swatches_custom_colors_list",
          "label": "Custom colors list)",
          "info": "CSS values (hex/gradient) split by a comma. Example: \"#000000, #FFFFFF\"",
          "default": "#000000, #FFFFFF"
        },
        {
          "type": "header",
          "content": "Dropdowns"
        },
        {
          "type": "paragraph",
          "content": "Customize dropdown colors, borders and shadows in Theme settings > Variant dropdowns."
        },
        {
          "type": "checkbox",
          "id": "full_width_dropdowns",
          "label": "Full width dropdowns",
          "default": false
        },
        {
          "type": "header",
          "content": "Quantity breaks"
        },
        {
          "type": "paragraph",
          "content": "DYNAMIC VALUES: Use these auto-calculated values based on variant: [name] - option name, [price] - option price, [compare_price] - option compare price, [amount_saved] - difference between price & compare price, [amount_saved_rounded] - rounded up difference (e.g., 19.95 > 20)."
        },
        {
          "type": "paragraph",
          "content": "NOTE: Dynamic values are available in ALL text boxes."
        },
        {
          "type": "select",
          "id": "breaks_style",
          "options": [
            {
              "value": "normal",
              "label": "Normal"
            },
            {
              "value": "compact",
              "label": "Compact"
            },
            {
              "value": "vertical",
              "label": "Vertical"
            }
          ],
          "label": "Style",
          "default": "normal"
        },
        {
          "type": "text",
          "id": "breaks_headline",
          "label": "Heading",
          "default": "BUNDLE & SAVE"
        },
        {
          "type": "select",
          "id": "breaks_color_scheme",
          "label": "Color scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "paragraph",
          "content": "HOW TO CUSTOMIZE TEXT: Every text field bellow is used to configure text of all options together. Split the texts for each option WITH A COMMA. Use [empty] to skip an option. Example: \"Caption 1, [empty], Caption 3\". All dynamic values are available."
        },
        {
          "type": "text",
          "id": "breaks_badges",
          "label": "Badges",
          "default": "[empty], Most popular, [empty]"
        },
        {
          "type": "select",
          "id": "breaks_displayed_images",
          "label": "Displayed images",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "variant_images",
              "label": "Variant images"
            },
            {
              "value": "custom",
              "label": "Custom images"
            }
          ],
          "default": "variant_images",
          "info": "Choose which images you want to display when the Style is set to Vertical"
        },
        {
          "type": "text",
          "id": "breaks_custom_images",
          "label": "Custom image URLs",
          "info": "To upload images, go to your Shopify admin > Content > Files > Upload an image > copy its link"
        },
        {
          "type": "range",
          "id": "breaks_image_width",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Images width",
          "default": 70,
          "info": "Width of images (relative to the container) that can be added when Style is set to Vertical"
        },
        {
          "type": "checkbox",
          "id": "breaks_space_images",
          "label": "Space images from top",
          "default": true
        },
        {
          "type": "text",
          "id": "breaks_labels",
          "label": "Labels",
          "default": "[name], [name], [name]"
        },
        {
          "type": "text",
          "id": "breaks_benefits",
          "label": "Benefits",
          "default": "[empty], Free Shipping, Free Shipping"
        },
        {
          "type": "text",
          "id": "breaks_captions",
          "label": "Captions",
          "default": "Variant 1 caption, Variant 2 caption, Variant 3 caption"
        },
        {
          "type": "text",
          "id": "breaks_price_texts",
          "label": "Price texts",
          "default": "[price], [price], [price]"
        },
        {
          "type": "text",
          "id": "breaks_compare_price_texts",
          "label": "Compare price texts",
          "default": "[compare_price], [compare_price], [compare_price]"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.main-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "paragraph",
          "content": "IMPORTANT: Variant selectors in Quantity breaks, gifts & preselected upsells do NOT work with dynamic checkout buttons."
        },
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": false,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "checkbox",
          "id": "skip_cart",
          "label": "Skip cart",
          "default": false,
          "info": "Your customers will be sent directly to checkout after click the Add to Cart button."
        },
        {
          "type": "header",
          "content": "Buttons & icons"
        },
        {
          "type": "checkbox",
          "id": "uppercase_text",
          "label": "Uppercase button labels",
          "default": true
        },
        {
          "type": "range",
          "id": "icon_scale",
          "min": 80,
          "max": 180,
          "step": 5,
          "unit": "%",
          "label": "Icons scale",
          "default": 120,
          "info": "Related to button label font size"
        },
        {
          "type": "range",
          "id": "icon_spacing",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Icons spacing",
          "default": 10,
          "info": "Empty space between the button label & the icons"
        },
        {
          "type": "header",
          "content": "Main button"
        },
        {
          "type": "checkbox",
          "id": "display_price",
          "label": "Display price inside the button",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "enable_custom_color",
          "label": "Enable custom button color",
          "default": false
        },
        {
          "type": "color",
          "id": "custom_color",
          "default": "#dd1d1d",
          "label": "Button custom color",
          "info": "Applied when Enable custom button color is checked."
        },
        {
          "type": "image_picker",
          "id": "prefix_icon",
          "label": "Label prefix icon"
        },
        {
          "type": "image_picker",
          "id": "suffix_icon",
          "label": "Label suffix icon"
        },
        {
          "type": "header",
          "content": "Secondary Skip cart ATC button"
        },
        {
          "type": "paragraph",
          "content": "This is a secondary add to cart button which goes straight to checkout. It's a replica of the unbranded \"Buy It Now\" button."
        },
        {
          "type": "paragraph",
          "content": "Since dynamic checkout buttons don't work with multiple variants, this is meant to be a substitute for it."
        },
        {
          "type": "checkbox",
          "id": "enable_secondary_btn",
          "label": "Display secondary Skip cart ATC button",
          "default": false
        },
        {
          "type": "text",
          "id": "secondary_btn_label",
          "label": "Button label",
          "default": "Buy It Now"
        },
        {
          "type": "checkbox",
          "id": "secondary_btn_enable_custom_color",
          "label": "Enable custom button color",
          "default": false
        },
        {
          "type": "color",
          "id": "secondary_btn_custom_color",
          "default": "#dd1d1d",
          "label": "Button custom color",
          "info": "Applied when Enable custom button color is checked."
        },
        {
          "type": "image_picker",
          "id": "secondary_btn_prefix_icon",
          "label": "Label prefix icon"
        },
        {
          "type": "image_picker",
          "id": "secondary_btn_suffix_icon",
          "label": "Label suffix icon"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    }, 
    {
      "type": "sticky_atc",
      "name": "Sticky Add To Cart",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "function",
          "options": [
            {
              "value": "add_to_cart",
              "label": "Add to Cart"
            }, 
            {
              "value": "scroll_#ProductInfo-id",
              "label": "Scroll to Product information"
            }, 
            {
              "value": "scroll_#variant-selects-id",
              "label": "Scroll to Variant picker"
            }, 
            {
              "value": "scroll_#quantity-breaks-id",
              "label": "Scroll to Quantity breaks"
            }, 
            {
              "value": "scroll_.pricing-table",
              "label": "Scroll to Pricing table"
            }
          ],
          "label": "Button function",
          "default": "add_to_cart"
        },
        {
          "type": "select",
          "id": "display_when",
          "options": [
            {
              "value": "always",
              "label": "Always"
            }, 
            {
              "value": "after_scroll",
              "label": "After scrolling past regular ATC button"
            }
          ],
          "label": "Display:",
          "default": "after_scroll"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Add to Cart"
        },
        {
          "type": "checkbox",
          "id": "enable_custom_btn_color",
          "label": "Enable custom button color",
          "default": false
        },
        {
          "type": "color",
          "id": "custom_btn_color",
          "default": "#dd1d1d",
          "label": "Button custom color",
          "info": "Applied when Enable custom button color is checked."
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "t:sections.all.colors.label"
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Rating stars color"
        },
        {
          "type": "inline_richtext",
          "id": "stars_label",
          "label": "Rating stars text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "combined",
              "label": "Combined"
            }, 
            {
              "value": "separate",
              "label": "Separate"
            }
          ],
          "label": "Varaint picker options",
          "default": "combined",
          "info": "Combined combines all options into one dropdown (eg. Black/S), Separate displays a separate dropdown for each option."
        },
        {
          "type": "header",
          "content": "Desktop layout"
        },
        {
          "type": "checkbox",
          "id": "desktop_show_image",
          "default": false,
          "label": "Show product image"
        },
        {
          "type": "checkbox",
          "id": "desktop_show_title",
          "default": true,
          "label": "Show product title"
        },
        {
          "type": "checkbox",
          "id": "desktop_rating_stars",
          "default": false,
          "label": "Show rating stars"
        },
        {
          "type": "checkbox",
          "id": "desktop_show_price",
          "default": true,
          "label": "Show product price"
        },
        {
          "type": "checkbox",
          "id": "desktop_show_sale_badge",
          "default": true,
          "label": "Show price sale badge"
        },
        {
          "type": "checkbox",
          "id": "desktop_variant_picker",
          "default": false,
          "label": "Show variant picker"
        },
        {
          "type": "checkbox",
          "id": "desktop_full_button_width",
          "default": false,
          "label": "Button full width"
        },
        {
          "type": "checkbox",
          "id": "desktop_show_price_in_button",
          "default": false,
          "label": "Show price inside the button"
        },
        {
          "type": "checkbox",
          "id": "desktop_transparent_bg",
          "default": false,
          "label": "Use transparent background style.",
          "info": "This option automatically makes the button full width and hides all other elements."
        },
        {
          "type": "header",
          "content": "Mobile layout"
        },
        {
          "type": "checkbox",
          "id": "mobile_show_image",
          "default": false,
          "label": "Show product image"
        },
        {
          "type": "checkbox",
          "id": "mobile_show_title",
          "default": true,
          "label": "Show product title"
        },
        {
          "type": "checkbox",
          "id": "mobile_rating_stars",
          "default": false,
          "label": "Show rating stars"
        },
        {
          "type": "checkbox",
          "id": "mobile_show_price",
          "default": true,
          "label": "Show product price"
        },
        {
          "type": "checkbox",
          "id": "mobile_show_sale_badge",
          "default": true,
          "label": "Show price sale badge"
        },
        {
          "type": "checkbox",
          "id": "mobile_variant_picker",
          "default": false,
          "label": "Show variant picker"
        },
        {
          "type": "checkbox",
          "id": "mobile_full_button_width",
          "default": false,
          "label": "Button full width",
          "info": "This option is only recommended if title, price and variant picker ara disabled."
        },
        {
          "type": "checkbox",
          "id": "mobile_show_price_in_button",
          "default": false,
          "label": "Show price inside the button"
        },
        {
          "type": "checkbox",
          "id": "mobile_transparent_bg",
          "default": false,
          "label": "Use transparent background style.",
          "info": "This option automatically makes the button full width and hides all other elements."
        }
      ]
    },
    {
      "type": "bundle_deals",
      "name": "Bundle Deals",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Bundle Configuration"
        },
        {
          "type": "text",
          "id": "bundle_title",
          "label": "Bundle Section Title",
          "default": "Choose Your Bundle"
        },
        {
          "type": "paragraph",
          "content": "This block will only show on products with the 'bundle' tag. Collections should contain products with 'Bundle' variants."
        },
        {
          "type": "header",
          "content": "Bundle 1 - Starter Pack"
        },
        {
          "type": "checkbox",
          "id": "bundle_1_enabled",
          "label": "Enable Bundle 1",
          "default": true
        },
        {
          "type": "text",
          "id": "bundle_1_name",
          "label": "Bundle 1 Name",
          "default": "Starter Pack"
        },
        {
          "type": "text",
          "id": "bundle_1_description",
          "label": "Bundle 1 Description",
          "default": "Perfect for beginners"
        },
        {
          "type": "collection",
          "id": "bundle_1_collection",
          "label": "Bundle 1 Collection",
          "info": "Collection containing products with Bundle variants"
        },
        {
          "type": "text",
          "id": "bundle_1_save_text",
          "label": "Bundle 1 Save Text",
          "default": "Save £20"
        },
        {
          "type": "header",
          "content": "Bundle 2 - Complete Treatment"
        },
        {
          "type": "checkbox",
          "id": "bundle_2_enabled",
          "label": "Enable Bundle 2",
          "default": true
        },
        {
          "type": "text",
          "id": "bundle_2_name",
          "label": "Bundle 2 Name",
          "default": "Complete Treatment"
        },
        {
          "type": "text",
          "id": "bundle_2_description",
          "label": "Bundle 2 Description",
          "default": "Most popular choice"
        },
        {
          "type": "collection",
          "id": "bundle_2_collection",
          "label": "Bundle 2 Collection",
          "info": "Collection containing products with Bundle variants"
        },
        {
          "type": "text",
          "id": "bundle_2_save_text",
          "label": "Bundle 2 Save Text",
          "default": "Save £35"
        },
        {
          "type": "header",
          "content": "Bundle 3 - Premium Pack"
        },
        {
          "type": "checkbox",
          "id": "bundle_3_enabled",
          "label": "Enable Bundle 3",
          "default": true
        },
        {
          "type": "text",
          "id": "bundle_3_name",
          "label": "Bundle 3 Name",
          "default": "Premium Pack"
        },
        {
          "type": "text",
          "id": "bundle_3_description",
          "label": "Bundle 3 Description",
          "default": "Complete solution"
        },
        {
          "type": "collection",
          "id": "bundle_3_collection",
          "label": "Bundle 3 Collection",
          "info": "Collection containing products with Bundle variants"
        },
        {
          "type": "text",
          "id": "bundle_3_save_text",
          "label": "Bundle 3 Save Text",
          "default": "Save £50"
        },
        {
          "type": "header",
          "content": "Color Customization"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Bundle Title Color",
          "default": "#333333"
        },
        {
          "type": "color",
          "id": "tab_text_color",
          "label": "Tab Text Color",
          "default": "#666666"
        },
        {
          "type": "color",
          "id": "tab_active_color",
          "label": "Active Tab Color",
          "default": "#2c5aa0"
        },
        {
          "type": "color",
          "id": "tab_border_color",
          "label": "Tab Border Color",
          "default": "#e0e0e0"
        },
        {
          "type": "color",
          "id": "product_card_border",
          "label": "Product Card Border Color",
          "default": "#2c5aa0"
        },
        {
          "type": "color",
          "id": "product_card_background",
          "label": "Product Card Background",
          "default": "#f8f9ff"
        },
        {
          "type": "color",
          "id": "save_text_color",
          "label": "Save Text Color",
          "default": "#2c5aa0"
        },
        {
          "type": "color",
          "id": "free_gifts_background",
          "label": "Free Gifts Background",
          "default": "#f0f8f0"
        },
        {
          "type": "color",
          "id": "free_text_color",
          "label": "FREE Text Color",
          "default": "#28a745"
        },
        {
          "type": "color",
          "id": "price_color",
          "label": "Price Text Color",
          "default": "#333333"
        },
        {
          "type": "color",
          "id": "button_background",
          "label": "Add to Cart Button Background",
          "default": "#5cb85c"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Add to Cart Button Text",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "main_badge_background",
          "label": "Main Badge Background",
          "default": "#5cb85c"
        },
        {
          "type": "color",
          "id": "main_badge_text_color",
          "label": "Main Badge Text Color",
          "default": "#ffffff"
        },
        {
          "type": "header",
          "content": "Styling"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 100,
          "step": 4,
          "unit": "px",
          "label": "Top margin",
          "default": 36
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 100,
          "step": 4,
          "unit": "px",
          "label": "Bottom margin",
          "default": 36
        }
      ]
    },
    {
      "type": "reviews",
      "name": "Reviews",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            { 
              "value": "inverse", 
              "label": "t:sections.all.colors.inverse.label" 
            }
          ],
          "default": "background-1",
          "label": "Color scheme"
        },
        {
          "type": "checkbox",
          "id": "show_custom_bg",
          "label": "Show custom background",
          "default": false
        },
        {
          "type": "color",
          "id": "custom_bg_color",
          "label": "Custom background color",
          "default": "#F2F2F2"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 12
        },
        {
          "type": "range",
          "id": "border_width",
          "min": 0,
          "max": 5,
          "step": 1,
          "label": "Border thickness",
          "unit": "px",
          "default": 0
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#B7B7B7"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "select",
          "id": "avatar_alignment",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "middle",
              "label": "Middle"
            }
          ],
          "label": "Avatar alignment",
          "default": "top"
        },
        {
          "type": "range",
          "id": "avatar_corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Avatar corner radius",
          "default": 40
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "range",
          "id": "stars_translate",
          "min": -10,
          "max": 20,
          "step": 1,
          "unit": "%",
          "default": 0,
          "label": "Stars vertical position adjustment",
          "info": "Move the stars up/down to adjust them to different fonts. Negative values move the stars up."
        },
        {
          "type": "color",
          "id": "checkmark_color",
          "default": "#6D388B",
          "label": "Checkmark background color"
        },
        {
          "type": "color",
          "id": "checkmark_icon_color",
          "default": "#FFFFFF",
          "label": "Checkmark icon color"
        },
        {
          "type": "header",
          "content": "Slider"
        },
        {
          "type": "select",
          "id": "slider_type",
          "options": [
            {
              "value": "slide",
              "label": "Classic"
            },
            {
              "value": "loop",
              "label": "Infinite"
            },
            {
              "value": "fade",
              "label": "Fade"
            }
          ],
          "default": "slide",
          "label": "Type"
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "Enable autoplay",
          "default": false
        },
        {
          "type": "range",
          "id": "autoplay_speed",
          "min": 1,
          "max": 15,
          "step": 0.5,
          "default": 5,
          "unit": "sec",
          "label": "Autoplay speed"
        },
        {
          "type": "checkbox",
          "id": "display_arrows",
          "label": "Display arrows",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "display_dots",
          "label": "Display dots",
          "default": true
        },
        {
          "type": "header",
          "content": "Review #1"
        },
        {
          "type": "inline_richtext",
          "id": "author_1",
          "label": "Review #1 Author name",
          "default": "<em>Author</em> [stars]",
          "info": "Use [stars] to display review stars and [checkmark] to display the verified checkmark icon."
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Review #1 Author image"
        },
        {
          "type": "richtext",
          "id": "text_1",
          "label": "Review #1 Text",
          "default": "<p>Share positive thoughts and feedback from your customer.</p>"
        },
        {
          "type": "header",
          "content": "Review #2"
        },
        {
          "type": "inline_richtext",
          "id": "author_2",
          "label": "Review #2 Author name",
          "default": "<em>Author</em> [stars]",
          "info": "Use [stars] to display review stars and [checkmark] to display the verified checkmark icon."
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Review #2 Author image"
        },
        {
          "type": "richtext",
          "id": "text_2",
          "label": "Review #2 Text",
          "default": "<p>Share positive thoughts and feedback from your customer.</p>"
        },
        {
          "type": "header",
          "content": "Review #3"
        },
        {
          "type": "inline_richtext",
          "id": "author_3",
          "label": "Review #3 Author name",
          "default": "<em>Author</em> [stars]",
          "info": "Use [stars] to display review stars and [checkmark] to display the verified checkmark icon."
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "Review #3 Author image"
        },
        {
          "type": "richtext",
          "id": "text_3",
          "label": "Review #3 Text",
          "default": "<p>Share positive thoughts and feedback from your customer.</p>"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "description",
      "name": "t:sections.main-product.blocks.description.name",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "share",
      "name": "t:sections.main-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.main-product.blocks.share.settings.text.label",
          "default": "Share"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.title_info.content"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.main-product.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.main-product.blocks.custom_liquid.settings.custom_liquid.label",
          "info": "t:sections.main-product.blocks.custom_liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.main-product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "Heading size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium"
        },
        {
          "type": "text",
          "id": "icon",
          "default": "check_box",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon"
        },
        {
          "type": "select",
          "id": "collapse_icon",
          "label": "Collapse icon",
          "options": [
            {
              "value": "carret",
              "label": "Carret"
            },
            {
              "value": "plus",
              "label": "Plus"
            }
          ],
          "default": "carret"
        },
        {
          "type": "checkbox",
          "id": "display_top_border",
          "label": "Display top border",
          "default": true,
          "info": "This option is automatically optimized for stacked rows."
        },
        {
          "type": "checkbox",
          "id": "open",
          "label": "Open row by default",
          "default": false
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label",
          "info": "Use [description] to automatically display product description."
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 0
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Pop-up link text",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        },
        {
          "type": "select",
          "id": "close_button_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "minimalistic",
              "label": "Minimalistic"
            }
          ],
          "default": "minimalistic",
          "label": "Close button style"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 12
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 12
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.main-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.rating.settings.paragraph.content"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "complementary",
      "name": "t:sections.main-product.blocks.complementary_products.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.complementary_products.settings.paragraph.content"
        },
        {
          "type": "text",
          "id": "block_heading",
          "default": "Pairs well with",
          "label": "t:sections.main-product.blocks.complementary_products.settings.heading.label"
        },
        {
          "type": "checkbox",
          "id": "make_collapsible_row",
          "default": false,
          "label": "t:sections.main-product.blocks.complementary_products.settings.make_collapsible_row.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "price_tag",
          "info": "t:sections.main-product.blocks.complementary_products.settings.icon.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "range",
          "id": "product_list_limit",
          "min": 1,
          "max": 10,
          "step": 1,
          "default": 10,
          "label": "t:sections.main-product.blocks.complementary_products.settings.product_list_limit.label"
        },
        {
          "type": "range",
          "id": "products_per_page",
          "min": 1,
          "max": 4,
          "step": 1,
          "default": 3,
          "label": "t:sections.main-product.blocks.complementary_products.settings.products_per_page.label"
        },
        {
          "type": "select",
          "id": "pagination_style",
          "options": [
            {
              "value": "dots",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_1"
            },
            {
              "value": "counter",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_2"
            },
            {
              "value": "numbers",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_3"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.label",
          "default": "counter"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.complementary_products.settings.product_card.heading"
        },
        {
          "type": "select",
          "id": "image_ratio",
          "options": [
            {
              "value": "portrait",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_1"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_2"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.label",
          "default": "square"
        },
        {
          "type": "checkbox",
          "id": "enable_quick_add",
          "label": "t:sections.main-product.blocks.complementary_products.settings.enable_quick_add.label",
          "default": false
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "sizing_chart",
      "name": "Sizing chart",
      "settings": [
        {
          "type": "checkbox",
          "id": "test_mode",
          "label": "Test mode",
          "info": "Keeps the popup open for faster customization.",
          "default": false
        },
        {
          "type": "header",
          "content": "Open button"
        },
        {
          "type": "paragraph",
          "content": "IMPORTANT: If using the append, the Sizing blcok has to be placed ABOVE the Variant picker block."
        },
        {
          "type": "select",
          "id": "append",
          "options": [
            {
              "value": "none",
              "label": "Don't append"
            },
            {
              "value": "option_1_label_container",
              "label": "Option #1 label"
            },
            {
              "value": "option_1_under_container",
              "label": "Under option #1 picker"
            },
            {
              "value": "option_2_label_container",
              "label": "Option #2 label"
            },
            {
              "value": "option_2_under_container",
              "label": "Under option #2 picker"
            },
            {
              "value": "option_3_label_container",
              "label": "Option #3 label"
            },
            {
              "value": "option_3_under_container",
              "label": "Under option #3 picker"
            }
          ],
          "default": "none",
          "label": "Append the open button into the variant picker"
        },
        {
          "type": "inline_richtext",
          "id": "button_label",
          "label": "Button label",
          "default": "Sizing chart"
        },
        {
          "type": "text",
          "id": "button_icon",
          "default": "straighten",
          "label": "Button icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "button_filled_icon",
          "default": false,
          "label": "Button filled icon"
        },
        {
          "type": "image_picker",
          "id": "button_custom_icon",
          "label": "Button custom icon"
        },
        {
          "type": "range",
          "id": "text_size",
          "min": 12,
          "max": 22,
          "step": 1,
          "unit": "px",
          "default": 17,
          "label": "Button text size"
        },
        {
          "type": "select",
          "id": "button_alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ],
          "default": "start",
          "label": "Button alignment"
        },
        {
          "type": "checkbox",
          "id": "button_underline",
          "default": false,
          "label": "Button underlined text"
        },
        {
          "type": "header",
          "content": "Popup"
        },
        {
          "type": "text",
          "id": "headline",
          "default": "Sizing Chart",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h3",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "close_button_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "minimalistic",
              "label": "Minimalistic"
            }
          ],
          "default": "minimalistic",
          "label": "Close button style"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            { 
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "t:sections.all.colors.label"
        },
        {
          "type": "header",
          "content": "Table"
        },
        {
          "type": "inline_richtext",
          "id": "table_header_content",
          "label": "Header content",
          "default": "Size, Chest, Length, Shoulder",
          "info": "Split the words with a comma to create columns. If you wish to leave a cell empty, use [empty]. Avoid styling the comma with bold,italic etc. to avoid unexpected content styling."
        },
        {
          "type": "select",
          "id": "table_header_color_scheme",
          "options": [
            { 
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "inverse",
          "label": "Header color scheme"
        },
        {
          "type": "richtext",
          "id": "table_content",
          "label": "Body content",
          "default": "<p>S, 95cm, 61cm, 44cm</p><p>M, 102cm, 66cm, 46cm</p><p>L, 106cm, 71cm, 48cm</p><p>XL, 111cm, 74cm, 51cm</p>",
          "info": "Go into a new line to create rows. Split the words with a comma to create columns. If you wish to leave a cell empty, use [empty]. Avoid styling the comma with bold,italic etc. to avoid unexpected content styling."
        },
        {
          "type": "header",
          "content": "Caption"
        },
        {
          "type": "richtext",
          "id": "caption_text",
          "label": "Caption",
          "default": "<p>Give your customers further information about choosing the correct size.</p>"
        },
        {
          "type": "select",
          "id": "caption_size",
          "options": [
            {
              "value": "1.2rem",
              "label": "Small"
            },
            {
              "value": "1.45rem",
              "label": "Medium"
            },
            {
              "value": "1.7rem",
              "label": "Large"
            }
          ],
          "default": "1.45rem",
          "label": "Caption size"
        },
        {
          "type": "color",
          "id": "caption_color",
          "label": "Caption color",
          "default": "#121212"
        },
        {
          "type": "select",
          "id": "caption_alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Caption alignment"
        },
        {
          "type": "header",
          "content": "Images"
        },
        {
          "type": "image_picker",
          "id": "top_image",
          "label": "Top image",
          "info": "Displayed above the table."
        },
        {
          "type": "image_picker",
          "id": "bottom_image",
          "label": "Bottom image",
          "info": "Displayed bellow the table and the caption."
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "product_upsell",
      "name": "Product upsells",
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "style",
          "options": [
            {
              "value": "toggle_switch",
              "label": "Toggle switch"
            },
            {
              "value": "checkbox_1",
              "label": "Checkbox style 1"
            },
            {
              "value": "checkbox_2",
              "label": "Checkbox style 2"
            },
            {
              "value": "plus_button",
              "label": "Plus button"
            },
            {
              "value": "add_button",
              "label": "Classic add button"
            }
          ],
          "default": "toggle_switch",
          "label": "Toggle button style"
        },
        {
          "type": "select",
          "id": "btn_position",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "right",
          "label": "Toggle button position"
        },
        {
          "type": "select",
          "id": "toggle_element",
          "options": [
            {
              "value": "button",
              "label": "Toggle button"
            },
            {
              "value": "container",
              "label": "Whole container"
            }
          ],
          "default": "button",
          "label": "Toggle product selection by clicking on:"
        },
        {
          "type": "inline_richtext",
          "id": "add_btn_label",
          "label": "Classic add button label",
          "default":  "<strong>Add</strong>"
        },
        {
          "type": "select",
          "id": "stacking",
          "options": [
            {
              "value": "row",
              "label": "Under each other"
            },
            {
              "value": "column",
              "label": "Next to each other"
            },
            {
              "value": "slider",
              "label": "Slider"
            }
          ],
          "default": "row",
          "label": "Multiple products stacking",
          "info": "If Next to each other is selected, featured images with transparent backgrounds are recommended."
        },
        {
          "type": "select",
          "id": "accent_color",
          "label": "Accent color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            { 
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Products"
        },
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Products",
          "limit": 3,
          "info": "Up to 3 products per block are supported."
        },
        {
          "type": "paragraph",
          "content": "Or"
        },
        {
          "type": "checkbox",
          "id": "enable_dynamic_recommendations",
          "label": "Enable dynamic product recommendations",
          "default": false,
          "info": "If this option is enabled & recommendations are available, dynamic recommendations created by Shopify will be displayed inside the upsell block."
        },
        {
          "type": "header",
          "content": "Product #1"
        },
        {
          "type": "checkbox",
          "id": "prdouct_1_preselected",
          "label": "Product #1 Preselected",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "product_1_image",
          "label": "Product #1 Featured image"
        },
        {
          "type": "inline_richtext",
          "id": "product_1_desc",
          "label": "Product #1 Description"
        },
        {
          "type": "text",
          "id": "product_1_percentage_discount",
          "label": "Product #1 Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_1_fixed_amount_discount",
          "label": "Product #1 Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "header",
          "content": "Product #2"
        },
        {
          "type": "checkbox",
          "id": "prdouct_2_preselected",
          "label": "Product #2 Preselected",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "product_2_image",
          "label": "Product #2 Featured image"
        },
        {
          "type": "inline_richtext",
          "id": "product_2_desc",
          "label": "Product #2 Description"
        },
        {
          "type": "text",
          "id": "product_2_percentage_discount",
          "label": "Product #2 Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_2_fixed_amount_discount",
          "label": "Product #2 Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "header",
          "content": "Product #3"
        },
        {
          "type": "checkbox",
          "id": "prdouct_3_preselected",
          "label": "Product #3 Preselected",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "product_3_image",
          "label": "Product #3 Featured image"
        },
        {
          "type": "inline_richtext",
          "id": "product_3_desc",
          "label": "Product #3 Description"
        },
        {
          "type": "text",
          "id": "product_3_percentage_discount",
          "label": "Product #3 Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_3_fixed_amount_discount",
          "label": "Product #3 Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "checkbox",
          "id": "show_images",
          "label": "Show product images",
          "default": true
        },
        {
          "type": "select",
          "id": "images_size",
          "options": [
            {
              "value": "3.5",
              "label": "Extra small"
            },
            {
              "value": "4.25",
              "label": "Small"
            },
            {
              "value": "5",
              "label": "Medium"
            },
            {
              "value": "5.75",
              "label": "Large"
            },
            {
              "value": "6.5",
              "label": "Extra large"
            }
          ],
          "default": "5",
          "label": "Images size"
        },
        {
          "type": "range",
          "id": "images_corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Images corner radius",
          "default": 0
        },
        {
          "type": "select",
          "id": "title_size",
          "options": [
            {
              "value": "1",
              "label": "Extra small"
            },
            {
              "value": "1.2",
              "label": "Small"
            },
            {
              "value": "1.4",
              "label": "Medium"
            },
            {
              "value": "1.6",
              "label": "Large"
            },
            {
              "value": "1.8",
              "label": "Extra large"
            }
          ],
          "default": "1.4",
          "label": "Product title font size"
        },
        {
          "type": "checkbox",
          "id": "title_link",
          "label": "Make title a product page link",
          "default": false
        },
        {
          "type": "select",
          "id": "desc_size",
          "options": [
            {
              "value": "0.9",
              "label": "Extra small"
            },
            {
              "value": "1.05",
              "label": "Small"
            },
            {
              "value": "1.2",
              "label": "Medium"
            },
            {
              "value": "1.35",
              "label": "Large"
            },
            {
              "value": "1.5",
              "label": "Extra large"
            }
          ],
          "default": "1.2",
          "label": "Description font size"
        },
        {
          "type": "select",
          "id": "price_position",
          "options": [
            {
              "value": "next_to_title",
              "label": "Next to title"
            },
            {
              "value": "separate",
              "label": "Separate"
            },
            {
              "value": "hidden",
              "label": "Hidden"
            }
          ],
          "default": "next_to_title",
          "label": "Price position"
        },
        {
          "type": "select",
          "id": "price_size",
          "options": [
            {
              "value": "1",
              "label": "Extra small"
            },
            {
              "value": "1.2",
              "label": "Small"
            },
            {
              "value": "1.4",
              "label": "Medium"
            },
            {
              "value": "1.6",
              "label": "Large"
            },
            {
              "value": "1.8",
              "label": "Extra large"
            }
          ],
          "default": "1.4",
          "label": "Price font size"
        },
        {
          "type": "checkbox",
          "id": "hide_compare_price",
          "label": "Hide compare price",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_variant_picker",
          "label": "Show variant picker",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "update_prices",
          "label": "Enable variant price updates",
          "default": false,
          "info": "This option will dynamically change the displayed price based on the selected variant. ATTENTION: This option might NOT work with currency converters."
        },
        {
          "type": "checkbox",
          "id": "skip_unavailable",
          "label": "Hide & automatically skip sold out variants",
          "default": false
        },
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "range",
          "id": "corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "t:settings_schema.global.settings.corner_radius.label",
          "default": 0
        },
        {
          "type": "checkbox",
          "id": "show_box_shadow",
          "label": "Show drop shadow",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_border",
          "label": "Show border",
          "default": false
        },
        {
          "type": "color",
          "id": "regular_border_color",
          "label": "Border color",
          "default": "#E6E6E6"
        },
        {
          "type": "color",
          "id": "selected_border_color",
          "label": "Selected border color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "border_width",
          "options": [
            {
              "value": "0.1",
              "label": "Thin"
            },
            {
              "value": "0.2",
              "label": "Normal"
            }
          ],
          "default": "0.2",
          "label": "Border thickness"
        },
        {
          "type": "checkbox",
          "id": "show_custom_bg",
          "label": "Show custom background",
          "default": false
        },
        {
          "type": "color",
          "id": "regular_bg_color",
          "label": "Background color",
          "default": "#F2F2F2"
        },
        {
          "type": "color",
          "id": "selected_bg_color",
          "label": "Selected background color",
          "default": "#F2F2F2"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 21
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 21
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Image width",
          "default": 100
        },
        {
          "type": "checkbox",
          "id": "Full_mobile_width",
          "label": "Full container width on mobile",
          "default": false,
          "info": "If enabled & Width is set to 100%, the image will be edge-to-edge on mobile."
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Image alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 0
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "image_picker",
          "id": "thumbnail",
          "label": "Video Thumbnail",
          "info": "If empty, the first frame of the video will be displayed"
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Video looping",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "muted_autoplay",
          "label": "Muted autoplay",
          "default": true,
          "info": "Use this instead of GIFs & animated WEBPs."
        },
        {
          "type": "checkbox",
          "id": "display_play_btn",
          "label": "Enable play & pause on click",
          "info": "Automatically enabled if autoplay is disabled.",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "btn_animation",
          "label": "Enable button ripple animation",
          "default": false
        },
        {
          "type": "select",
          "id": "btn_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "accent-1",
          "label": "Play button color scheme"
        },
        {
          "type": "checkbox",
          "id": "display_sound_btn",
          "label": "Display mute/unmute button",
          "default": false
        },
        {
          "type": "select",
          "id": "sound_btn_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "inverse",
          "label": "Sound button color scheme"
        },
        {
          "type": "checkbox",
          "id": "display_timeline",
          "label": "Display timeline",
          "default": false
        },
        {
          "type": "select",
          "id": "timeline_color",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "accent-1",
          "label": "Timeline color"
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Video width",
          "default": 100
        },
        {
          "type": "checkbox",
          "id": "Full_mobile_width",
          "label": "Full container width on mobile",
          "default": false,
          "info": "If enabled & Width is set to 100%, the video will be edge-to-edge on mobile."
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Video alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 0
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "divider",
      "name": "Divider",
      "settings": [
        {
          "type": "color",
          "id": "color",
          "label": "Color",
          "default": "#E1E1E1"
        },
        {
          "type": "range",
          "id": "height",
          "min": 1,
          "max": 10,
          "step": 1,
          "unit": "px",
          "label": "Height",
          "default": 1
        },
        {
          "type": "range",
          "id": "width",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Width",
          "default": 100
        },
        {
          "type": "checkbox",
          "id": "Full_mobile_width",
          "label": "Full container width on mobile",
          "default": false,
          "info": "If enabled & Width is set to 100%, the divider will be edge-to-edge on mobile."
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 5,
          "step": 1,
          "unit": "px",
          "label": "Corner radius",
          "default": 0,
          "info": "Only visible with bigger heights."
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "payment_badges",
      "name": "Payment badges",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "enabled_payment_types",
          "label": "Custom payment icons to show",
          "info": "List of payments you want to show, split with a comma. Options are: afterpay, american_express, apple_pay, bitcoin, dankort, diners_club, discover, dogecoin, dwolla, facebook_pay, forbrugsforeningen, google_pay, ideal, jcb, klarna, klarna-pay-later, litecoin, maestro, master, paypal, shopify_pay, sofort, unionpay, visa"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "urgency",
      "name": "Urgency text",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "urgency",
          "label": "Text",
          "default": "HOT PRODUCT | LOW STOCK"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "accent-1",
          "label": "Color"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 0
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "emoji_benefits",
      "name": "Emoji benefits",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "benefits",
          "label": "Benefits",
          "default": "<p>🙌 Benefit</p><p>💪 Benefit</p><p>🌟 Benefit</p>"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "bundle_offer",
      "name": "Bundle offer",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Give customers an option of a premade bundle of 2-3 units of your main proudct/different products to increase AOV."
        },
        {
          "type": "paragraph",
          "content": "IMPORTANT: Automatic discounts for the bundle discount/free gift can not be combined with other automatic discounts/codes."
        },
        {
          "type": "paragraph",
          "content": "To overcome this, you can create a duplicate of the product with a lower price."
        },
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "type",
          "label": "Offer type",
          "options": [
            {
              "value": "main",
              "label": "Main"
            },
            {
              "value": "separate",
              "label": "Separate"
            }
          ],
          "default": "separate",
          "info": "Main acts as the main page offer, is connected to the main Add to Cart button and includes the quantity gifts & proudct page upsells. Separate is a separate offer, has a separate Add to Cart button, doesn't include quantity gifts and optionally includes product page upsells."
        },
        {
          "type": "checkbox",
          "id": "include_pdp_upsells",
          "label": "Include product page upsells with a separate offer",
          "default": false
        },
        {
          "type": "text",
          "id": "title_text",
          "label": "Title",
          "default": "BUY X & GET Y"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h5",
              "label": "Small"
            },
            {
              "value": "h4",
              "label": "Medium"
            },
            {
              "value": "h3",
              "label": "Large"
            }
          ],
          "default": "h4",
          "label": "Title size"
        },
        {
          "type": "select",
          "id": "title_alignment",
          "label": "Title alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "accent_color",
          "label": "Prices color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            { 
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "Color scheme"
        },
        {
          "type": "header",
          "content": "Container"
        },
        {
          "type": "select",
          "id": "container_border_color",
          "label": "Border & dividers color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "text"
        },
        {
          "type": "range",
          "id": "container_border_thickness",
          "min": 0,
          "max": 5,
          "step": 1,
          "unit": "px",
          "label": "Border thickness",
          "default": 1
        },
        {
          "type": "range",
          "id": "container_border_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Border opacity",
          "default": 100
        },
        {
          "type": "range",
          "id": "border_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Corner radius",
          "default": 4
        },
        {
          "type": "range",
          "id": "divider_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Products divider opacity",
          "default": 100
        },
        {
          "type": "range",
          "id": "plus_border_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Divider plus icon border opacity",
          "default": 100
        },
        {
          "type": "header",
          "content": "Footer"
        },
        {
          "type": "checkbox",
          "id": "show_footer",
          "label": "Show the footer in main offer",
          "default": true
        },
        {
          "type": "inline_richtext",
          "id": "total_price_text",
          "label": "Total price label",
          "default": "<strong>Total:</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "total_price_benefit",
          "label": "Total price benefit",
          "default": "<strong>FREE XXX INCLUDED</strong>"
        },
        {
          "type": "select",
          "id": "benefit_color",
          "label": "Benefit color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "outline-button",
              "label": "Outline button"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": "accent-1"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Add to Cart button label",
          "default": "GRAB THIS DEAL",
          "info": "Displayd if Offer type is set to Separate."
        },
        {
          "type": "header",
          "content": "Prices & discounts"
        },
        {
          "type": "checkbox",
          "id": "update_prices",
          "label": "Enable variant price updates",
          "info": "Enables live changes to item and total prices after variant change. IMPORTANT: Due to Liquid limitations, after a change, new price might NOT work with currency converters.",
          "default": false
        },
        {
          "type": "text",
          "id": "percentage_discount",
          "label": "Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the total price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "fixed_amount_discount",
          "label": "Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the total price, WITHOUT the currency symbol."
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "checkbox",
          "id": "show_images",
          "label": "Show product images",
          "default": true
        },
        {
          "type": "select",
          "id": "displayed_image",
          "options": [
            {
              "value": "featured",
              "label": "Featured image"
            },
            {
              "value": "variant",
              "label": "First variant image"
            }
          ],
          "default": "variant",
          "label": "Displayed product images"
        },
        {
          "type": "select",
          "id": "images_size",
          "options": [
            {
              "value": "3.5",
              "label": "Extra small"
            },
            {
              "value": "4.25",
              "label": "Small"
            },
            {
              "value": "5",
              "label": "Medium"
            },
            {
              "value": "5.75",
              "label": "Large"
            },
            {
              "value": "6.5",
              "label": "Extra large"
            }
          ],
          "default": "5",
          "label": "Images size"
        },
        {
          "type": "range",
          "id": "images_corner_radius",
          "min": 0,
          "max": 40,
          "step": 2,
          "unit": "px",
          "label": "Images corner radius",
          "default": 0
        },
        {
          "type": "select",
          "id": "title_size",
          "options": [
            {
              "value": "1",
              "label": "Extra small"
            },
            {
              "value": "1.2",
              "label": "Small"
            },
            {
              "value": "1.4",
              "label": "Medium"
            },
            {
              "value": "1.6",
              "label": "Large"
            },
            {
              "value": "1.8",
              "label": "Extra large"
            }
          ],
          "default": "1.4",
          "label": "Product title font size"
        },
        {
          "type": "checkbox",
          "id": "title_link",
          "label": "Display titles as product page links",
          "default": false
        },
        {
          "type": "select",
          "id": "desc_size",
          "options": [
            {
              "value": "0.9",
              "label": "Extra small"
            },
            {
              "value": "1.05",
              "label": "Small"
            },
            {
              "value": "1.2",
              "label": "Medium"
            },
            {
              "value": "1.35",
              "label": "Large"
            },
            {
              "value": "1.5",
              "label": "Extra large"
            }
          ],
          "default": "1.2",
          "label": "Description font size"
        },
        {
          "type": "select",
          "id": "price_position",
          "options": [
            {
              "value": "next_to_title",
              "label": "Next to title"
            },
            {
              "value": "separate",
              "label": "Separate"
            },
            {
              "value": "hidden",
              "label": "Hidden"
            }
          ],
          "default": "next_to_title",
          "label": "Price position"
        },
        {
          "type": "select",
          "id": "price_size",
          "options": [
            {
              "value": "1",
              "label": "Extra small"
            },
            {
              "value": "1.2",
              "label": "Small"
            },
            {
              "value": "1.4",
              "label": "Medium"
            },
            {
              "value": "1.6",
              "label": "Large"
            },
            {
              "value": "1.8",
              "label": "Extra large"
            }
          ],
          "default": "1.4",
          "label": "Price font size"
        },
        {
          "type": "select",
          "id": "displayed_free_price",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "number",
              "label": "Regular price"
            }
          ],
          "default": "text",
          "label": "Free price content"
        },
        {
          "type": "text",
          "id": "displayed_free_price_text",
          "label": "Free price text",
          "default": "FREE",
          "info": "Displayed instead of the regular price (eg. $0.00) if the product is free."
        },
        {
          "type": "checkbox",
          "id": "show_variant_picker",
          "label": "Show variant picker",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "skip_unavailable",
          "label": "Hide & automatically skip sold out variants",
          "default": false
        },
        {
          "type": "header",
          "content": "Product #1"
        },
        {
          "type": "product",
          "id": "product_1",
          "label": "Product #1"
        },
        {
          "type": "inline_richtext",
          "id": "product_1_desc",
          "label": "Description"
        },
        {
          "type": "text",
          "id": "product_1_percentage_discount",
          "label": "Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_1_fixed_amount_discount",
          "label": "Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "select",
          "id": "product_1_top_padding",
          "options": [
            {
              "value": "pt-0",
              "label": "Reduced"
            },
            {
              "value": "pt-auto",
              "label": "Normal"
            }
          ],
          "default": "pt-auto",
          "label": "Top spacing"
        },
        {
          "type": "select",
          "id": "product_1_bottom_padding",
          "options": [
            {
              "value": "pb-0",
              "label": "Reduced"
            },
            {
              "value": "pb-auto",
              "label": "Normal"
            }
          ],
          "default": "pb-auto",
          "label": "Bottom spacing"
        },
        {
          "type": "header",
          "content": "Product #2"
        },
        {
          "type": "product",
          "id": "product_2",
          "label": "Product #2"
        },
        {
          "type": "inline_richtext",
          "id": "product_2_desc",
          "label": "Description"
        },
        {
          "type": "text",
          "id": "product_2_percentage_discount",
          "label": "Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_2_fixed_amount_discount",
          "label": "Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "select",
          "id": "product_2_top_padding",
          "options": [
            {
              "value": "pt-0",
              "label": "Reduced"
            },
            {
              "value": "pt-auto",
              "label": "Normal"
            }
          ],
          "default": "pt-auto",
          "label": "Top spacing"
        },
        {
          "type": "select",
          "id": "product_2_bottom_padding",
          "options": [
            {
              "value": "pb-0",
              "label": "Reduced"
            },
            {
              "value": "pb-auto",
              "label": "Normal"
            }
          ],
          "default": "pb-auto",
          "label": "Bottom spacing"
        },
        {
          "type": "header",
          "content": "Product #3"
        },
        {
          "type": "product",
          "id": "product_3",
          "label": "Product #3"
        },
        {
          "type": "inline_richtext",
          "id": "product_3_desc",
          "label": "Description"
        },
        {
          "type": "text",
          "id": "product_3_percentage_discount",
          "label": "Percentage discount",
          "default": "0",
          "info": "Percentae to be discounted from the product price, WITHOUT the % symbol."
        },
        {
          "type": "text",
          "id": "product_3_fixed_amount_discount",
          "label": "Fixed amount discount",
          "default": "0",
          "info": "Fixed amount of money to be discounted from the product price, WITHOUT the currency symbol."
        },
        {
          "type": "select",
          "id": "product_3_top_padding",
          "options": [
            {
              "value": "pt-0",
              "label": "Reduced"
            },
            {
              "value": "pt-auto",
              "label": "Normal"
            }
          ],
          "default": "pt-auto",
          "label": "Top spacing"
        },
        {
          "type": "select",
          "id": "product_3_bottom_padding",
          "options": [
            {
              "value": "pb-0",
              "label": "Reduced"
            },
            {
              "value": "pb-auto",
              "label": "Normal"
            }
          ],
          "default": "pb-auto",
          "label": "Bottom spacing"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 21
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 21
        }
      ]
    },
    {
      "type": "quantity_gifts",
      "name": "Gifts on quantity",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "unlocked_bg_color",
          "default": "#FEF8F8",
          "label": "Unlocked item background color"
        },
        {
          "type": "color",
          "id": "unlocked_border_color",
          "default": "#EB7E7E",
          "label": "Unlocked item border color"
        },
        {
          "type": "select",
          "id": "gift_box_color",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "accent-1",
          "label": "Gift box color"
        },
        {
          "type": "color",
          "id": "gift_box_tie_color",
          "default": "#FAE787",
          "label": "Gift box tie color"
        },
        {
          "type": "header",
          "content": "Gift #1"
        },
        {
          "type": "product",
          "id": "gift_1",
          "label": "Gift #1"
        },
        {
          "type": "number",
          "id": "gift_1_quantity",
          "default": 1,
          "label": "Gift #1 minimum quantity",
          "info": "Minimum quantity required to unlock the gift."
        },
        {
          "type": "image_picker",
          "id": "gift_1_image",
          "label": "Gift #1 featured image",
          "info": "An image with transparent background is recommended."
        },
        {
          "type": "inline_richtext",
          "id": "gift_1_top_text",
          "label": "Gift #1 locked top text",
          "default": "<strong>1 PACK</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "gift_1_bottom_text",
          "label": "Gift #1 locked bottom text",
          "default": "<strong>Exclusive</strong>"
        },
        {
          "type": "header",
          "content": "Gift #2"
        },
        {
          "type": "product",
          "id": "gift_2",
          "label": "Gift #2"
        },
        {
          "type": "number",
          "id": "gift_2_quantity",
          "default": 2,
          "label": "Gift #2 minimum quantity",
          "info": "Minimum quantity required to unlock the gift."
        },
        {
          "type": "image_picker",
          "id": "gift_2_image",
          "label": "Gift #2 featured image",
          "info": "An image with transparent background is recommended."
        },
        {
          "type": "inline_richtext",
          "id": "gift_2_top_text",
          "label": "Gift #2 locked top text",
          "default": "<strong>2 PACK</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "gift_2_bottom_text",
          "label": "Gift #2 locked bottom text",
          "default": "<strong>Exclusive</strong>"
        },
        {
          "type": "header",
          "content": "Gift #3"
        },
        {
          "type": "product",
          "id": "gift_3",
          "label": "Gift #3"
        },
        {
          "type": "number",
          "default": 3,
          "id": "gift_3_quantity",
          "label": "Gift #3 minimum quantity",
          "info": "Minimum quantity required to unlock the gift."
        },
        {
          "type": "image_picker",
          "id": "gift_3_image",
          "label": "Gift #3 featured image",
          "info": "An image with transparent background is recommended."
        },
        {
          "type": "inline_richtext",
          "id": "gift_3_top_text",
          "label": "Gift #3 locked top text",
          "default": "<strong>3 PACK</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "gift_3_bottom_text",
          "label": "Gift #3 locked bottom text",
          "default": "<strong>Exclusive</strong>"
        },
        {
          "type": "header",
          "content": "Gift #4"
        },
        {
          "type": "product",
          "id": "gift_4",
          "label": "Gift #4"
        },
        {
          "type": "number",
          "default": 4,
          "id": "gift_4_quantity",
          "label": "Gift #4 minimum quantity",
          "info": "Minimum quantity required to unlock the gift."
        },
        {
          "type": "image_picker",
          "id": "gift_4_image",
          "label": "Gift #4 featured image",
          "info": "An image with transparent background is recommended."
        },
        {
          "type": "inline_richtext",
          "id": "gift_4_top_text",
          "label": "Gift #4 locked top text",
          "default": "<strong>4 PACK</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "gift_4_bottom_text",
          "label": "Gift #4 locked bottom text",
          "default": "<strong>Exclusive</strong>"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 30,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "estimated_shipping",
      "name": "Estimated shipping",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "icon",
          "default": "local_shipping",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon"
        },
        {
          "type": "select",
          "id": "icon_size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium",
          "label": "Icon size"
        },
        {
          "type": "select",
          "id": "icon_alignment",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "middle",
              "label": "Middle"
            }
          ],
          "default": "top",
          "label": "Icon alignment"
        },
        {
          "type": "richtext",
          "id": "message",
          "label": "Text",
          "default": "<p>Get it between <strong>[start_date]</strong> and <strong>[end_date]</strong>.</p>",
          "info": "Use [start_date] to display the earliest date the package can arrive and [end_date] to display the latest date the package can arrive."
        },
        {
          "type": "number",
          "id": "min_shipping_days",
          "label": "Minimum shipping days",
          "default": 7
        },
        {
          "type": "number",
          "id": "max_shipping_days",
          "label": "Maximum shipping days",
          "default": 15
        },
        {
          "type": "select",
          "id": "date_format",
          "label": "Date format",
          "options": [
            {
              "value": "day_mm_dd",
              "label": "Monday, February 1st"
            },
            {
              "value": "day_dd_mm",
              "label": "Monday, 1. February"
            },
            {
              "value": "mm_dd",
              "label": "February 1st"
            },
            {
              "value": "dd_mm",
              "label": "1. February"
            },
            {
              "value": "dd_mm_no_dot",
              "label": "1 February"
            },
            {
              "value": "day_dd_mm_numeric",
              "label": "Monday, 01. 02."
            },
            {
              "value": "dd_mm_numeric",
              "label": "01. 02."
            }
          ],
          "default": "day_mm_dd"
        },
        {
          "type": "text",
          "id": "days_labels",
          "label": "Day labels",
          "default": "Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday"
        },
        {
          "type": "text",
          "id": "months_labels",
          "label": "Month labels",
          "default": "January, February, March, April, May, June, July, August, September, October, November, December"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "shipping_checkpoints",
      "name": "Shipping checkpoints",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "How to use"
        },
        {
          "type": "paragraph",
          "content": "Use [start_date} and {end_date} to automatically display the dates calculated based on the required days needed you enter."
        },
        {
          "type": "header",
          "content": "Checkpoint #1"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "add_shopping_cart",
          "label": "#1 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_1",
          "default": false,
          "label": "#1 Filled icon"
        },
        {
          "type": "image_picker",
          "id": "icon_1_image",
          "label": "#1 Custom icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_1",
          "label": "#1 Top Text",
          "default": "<strong>[start_date]</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_1",
          "label": "#1 Bottom Text",
          "default": "Ordered"
        },
        {
          "type": "number",
          "id": "min_days_1",
          "label": "#1 Minimum required days",
          "default": 0
        },
        {
          "type": "number",
          "id": "max_days_1",
          "label": "#1 Maximum required days",
          "default": 0
        },
        {
          "type": "header",
          "content": "Checkpoint #2"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "local_shipping",
          "label": "#2 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_2",
          "default": false,
          "label": "#2 Filled icon"
        },
        {
          "type": "image_picker",
          "id": "icon_2_image",
          "label": "#2 Custom icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_2",
          "label": "#2 Top Text",
          "default": "<strong>[start_date] - [end_date]</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_2",
          "label": "#2 Bottom Text",
          "default": "Order Ready"
        },
        {
          "type": "number",
          "id": "min_days_2",
          "label": "#2 Minimum required days",
          "default": 1
        },
        {
          "type": "number",
          "id": "max_days_2",
          "label": "#2 Maximum required days",
          "default": 2
        },
        {
          "type": "header",
          "content": "Checkpoint #3"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "redeem",
          "label": "#3 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_3",
          "default": false,
          "label": "#3 Filled icon"
        },
        {
          "type": "image_picker",
          "id": "icon_3_image",
          "label": "#3 Custom icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_3",
          "label": "#3 Top Text",
          "default": "<strong>[start_date] - [end_date]</strong>"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_3",
          "label": "#3 Bottom Text",
          "default": "Delivered"
        },
        {
          "type": "number",
          "id": "min_days_3",
          "label": "#3 Minimum required days",
          "default": 10
        },
        {
          "type": "number",
          "id": "max_days_3",
          "label": "#3 Maximum required days",
          "default": 12
        },
        {
          "type": "header",
          "content": "Checkpoint #4"
        },
        {
          "type": "text",
          "id": "icon_4",
          "label": "#4 Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon_4",
          "default": false,
          "label": "#4 Filled icon"
        },
        {
          "type": "inline_richtext",
          "id": "top_text_4",
          "label": "#4 Top Text"
        },
        {
          "type": "inline_richtext",
          "id": "bottom_text_4",
          "label": "#4 Bottom Text"
        },
        {
          "type": "number",
          "id": "min_days_4",
          "label": "#4 Minimum required days"
        },
        {
          "type": "number",
          "id": "max_days_4",
          "label": "#4 Maximum required days"
        },
        {
          "type": "header",
          "content": "Date formating"
        },
        {
          "type": "select",
          "id": "date_format",
          "label": "Date format",
          "options": [
            {
              "value": "day_mm_dd",
              "label": "Monday, February 1st"
            },
            {
              "value": "day_dd_mm",
              "label": "Monday, 1. February"
            },
            {
              "value": "mm_dd",
              "label": "February 1st"
            },
            {
              "value": "dd_mm",
              "label": "1. February"
            },
            {
              "value": "dd_mm_no_dot",
              "label": "1 February"
            },
            {
              "value": "day_dd_mm_numeric",
              "label": "Monday, 01. 02."
            },
            {
              "value": "dd_mm_numeric",
              "label": "01. 02."
            }
          ],
          "default": "mm_dd"
        },
        {
          "type": "text",
          "id": "days_labels",
          "label": "Day labels",
          "default": "Mon, Tue, Wed, Thu, Fri, Sat, Sun"
        },
        {
          "type": "text",
          "id": "months_labels",
          "label": "Month labels",
          "default": "Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec"
        },
        {
          "type": "header",
          "content": "Color"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "inverse",
          "label": "Bar color scheme"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    },
    {
      "type": "custom_product_field",
      "name": "Custom product field",
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "type",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "number",
              "label": "Number"
            },
            {
              "value": "textarea",
              "label": "Textarea"
            },
            {
              "value": "pills",
              "label": "Pills"
            },
            {
              "value": "select",
              "label": "Dropdown"
            }
          ],
          "default": "text",
          "label": "Field type"
        },
        {
          "type": "text",
          "id": "field_name",
          "label": "Field name",
          "default": "Name",
          "info": "Custom property name that's sent to order information."
        },
        {
          "type": "text",
          "id": "field_label",
          "label": "Field label",
          "default": "Your name"
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "Full width input & dropdown",
          "default": false
        },
        {
          "type": "header",
          "content": "Validation"
        },
        {
          "type": "checkbox",
          "id": "required",
          "default": false,
          "label": "Required field",
          "info": "Disables all Add to Cart buttons unless all required fields are filled out & displays an error message."
        },
        {
          "type": "text",
          "id": "atc_error_message",
          "default": "Please enter your name",
          "label": "ATC button error message",
          "info": "Displayed inside the Add to Cart button if a required field is empty. If multiple required fields exist, the message from the last one will be applied."
        },
        {
          "type": "text",
          "id": "field_error_message",
          "default": "This field is required!",
          "label": "Field error message",
          "info": "Displayed if a required field value is changed to empty."
        },
        {
          "type": "checkbox",
          "id": "sticky_atc_error_msg",
          "default": false,
          "label": "Apply error message to Sticky ATC",
          "info": "Displays ATC button error message in the Sticky Add to Cart as well."
        },
        {
          "type": "header",
          "content": "Text input"
        },
        {
          "type": "text",
          "id": "text_placeholder",
          "label": "Placeholder",
          "default": "Your name"
        },
        {
          "type": "number",
          "id": "text_max_characters",
          "label": "Maximum characters"
        },
        {
          "type": "header",
          "content": "Number input"
        },
        {
          "type": "text",
          "id": "number_placeholder",
          "label": "Placeholder"
        },
        {
          "type": "number",
          "id": "number_min",
          "label": "Minimum value"
        },
        {
          "type": "number",
          "id": "number_max",
          "label": "Maximum value"
        },
        {
          "type": "header",
          "content": "Textarea input"
        },
        {
          "type": "text",
          "id": "textarea_placeholder",
          "label": "Placeholder"
        },
        {
          "type": "select",
          "id": "textarea_height",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium",
          "label": "Height"
        },
        {
          "type": "header",
          "content": "Pills & Dropdown input"
        },
        {
          "type": "text",
          "id": "select_options",
          "label": "Options separated by comma",
          "default": "Option 1,Option 2,Option 3"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 15
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 15
        }
      ]
    },
    {
      "type": "button",
      "name": "Link button",
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "Button label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "default": false,
          "label": "Use outline button style"
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "default": false,
          "label": "Full buttons width"
        },
        {
          "type": "checkbox",
          "id": "large_font_size",
          "default": false,
          "label": "Use larger font size"
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "flex-start",
          "label": "Desktop alignment"
        },
        {
          "type": "select",
          "id": "mobile_alignment",
          "options": [
            {
              "value": "flex-start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "flex-end",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Mobile alignment"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 21
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 21
        }
      ]
    },
    {
      "type": "icon_with_text",
      "name": "Icons with text",
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "default": "horizontal",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.label"
        },
        {
          "type": "select",
          "id": "icon_color",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "outline-button",
              "label": "t:settings_schema.styles.settings.accent_icons.options__3.label"
            },
            {
              "value": "text",
              "label": "t:settings_schema.styles.settings.accent_icons.options__4.label"
            }
          ],
          "default": "accent-1",
          "label": "Icon color"
        },
        {
          "type": "header",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "desktop_icon_size",
          "min": 16,
          "max": 72,
          "step": 4,
          "unit": "px",
          "default": 48,
          "label": "Icon size"
        },
        {
          "type": "range",
          "id": "desktop_spacing",
          "min": 2,
          "max": 24,
          "step": 2,
          "unit": "px",
          "default": 12,
          "label": "Icon & text spacing"
        },
        {
          "type": "range",
          "id": "desktop_text_size",
          "min": 12,
          "max": 34,
          "step": 2,
          "unit": "px",
          "default": 18,
          "label": "Text size"
        },
        {
          "type": "header",
          "content": "Mobile"
        },
        {
          "type": "range",
          "id": "mobile_icon_size",
          "min": 12,
          "max": 60,
          "step": 4,
          "unit": "px",
          "default": 40,
          "label": "Icon size"
        },
        {
          "type": "range",
          "id": "mobile_spacing",
          "min": 2,
          "max": 24,
          "step": 2,
          "unit": "px",
          "default": 10,
          "label": "Icon & text spacing"
        },
        {
          "type": "range",
          "id": "mobile_text_size",
          "min": 8,
          "max": 26,
          "step": 2,
          "unit": "px",
          "default": 14,
          "label": "Text size"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.content.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.content.info"
        },
        {
          "type": "text",
          "id": "icon_1",
          "default": "favorite",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_1.label",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "icon_1_fill",
          "default": false,
          "label": "First icon fill"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_1.label"
        },
        {
          "type": "text",
          "id": "heading_1",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "text",
          "id": "icon_2",
          "default": "undo",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_2.label",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "icon_2_fill",
          "default": false,
          "label": "Second icon fill"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_2.label"
        },
        {
          "type": "text",
          "id": "heading_2",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "text",
          "id": "icon_3",
          "default": "local_shipping",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_3.label",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "icon_3_fill",
          "default": false,
          "label": "Third icon fill"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_3.label"
        },
        {
          "type": "text",
          "id": "heading_3",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "header",
          "content": "Block margin"
        },
        {
          "type": "range",
          "id": "margin_top",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Top margin",
          "default": 24
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 45,
          "step": 3,
          "unit": "px",
          "label": "Bottom margin",
          "default": 24
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID can be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_sticky_info",
      "default": true,
      "label": "Enable sticky content on desktop"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "checkbox",
      "id": "display_variant_image_first",
      "default": false,
      "label": "Display first variant image as featured image"
    },
    {
      "type": "checkbox",
      "id": "disable_prepend",
      "label": "Disable prepend of the new active media",
      "info": "Prepend moves the new active variant media to the first place in the slider.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "Hide other variants’ media after selecting a variant",
      "info": "Related to media that are natively assigned in Shopify admin > Products (one media per variant). If you wish to hide multiple media per variant, use the setting below."
    },
    {
      "type": "select",
      "id": "variant_image_filtering",
      "label": "Active variant images filtering",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "option1",
          "label": "Option 1"
        },
        {
          "value": "option2",
          "label": "Option 2"
        },
        {
          "value": "option3",
          "label": "Option 3"
        }
      ],
      "default": "none",
      "info": "This setting allows you to show only the images related to the selected variant. Check the tutorial for this option [here](https://dashboard.shrinesolutions.com/customer/help-center?category=Features&element=Variant+Images+Filtering)."
    },
    {
      "type": "select",
      "id": "image_zoom",
      "options": [
        {
          "value": "lightbox",
          "label": "Open lighbox"
        },
        {
          "value": "none",
          "label": "None"
        }
      ],
      "default": "none",
      "label": "Image zoom"
    },
    {
      "type": "select",
      "id": "arrows_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "inverse",
      "label": "Arrows color scheme"
    },
    {
      "type": "checkbox",
      "id": "transparent_arrows",
      "label": "Transparent arrows",
      "default": false
    },
    {
      "type": "select",
      "id": "dots_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Text"
        }
      ],
      "default": "inverse",
      "label": "Dots color"
    },
    {
      "type": "header",
      "content": "Videos"
    },
    {
      "type": "select",
      "id": "video_player",
      "options": [
        {
          "value": "autoplay",
          "label": "Muted autoplay"
        },
        {
          "value": "play_btn",
          "label": "Play button"
        },
        {
          "value": "modal",
          "label": "Play button with modal"
        }
      ],
      "default": "play_btn",
      "label": "Video player type"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "Enable non-autoplay video looping"
    },
    {
      "type": "paragraph",
      "content": "Settings bellow are applied if Video player type is set to Muted autoplay or Play button."
    },
    {
      "type": "checkbox",
      "id": "autoplay_videos_pause_btn",
      "default": false,
      "label": "Enable pause button in autoplay videos"
    },
    {
      "type": "checkbox",
      "id": "video_sound_btn",
      "default": false,
      "label": "Enable sound button"
    },
    {
      "type": "checkbox",
      "id": "video_timeline",
      "default": false,
      "label": "Enable timeline"
    },
    {
      "type": "select",
      "id": "play_btn_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "accent-1",
      "label": "Play button color scheme"
    },
    {
      "type": "select",
      "id": "sound_btn_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "inverse",
      "label": "Sound button color scheme"
    },
    {
      "type": "select",
      "id": "timeline_color",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "accent-1",
      "label": "Timeline color"
    },
    {
      "type": "header",
      "content": "Desktop media"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium",
      "label": "Desktop media width",
      "info": "Media is automatically optimized for mobile."
    },
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Desktop media position",
      "info": "Position is automatically optimized for mobile."
    },
    {
      "type": "select",
      "id": "gallery_layout",
      "options": [
        {
          "value": "stacked",
          "label": "Stacked"
        },
        {
          "value": "columns",
          "label": "2 columns"
        },
        {
          "value": "thumbnail",
          "label": "Thumbnails"
        },
        {
          "value": "thumbnail_slider",
          "label": "Thumbnail carousel"
        }
      ],
      "default": "thumbnail_slider",
      "label": "Desktop layout"
    },
    {
      "type": "range",
      "id": "desktop_thumbnails_count",
      "min": 4,
      "max": 12,
      "step": 1,
      "label": "Number of displayed thumbnails",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "Constrain media to screen height"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "Original"
        },
        {
          "value": "cover",
          "label": "Fill"
        }
      ],
      "default": "contain",
      "label": "Media fit"
    },
    {
      "type": "select",
      "id": "desktop_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "sides",
          "label": "On the sides of the slider"
        }
      ],
      "default": "hidden",
      "label": "Arrows position"
    },
    {
      "type": "header",
      "content": "Mobile media"
    },
    {
      "type": "range",
      "id": "mobile_media_corner_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Corner radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "mobile_spacing_pixels",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Spacing between slides",
      "default": 0
    },
    {
      "type": "select",
      "id": "mobile_arrows_position",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "pagination",
          "label": "Next to pagination"
        },
        {
          "value": "sides",
          "label": "On the sides of the slider"
        }
      ],
      "default": "sides",
      "label": "Arrows position"
    },
    {
      "type": "select",
      "id": "mobile_pagination",
      "options": [
        {
          "value": "hidden",
          "label": "Hidden"
        },
        {
          "value": "numeric",
          "label": "Numeric"
        },
        {
          "value": "dots_under",
          "label": "Dots - Under media"
        },
        {
          "value": "dots_overlay",
          "label": "Dots - Over media"
        }
      ],
      "default": "dots_overlay",
      "label": "Pagination type"
    },
    {
      "type": "select",
      "id": "mobile_thumbnails",
      "options": [
        {
          "value": "show",
          "label": "Show thumbnails"
        },
        {
          "value": "hide",
          "label": "Hide thumbnails"
        }
      ],
      "default": "hide",
      "label": "Thumbnails"
    },
    {
      "type": "range",
      "id": "mobile_thumbnails_count",
      "min": 3,
      "max": 10,
      "step": 1,
      "label": "Number of displayed thumbnails",
      "default": 5
    },
    {
      "type": "header",
      "content": "Mobile slider scroll padding",
      "info": "Scroll padding adjusts the visible portion of the previous & next slides on the sides of the slider, creating an inset effect. The settings for percentage and pixels are combined to define this space, enhancing the slider's usability by indicating more content is available to view."
    },
    {
      "type": "range",
      "id": "mobile_scroll_padding_percentage",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "%",
      "label": "Scroll padding percentage",
      "default": 0
    },
    {
      "type": "range",
      "id": "mobile_scroll_padding_pixels",
      "min": 0,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Scroll padding pixels",
      "default": 16
    },
    {
      "type": "checkbox",
      "id": "enable_mobile_outher_spacing",
      "label": "First & last slide outer spacing",
      "info": "If enabled, spaces the first & last slides from the outer edges. The spacing is equal to the selected scroll padding.",
      "default": false
    },
    {
      "type": "header",
      "content": "Mobile slides width"
    },
    {
      "type": "range",
      "id": "mobile_slides_container_width",
      "min": 50,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Container width from available space",
      "default": 100,
      "info": "This setting adjusts the width of the slide container relative to the available space (full screen width minus scroll paddings & spacings). At 100%, the container takes up the full width, with equal scroll padding on both sides. Reducing the percentage increases the right-side scroll padding relative to the left, creating an asymmetrical padding effect."
    },
    {
      "type": "range",
      "id": "mobile_slides_inner_width",
      "min": 50,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Inner slide content width",
      "default": 100,
      "info": "This setting controls the width of the content within each slide, relative to the slide container's width set previously. It allows you to adjust how much of the container's width the content occupies, without affecting the scroll padding. A value of 100% means the content will fill the entire width of the container, while a lower percentage will reduce the content width, centering it within the container."
    },
    {
      "type": "header",
      "content": "Trust badge"
    },
    {
      "type": "image_picker",
      "id": "trust_badge",
      "label": "Trust badge"
    },
    {
      "type": "select",
      "id": "trust_badge_position",
      "options": [
        {
          "value": "top-left",
          "label": "Top left"
        },
        {
          "value": "top-right",
          "label": "Top right"
        },
        {
          "value": "bottom-left",
          "label": "Bottom left"
        },
        {
          "value": "bottom-right",
          "label": "Bottom right"
        }
      ],
      "default": "top-right",
      "label": "Trust badge position"
    },
    {
      "type": "select",
      "id": "trust_badge_size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium",
      "label": "Trust badge size"
    },
    {
      "type": "header",
      "content": "Mobile padding"
    },
    {
      "type": "range",
      "id": "mobile_padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "mobile_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 16
    },
    {
      "type": "header",
      "content": "Desktop padding"
    },
    {
      "type": "range",
      "id": "desktop_padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "desktop_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ]
}
{% endschema %}
