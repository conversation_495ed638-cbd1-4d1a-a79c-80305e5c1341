[{"name": "theme_info", "theme_name": "Shrine PRO", "theme_version": "1.2.1", "theme_author": "Shrine", "theme_documentation_url": "https://dashboard.shrinesolutions.com/customer/help-center", "theme_support_url": "https://shrinesolutions.com/pages/contact"}, {"name": "Authentication", "settings": [{"type": "text", "id": "animations_type", "label": "Authentication token", "info": "Refresh the page if the error message doesnt disappear after entering your token."}]}, {"name": "Help", "settings": [{"type": "paragraph", "content": "Full theme guide can be viewed [here](https://dashboard.shrinesolutions.com/customer/help-center)."}]}, {"name": "Security", "settings": [{"type": "checkbox", "id": "disable_inspect", "label": "Disable copying text, downloading images and opening Inspect element", "default": true}, {"type": "header", "content": "Country blocker"}, {"type": "paragraph", "content": "Block your store from countries that you aren't targeting to decrease the amount of competitors visiting your store."}, {"type": "select", "id": "country_list_function", "label": "Country list function", "options": [{"value": "block", "label": "Block"}, {"value": "whitelist", "label": "Whitelist"}], "info": "\"Block\" blocks selected countries from the list, \"Whitelist\" only allows the countries from the list.", "default": "block"}, {"type": "text", "id": "country_list", "label": "Country codes list", "info": "Country codes in Alpha-2 code format, split by comma. View all country codes [here](https://www.iban.com/country-codes). Example: \"US, GB\""}, {"type": "richtext", "id": "country_invalid_error_msg", "label": "Country invalid error message", "default": "<h2>Unfortunately this store isn't available in your country.</h2>"}]}, {"name": "t:settings_schema.logo.name", "settings": [{"type": "image_picker", "id": "logo", "label": "t:settings_schema.logo.settings.logo_image.label"}, {"type": "image_picker", "id": "secondary_logo", "label": "Secondary logo", "info": "Used for the transparent header option, if needed."}, {"type": "range", "id": "logo_width", "min": 50, "max": 350, "step": 10, "default": 200, "unit": "px", "label": "Desktop logo width"}, {"type": "range", "id": "mobile_logo_width", "min": 50, "max": 300, "step": 10, "default": 120, "unit": "px", "label": "Mobile logo width"}, {"type": "image_picker", "id": "favicon", "label": "t:settings_schema.logo.settings.favicon.label", "info": "t:settings_schema.logo.settings.favicon.info"}]}, {"name": "t:settings_schema.colors.name", "settings": [{"type": "header", "content": "t:settings_schema.colors.settings.header__1.content"}, {"type": "color", "id": "colors_solid_button_labels", "default": "#FDFBF7", "label": "t:settings_schema.colors.settings.colors_solid_button_labels.label", "info": "t:settings_schema.colors.settings.colors_solid_button_labels.info"}, {"type": "color", "id": "colors_accent_1", "default": "#dd1d1d", "label": "t:settings_schema.colors.settings.colors_accent_1.label", "info": "t:settings_schema.colors.settings.colors_accent_1.info"}, {"id": "gradient_accent_1", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_accent_1.label"}, {"type": "color", "id": "colors_accent_2", "default": "#dd1d1d", "label": "t:settings_schema.colors.settings.colors_accent_2.label"}, {"type": "color_background", "id": "gradient_accent_2", "label": "t:settings_schema.colors.settings.gradient_accent_2.label"}, {"type": "header", "content": "t:settings_schema.colors.settings.header__2.content"}, {"type": "color", "id": "colors_text", "default": "#2E2A39", "label": "t:settings_schema.colors.settings.colors_text.label", "info": "t:settings_schema.colors.settings.colors_text.info"}, {"type": "color", "id": "colors_outline_button_labels", "default": "#2E2A39", "label": "t:settings_schema.colors.settings.colors_outline_button_labels.label", "info": "t:settings_schema.colors.settings.colors_outline_button_labels.info"}, {"type": "color", "id": "colors_background_1", "default": "#FFFFFF", "label": "t:settings_schema.colors.settings.colors_background_1.label"}, {"type": "color_background", "id": "gradient_background_1", "label": "t:settings_schema.colors.settings.gradient_background_1.label"}, {"type": "color", "id": "colors_background_2", "default": "#F3F3F3", "label": "t:settings_schema.colors.settings.colors_background_2.label"}, {"type": "color_background", "id": "gradient_background_2", "label": "t:settings_schema.colors.settings.gradient_background_2.label"}]}, {"name": "t:settings_schema.typography.name", "settings": [{"type": "header", "content": "t:settings_schema.typography.settings.header__1.content"}, {"type": "font_picker", "id": "type_header_font", "default": "harmonia_sans_n6", "label": "t:settings_schema.typography.settings.type_header_font.label", "info": "t:settings_schema.typography.settings.type_header_font.info"}, {"type": "paragraph", "content": "Or"}, {"type": "text", "id": "custom_header_font_link", "label": "Custom font link", "info": "Upload the font file to Shopify admin > Content > Files. The file name must not have any spaces, hyphens, underscores etc."}, {"type": "text", "id": "custom_header_font_name", "label": "Custom font name", "default": "Custom headings font", "info": "Doesn't have to be identical to file name, just unique."}, {"type": "range", "id": "custom_header_font_weight", "min": 300, "max": 1000, "step": 100, "label": "Custom font weight", "default": 700}, {"type": "range", "id": "heading_scale", "min": 100, "max": 150, "step": 5, "unit": "%", "label": "t:settings_schema.typography.settings.heading_scale.label", "default": 130}, {"type": "range", "id": "heading_line_height", "min": 1, "max": 2, "step": 0.1, "label": "Line height", "default": 1.3}, {"type": "range", "id": "heading_letter_spacing", "min": 0, "max": 3, "step": 0.2, "label": "Letter spacing", "default": 0.6}, {"type": "header", "content": "t:settings_schema.typography.settings.header__2.content"}, {"type": "font_picker", "id": "type_body_font", "default": "harmonia_sans_n4", "label": "t:settings_schema.typography.settings.type_body_font.label", "info": "t:settings_schema.typography.settings.type_body_font.info"}, {"type": "paragraph", "content": "Or"}, {"type": "text", "id": "custom_body_font_link", "label": "Custom font link", "info": "Upload the font file to Shopify admin > Content > Files. The file name must not have any spaces, hyphens, underscores etc."}, {"type": "text", "id": "custom_body_font_name", "label": "Custom font name", "default": "Custom body font", "info": "Doesn't have to be identical to file name, just unique."}, {"type": "range", "id": "custom_body_font_weight", "min": 300, "max": 1000, "step": 100, "label": "Custom font weight", "default": 400}, {"type": "text", "id": "custom_body_bold_font_link", "label": "OPTIONAL: Custom bold font link", "info": "If your custom body font file doesn't automatically include the bold version in it, upload it separately and paste the URL here."}, {"type": "range", "id": "body_scale", "min": 100, "max": 130, "step": 5, "unit": "%", "label": "t:settings_schema.typography.settings.body_scale.label", "default": 100}, {"type": "range", "id": "body_line_height", "min": 1.3, "max": 2.3, "step": 0.1, "label": "Line height", "default": 1.8}, {"type": "range", "id": "body_letter_spacing", "min": 0, "max": 1.2, "step": 0.1, "label": "Letter spacing", "default": 0.6}]}, {"name": "Animations", "settings": [{"type": "checkbox", "id": "enable_load_animations", "label": "Enable section load animations", "default": false, "info": "Display load animations when the section comes into the viewport."}, {"type": "checkbox", "id": "repeat_section_animations", "label": "Repeat load animations", "default": false, "info": "If checked, the laod animation will be played each time the section comes into viewport."}, {"type": "header", "content": "Duration & timing"}, {"type": "range", "id": "animation_duration", "label": "Animation duration", "min": 300, "max": 1000, "step": 50, "default": 650, "unit": "ms"}, {"type": "range", "id": "animation_init_delay", "label": "Animation initial delay", "min": 0, "max": 300, "step": 30, "default": 150, "unit": "ms"}, {"type": "range", "id": "animation_children_delay", "label": "Animation children delay", "min": 0, "max": 300, "step": 30, "default": 150, "unit": "ms"}, {"type": "header", "content": "Starting position", "info": "The starting point of the element before the animation starts"}, {"type": "range", "id": "animation_start_opacity", "label": "Opacity", "min": 0, "max": 100, "step": 5, "default": 0, "unit": "%"}, {"type": "range", "id": "animation_start_x", "label": "Horizontal position", "min": -100, "max": 100, "step": 5, "default": 0, "unit": "%"}, {"type": "range", "id": "animation_start_y", "label": "Vertical position", "min": -100, "max": 100, "step": 5, "default": 0, "unit": "%"}, {"type": "range", "id": "animation_start_zoom", "label": "Zoom", "min": 100, "max": 200, "step": 5, "default": 100, "unit": "%"}]}, {"name": "t:settings_schema.badges.name", "settings": [{"type": "select", "id": "badge_position", "options": [{"value": "bottom left", "label": "t:settings_schema.badges.settings.position.options__1.label"}, {"value": "bottom right", "label": "t:settings_schema.badges.settings.position.options__2.label"}, {"value": "top left", "label": "t:settings_schema.badges.settings.position.options__3.label"}, {"value": "top right", "label": "t:settings_schema.badges.settings.position.options__4.label"}], "default": "bottom left", "label": "t:settings_schema.badges.settings.position.label", "info": "Badge position for product cards is adjusted separately in Theme settings > Product cards"}, {"type": "range", "id": "badge_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 6}, {"type": "select", "id": "sale_badge_color_scheme", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "background-2", "label": "t:sections.all.colors.background_2.label"}], "default": "accent-2", "label": "t:settings_schema.badges.settings.sale_badge_color_scheme.label"}, {"type": "text", "id": "sale_badge_text", "label": "Sale badge text", "default": "SAVE [percentage]", "info": "Use [percentage] to display the percentage saved and [amount] to display the amount of money saved."}, {"type": "checkbox", "id": "sale_basge_discount_icon", "label": "Display sale badge discount icon", "default": true}, {"type": "select", "id": "sold_out_badge_color_scheme", "options": [{"value": "background-1", "label": "t:sections.all.colors.background_1.label"}, {"value": "inverse", "label": "t:sections.all.colors.inverse.label"}], "default": "inverse", "label": "t:settings_schema.badges.settings.sold_out_badge_color_scheme.label"}]}, {"name": "Slider controls", "settings": [{"type": "header", "content": "Arrows"}, {"type": "range", "id": "slider_arrow_size", "min": 10, "max": 80, "step": 2, "unit": "px", "label": "Button size", "default": 30}, {"type": "range", "id": "slider_arrow_border_radius", "min": 0, "max": 100, "step": 1, "unit": "%", "label": "Corner radius", "default": 100}, {"type": "range", "id": "slider_arrow_icon_size", "min": 20, "max": 100, "step": 5, "unit": "%", "label": "Icon size", "default": 60}, {"type": "header", "content": "Pagination dots"}, {"type": "range", "id": "pagination_dot_width", "min": 2, "max": 30, "step": 1, "unit": "px", "label": "<PERSON><PERSON><PERSON>", "default": 6}, {"type": "range", "id": "pagination_dot_height", "min": 2, "max": 30, "step": 1, "unit": "px", "label": "Height", "default": 6}, {"type": "range", "id": "pagination_dot_active_scale", "min": 100, "max": 200, "step": 5, "unit": "%", "label": "Active dot scale", "default": 150}, {"type": "range", "id": "pagination_dot_spacing", "min": 1, "max": 40, "step": 1, "unit": "px", "label": "Space between dots", "default": 12}, {"type": "range", "id": "pagination_dot_radius", "min": 0, "max": 15, "step": 1, "unit": "px", "label": "Corner radius", "default": 5}]}, {"name": "t:settings_schema.styles.name", "settings": [{"type": "select", "id": "accent_icons", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "outline-button", "label": "t:settings_schema.styles.settings.accent_icons.options__3.label"}, {"value": "text", "label": "t:settings_schema.styles.settings.accent_icons.options__4.label"}], "default": "text", "label": "t:settings_schema.styles.settings.accent_icons.label"}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "range", "id": "page_width", "min": 1000, "max": 1600, "step": 100, "default": 1200, "unit": "px", "label": "t:settings_schema.layout.settings.page_width.label"}, {"type": "range", "id": "spacing_sections", "min": 0, "max": 100, "step": 4, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_sections.label", "default": 36}, {"type": "header", "content": "t:settings_schema.layout.settings.header__grid.content"}, {"type": "paragraph", "content": "t:settings_schema.layout.settings.paragraph__grid.content"}, {"type": "range", "id": "spacing_grid_horizontal", "min": 4, "max": 40, "step": 4, "default": 40, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_horizontal.label"}, {"type": "range", "id": "spacing_grid_vertical", "min": 4, "max": 40, "step": 4, "default": 40, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_vertical.label"}]}, {"name": "t:settings_schema.buttons.name", "settings": [{"type": "header", "content": "Hover effects"}, {"type": "select", "id": "link_btn_hover", "options": [{"value": "glow", "label": "Glow"}, {"value": "center", "label": "Center background transition"}, {"value": "left", "label": "Left-right background transition"}, {"value": "arrow", "label": "Arrow"}], "default": "arrow", "label": "Link buttons hover effect"}, {"type": "select", "id": "action_btn_hover", "options": [{"value": "glow", "label": "Glow"}, {"value": "center", "label": "Center background transition"}, {"value": "left", "label": "Left-right background transition"}, {"value": "arrow", "label": "Arrow"}], "default": "center", "label": "Action buttons hover effect"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "buttons_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "buttons_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 15}, {"type": "range", "id": "buttons_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "paragraph", "content": "NOTE: Button shadow is not compatible with the Arrow hover effect."}, {"type": "range", "id": "buttons_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "buttons_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "Color swatches", "settings": [{"type": "header", "content": "Style"}, {"type": "range", "id": "swatches_border_radius", "min": 0, "max": 100, "step": 4, "unit": "%", "label": "Corner radius", "default": 100}, {"type": "range", "id": "swatches_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "Outer border opacity", "default": 0}, {"type": "range", "id": "swatches_selected_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "Selected swatch outer border opacity", "default": 50}, {"type": "header", "content": "Color swatches predefined colors", "info": "To predefine a color, first type the exact option name and a = sybmol. Then, after the = symbol, write a color hex value or a CSS gradient value. Example: Red = #FF0000"}, {"type": "richtext", "id": "swatches_predefined_colors_list", "label": "Predefined custom colors", "info": "These predefined colors are used in color swatches in collectoin cards. And, they can also be used on product pages. To define multiple colors, go into a new line.", "default": "<p>Black = #000000</p><p>White = #FFFFFF</p><p>Red = #FF0000</p><p>Blue = #0000FF</p><p>Green = #008000</p><p>Yellow = #FFFF00</p><p>Orange = #FFA500</p><p>Purple = #800080</p><p>Pink = #FFC0CB</p><p>Brown = #A52A2A</p><p>Gray = #808080</p><p>Navy = #000080</p><p>Teal = #008080</p><p>Maroon = #800000</p><p>Olive = #808000</p><p>Lime = #00FF00</p><p>Aqua = #00FFFF</p><p>Silver = #C0C0C0</p><p>Gold = #FFD700</p><p>Beige = #F5F5DC</p>"}]}, {"name": "t:settings_schema.variant_pills.name", "settings": [{"type": "header", "content": "Color"}, {"type": "select", "id": "variant_pills_accent_color", "label": "Color", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "outline-button", "label": "Outline button"}, {"value": "text", "label": "Text"}], "default": "text"}, {"type": "range", "id": "variant_pills_inactive_overlay_opacity", "min": 0, "max": 20, "step": 1, "unit": "%", "label": "Inactive pills background overlay opacity", "default": 0}, {"type": "header", "content": "Text & size"}, {"type": "checkbox", "id": "variant_pills_bold_text", "default": false, "label": "Enable bold text"}, {"type": "range", "id": "variant_pills_text_size", "min": 10, "max": 20, "step": 1, "unit": "px", "label": "Text size", "default": 14}, {"type": "range", "id": "variant_pills_padding_y", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "Top & bottom padding", "default": 10}, {"type": "range", "id": "variant_pills_padding_x", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "Side padding", "default": 20}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "variant_pills_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "variant_pills_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "variant_pills_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "variant_pills_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "Variant dropdowns", "settings": [{"type": "header", "content": "Background & color"}, {"type": "select", "id": "pickers_color_scheme", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "inverse", "label": "Text"}], "default": "background-1", "label": "Background color"}, {"type": "select", "id": "pickers_border_color", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "text", "label": "Text"}], "default": "text", "label": "Border color"}, {"type": "select", "id": "pickers_text_color", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "text", "label": "Text"}], "default": "text", "label": "Text & arrow icon color"}, {"type": "select", "id": "pickers_overlay_color", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "text", "label": "Text"}], "default": "text", "label": "Overlay color"}, {"type": "range", "id": "pickers_overlay_opacity", "min": 0, "max": 20, "step": 1, "unit": "%", "label": "Background overlay opacity", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "pickers_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "pickers_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 15}, {"type": "range", "id": "pickers_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "pickers_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "pickers_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "pickers_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "pickers_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}, {"type": "header", "content": "Hover state"}, {"type": "range", "id": "pickers_hover_overlay_opacity", "min": 0, "max": 20, "step": 1, "unit": "%", "label": "Background overlay opacity", "default": 0}, {"type": "range", "id": "pickers_hover_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "Border opacity", "default": 15}]}, {"name": "Quantity pickers", "settings": [{"type": "paragraph", "content": "These settings affect quantity picker on the product page, cart page & Featured product section. Quantity pickers in cart drawer are customized in the Cart items block, under the Cart drawer section."}, {"type": "header", "content": "Background & color"}, {"type": "select", "id": "quantity_color_scheme", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "inverse", "label": "Text"}], "default": "background-1", "label": "Background color"}, {"type": "select", "id": "quantity_border_color", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "text", "label": "Text"}], "default": "text", "label": "Border color"}, {"type": "select", "id": "quantity_text_color", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "text", "label": "Text"}], "default": "text", "label": "Number & icons color"}, {"type": "select", "id": "quantity_overlay_color", "options": [{"value": "accent-1", "label": "Accent 1"}, {"value": "accent-2", "label": "Accent 2"}, {"value": "background-1", "label": "Background 1"}, {"value": "background-2", "label": "Background 2"}, {"value": "text", "label": "Text"}], "default": "text", "label": "Overlay color"}, {"type": "range", "id": "quantity_overlay_opacity", "min": 0, "max": 20, "step": 1, "unit": "%", "label": "Background overlay opacity", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "quantity_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "quantity_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 15}, {"type": "range", "id": "quantity_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "quantity_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "quantity_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "quantity_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "quantity_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}, {"type": "header", "content": "Hover state"}, {"type": "range", "id": "quantity_hover_overlay_opacity", "min": 0, "max": 20, "step": 1, "unit": "%", "label": "Background overlay opacity", "default": 0}, {"type": "range", "id": "quantity_hover_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "Border opacity", "default": 15}]}, {"name": "t:settings_schema.inputs.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "inputs_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "inputs_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 15}, {"type": "range", "id": "inputs_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "inputs_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "inputs_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.cards.name", "settings": [{"type": "header", "content": "General"}, {"type": "select", "id": "card_style", "options": [{"value": "standard", "label": "t:settings_schema.cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.cards.settings.style.options__2.label"}], "default": "card", "label": "t:settings_schema.cards.settings.style.label"}, {"type": "range", "id": "card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "center", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "select", "id": "card_color_scheme", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "background-1", "label": "t:sections.all.colors.background_1.label"}, {"value": "background-2", "label": "t:sections.all.colors.background_2.label"}, {"value": "inverse", "label": "t:sections.all.colors.inverse.label"}], "default": "background-1", "label": "t:sections.all.colors.label"}, {"type": "header", "content": "Badge"}, {"type": "select", "id": "product_card_badge_position", "options": [{"value": "bottom left", "label": "Bottom left"}, {"value": "bottom right", "label": "Bottom right"}, {"value": "top center", "label": "Top center"}, {"value": "top left", "label": "Top left"}, {"value": "top right", "label": "Top right"}], "default": "bottom left", "label": "Badge position"}, {"type": "checkbox", "id": "product_cards_badge_push_sides", "label": "Push badge to the side", "info": "Applied unless Badge position is set to Top center.", "default": true}, {"type": "text", "id": "product_cards_custom_badges_list", "label": "Custom badges list", "default": "BEST DEAL, BEST SELLER, [percentage] OFF", "info": "Predefined custom badges list, split by comma. [percentage] and [amount] for dynamic savings are available."}, {"type": "paragraph", "content": "IMPORTANT: Custom badges are turned on in each section settings individually."}, {"type": "paragraph", "content": "Add a \"custom-badge-[index]\" tag to your product to display one of the custom badges you've created in the text field above."}, {"type": "paragraph", "content": "EXAMPLE: If Custom badges list is set to \"BEST SELLER, ON SALE, BEST DEAL\", adding a \"custom-badge-1\" tag to a product would display BEST SELLER in the badge, custom-badge-3 would display BEST DEAL etc."}, {"type": "header", "content": "Card info"}, {"type": "select", "id": "product_card_title_limited_lines", "options": [{"value": "unlimited", "label": "Don't limit"}, {"value": "1", "label": "1 line"}, {"value": "2", "label": "2 lines"}, {"value": "3", "label": "3 lines"}], "default": "unlimited", "label": "Limit long titles"}, {"type": "select", "id": "card_button_style", "options": [{"value": "primary", "label": "Primary"}, {"value": "secondary", "label": "Secondary"}], "default": "primary", "label": "Add button style"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 12}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 5}, {"type": "range", "id": "card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 10}, {"type": "range", "id": "card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 10}, {"type": "range", "id": "card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 35}]}, {"name": "t:settings_schema.collection_cards.name", "settings": [{"type": "select", "id": "collection_card_style", "options": [{"value": "standard", "label": "t:settings_schema.collection_cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.collection_cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.collection_cards.settings.style.label"}, {"type": "range", "id": "collection_card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "collection_card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "select", "id": "collection_card_color_scheme", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "background-1", "label": "t:sections.all.colors.background_1.label"}, {"value": "background-2", "label": "t:sections.all.colors.background_2.label"}, {"value": "inverse", "label": "t:sections.all.colors.inverse.label"}], "default": "background-2", "label": "t:sections.all.colors.label"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "collection_card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "collection_card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "collection_card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "collection_card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "collection_card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "collection_card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "collection_card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.blog_cards.name", "settings": [{"type": "select", "id": "blog_card_style", "options": [{"value": "standard", "label": "t:settings_schema.blog_cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.blog_cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.blog_cards.settings.style.label"}, {"type": "range", "id": "blog_card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "blog_card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "select", "id": "blog_card_color_scheme", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "background-1", "label": "t:sections.all.colors.background_1.label"}, {"value": "background-2", "label": "t:sections.all.colors.background_2.label"}, {"value": "inverse", "label": "t:sections.all.colors.inverse.label"}], "default": "background-2", "label": "t:sections.all.colors.label"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "blog_card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "blog_card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "blog_card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "blog_card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "blog_card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "blog_card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "blog_card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.content_containers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "text_boxes_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "text_boxes_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 24}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "text_boxes_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 10}, {"type": "range", "id": "text_boxes_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 12}, {"type": "range", "id": "text_boxes_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 20}]}, {"name": "t:settings_schema.media.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "media_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "media_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "media_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 12}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "media_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "media_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 10}, {"type": "range", "id": "media_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 12}, {"type": "range", "id": "media_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 20}]}, {"name": "t:settings_schema.popups.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.popups.paragraph"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "popup_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "popup_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "popup_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 22}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "popup_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "popup_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 10}, {"type": "range", "id": "popup_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 12}, {"type": "range", "id": "popup_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 20}]}, {"name": "t:settings_schema.drawers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "drawer_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "drawer_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "drawer_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "drawer_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.brand_information.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.brand_information.settings.paragraph.content"}, {"type": "inline_richtext", "id": "brand_headline", "label": "t:settings_schema.brand_information.settings.brand_headline.label"}, {"type": "richtext", "id": "brand_description", "label": "t:settings_schema.brand_information.settings.brand_description.label"}, {"type": "image_picker", "id": "brand_image", "label": "t:settings_schema.brand_information.settings.brand_image.label"}, {"type": "range", "id": "brand_image_width", "min": 50, "max": 550, "step": 5, "default": 100, "unit": "px", "label": "t:settings_schema.brand_information.settings.brand_image_width.label"}]}, {"name": "t:settings_schema.social-media.name", "settings": [{"type": "header", "content": "t:settings_schema.social-media.settings.header.content"}, {"type": "text", "id": "social_facebook_link", "label": "t:settings_schema.social-media.settings.social_facebook_link.label", "info": "t:settings_schema.social-media.settings.social_facebook_link.info"}, {"type": "text", "id": "social_instagram_link", "label": "t:settings_schema.social-media.settings.social_instagram_link.label", "info": "t:settings_schema.social-media.settings.social_instagram_link.info"}, {"type": "text", "id": "social_youtube_link", "label": "t:settings_schema.social-media.settings.social_youtube_link.label", "info": "t:settings_schema.social-media.settings.social_youtube_link.info"}, {"type": "text", "id": "social_tiktok_link", "label": "t:settings_schema.social-media.settings.social_tiktok_link.label", "info": "t:settings_schema.social-media.settings.social_tiktok_link.info"}, {"type": "text", "id": "social_twitter_link", "label": "t:settings_schema.social-media.settings.social_twitter_link.label", "info": "t:settings_schema.social-media.settings.social_twitter_link.info"}, {"type": "text", "id": "social_snapchat_link", "label": "t:settings_schema.social-media.settings.social_snapchat_link.label", "info": "t:settings_schema.social-media.settings.social_snapchat_link.info"}, {"type": "text", "id": "social_pinterest_link", "label": "t:settings_schema.social-media.settings.social_pinterest_link.label", "info": "t:settings_schema.social-media.settings.social_pinterest_link.info"}, {"type": "text", "id": "social_tumblr_link", "label": "t:settings_schema.social-media.settings.social_tumblr_link.label", "info": "t:settings_schema.social-media.settings.social_tumblr_link.info"}, {"type": "text", "id": "social_vimeo_link", "label": "t:settings_schema.social-media.settings.social_vimeo_link.label", "info": "t:settings_schema.social-media.settings.social_vimeo_link.info"}]}, {"name": "t:settings_schema.search_input.name", "settings": [{"type": "paragraph", "content": "Add a \"search-hidden\" tag to the products you want to hide from the search results."}, {"type": "header", "content": "t:settings_schema.search_input.settings.header.content"}, {"type": "checkbox", "id": "predictive_search_enabled", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_enabled.label"}, {"type": "checkbox", "id": "predictive_search_show_vendor", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_vendor.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_vendor.info"}, {"type": "checkbox", "id": "predictive_search_show_price", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_show_price.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_price.info"}]}, {"name": "t:settings_schema.currency_format.name", "settings": [{"type": "header", "content": "t:settings_schema.currency_format.settings.content"}, {"type": "paragraph", "content": "t:settings_schema.currency_format.settings.paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:settings_schema.currency_format.settings.currency_code_enabled.label", "default": false}]}, {"name": "Scrollbar & Cursor", "settings": [{"type": "header", "content": "Sc<PERSON><PERSON>"}, {"type": "select", "id": "scrollbar_style", "options": [{"value": "default", "label": "<PERSON><PERSON><PERSON>"}, {"value": "customized", "label": "Customized"}], "label": "Desktop scrollbar style", "default": "default"}, {"type": "color", "id": "scrollbar_thumb_color", "label": "Scrollbar thumb color", "default": "#555555"}, {"type": "color", "id": "scrollbar_track_color", "label": "Scrollbar track color", "default": "#F5F5F5"}, {"type": "range", "id": "scrollbar_width", "label": "Scrollbar width", "min": 2, "max": 20, "step": 1, "unit": "px", "default": 9}, {"type": "header", "content": "<PERSON><PERSON><PERSON>"}, {"type": "image_picker", "id": "custom_cursor", "label": "Custom cursor"}]}, {"name": "t:settings_schema.cart.name", "settings": [{"type": "header", "content": "To customize the cart drawer, customize the Cart drawer section (between the Header group & your added page sections)."}, {"type": "select", "id": "cart_type", "options": [{"value": "drawer", "label": "t:settings_schema.cart.settings.cart_type.drawer.label"}, {"value": "page", "label": "t:settings_schema.cart.settings.cart_type.page.label"}, {"value": "notification", "label": "t:settings_schema.cart.settings.cart_type.notification.label"}], "default": "drawer", "label": "t:settings_schema.cart.settings.cart_type.label"}, {"type": "select", "id": "cart_icon", "options": [{"value": "cart_1", "label": "Cart 1"}, {"value": "cart_2", "label": "Cart 2"}, {"value": "basket_1", "label": "Basket 1"}, {"value": "basket_2", "label": "Basket 2"}, {"value": "bag_1", "label": "Bag 1"}, {"value": "bag_2", "label": "Bag 2"}], "default": "cart_1", "label": "Header cart icon"}, {"type": "checkbox", "id": "show_vendor", "label": "t:settings_schema.cart.settings.show_vendor.label", "default": false}, {"type": "checkbox", "id": "show_cart_note", "label": "t:settings_schema.cart.settings.show_cart_note.label", "default": false}, {"type": "header", "content": "Empty cart"}, {"type": "collection", "id": "cart_drawer_collection", "label": "t:settings_schema.cart.settings.cart_drawer.collection.label", "info": "t:settings_schema.cart.settings.cart_drawer.collection.info"}, {"type": "checkbox", "id": "display_continue_shopping", "label": "Display the Continue shopping button link when the cart is empty", "default": true}, {"type": "url", "id": "continue_shopping_url", "label": "Continue shopping button custom link", "info": "If empty, a link to all products will be applied."}]}]