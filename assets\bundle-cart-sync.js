/**
 * Bundle Cart Synchronization System
 * Prevents checkout exploits by automatically removing entire bundles when any item is removed
 */

class BundleCartSync {
  constructor() {
    this.isProcessingSync = false;
    this.pendingOperations = new Set();
    this.cartUpdateQueue = [];
    this.isProcessingQueue = false;
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.observeCartChanges();
  }

  setupEventListeners() {
    // Listen for cart updates
    document.addEventListener('cart:updated', () => {
      this.checkCartForBundleIntegrity();
    });

    // Listen for quantity changes in cart drawer/page
    document.addEventListener('change', (event) => {
      if (event.target.matches('[name="updates[]"]') || 
          event.target.matches('.cart-item__quantity-input') ||
          event.target.matches('.quantity__input')) {
        this.handleQuantityChange(event.target);
      }
    });

    // Optimized click listener - only for cart areas
    document.addEventListener('click', (event) => {
      // Only process clicks in cart areas to avoid performance issues
      const cartArea = event.target.closest('.cart-drawer, .cart-page, #cart-drawer, [data-cart]');
      if (!cartArea) return;

      // Check for remove buttons with minimal processing
      const isRemoveButton = event.target.closest('cart-remove-button') ||
                            event.target.closest('[class*="TrashButton"]') ||
                            event.target.closest('[class*="deleteButton"]') ||
                            event.target.closest('.cart-remove') ||
                            event.target.closest('[data-cart-remove]');

      if (isRemoveButton) {
        this.handleRemoveClick(event.target);
      }
    }, { passive: true });

    // Listen for AJAX cart operations
    this.interceptAjaxCartCalls();
  }

  observeCartChanges() {
    // Observe DOM changes in cart areas
    const cartSelectors = [
      '.cart-drawer',
      '.cart-page',
      '.cart-items',
      '#cart-drawer',
      '[data-cart-drawer]'
    ];

    cartSelectors.forEach(selector => {
      const element = document.querySelector(selector);
      if (element) {
        const observer = new MutationObserver(() => {
          this.checkCartForBundleIntegrity();
        });
        observer.observe(element, { childList: true, subtree: true });
      }
    });
  }

  async handleQuantityChange(input) {
    if (this.isProcessingSync) return;

    const newQuantity = parseInt(input.value) || 0;
    const lineItem = this.findLineItemElement(input);
    
    if (lineItem && newQuantity === 0) {
      const bundleId = this.getBundleIdFromElement(lineItem);
      if (bundleId) {
        await this.removeBundleById(bundleId);
      }
    }
  }

  async handleRemoveClick(element) {
    if (this.isProcessingSync) return;

    const lineItem = this.findLineItemElement(element);
    if (!lineItem) return;

    const bundleId = this.getBundleIdFromElement(lineItem);
    if (bundleId) {
      console.log(`🗑️ Removing bundle: ${bundleId}`);

      // Prevent the default remove action
      if (event && event.preventDefault) {
        event.preventDefault();
      }

      await this.removeBundleById(bundleId);
    }
  }

  async handleBundleGroupRemove(event) {
    event.preventDefault();
    if (this.isProcessingSync) return;

    const button = event.target.closest('.cart-bundle-remove');
    if (!button) return;

    const bundleId = button.getAttribute('data-bundle-id');
    if (!bundleId) return;

    console.log(`🗑️ Removing entire bundle group: ${bundleId}`);

    // Add visual feedback
    const bundleGroup = button.closest('.cart-bundle-group');
    if (bundleGroup) {
      bundleGroup.classList.add('removing');
    }

    try {
      await this.removeBundleById(bundleId);
    } catch (error) {
      console.error('Failed to remove bundle group:', error);
      // Remove visual feedback on error
      if (bundleGroup) {
        bundleGroup.classList.remove('removing');
      }
    }
  }

  findLineItemElement(element) {
    return element.closest('.cart-item, .cart-drawer-item, [id*="CartDrawer-Item-"]');
  }

  getBundleIdFromElement(lineItem) {
    // Quick check for data attribute
    if (lineItem.dataset.bundleId) {
      return lineItem.dataset.bundleId;
    }

    // Check for Bundle ID in product options (most common case)
    const bundleIdText = lineItem.querySelector('.product-option');
    if (bundleIdText && bundleIdText.textContent.includes('Bundle ID:')) {
      const match = bundleIdText.textContent.match(/Bundle ID:\s*([^\s\n]+)/);
      if (match) return match[1];
    }

    return null;

    // Try to find in JSON script tags (some themes store cart data this way)
    const scriptElements = lineItem.querySelectorAll('script[type="application/json"]');
    for (const script of scriptElements) {
      try {
        const data = JSON.parse(script.textContent);
        if (data.properties && data.properties._bundle_id) {
          return data.properties._bundle_id;
        }
      } catch (e) {
        // Ignore JSON parse errors
      }
    }

    return null;
  }

  // Enhanced method to detect both hidden variant and static bundles
  getBundleIdFromCartItem(item) {
    if (!item || !item.properties) return null;

    // Check for new public bundle ID format first (preferred)
    if (item.properties['Bundle ID']) {
      return item.properties['Bundle ID'];
    }

    // Check for legacy private bundle ID format
    if (item.properties._bundle_id) {
      return item.properties._bundle_id;
    }

    // Check for bundle type indicators
    if (item.properties._bundle_type === 'hidden_variant' ||
        item.properties._bundle_type === 'static_bundle' ||
        item.properties._bundle_type === 'collection_bundle' ||
        item.properties['Bundle Type'] === 'Collection Bundle') {
      // Try to extract bundle ID from other properties
      for (const [key, value] of Object.entries(item.properties)) {
        if (key.includes('bundle') && key.includes('id')) {
          return value;
        }
      }
    }

    return null;
  }

  async checkCartForBundleIntegrity() {
    if (this.isProcessingSync) return;

    try {
      const cart = await this.getCart();
      const bundleGroups = this.groupItemsByBundle(cart.items);
      
      // Check for incomplete bundles and remove them
      for (const [bundleId, items] of bundleGroups) {
        if (this.isBundleIncomplete(items)) {
          console.log(`Removing incomplete bundle: ${bundleId}`);
          await this.removeBundleById(bundleId);
        }
      }
    } catch (error) {
      console.error('Error checking cart bundle integrity:', error);
    }
  }

  groupItemsByBundle(cartItems) {
    const bundleGroups = new Map();
    
    cartItems.forEach(item => {
      const bundleId = this.getBundleIdFromItem(item);
      if (bundleId) {
        if (!bundleGroups.has(bundleId)) {
          bundleGroups.set(bundleId, []);
        }
        bundleGroups.get(bundleId).push(item);
      }
    });
    
    return bundleGroups;
  }

  getBundleIdFromItem(item) {
    // Use the enhanced method that works for both bundle types
    return this.getBundleIdFromCartItem(item);
  }

  isBundleIncomplete(bundleItems) {
    if (!bundleItems || bundleItems.length === 0) {
      return true;
    }

    // Check if any item has quantity 0
    if (bundleItems.some(item => item.quantity === 0)) {
      return true;
    }

    // Get bundle type from first item
    const bundleType = bundleItems[0]?.properties?._bundle_type || 'unknown';

    // For static and collection bundles, single items are valid
    if (bundleType === 'static_bundle' || bundleType === 'collection_bundle') {
      return false; // Static and collection bundles can have any number of items
    }

    // For hidden variant bundles, we expect multiple items
    if (bundleType === 'hidden_variant' && bundleItems.length === 1) {
      return true; // Hidden variant bundles should have multiple items
    }

    return false;
  }

  async removeBundleById(bundleId) {
    // Prevent duplicate operations for the same bundle
    if (this.pendingOperations.has(bundleId)) {
      console.log(`Bundle removal already in progress for: ${bundleId}`);
      return;
    }

    if (this.isProcessingSync) {
      // Queue the operation if another sync is in progress
      return new Promise((resolve) => {
        this.cartUpdateQueue.push(() => this.removeBundleById(bundleId).then(resolve));
        this.processQueue();
      });
    }

    this.isProcessingSync = true;
    this.pendingOperations.add(bundleId);
    
    try {
      console.log(`Removing bundle: ${bundleId}`);
      
      // Get current cart
      const cart = await this.getCart();
      
      // Find all items with this bundle ID
      const bundleItems = cart.items.filter(item => 
        this.getBundleIdFromItem(item) === bundleId
      );
      
      if (bundleItems.length === 0) {
        console.log(`No items found for bundle: ${bundleId}`);
        return;
      }
      
      // Use individual cart/change.js calls instead of bulk update to avoid race conditions
      // This is more reliable than using line numbers which can change
      const removePromises = bundleItems.map(async (item) => {
        try {
          const response = await fetch('/cart/change.js', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify({
              id: item.key,
              quantity: 0
            })
          });

          if (!response.ok) {
            console.warn(`Failed to remove item ${item.key}:`, response.status);
          }

          return response.ok;
        } catch (error) {
          console.error(`Error removing item ${item.key}:`, error);
          return false;
        }
      });

      // Wait for all removals to complete
      const results = await Promise.allSettled(removePromises);
      const successCount = results.filter(result => result.status === 'fulfilled' && result.value).length;

      if (successCount < bundleItems.length) {
        console.warn(`Only ${successCount}/${bundleItems.length} bundle items were successfully removed`);
      }
      
      console.log(`Successfully removed bundle: ${bundleId}`);
      
      // Dispatch custom event
      document.dispatchEvent(new CustomEvent('bundle:removed', {
        detail: { bundleId, removedItems: bundleItems }
      }));
      
    } catch (error) {
      console.error(`Error removing bundle ${bundleId}:`, error);
    } finally {
      this.isProcessingSync = false;
      this.pendingOperations.delete(bundleId);
      this.processQueue();
    }
  }

  async processQueue() {
    if (this.isProcessingQueue || this.cartUpdateQueue.length === 0) return;

    this.isProcessingQueue = true;

    while (this.cartUpdateQueue.length > 0 && !this.isProcessingSync) {
      const operation = this.cartUpdateQueue.shift();
      try {
        await operation();
      } catch (error) {
        console.error('Queue operation failed:', error);
      }
    }

    this.isProcessingQueue = false;
  }

  async getCart(retryCount = 0) {
    const maxRetries = 3;

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

      const response = await fetch('/cart.js', {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch cart`);
      }

      const cart = await response.json();

      // Validate cart data
      if (!cart || !Array.isArray(cart.items)) {
        throw new Error('Invalid cart data received');
      }

      return cart;
    } catch (error) {
      console.error(`Error fetching cart (attempt ${retryCount + 1}):`, error);

      if (retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
        return this.getCart(retryCount + 1);
      }

      throw error;
    }
  }

  async updateCart(updates, retryCount = 0) {
    const maxRetries = 3;

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('/cart/update.js', {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ updates })
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText || 'Failed to update cart'}`);
      }

      const result = await response.json();

      // Trigger cart update events with delay to allow DOM updates
      setTimeout(() => {
        document.dispatchEvent(new CustomEvent('cart:updated'));

        // Also trigger theme-specific events
        if (window.theme && window.theme.cartUpdated) {
          window.theme.cartUpdated();
        }

        // Trigger other common cart events
        document.dispatchEvent(new CustomEvent('cart:change'));
        document.dispatchEvent(new CustomEvent('cartUpdated'));
      }, 100);

      return result;
    } catch (error) {
      console.error(`Error updating cart (attempt ${retryCount + 1}):`, error);

      if (retryCount < maxRetries && (
          error.name === 'AbortError' ||
          error.message.includes('fetch') ||
          error.message.includes('network')
        )) {
        await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
        return this.updateCart(updates, retryCount + 1);
      }

      throw error;
    }
  }

  interceptAjaxCartCalls() {
    // Only intercept if not already done
    if (window._bundleCartSyncIntercepted) return;
    window._bundleCartSyncIntercepted = true;

    // Intercept fetch calls to cart endpoints
    const originalFetch = window.fetch;

    window.fetch = async (...args) => {
      try {
        const response = await originalFetch.apply(this, args);

        // Check if this was a cart-related request
        const url = args[0];
        if (typeof url === 'string' && (
            url.includes('/cart/add.js') ||
            url.includes('/cart/update.js') ||
            url.includes('/cart/change.js') ||
            url.includes('/cart/clear.js')
          )) {
          // Delay the integrity check to allow the cart to update
          setTimeout(() => {
            if (!this.isProcessingSync) {
              this.checkCartForBundleIntegrity();
            }
          }, 150);
        }

        return response;
      } catch (error) {
        console.error('Bundle cart sync fetch error:', error);
        return originalFetch.apply(this, args);
      }
    };

    // Also intercept XMLHttpRequest for older themes
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      this._bundleCartUrl = url;
      return originalXHROpen.apply(this, [method, url, ...args]);
    };

    const originalXHRSend = XMLHttpRequest.prototype.send;
    XMLHttpRequest.prototype.send = function(...args) {
      if (this._bundleCartUrl && typeof this._bundleCartUrl === 'string' && (
          this._bundleCartUrl.includes('/cart/add.js') ||
          this._bundleCartUrl.includes('/cart/update.js') ||
          this._bundleCartUrl.includes('/cart/change.js') ||
          this._bundleCartUrl.includes('/cart/clear.js')
        )) {
        this.addEventListener('load', () => {
          setTimeout(() => {
            if (window.bundleCartSync && !window.bundleCartSync.isProcessingSync) {
              window.bundleCartSync.checkCartForBundleIntegrity();
            }
          }, 150);
        });
      }
      return originalXHRSend.apply(this, args);
    };
  }
}

// Initialize the bundle cart sync system with error handling
function initializeBundleCartSync() {
  try {
    if (!window.bundleCartSync) {
      window.bundleCartSync = new BundleCartSync();
      console.log('🔗 Bundle cart sync system initialized successfully');
    }
  } catch (error) {
    console.error('Failed to initialize bundle cart sync:', error);
    // Retry after a delay
    setTimeout(() => {
      try {
        if (!window.bundleCartSync) {
          window.bundleCartSync = new BundleCartSync();
        }
      } catch (retryError) {
        console.error('Bundle cart sync retry failed:', retryError);
      }
    }, 2000);
  }
}

// Initialize based on document state
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeBundleCartSync);
} else {
  initializeBundleCartSync();
}

// Handle theme section reloads
document.addEventListener('shopify:section:load', () => {
  // Re-check cart integrity when sections reload
  setTimeout(() => {
    if (window.bundleCartSync && !window.bundleCartSync.isProcessingSync) {
      window.bundleCartSync.checkCartForBundleIntegrity();
    }
  }, 500);
});

// Handle page visibility changes (user returns to tab)
document.addEventListener('visibilitychange', () => {
  if (!document.hidden && window.bundleCartSync && !window.bundleCartSync.isProcessingSync) {
    setTimeout(() => {
      window.bundleCartSync.checkCartForBundleIntegrity();
    }, 1000);
  }
});

// Export for debugging
window.BundleCartSync = BundleCartSync;

// Simple test function
window.testBundleRemoval = function() {
  if (window.bundleCartSync) {
    fetch('/cart.js')
      .then(r => r.json())
      .then(cart => {
        const bundleItems = cart.items.filter(item =>
          item.properties['Bundle ID'] || item.properties._bundle_id
        );
        if (bundleItems.length > 0) {
          const bundleId = bundleItems[0].properties['Bundle ID'] || bundleItems[0].properties._bundle_id;
          window.bundleCartSync.removeBundleById(bundleId);
        }
      });
  }
};
