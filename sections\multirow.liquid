{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }

  .section-color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_section_colors_background.red }}, {{ section.settings.custom_section_colors_background.green }}, {{ section.settings.custom_section_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_section_gradient_background != blank %}{{ section.settings.custom_section_gradient_background }}{% else %}{{ section.settings.custom_section_colors_background }}{% endif %};
  }
{%- endstyle -%}

{%- liquid
  if section.settings.image_layout contains 'right'
    assign odd_class = ' image-with-text__grid--reverse'
  else
    assign even_class = ' image-with-text__grid--reverse'
  endif

  if section.settings.row_color_scheme == section.settings.section_color_scheme
    assign no_content_background = true
  endif
  if section.settings.section_color_scheme == 'custom' and section.settings.row_color_scheme == 'custom'
    unless section.settings.custom_colors_background == section.settings.custom_section_colors_background
      assign no_content_background = false
    endunless
  endif

  if settings.text_boxes_shadow_opacity == 0 and settings.text_boxes_border_thickness == 0 or settings.text_boxes_border_opacity == 0
    assign no_content_styles = true
  endif

  if settings.text_boxes_border_thickness > 0 and settings.text_boxes_border_opacity > 0 and settings.media_border_thickness > 0 and settings.media_border_opacity > 0
    assign borders_class = ' collapse-borders'
  endif

  if no_content_background and no_content_styles
    assign padding_class = ' collapse-padding'
    assign same_bgs = true
  endif

  unless no_content_background and no_content_styles
    assign corners_class = ' collapse-corners'
  endunless
-%}

<div class="multirow section-{{ section.id }}-padding section-color-scheme-{{ section.id }} color-{{ section.settings.section_color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="multirow__inner page-width">
    {%- for block in section.blocks -%}
      <div
        class="image-with-text image-with-text--mobile-{{ section.settings.mobile_direction }} isolate{{ borders_class }}{{ corners_class }}{{ padding_class }}{% if same_bgs %} same-colors{% else %} different-colors{% endif %} animate-item animate-item--child"
        style="--index:{{ forloop.index }};"
        {{ block.shopify_attributes }}
      >
        <div class="image-with-text__grid grid grid--gapless grid--1-col grid--{% if section.settings.desktop_image_width == 'medium' %}2-col-tablet{% else %}3-col-tablet{% endif %}{% if section.settings.image_layout contains 'alternate' %}{% cycle odd_class, even_class %}{% else %}{{ odd_class }}{% endif %}">
          <div class="image-with-text__media-item image-with-text__media-item--{{ section.settings.desktop_image_width }} image-with-text__media-item--{{ section.settings.desktop_content_position }}{% if section.settings.mobile_full_media_width %} image-with-text__media--mobile-full{% endif %} grid__item">
            <div
              class="image-with-text__media image-with-text__media--{{ section.settings.image_height }} color-scheme-{{ section.id }} color-{{ section.settings.row_color_scheme }} gradient global-media-settings{% if block.settings.image != blank or block.settings.video != blank %} media{% else %} image-with-text__media--placeholder placeholder{% endif %}"
              {% if section.settings.height == 'adapt' and block.settings.video != blank %}
                style="padding-bottom: {{ 1 | divided_by: block.settings.video.aspect_ratio | times: 100 }}%;"
              {% elsif section.settings.image_height == 'adapt' and block.settings.image != blank %}
                style="padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;"
              {% endif %}
            >
              {%- if block.settings.video != blank -%}
                {% liquid
                  assign autoplay = false
                  if block.settings.video_muted_autoplay
                    assign autoplay = true
                  endif
                %}
                <internal-video data-autoplay="{{ autoplay }}" data-no-play-btn="{{ autoplay }}">
                  <video
                    width="100%"
                    height="auto"
                    preload='metadata'
                    poster="{{ block.settings.video.preview_image | image_url }}"
                    {% if autoplay %}
                      muted autoplay loop
                    {% endif %}
                    playsinline disablepictureinpicture
                  >
                    {% for source in block.settings.video.sources %}
                      <source 
                        {% if autoplay %}data-{% endif %}src="{{ source.url }}" 
                        type="{{ source.mime_type }}"
                      >
                    {% endfor %}
                  </video>
                  <button class="internal-video__play"{% if autoplay %} style='visibility: hidden;'{% endif %}>
                    <div class="play-button color-accent-1">
                      {%- render 'icon-play' -%}
                    </div>
                  </button>
                </internal-video>
              {%- elsif block.settings.image != blank -%}
                {%- capture sizes -%}
                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                  (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
                {%- endcapture -%}
                {{
                  block.settings.image
                  | image_url: width: 1500
                  | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                }}
              {%- else -%}
                {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
              {%- endif -%}
            </div>
          </div>
          <div class="image-with-text__text-item grid__item">
            <div class="image-with-text__content image-with-text__content--{{ section.settings.desktop_content_position }} image-with-text__content--desktop-{{ section.settings.desktop_content_alignment }} image-with-text__content--mobile-{{ section.settings.mobile_content_alignment }} image-with-text__content--{{ section.settings.image_height }} content-container{% unless no_content_background and no_content_styles %} color-scheme-{{ section.id }} color-{{ section.settings.row_color_scheme }} gradient{% endunless %}">
              {%- if block.settings.caption -%}
                <p class="image-with-text__text image-with-text__text--caption caption-with-letter-spacing caption-with-letter-spacing--medium">
                  {{ block.settings.caption | escape }}
                </p>
              {%- endif -%}
              {%- if block.settings.title -%}
                <h2 class="image-with-text__heading {{ section.settings.heading_size }} rte title-with-highlight" style='--hightlight-color:{{ block.settings.title_highlight_color }}'>
                  {{ block.settings.title }}
                </h2>
              {%- endif -%}
              {%- if block.settings.text -%}
                <div class="image-with-text__text rte {{ section.settings.text_style }}">{{ block.settings.text }}</div>
              {%- endif -%}
              {%- if block.settings.button_label != blank -%}
                <a
                  {% if block.settings.button_link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.button_link }}"
                  {% endif %}
                  class="button button--{{ section.settings.button_style }}"
                >
                  {{ block.settings.button_label | escape }}
                </a>
              {%- endif -%}
              {%- if block.settings.atc_button_label != blank -%}
                {% if block.settings.atc_product == blank %}
                  <button
                    id="SectionAtcBtn-{{ section.id }}-{{ forloop.index }}"
                    type="button"
                    class="button main-product-atc button--has-spinner"
                    {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                      disabled
                    {% endif %}
                  >
                    {{ block.settings.atc_button_label }}
                    <div class="loading-overlay__spinner">
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        class="spinner"
                        viewBox="0 0 66 66"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                      </svg>
                    </div>
                  </button>
                {% else %}
                  {% assign product_form_id = 'section-product-form-'
                    | append: section.id
                    | append: block.id
                  %}
                  {% render 'separate-atc-btn',
                    product: block.settings.atc_product,
                    product_form_id: product_form_id,
                    label: block.settings.atc_button_label,
                    skip_cart: block.settings.atc_skip_cart
                  %}
                {% endif %}
              {%- endif -%}
            </div>
          </div>
        </div>
      </div>
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.multirow.name",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.multirow.settings.image_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.multirow.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.multirow.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.multirow.settings.image_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.multirow.settings.image_height.label"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        {
          "value": "small",
          "label": "t:sections.multirow.settings.desktop_image_width.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.multirow.settings.desktop_image_width.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.multirow.settings.desktop_image_width.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.multirow.settings.desktop_image_width.label",
      "info": "t:sections.multirow.settings.desktop_image_width.info"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.multirow.settings.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.multirow.settings.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.multirow.settings.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.multirow.settings.heading_size.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "body",
          "label": "t:sections.multirow.settings.text_style.options__1.label"
        },
        {
          "value": "subtitle",
          "label": "t:sections.multirow.settings.text_style.options__2.label"
        }
      ],
      "default": "body",
      "label": "t:sections.multirow.settings.text_style.label"
    },
    {
      "type": "select",
      "id": "button_style",
      "options": [
        {
          "value": "primary",
          "label": "t:sections.multirow.settings.button_style.options__1.label"
        },
        {
          "value": "secondary",
          "label": "t:sections.multirow.settings.button_style.options__2.label"
        }
      ],
      "default": "secondary",
      "label": "t:sections.multirow.settings.button_style.label"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top",
          "label": "t:sections.multirow.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "middle",
          "label": "t:sections.multirow.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "bottom",
          "label": "t:sections.multirow.settings.desktop_content_position.options__3.label"
        }
      ],
      "default": "middle",
      "label": "t:sections.multirow.settings.desktop_content_position.label",
      "info": "t:sections.multirow.settings.desktop_content_position.info"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multirow.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multirow.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.multirow.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multirow.settings.desktop_content_alignment.label"
    },
    {
      "type": "select",
      "id": "image_layout",
      "options": [
        {
          "value": "alternate-left",
          "label": "t:sections.multirow.settings.image_layout.options__1.label"
        },
        {
          "value": "alternate-right",
          "label": "t:sections.multirow.settings.image_layout.options__2.label"
        },
        {
          "value": "align-left",
          "label": "t:sections.multirow.settings.image_layout.options__3.label"
        },
        {
          "value": "align-right",
          "label": "t:sections.multirow.settings.image_layout.options__4.label"
        }
      ],
      "default": "alternate-left",
      "label": "t:sections.multirow.settings.image_layout.label",
      "info": "t:sections.multirow.settings.image_layout.info"
    },
    {
      "type": "select",
      "id": "row_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Rows content color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "select",
      "id": "section_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Section color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "t:sections.multirow.settings.header_mobile.content"
    },
    {
      "type": "checkbox",
      "id": "mobile_full_media_width",
      "label": "Full media width",
      "default": false
    },
    {
      "type": "select",
      "id": "mobile_direction",
      "options": [
        {
          "value": "normal",
          "label": "Media first"
        },
        {
          "value": "reverse",
          "label": "Media second"
        }
      ],
      "default": "normal",
      "label": "Mobile media position"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multirow.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multirow.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.multirow.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multirow.settings.mobile_content_alignment.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom content color scheme",
      "info": "Applied if Rows ontent color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#2E2A39",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#FFFFFF",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    },
    {
      "type": "header",
      "content": "Custom section color scheme",
      "info": "Applied if Section color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_section_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_section_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    }
  ],
  "blocks": [
    {
      "type": "row",
      "name": "t:sections.multirow.blocks.row.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multirow.blocks.row.settings.image.label"
        },
        {
          "type": "header",
          "content": "Or"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video",
          "info": "Use autoplay mp4 instead of GIFs & animated WEBPs."
        },
        {
          "type": "checkbox",
          "id": "video_muted_autoplay",
          "label": "Muted autoplay",
          "default": true,
          "info": "Use this instead of GIFs & animated WEBPs."
        },
        {
          "type": "text",
          "id": "caption",
          "default": "Caption",
          "label": "t:sections.multirow.blocks.row.settings.caption.label"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Row",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.multirow.blocks.row.settings.text.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.multirow.blocks.row.settings.button_label.label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.multirow.blocks.row.settings.button_link.label"
        },
        {
          "type": "text",
          "id": "atc_button_label",
          "label": "Add to Cart button label",
          "info": "Leave the label blank to hide the Add to Cart button."
        },
        {
          "type": "product",
          "id": "atc_product",
          "label": "ATC Custom product",
          "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
        },
        {
          "type": "checkbox",
          "id": "atc_skip_cart",
          "label": "ATC Custom product skip cart"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multirow.presets.name",
      "blocks": [
        {
          "type": "row"
        },
        {
          "type": "row"
        },
        {
          "type": "row"
        }
      ]
    }
  ]
}
{% endschema %}
