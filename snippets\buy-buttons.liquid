{% comment %}
  Renders product buy-buttons.
  Accepts:
  - product: {Object} product object.
  - block: {Object} passing the block information.
  - product_form_id: {String} product form id.
  - section_id: {String} id of section to which this snippet belongs.
  - show_pickup_availability:: {<PERSON><PERSON><PERSON>} for the pickup availability. If true the pickup availability is rendered, false - not rendered (optional).

  Usage:
  {% render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, show_pickup_availability: true %}
{% endcomment %}
<style>
  #ProductSubmitButton-{{ section_id }},
  #SectionAtcBtn-{{ section_id }} {
    --icon-scale: {{ block.settings.icon_scale | divided_by: 100.0 }}em;
    --icon-spacing: {{ block.settings.icon_spacing }}px;
  }
  {% if block.settings.enable_custom_color %}
    #ProductSubmitButton-{{ section_id }} {
      --color-button: {{ block.settings.custom_color.red }}, {{ block.settings.custom_color.green }}, {{ block.settings.custom_color.blue }};
    }
  {% endif %}
  {% if block.settings.prefix_icon != blank %}
    #ProductSubmitButton-{{ section_id }} .button__label::before {
      background-image: url('{{ block.settings.prefix_icon | image_url }}');
    }
  {% endif %}
  {% if block.settings.suffix_icon != blank %}
    #ProductSubmitButton-{{ section_id }} .button__label::after {
      background-image: url('{{ block.settings.suffix_icon | image_url }}');
    }
  {% endif %}
  
  {% if block.settings.secondary_btn_enable_custom_color %}
    #SectionAtcBtn-{{ section_id }} {
      --color-base-accent-1: {{ block.settings.secondary_btn_custom_color.red }}, {{ block.settings.secondary_btn_custom_color.green }}, {{ block.settings.secondary_btn_custom_color.blue }};
      --color-base-outline-button-labels: {{ block.settings.secondary_btn_custom_color.red }}, {{ block.settings.secondary_btn_custom_color.green }}, {{ block.settings.secondary_btn_custom_color.blue }};
    }
  {% endif %}
  {% if block.settings.secondary_btn_prefix_icon != blank %}
    #SectionAtcBtn-{{ section_id }} .button__label::before {
      background-image: url('{{ block.settings.secondary_btn_prefix_icon | image_url }}');
    }
  {% endif %}
  {% if block.settings.secondary_btn_suffix_icon != blank %}
    #SectionAtcBtn-{{ section_id }} .button__label::after {
      background-image: url('{{ block.settings.secondary_btn_suffix_icon | image_url }}');
    }
  {% endif %}
</style>
<div
  {{ block.shopify_attributes }}
  style="--margin-top: {{ block.settings.margin_top | divided_by: 10.0 }}rem;--margin-bottom: {{ block.settings.margin_bottom | divided_by: 10.0 }}rem;"
>
  {%- if product != blank -%}
    <product-form
      id="ProductForm-{{ section.id }}"
      class="product-form main-product-form"
      data-section="{{ section.id }}"
      data-options="{{ product.options | join: ',' }}"
      data-main="{{ main_product }}"
    >
      <div class="product-form__error-message-wrapper" role="alert" hidden>
        <svg
          aria-hidden="true"
          focusable="false"
          class="icon icon-error"
          viewBox="0 0 13 13"
        >
          <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
          <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
          <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
          <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
        </svg>
        <span class="product-form__error-message"></span>
      </div>

      {%- form 'product',
        product,
        id: product_form_id,
        class: 'form',
        novalidate: 'novalidate',
        data-type: 'add-to-cart-form'
      -%}
        <div class="hidden product-form__variants" id="product-form-{{ section.id }}__variants" data-values=""></div>
        <input
          type="hidden"
          name="id"
          value="{{ product.selected_or_first_available_variant.id }}"
          class="product-variant-id"
          {% if block.settings.skip_cart %}
            data-skip-cart="true"
          {% endif %}
        >
        <div class="product-form__buttons{% if block.settings.uppercase_text %} product-form__buttons--uppercase{% endif %}">
          {%- liquid
            assign check_against_inventory = true
            if product.selected_or_first_available_variant.inventory_management != 'shopify' or product.selected_or_first_available_variant.inventory_policy == 'continue'
              assign check_against_inventory = false
            endif
            if product.selected_or_first_available_variant.quantity_rule.min > product.selected_or_first_available_variant.inventory_quantity and check_against_inventory
              assign quantity_rule_soldout = true
            endif
          -%}
          <div class='product-form__quantity-and-btn product-form__quantity-and-btn--{{ quantity_atc_append }} product-form__quantity-and-btn--{{ quantity_atc_append_heights }}'>
            {% if quantity_selector_html != blank and quantity_atc_append != 'none' %}
              {{ quantity_selector_html }}
            {% endif %}
            <button
              id="ProductSubmitButton-{{ section_id }}"
              type="submit"
              name="add"
              class="atc-button product-form__submit button button--full-width button--margin-x{% if main_product %} main-product-atc{% else %} featured-product-atc-{{ section.id }}{% endif %}{% if block.settings.prefix_icon != blank %} button--prefix-icon{% endif %}{% if block.settings.suffix_icon != blank %} button--suffix-icon{% endif %}"
              {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                disabled
                data-unavailable="true"
              {% endif %}
              data-required-fields="0"
              data-valid-fields="0"
            >
              <span class="main-atc__error" style="display:none;"></span>
              <span class="main-atc__label button__label">
                {% if block.settings.display_price %}
                  <span id="main-atc-price-{{ section.id }}" class='main-atc-price{%- if product.selected_or_first_available_variant.available == false or quantity_rule_soldout -%} hidden{% endif %}'>
                    {{ product.selected_or_first_available_variant.price | money }} •
                  </span>
                {% endif %}
                <span class="main-atc__label__text">
                  {%- if product.selected_or_first_available_variant.available == false or quantity_rule_soldout -%}
                    {{ 'products.product.sold_out' | t }}
                  {%- else -%}
                    {{ 'products.product.add_to_cart' | t }}
                  {%- endif -%}
                </span>
              </span>
              <div class="loading-overlay__spinner hidden">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </button>
          </div>
          {%- if block.settings.enable_secondary_btn != blank -%}
            <button
              id="SectionAtcBtn-{{ section.id }}"
              type="button"
              class="button button--secondary{% if main_product %} main-product-atc{% else %} featured-product-atc-{{ section.id }}{% endif %} button--has-spinner button--full-width product-form__submit"
              data-skip-cart-button='true'
              {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                disabled
              {% endif %}
            >
              <span class='button__label'>
                {{ block.settings.secondary_btn_label }}
              </span>
              <div class="loading-overlay__spinner">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </button>
          {%- endif -%}
          {%- if block.settings.show_dynamic_checkout -%}
            {{ form | payment_button }}
          {%- endif -%}
        </div>
      {%- endform -%}
    </product-form>
  {%- else -%}
    <div class="product-form">
      <div class="product-form__buttons form">
        <button
          type="submit"
          name="add"
          class="product-form__submit button button--full-width button--primary"
          disabled
        >
          {{ 'products.product.sold_out' | t }}
        </button>
      </div>
    </div>
  {%- endif -%}

  {%- if show_pickup_availability -%}
    {{ 'component-pickup-availability.css' | asset_url | stylesheet_tag }}

    {%- assign pick_up_availabilities = product.selected_or_first_available_variant.store_availabilities
      | where: 'pick_up_enabled', true
    -%}

    <pickup-availability
      class="product__pickup-availabilities no-js-hidden quick-add-hidden"
      {% if product.selected_or_first_available_variant.available and pick_up_availabilities.size > 0 %}
        available
      {% endif %}
      data-root-url="{{ routes.root_url }}"
      data-variant-id="{{ product.selected_or_first_available_variant.id }}"
      data-has-only-default-variant="{{ product.has_only_default_variant }}"
    >
      <template>
        <pickup-availability-preview class="pickup-availability-preview">
          {% render 'icon-unavailable' %}
          <div class="pickup-availability-info">
            <p class="caption-large">{{ 'products.product.pickup_availability.unavailable' | t }}</p>
            <button class="pickup-availability-button link link--text underlined-link">
              {{ 'products.product.pickup_availability.refresh' | t }}
            </button>
          </div>
        </pickup-availability-preview>
      </template>
    </pickup-availability>

    <script src="{{ 'pickup-availability.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}
</div>
