/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "<PERSON><PERSON>u sắc",
      "settings": {
        "colors_solid_button_labels": {
          "label": "Nhãn nút đặc",
          "info": "Được sử dụng làm màu tiền cảnh trên màu nhấn."
        },
        "colors_accent_1": {
          "label": "Nhấn 1",
          "info": "<PERSON>ư<PERSON><PERSON> sử dụng làm nền nút đặc."
        },
        "colors_accent_2": {
          "label": "Nhấn 2"
        },
        "header__1": {
          "content": "Màu chính"
        },
        "header__2": {
          "content": "Màu phụ"
        },
        "colors_text": {
          "label": "Văn bản",
          "info": "Được sử dụng làm màu tiền cảnh trên màu nền."
        },
        "colors_outline_button_labels": {
          "label": "Nút viền ngoài",
          "info": "Cũng được sử dụng cho liên kết văn bản."
        },
        "colors_background_1": {
          "label": "Nền 1"
        },
        "colors_background_2": {
          "label": "Nền 2"
        },
        "gradient_accent_1": {
          "label": "Hiệu ứng chuyển màu của Điểm nhấn 1"
        },
        "gradient_accent_2": {
          "label": "Hiệu ứng chuyển màu của Điểm nhấn 2"
        },
        "gradient_background_1": {
          "label": "Hiệu ứng chuyển màu của Nền 1"
        },
        "gradient_background_2": {
          "label": "Hiệu ứng chuyển màu của Nền 2"
        }
      }
    },
    "typography": {
      "name": "Kiểu chữ",
      "settings": {
        "type_header_font": {
          "label": "Phông chữ",
          "info": "Việc chọn phông chữ khác có thể ảnh hưởng đến tốc độ của cửa hàng. [Tìm hiểu thêm về phông chữ hệ thống.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "header__1": {
          "content": "Tiêu đề"
        },
        "header__2": {
          "content": "Nội dung"
        },
        "type_body_font": {
          "label": "Phông chữ",
          "info": "Việc chọn phông chữ khác có thể ảnh hưởng đến tốc độ của cửa hàng. [Tìm hiểu thêm về phông chữ hệ thống.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "heading_scale": {
          "label": "Tỷ lệ cỡ phông chữ"
        },
        "body_scale": {
          "label": "Tỷ lệ cỡ phông chữ"
        }
      }
    },
    "styles": {
      "name": "Biểu tượng",
      "settings": {
        "accent_icons": {
          "options__3": {
            "label": "Nút viền ngoài"
          },
          "options__4": {
            "label": "Văn bản"
          },
          "label": "Màu sắc"
        }
      }
    },
    "social-media": {
      "name": "Truyền thông xã hội",
      "settings": {
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https://twitter.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "https://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Tài khoản mạng xã hội"
        }
      }
    },
    "currency_format": {
      "name": "Định dạng đơn vị tiền tệ",
      "settings": {
        "content": "Mã đơn vị tiền tệ",
        "currency_code_enabled": {
          "label": "Hiển thị mã đơn vị tiền tệ"
        },
        "paragraph": "Giá giỏ hàng và giá thanh toán luôn hiển thị mã đơn vị tiền tệ. Ví dụ: 1,00 USD."
      }
    },
    "layout": {
      "name": "Bố cục",
      "settings": {
        "page_width": {
          "label": "Chiều rộng trang"
        },
        "spacing_sections": {
          "label": "Khoảng cách giữa các mục mẫu"
        },
        "header__grid": {
          "content": "Lưới"
        },
        "paragraph__grid": {
          "content": "Ảnh hưởng đến vùng có nhiều cột hoặc hàng."
        },
        "spacing_grid_horizontal": {
          "label": "Không gian ngang"
        },
        "spacing_grid_vertical": {
          "label": "Không gian dọc"
        }
      }
    },
    "search_input": {
      "name": "Hành vi tìm kiếm",
      "settings": {
        "header": {
          "content": "Gợi ý tìm kiếm"
        },
        "predictive_search_enabled": {
          "label": "Bật gợi ý tìm kiếm"
        },
        "predictive_search_show_vendor": {
          "label": "Hiển thị nhà cung cấp sản phẩm",
          "info": "Hiển thị khi bật gợi ý tìm kiếm."
        },
        "predictive_search_show_price": {
          "label": "Hiển thị giá sản phẩm",
          "info": "Hiển thị khi bật gợi ý tìm kiếm."
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Đường viền"
        },
        "header__shadow": {
          "content": "Đổ bóng"
        },
        "blur": {
          "label": "Làm mờ"
        },
        "corner_radius": {
          "label": "Bán kính góc"
        },
        "horizontal_offset": {
          "label": "Khoảng bù ngang"
        },
        "vertical_offset": {
          "label": "Khoảng bù dọc"
        },
        "thickness": {
          "label": "Độ dày"
        },
        "opacity": {
          "label": "Độ chắn sáng"
        },
        "image_padding": {
          "label": "Vùng đệm ảnh"
        },
        "text_alignment": {
          "options__1": {
            "label": "Trái"
          },
          "options__2": {
            "label": "Giữa"
          },
          "options__3": {
            "label": "Phải"
          },
          "label": "Căn chỉnh văn bản"
        }
      }
    },
    "badges": {
      "name": "Huy hiệu",
      "settings": {
        "position": {
          "options__1": {
            "label": "Dưới cùng bên trái"
          },
          "options__2": {
            "label": "Dưới cùng bên phải"
          },
          "options__3": {
            "label": "Trên cùng bên trái"
          },
          "options__4": {
            "label": "Trên cùng bên phải"
          },
          "label": "Vị trí trên thẻ"
        },
        "sale_badge_color_scheme": {
          "label": "Bảng màu huy hiệu giảm giá"
        },
        "sold_out_badge_color_scheme": {
          "label": "Bảng màu huy hiệu đã hết hàng"
        }
      }
    },
    "buttons": {
      "name": "Nút"
    },
    "variant_pills": {
      "name": "Ô chọn mẫu mã",
      "paragraph": "Ô chọn mẫu mã là một cách để hiển thị mẫu mã sản phẩm. [Tìm hiểu thêm](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"
    },
    "inputs": {
      "name": "Thông tin đầu vào"
    },
    "content_containers": {
      "name": "Khoảng chứa nội dung"
    },
    "popups": {
      "name": "Menu thả xuống và cửa sổ bật lên",
      "paragraph": "Ảnh hưởng đến các vùng như menu điều hướng thả xuống, hộp tương tác bật lên và cửa sổ bật lên giỏ hàng."
    },
    "media": {
      "name": "Phương tiện"
    },
    "drawers": {
      "name": "Ngăn"
    },
    "cart": {
      "name": "Giỏ hàng",
      "settings": {
        "cart_type": {
          "label": "Loại giỏ hàng",
          "drawer": {
            "label": "Ngăn"
          },
          "page": {
            "label": "Trang"
          },
          "notification": {
            "label": "Thông báo cửa sổ bật lên"
          }
        },
        "show_vendor": {
          "label": "Hiển thị nhà cung cấp"
        },
        "show_cart_note": {
          "label": "Bật ghi chú trong giỏ hàng"
        },
        "cart_drawer": {
          "header": "Ngăn giỏ hàng",
          "collection": {
            "label": "Bộ sưu tập",
            "info": "Hiển thị khi ngăn giỏ hàng trống."
          }
        }
      }
    },
    "cards": {
      "name": "Thẻ sản phẩm",
      "settings": {
        "style": {
          "options__1": {
            "label": "Tiêu chuẩn"
          },
          "options__2": {
            "label": "Thẻ"
          },
          "label": "Kiểu"
        }
      }
    },
    "collection_cards": {
      "name": "Thẻ bộ sưu tập",
      "settings": {
        "style": {
          "options__1": {
            "label": "Tiêu chuẩn"
          },
          "options__2": {
            "label": "Thẻ"
          },
          "label": "Kiểu"
        }
      }
    },
    "blog_cards": {
      "name": "Thẻ blog",
      "settings": {
        "style": {
          "options__1": {
            "label": "Tiêu chuẩn"
          },
          "options__2": {
            "label": "Thẻ"
          },
          "label": "Kiểu"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Chiều rộng logo trên màn hình nền",
          "info": "Chiều rộng logo được tự động tối ưu hóa cho thiết bị di động."
        },
        "favicon": {
          "label": "Hình ảnh biểu tượng trang web",
          "info": "Sẽ được thu nhỏ xuống 32 x 32 px"
        }
      }
    },
    "brand_information": {
      "name": "Thông tin thương hiệu",
      "settings": {
        "brand_headline": {
          "label": "Tiêu đề"
        },
        "brand_description": {
          "label": "Mô tả"
        },
        "brand_image": {
          "label": "Hình ảnh"
        },
        "brand_image_width": {
          "label": "Chiều rộng hình ảnh"
        },
        "paragraph": {
          "content": "Thêm mô tả thương hiệu vào chân trang của cửa hàng."
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Vùng đệm mục",
        "padding_top": "Vùng đệm trên cùng",
        "padding_bottom": "Vùng đệm dưới cùng"
      },
      "spacing": "Khoảng cách",
      "colors": {
        "accent_1": {
          "label": "Điểm nhấn 1"
        },
        "accent_2": {
          "label": "Điểm nhấn 2"
        },
        "background_1": {
          "label": "Nền 1"
        },
        "background_2": {
          "label": "Nền 2"
        },
        "inverse": {
          "label": "Nghịch đảo"
        },
        "label": "Bảng màu",
        "has_cards_info": "Hãy cập nhật cài đặt chủ đề để thay đổi bảng màu thẻ."
      },
      "heading_size": {
        "label": "Cỡ tiêu đề",
        "options__1": {
          "label": "Nhỏ"
        },
        "options__2": {
          "label": "Trung bình"
        },
        "options__3": {
          "label": "Lớn"
        },
        "options__4": {
          "label": "Cực lớn"
        }
      }
    },
    "announcement-bar": {
      "name": "Thanh thông báo",
      "blocks": {
        "announcement": {
          "name": "Thông báo",
          "settings": {
            "text": {
              "label": "Văn bản"
            },
            "text_alignment": {
              "label": "Căn chỉnh văn bản",
              "options__1": {
                "label": "Bên trái"
              },
              "options__2": {
                "label": "Ở giữa"
              },
              "options__3": {
                "label": "Bên phải"
              }
            },
            "link": {
              "label": "Liên kết"
            }
          }
        }
      }
    },
    "collage": {
      "name": "Ghép",
      "settings": {
        "heading": {
          "label": "Tiêu đề"
        },
        "desktop_layout": {
          "label": "Bố cục màn hình nền",
          "options__1": {
            "label": "Khối lớn bên trái"
          },
          "options__2": {
            "label": "Khối lớn bên phải"
          }
        },
        "mobile_layout": {
          "label": "Bố cục di động",
          "options__1": {
            "label": "Ghép"
          },
          "options__2": {
            "label": "Cột"
          }
        },
        "card_styles": {
          "label": "Kiểu dáng thẻ",
          "info": "Có thể cập nhật kiểu dáng sản phẩm, bộ sưu tập và thẻ blog trong cài đặt chủ đề.",
          "options__1": {
            "label": "Sử dụng kiểu dáng thẻ riêng"
          },
          "options__2": {
            "label": "Tạo kiểu dáng cho tất cả như thẻ sản phẩm"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Hình ảnh",
          "settings": {
            "image": {
              "label": "Hình ảnh"
            }
          }
        },
        "product": {
          "name": "Sản phẩm",
          "settings": {
            "product": {
              "label": "Sản phẩm"
            },
            "secondary_background": {
              "label": "Hiển thị nền thứ cấp"
            },
            "second_image": {
              "label": "Hiển thị hình ảnh thứ cấp khi di chuột đến"
            }
          }
        },
        "collection": {
          "name": "Bộ sưu tập",
          "settings": {
            "collection": {
              "label": "Bộ sưu tập"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Ảnh bìa"
            },
            "video_url": {
              "label": "URL",
              "info": "Video chạy trong cửa sổ bật lên nếu mục này chứa các khối khác.",
              "placeholder": "Sử dụng URL Youtube hoặc Vimeo"
            },
            "description": {
              "label": "Văn bản thay thế cho video",
              "info": "Mô tả video cho khách hàng bằng trình đọc màn hình. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"
            }
          }
        }
      },
      "presets": {
        "name": "Ghép"
      }
    },
    "collection-list": {
      "name": "Danh sách bộ sưu tập",
      "settings": {
        "title": {
          "label": "Tiêu đề"
        },
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Chân dung"
          },
          "options__3": {
            "label": "Vuông"
          },
          "info": "Chỉnh sửa bộ sưu tập để thêm hình ảnh. [Tìm hiểu thêm](https://help.shopify.com/manual/products/collections)"
        },
        "swipe_on_mobile": {
          "label": "Bật tính năng quẹt trên di động"
        },
        "show_view_all": {
          "label": "Bật nút \"View all\" (Xem tất cả) nếu danh sách chứa nhiều bộ sưu tập hơn số lượng hiển thị"
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        },
        "columns_mobile": {
          "label": "Số cột trên thiết bị di động",
          "options__1": {
            "label": "1 cột"
          },
          "options__2": {
            "label": "2 cột"
          }
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Bộ sưu tập",
          "settings": {
            "collection": {
              "label": "Bộ sưu tập"
            }
          }
        }
      },
      "presets": {
        "name": "Danh sách bộ sưu tập"
      }
    },
    "contact-form": {
      "name": "Biểu mẫu liên hệ",
      "presets": {
        "name": "Biểu mẫu liên hệ"
      }
    },
    "custom-liquid": {
      "name": "Liquid tùy chỉnh",
      "settings": {
        "custom_liquid": {
          "label": "Liquid tùy chỉnh",
          "info": "Thêm đoạn mã ứng dụng hoặc mã Liquid khác để tạo các tùy chỉnh nâng cao."
        }
      },
      "presets": {
        "name": "Liquid tùy chỉnh"
      }
    },
    "featured-blog": {
      "name": "Bài viết blog",
      "settings": {
        "heading": {
          "label": "Tiêu đề"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Số bài viết blog hiển thị"
        },
        "show_view_all": {
          "label": "Bật nút \"View all\" (Xem tất cả) nếu blog chứa nhiều bài viết blog hơn số lượng hiển thị"
        },
        "show_image": {
          "label": "Hiển thị hình ảnh nổi bật",
          "info": "Để có kết quả tốt nhất, hãy sử dụng hình ảnh có tỷ lệ khung hình 3:2. [Tìm hiểu thêm](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "show_date": {
          "label": "Hiển thị ngày"
        },
        "show_author": {
          "label": "Hiển thị tác giả"
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        }
      },
      "presets": {
        "name": "Bài viết blog"
      }
    },
    "featured-collection": {
      "name": "Bộ sưu tập nổi bật",
      "settings": {
        "title": {
          "label": "Tiêu đề"
        },
        "collection": {
          "label": "Bộ sưu tập"
        },
        "products_to_show": {
          "label": "Số lượng sản phẩm hiển thị tối đa"
        },
        "show_view_all": {
          "label": "Bật tùy chọn \"Xem tất cả\" nếu bộ sưu tập có nhiều sản phẩm hơn số lượng hiển thị"
        },
        "header": {
          "content": "Thẻ sản phẩm"
        },
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Chân dung"
          },
          "options__3": {
            "label": "Vuông"
          }
        },
        "show_secondary_image": {
          "label": "Hiển thị hình ảnh thứ cấp khi di chuột đến"
        },
        "show_vendor": {
          "label": "Hiển thị nhà cung cấp"
        },
        "show_rating": {
          "label": "Hiển thị thứ hạng sản phẩm",
          "info": "Nếu muốn hiển thị thứ hạng, hãy thêm ứng dụng xếp hạng sản phẩm. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "enable_quick_buy": {
          "label": "Bật nút thêm nhanh",
          "info": "Tối ưu với cửa sổ bật lên hoặc giỏ hàng kiểu ngăn kéo."
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        },
        "description": {
          "label": "Mô tả"
        },
        "show_description": {
          "label": "Hiển thị mô tả bộ sưu tập trong trang quản trị"
        },
        "description_style": {
          "label": "Kiểu mô tả",
          "options__1": {
            "label": "Nội dung"
          },
          "options__2": {
            "label": "Tiêu đề phụ"
          },
          "options__3": {
            "label": "Chữ viết hoa"
          }
        },
        "view_all_style": {
          "label": "Kiểu \"Xem tất cả\"",
          "options__1": {
            "label": "Liên kết"
          },
          "options__2": {
            "label": "Nút viền ngoài"
          },
          "options__3": {
            "label": "Nút đặc"
          }
        },
        "enable_desktop_slider": {
          "label": "Bật tính năng quay vòng trên màn hình nền"
        },
        "full_width": {
          "label": "Tạo chiều rộng đầy đủ cho sản phẩm"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        },
        "columns_mobile": {
          "label": "Số cột trên thiết bị di động",
          "options__1": {
            "label": "1 cột"
          },
          "options__2": {
            "label": "2 cột"
          }
        },
        "swipe_on_mobile": {
          "label": "Bật tính năng quẹt trên di động"
        }
      },
      "presets": {
        "name": "Bộ sưu tập nổi bật"
      }
    },
    "footer": {
      "name": "Chân trang",
      "blocks": {
        "link_list": {
          "name": "Menu",
          "settings": {
            "heading": {
              "label": "Tiêu đề"
            },
            "menu": {
              "label": "Menu",
              "info": "Chỉ hiển thị mục menu cấp cao nhất."
            }
          }
        },
        "text": {
          "name": "Văn bản",
          "settings": {
            "heading": {
              "label": "Tiêu đề"
            },
            "subtext": {
              "label": "Văn bản phụ"
            }
          }
        },
        "brand_information": {
          "name": "Thông tin thương hiệu",
          "settings": {
            "paragraph": {
              "content": "Khối này sẽ hiển thị thông tin thương hiệu của bạn. [Chỉnh sửa thông tin thương hiệu.](/editor?context=theme&category=brand%20information)"
            },
            "header__1": {
              "content": "Biểu tượng truyền thông xã hội"
            },
            "show_social": {
              "label": "Hiển thị biểu tượng truyền thông xã hội",
              "info": "Để hiển thị tài khoản truyền thông xã hội, hãy liên kết các tài khoản đó trong [cài đặt chủ đề](/editor?context=theme&category=social%20media)."
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Hiển thị đăng ký nhận email"
        },
        "newsletter_heading": {
          "label": "Tiêu đề"
        },
        "header__1": {
          "content": "Đăng ký nhận email",
          "info": "Đã tự động thêm người đăng ký vào danh sách khách hàng \"chấp nhận tiếp thị\". [Tìm hiểu thêm](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "header__2": {
          "content": "Biểu tượng truyền thông xã hội",
          "info": "Để hiển thị tài khoản truyền thông xã hội, hãy liên kết các tài khoản đó trong [cài đặt chủ đề](/editor?context=theme&category=social%20media)."
        },
        "show_social": {
          "label": "Hiển thị biểu tượng truyền thông xã hội"
        },
        "header__3": {
          "content": "Hộp chọn quốc gia/vùng"
        },
        "header__4": {
          "info": "Để thêm quốc gia/khu vực, vào [cài đặt thị trường.](/admin/settings/markets)"
        },
        "enable_country_selector": {
          "label": "Bật hộp chọn quốc gia/vùng"
        },
        "header__5": {
          "content": "Hộp chọn ngôn ngữ"
        },
        "header__6": {
          "info": "Để thêm ngôn ngữ, vào [cài đặt ngôn ngữ](/admin/settings/languages)."
        },
        "enable_language_selector": {
          "label": "Bật hộp chọn ngôn ngữ"
        },
        "header__7": {
          "content": "Phương thức thanh toán"
        },
        "payment_enable": {
          "label": "Hiển thị biểu tượng thanh toán"
        },
        "margin_top": {
          "label": "Lề trên"
        },
        "header__8": {
          "content": "Liên kết chính sách",
          "info": "Để thêm chính sách cửa hàng, truy cập [cài đặt chính sách](/admin/settings/legal)."
        },
        "show_policy": {
          "label": "Hiển thị liên kết chính sách"
        },
        "header__9": {
          "content": "Theo dõi trên Shop",
          "info": "Hiển thị nút theo dõi cho cửa hàng của bạn trên ứng dụng Shop. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        },
        "enable_follow_on_shop": {
          "label": "Bật tính năng Theo dõi trên Shop"
        }
      }
    },
    "header": {
      "name": "Đầu trang",
      "settings": {
        "logo_position": {
          "label": "Vị trí logo trên màn hình nền",
          "options__1": {
            "label": "Ở giữa bên trái"
          },
          "options__2": {
            "label": "Trên cùng bên trái"
          },
          "options__3": {
            "label": "Trên cùng ở giữa"
          },
          "options__4": {
            "label": "Chính giữa"
          }
        },
        "menu": {
          "label": "Menu"
        },
        "show_line_separator": {
          "label": "Hiển thị đường phân cách"
        },
        "margin_bottom": {
          "label": "Lề dưới"
        },
        "menu_type_desktop": {
          "label": "Loại menu trên màn hình nền",
          "info": "Loại menu được tự động tối ưu hóa cho thiết bị di động.",
          "options__1": {
            "label": "Menu thả xuống"
          },
          "options__2": {
            "label": "Menu lớn"
          }
        },
        "mobile_layout": {
          "content": "Bố cục di động"
        },
        "mobile_logo_position": {
          "label": "Vị trí logo di động",
          "options__1": {
            "label": "Ở giữa"
          },
          "options__2": {
            "label": "Bên trái"
          }
        },
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Chỉnh sửa logo trong [cài đặt chủ đề](/editor?context=theme&category=logo)."
        },
        "sticky_header_type": {
          "label": "Đầu trang dính",
          "options__1": {
            "label": "Không"
          },
          "options__2": {
            "label": "Khi cuộn lên"
          },
          "options__3": {
            "label": "Luôn luôn"
          },
          "options__4": {
            "label": "Luôn luôn, giảm kích cỡ logo"
          }
        }
      }
    },
    "image-banner": {
      "name": "Biểu ngữ hình ảnh",
      "settings": {
        "image": {
          "label": "Hình ảnh đầu tiên"
        },
        "image_2": {
          "label": "Hình ảnh thứ hai"
        },
        "color_scheme": {
          "info": "Có thể nhìn thấy khi vùng chứa hiển thị."
        },
        "stack_images_on_mobile": {
          "label": "Xếp chồng hình ảnh trên điện thoại di động"
        },
        "adapt_height_first_image": {
          "label": "Điều chỉnh độ cao mục theo cỡ hình ảnh thứ nhất",
          "info": "Nếu chọn, cài đặt chiều cao biểu ngữ hình ảnh sẽ được ghi đè."
        },
        "show_text_box": {
          "label": "Hiện vùng chứa trên màn hình nền"
        },
        "image_overlay_opacity": {
          "label": "Độ chắn sáng của lớp phủ hình ảnh"
        },
        "header": {
          "content": "Bố cục trên thiết bị di động"
        },
        "show_text_below": {
          "label": "Hiện vùng chứa trên thiết bị di động"
        },
        "image_height": {
          "label": "Chiều cao biểu ngữ",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh đầu tiên"
          },
          "options__2": {
            "label": "Nhỏ"
          },
          "options__3": {
            "label": "Trung bình"
          },
          "info": "Để có kết quả tốt nhất, hãy sử dụng hình ảnh có tỷ lệ khung hình 3:2. [Tìm hiểu thêm](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
          "options__4": {
            "label": "Lớn"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Trên cùng bên trái"
          },
          "options__2": {
            "label": "Trên cùng ở giữa"
          },
          "options__3": {
            "label": "Trên cùng bên phải"
          },
          "options__4": {
            "label": "Ở giữa bên trái"
          },
          "options__5": {
            "label": "Chính giữa"
          },
          "options__6": {
            "label": "Ở giữa bên phải"
          },
          "options__7": {
            "label": "Dưới cùng bên trái"
          },
          "options__8": {
            "label": "Dưới cùng ở giữa"
          },
          "options__9": {
            "label": "Dưới cùng bên phải"
          },
          "label": "Vị trí nội dung trên màn hình nền"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên màn hình nền"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên thiết bị di động"
        }
      },
      "blocks": {
        "heading": {
          "name": "Tiêu đề",
          "settings": {
            "heading": {
              "label": "Tiêu đề"
            }
          }
        },
        "text": {
          "name": "Văn bản",
          "settings": {
            "text": {
              "label": "Mô tả"
            },
            "text_style": {
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề"
              },
              "options__3": {
                "label": "Chữ viết hoa"
              },
              "label": "Kiểu văn bản"
            }
          }
        },
        "buttons": {
          "name": "Nút",
          "settings": {
            "button_label_1": {
              "label": "Nhãn nút thứ nhất",
              "info": "Để nhãn trống để ẩn nút."
            },
            "button_link_1": {
              "label": "Liên kết trên nút thứ nhất"
            },
            "button_style_secondary_1": {
              "label": "Sử dụng kiểu nút viền ngoài"
            },
            "button_label_2": {
              "label": "Nhãn nút thứ hai",
              "info": "Để nhãn trống để ẩn nút."
            },
            "button_link_2": {
              "label": "Liên kết trên nút thứ hai"
            },
            "button_style_secondary_2": {
              "label": "Sử dụng kiểu nút viền ngoài"
            }
          }
        }
      },
      "presets": {
        "name": "Biểu ngữ hình ảnh"
      }
    },
    "image-with-text": {
      "name": "Hình ảnh có chữ",
      "settings": {
        "image": {
          "label": "Hình ảnh"
        },
        "height": {
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Nhỏ"
          },
          "options__3": {
            "label": "Trung bình"
          },
          "label": "Chiều cao hình ảnh",
          "options__4": {
            "label": "Lớn"
          }
        },
        "layout": {
          "options__1": {
            "label": "Hình ảnh trước"
          },
          "options__2": {
            "label": "Hình ảnh thứ hai"
          },
          "label": "Vị trí hình ảnh trên màn hình nền",
          "info": "Hình ảnh trước là bố cục mặc định trên di động."
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Nhỏ"
          },
          "options__2": {
            "label": "Trung bình"
          },
          "options__3": {
            "label": "Lớn"
          },
          "label": "Chiều rộng hình ảnh trên màn hình nền",
          "info": "Hình ảnh được tự động tối ưu hóa cho thiết bị di động."
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên màn hình nền",
          "options__2": {
            "label": "Giữa"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Bên trên"
          },
          "options__2": {
            "label": "Ở giữa"
          },
          "options__3": {
            "label": "Bên dưới"
          },
          "label": "Vị trí nội dung trên màn hình nền"
        },
        "content_layout": {
          "options__1": {
            "label": "Không trùng"
          },
          "options__2": {
            "label": "Trùng"
          },
          "label": "Bố cục nội dung"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên thiết bị di động",
          "options__2": {
            "label": "Giữa"
          }
        }
      },
      "blocks": {
        "heading": {
          "name": "Tiêu đề",
          "settings": {
            "heading": {
              "label": "Tiêu đề"
            }
          }
        },
        "text": {
          "name": "Văn bản",
          "settings": {
            "text": {
              "label": "Nội dung"
            },
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề"
              }
            }
          }
        },
        "button": {
          "name": "Nút",
          "settings": {
            "button_label": {
              "label": "Nhãn nút",
              "info": "Để nhãn trống để ẩn nút."
            },
            "button_link": {
              "label": "Liên kết trên nút"
            }
          }
        },
        "caption": {
          "name": "Phụ đề",
          "settings": {
            "text": {
              "label": "Văn bản"
            },
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Tiêu đề phụ"
              },
              "options__2": {
                "label": "Chữ viết hoa"
              }
            },
            "caption_size": {
              "label": "Cỡ chữ",
              "options__1": {
                "label": "Nhỏ"
              },
              "options__2": {
                "label": "Trung bình"
              },
              "options__3": {
                "label": "Lớn"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Hình ảnh có chữ"
      }
    },
    "main-article": {
      "name": "Bài viết blog",
      "blocks": {
        "featured_image": {
          "name": "Hình ảnh nổi bật",
          "settings": {
            "image_height": {
              "label": "Chiều cao hình ảnh nổi bật",
              "options__1": {
                "label": "Điều chỉnh theo hình ảnh"
              },
              "options__2": {
                "label": "Nhỏ"
              },
              "options__3": {
                "label": "Trung bình"
              },
              "info": "Để có kết quả tốt nhất, hãy sử dụng hình ảnh có tỷ lệ khung hình 16:9. [Tìm hiểu thêm](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
              "options__4": {
                "label": "Lớn"
              }
            }
          }
        },
        "title": {
          "name": "Tên",
          "settings": {
            "blog_show_date": {
              "label": "Hiển thị ngày"
            },
            "blog_show_author": {
              "label": "Hiển thị tác giả"
            }
          }
        },
        "content": {
          "name": "Nội dung"
        },
        "share": {
          "name": "Chia sẻ",
          "settings": {
            "featured_image_info": {
              "content": "Nếu bạn đưa liên kết vào bài đăng trên truyền thông xã hội, hình ảnh nổi bật của trang sẽ hiển thị giống hình ảnh xem trước. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Hình ảnh xem trước có chứa tiêu đề và mô tả của cửa hàng. [Tìm hiểu thêm](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Văn bản"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Bài viết blog",
      "settings": {
        "header": {
          "content": "Thẻ bài viết blog"
        },
        "show_image": {
          "label": "Hiển thị hình ảnh nổi bật"
        },
        "paragraph": {
          "content": "Chỉnh sửa bài viết blog để thay đổi đoạn trích. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"
        },
        "show_date": {
          "label": "Hiển thị ngày"
        },
        "show_author": {
          "label": "Hiển thị tác giả"
        },
        "layout": {
          "label": "Bố cục màn hình nền",
          "options__1": {
            "label": "Lưới"
          },
          "options__2": {
            "label": "Ghép"
          },
          "info": "Bài viết được xếp chồng trên thiết bị di động."
        },
        "image_height": {
          "label": "Chiều cao hình ảnh nổi bật",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Nhỏ"
          },
          "options__3": {
            "label": "Trung bình"
          },
          "options__4": {
            "label": "Lớn"
          },
          "info": "Để có kết quả tốt nhất, hãy sử dụng hình ảnh có tỷ lệ khung hình 3:2. [Tìm hiểu thêm](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-cart-footer": {
      "name": "Tổng phụ",
      "blocks": {
        "subtotal": {
          "name": "Giá tổng phụ"
        },
        "buttons": {
          "name": "Nút thanh toán"
        }
      }
    },
    "main-cart-items": {
      "name": "Mặt hàng"
    },
    "main-collection-banner": {
      "name": "Biểu ngữ bộ sưu tập",
      "settings": {
        "paragraph": {
          "content": "Chỉnh sửa bộ sưu tập để thêm mô tả hoặc hình ảnh. [Tìm hiểu thêm](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Hiển thị mô tả bộ sưu tập"
        },
        "show_collection_image": {
          "label": "Hiển thị hình ảnh bộ sưu tập",
          "info": "Để có kết quả tốt nhất, hãy sử dụng hình ảnh có tỷ lệ khung hình 16:9. [Tìm hiểu thêm](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Lưới sản phẩm",
      "settings": {
        "products_per_page": {
          "label": "Sản phẩm trên một trang"
        },
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Chân dung"
          },
          "options__3": {
            "label": "Vuông"
          }
        },
        "show_secondary_image": {
          "label": "Hiển thị hình ảnh thứ cấp khi di chuột đến"
        },
        "show_vendor": {
          "label": "Hiển thị nhà cung cấp"
        },
        "enable_tags": {
          "label": "Bật lọc",
          "info": "Tùy chỉnh bộ lọc bằng ứng dụng Search & Discovery. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_filtering": {
          "label": "Bật lọc",
          "info": "Tùy chỉnh bộ lọc bằng ứng dụng Search & Discovery. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Bật sắp xếp"
        },
        "header__1": {
          "content": "Lọc và sắp xếp"
        },
        "header__3": {
          "content": "Thẻ sản phẩm"
        },
        "show_rating": {
          "label": "Hiển thị thứ hạng sản phẩm",
          "info": "Nếu muốn hiển thị thứ hạng, hãy thêm ứng dụng xếp hạng sản phẩm. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"
        },
        "enable_quick_buy": {
          "label": "Bật nút thêm nhanh",
          "info": "Tối ưu với cửa sổ bật lên hoặc giỏ hàng kiểu ngăn kéo."
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        },
        "columns_mobile": {
          "label": "Số cột trên thiết bị di động",
          "options__1": {
            "label": "1 cột"
          },
          "options__2": {
            "label": "2 cột"
          }
        },
        "filter_type": {
          "label": "Bố cục bộ lọc trên màn hình nền",
          "options__1": {
            "label": "Ngang"
          },
          "options__2": {
            "label": "Dọc"
          },
          "options__3": {
            "label": "Ngăn"
          },
          "info": "Ngăn là bố cục mặc định trên di động."
        }
      }
    },
    "main-list-collections": {
      "name": "Trang danh sách bộ sưu tập",
      "settings": {
        "title": {
          "label": "Tiêu đề"
        },
        "sort": {
          "label": "Sắp xếp bộ sưu tập theo:",
          "options__1": {
            "label": "Thứ tự bảng chữ cái (từ A-Z)"
          },
          "options__2": {
            "label": "Thứ tự bảng chữ cái (từ Z-A)"
          },
          "options__3": {
            "label": "Ngày (từ mới đến cũ)"
          },
          "options__4": {
            "label": "Ngày (từ cũ đến mới)"
          },
          "options__5": {
            "label": "Số lượng sản phẩm (từ cao xuống thấp)"
          },
          "options__6": {
            "label": "Số lượng sản phẩm (từ thấp lên cao)"
          }
        },
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Chân dung"
          },
          "options__3": {
            "label": "Vuông"
          },
          "info": "Chỉnh sửa bộ sưu tập để thêm hình ảnh. [Tìm hiểu thêm](https://help.shopify.com/manual/products/collections)"
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        },
        "columns_mobile": {
          "label": "Số cột trên thiết bị di động",
          "options__1": {
            "label": "1 cột"
          },
          "options__2": {
            "label": "2 cột"
          }
        }
      }
    },
    "main-page": {
      "name": "Trang"
    },
    "main-password-footer": {
      "name": "Chân trang mật khẩu"
    },
    "main-password-header": {
      "name": "Đầu trang mật khẩu",
      "settings": {
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Chỉnh sửa logo trong cài đặt chủ đề."
        }
      }
    },
    "main-product": {
      "name": "Thông tin sản phẩm",
      "blocks": {
        "text": {
          "name": "Văn bản",
          "settings": {
            "text": {
              "label": "Văn bản"
            },
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề"
              },
              "options__3": {
                "label": "Chữ viết hoa"
              }
            }
          }
        },
        "title": {
          "name": "Tên"
        },
        "price": {
          "name": "Giá"
        },
        "quantity_selector": {
          "name": "Hộp chọn số lượng"
        },
        "variant_picker": {
          "name": "Trình chọn mẫu mã",
          "settings": {
            "picker_type": {
              "label": "Loại",
              "options__1": {
                "label": "Menu thả xuống"
              },
              "options__2": {
                "label": "Góc bo tròn"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Nút mua",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Hiển thị nút thanh toán động",
              "info": "Sử dụng phương thức thanh toán được hỗ trợ trong cửa hàng, khách hàng sẽ thấy tùy chọn ưu tiên của họ như PayPal hoặc Apple Pay. [Tìm hiểu thêm](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "pickup_availability": {
          "name": "Khả năng nhận hàng tại cửa hàng"
        },
        "description": {
          "name": "Mô tả"
        },
        "share": {
          "name": "Chia sẻ",
          "settings": {
            "featured_image_info": {
              "content": "Nếu bạn đưa liên kết vào bài đăng trên truyền thông xã hội, hình ảnh nổi bật của trang sẽ hiển thị giống hình ảnh xem trước. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Hình ảnh xem trước có chứa tiêu đề và mô tả của cửa hàng. [Tìm hiểu thêm](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Văn bản"
            }
          }
        },
        "collapsible_tab": {
          "name": "Hàng có thể thu gọn",
          "settings": {
            "heading": {
              "info": "Bao gồm tiêu đề giải thích nội dung.",
              "label": "Tiêu đề"
            },
            "content": {
              "label": "Nội dung hàng"
            },
            "page": {
              "label": "Nội dung hàng trong trang"
            },
            "icon": {
              "label": "Biểu tượng",
              "options__1": {
                "label": "Không"
              },
              "options__2": {
                "label": "Táo"
              },
              "options__3": {
                "label": "Chuối"
              },
              "options__4": {
                "label": "Chai"
              },
              "options__5": {
                "label": "Hộp"
              },
              "options__6": {
                "label": "Cà rốt"
              },
              "options__7": {
                "label": "Bong bóng trò chuyện"
              },
              "options__8": {
                "label": "Dấu kiểm"
              },
              "options__9": {
                "label": "Bìa kẹp hồ sơ"
              },
              "options__10": {
                "label": "Sữa"
              },
              "options__11": {
                "label": "Không chứa sữa"
              },
              "options__12": {
                "label": "Máy sấy"
              },
              "options__13": {
                "label": "Mắt"
              },
              "options__14": {
                "label": "Lửa"
              },
              "options__15": {
                "label": "Không chứa gluten"
              },
              "options__16": {
                "label": "Trái tim"
              },
              "options__17": {
                "label": "Bàn là"
              },
              "options__18": {
                "label": "Lá"
              },
              "options__19": {
                "label": "Da"
              },
              "options__20": {
                "label": "Tia sét"
              },
              "options__21": {
                "label": "Son"
              },
              "options__22": {
                "label": "Ổ khóa"
              },
              "options__23": {
                "label": "Ghim bản đồ"
              },
              "options__24": {
                "label": "Không chứa hạt"
              },
              "options__25": {
                "label": "Quần"
              },
              "options__26": {
                "label": "Dấu chân"
              },
              "options__27": {
                "label": "Hạt tiêu"
              },
              "options__28": {
                "label": "Nước hoa"
              },
              "options__29": {
                "label": "Máy bay"
              },
              "options__30": {
                "label": "Thực vật"
              },
              "options__31": {
                "label": "Thẻ ghi giá"
              },
              "options__32": {
                "label": "Dấu hỏi"
              },
              "options__33": {
                "label": "Tái chế"
              },
              "options__34": {
                "label": "Đơn hàng trả lại"
              },
              "options__35": {
                "label": "Thước kẻ"
              },
              "options__36": {
                "label": "Đĩa ăn"
              },
              "options__37": {
                "label": "Áo sơ mi"
              },
              "options__38": {
                "label": "Giày"
              },
              "options__39": {
                "label": "Hình chiếu"
              },
              "options__40": {
                "label": "Hoa tuyết"
              },
              "options__41": {
                "label": "Ngôi sao"
              },
              "options__42": {
                "label": "Đồng hồ bấm giờ"
              },
              "options__43": {
                "label": "Xe tải"
              },
              "options__44": {
                "label": "Giặt"
              }
            }
          }
        },
        "popup": {
          "name": "Cửa sổ bật lên",
          "settings": {
            "link_label": {
              "label": "Nhãn liên kết"
            },
            "page": {
              "label": "Trang"
            }
          }
        },
        "custom_liquid": {
          "name": "Liquid tùy chỉnh",
          "settings": {
            "custom_liquid": {
              "label": "Liquid tùy chỉnh",
              "info": "Thêm đoạn mã ứng dụng hoặc mã Liquid khác để tạo các tùy chỉnh nâng cao."
            }
          }
        },
        "rating": {
          "name": "Thứ hạng sản phẩm",
          "settings": {
            "paragraph": {
              "content": "Nếu muốn hiển thị thứ hạng, hãy thêm ứng dụng xếp hạng sản phẩm. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Sản phẩm bổ sung",
          "settings": {
            "paragraph": {
              "content": "Để chọn sản phẩm bổ sung, hãy thêm ứng dụng Search & Discovery. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Tiêu đề"
            },
            "make_collapsible_row": {
              "label": "Hiển thị dưới dạng hàng có thể thu gọn"
            },
            "icon": {
              "info": "Hiển thị khi hàng có thể thu gọn hiển thị."
            },
            "product_list_limit": {
              "label": "Số lượng sản phẩm hiển thị tối đa"
            },
            "products_per_page": {
              "label": "Số lượng sản phẩm trên một trang"
            },
            "pagination_style": {
              "label": "Kiểu phân trang",
              "options": {
                "option_1": "Chấm",
                "option_2": "Bộ đếm",
                "option_3": "Số"
              }
            },
            "product_card": {
              "heading": "Thẻ sản phẩm"
            },
            "image_ratio": {
              "label": "Tỷ lệ hình ảnh",
              "options": {
                "option_1": "Dọc",
                "option_2": "Vuông"
              }
            },
            "enable_quick_add": {
              "label": "Bật nút thêm nhanh"
            }
          }
        },
        "icon_with_text": {
          "name": "Biểu tượng có văn bản",
          "settings": {
            "layout": {
              "label": "Bố cục",
              "options__1": {
                "label": "Ngang"
              },
              "options__2": {
                "label": "Dọc"
              }
            },
            "content": {
              "label": "Nội dung",
              "info": "Chọn biểu tượng hoặc thêm ảnh cho từng cột hoặc hàng."
            },
            "heading": {
              "info": "Để trống nhãn tiêu đề để ẩn cột biểu tượng."
            },
            "icon_1": {
              "label": "Biểu tượng đầu tiên"
            },
            "image_1": {
              "label": "Hình ảnh đầu tiên"
            },
            "heading_1": {
              "label": "Tiêu đề đầu tiên"
            },
            "icon_2": {
              "label": "Biểu tượng thứ hai"
            },
            "image_2": {
              "label": "Hình ảnh thứ hai"
            },
            "heading_2": {
              "label": "Tiêu đề thứ hai"
            },
            "icon_3": {
              "label": "Biểu tượng thứ ba"
            },
            "image_3": {
              "label": "Hình ảnh thứ ba"
            },
            "heading_3": {
              "label": "Tiêu đề thứ ba"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề phụ"
              },
              "options__3": {
                "label": "Chữ viết hoa"
              }
            }
          }
        },
        "inventory": {
          "name": "Trạng thái hàng lưu kho",
          "settings": {
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề phụ"
              },
              "options__3": {
                "label": "Chữ viết hoa"
              }
            },
            "inventory_threshold": {
              "label": "Ngưỡng hàng trong kho thấp",
              "info": "Chọn 0 để luôn hiển thị còn hàng nếu có."
            },
            "show_inventory_quantity": {
              "label": "Hiển thị số lượng hàng trong kho"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Phương tiện",
          "info": "Tìm hiểu thêm về [loại phương tiện.](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Bật vòng lặp video"
        },
        "enable_sticky_info": {
          "label": "Bật nội dung dính trên màn hình nền"
        },
        "hide_variants": {
          "label": "Ẩn phương tiện của các mẫu mã khác sau khi chọn một mẫu mã"
        },
        "gallery_layout": {
          "label": "Bố cục màn hình nền",
          "options__1": {
            "label": "Đã xếp chồng"
          },
          "options__2": {
            "label": "2 cột"
          },
          "options__3": {
            "label": "Hình thu nhỏ"
          },
          "options__4": {
            "label": "Quay vòng hình thu nhỏ"
          }
        },
        "media_size": {
          "label": "Chiều rộng phương tiện trên màn hình nền",
          "info": "Phương tiện được tự động tối ưu hóa cho thiết bị di động.",
          "options__1": {
            "label": "Nhỏ"
          },
          "options__2": {
            "label": "Trung bình"
          },
          "options__3": {
            "label": "Lớn"
          }
        },
        "mobile_thumbnails": {
          "label": "Bố cục di động",
          "options__1": {
            "label": "2 cột"
          },
          "options__2": {
            "label": "Hiển thị hình thu nhỏ"
          },
          "options__3": {
            "label": "Ẩn hình thu nhỏ"
          }
        },
        "media_position": {
          "label": "Vị trí phương tiện trên màn hình nền",
          "info": "Vị trí được tự động tối ưu hóa cho thiết bị di động.",
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Bên phải"
          }
        },
        "image_zoom": {
          "label": "Thu phóng hình ảnh",
          "info": "Nhấp và di chuột đến mục Mặc định để mở lightbox trên thiết bị di động.",
          "options__1": {
            "label": "Mở lightbox"
          },
          "options__2": {
            "label": "Nhấp và di chuột"
          },
          "options__3": {
            "label": "Không thu phóng"
          }
        },
        "constrain_to_viewport": {
          "label": "Giới hạn phương tiện theo chiều cao màn hình"
        },
        "media_fit": {
          "label": "Phù hợp với phương tiện",
          "options__1": {
            "label": "Gốc"
          },
          "options__2": {
            "label": "Tràn màn hình"
          }
        }
      }
    },
    "main-search": {
      "name": "Kết quả tìm kiếm",
      "settings": {
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Chân dung"
          },
          "options__3": {
            "label": "Vuông"
          }
        },
        "show_secondary_image": {
          "label": "Hiển thị hình ảnh thứ cấp khi di chuột đến"
        },
        "show_vendor": {
          "label": "Hiển thị nhà cung cấp"
        },
        "header__1": {
          "content": "Thẻ sản phẩm"
        },
        "header__2": {
          "content": "Thẻ blog",
          "info": "Kiểu dáng của thẻ blog cũng áp dụng cho thẻ trang trong kết quả tìm kiếm. Hãy cập nhật cài đặt chủ đề để thay đổi kiểu dáng thẻ."
        },
        "article_show_date": {
          "label": "Hiển thị ngày"
        },
        "article_show_author": {
          "label": "Hiển thị tác giả"
        },
        "show_rating": {
          "label": "Hiển thị thứ hạng sản phẩm",
          "info": "Nếu muốn hiển thị thứ hạng, hãy thêm ứng dụng xếp hạng sản phẩm. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        },
        "columns_mobile": {
          "label": "Số cột trên thiết bị di động",
          "options__1": {
            "label": "1 cột"
          },
          "options__2": {
            "label": "2 cột"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Nhiều cột",
      "settings": {
        "title": {
          "label": "Tiêu đề"
        },
        "image_width": {
          "label": "Chiều rộng hình ảnh",
          "options__1": {
            "label": "Chiều rộng một phần ba cột"
          },
          "options__2": {
            "label": "Chiều rộng một nửa cột"
          },
          "options__3": {
            "label": "Chiều rộng toàn bộ cột"
          }
        },
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Chân dung"
          },
          "options__3": {
            "label": "Vuông"
          },
          "options__4": {
            "label": "Tròn"
          }
        },
        "column_alignment": {
          "label": "Căn chỉnh cột",
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Giữa"
          }
        },
        "background_style": {
          "label": "Nền thứ cấp",
          "options__1": {
            "label": "Không"
          },
          "options__2": {
            "label": "Hiển thị dưới dạng nền cột"
          }
        },
        "button_label": {
          "label": "Nhãn nút"
        },
        "button_link": {
          "label": "Liên kết trên nút"
        },
        "swipe_on_mobile": {
          "label": "Bật tính năng quẹt trên di động"
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        },
        "columns_mobile": {
          "label": "Số cột trên thiết bị di động",
          "options__1": {
            "label": "1 cột"
          },
          "options__2": {
            "label": "2 cột"
          }
        }
      },
      "blocks": {
        "column": {
          "name": "Cột",
          "settings": {
            "image": {
              "label": "Hình ảnh"
            },
            "title": {
              "label": "Tiêu đề"
            },
            "text": {
              "label": "Mô tả"
            },
            "link_label": {
              "label": "Nhãn liên kết"
            },
            "link": {
              "label": "Liên kết"
            }
          }
        }
      },
      "presets": {
        "name": "Nhiều cột"
      }
    },
    "newsletter": {
      "name": "Đăng ký nhận email",
      "settings": {
        "full_width": {
          "label": "Làm cho mục có chiều rộng đầy đủ"
        },
        "paragraph": {
          "content": "Mỗi gói đăng ký qua email sẽ tạo một tài khoản khách hàng. [Tìm hiểu thêm](https://help.shopify.com/manual/customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Tiêu đề",
          "settings": {
            "heading": {
              "label": "Tiêu đề"
            }
          }
        },
        "paragraph": {
          "name": "Tiêu đề phụ",
          "settings": {
            "paragraph": {
              "label": "Mô tả"
            }
          }
        },
        "email_form": {
          "name": "Mẫu email"
        }
      },
      "presets": {
        "name": "Đăng ký nhận email"
      }
    },
    "page": {
      "name": "Trang",
      "settings": {
        "page": {
          "label": "Trang"
        }
      },
      "presets": {
        "name": "Trang"
      }
    },
    "rich-text": {
      "name": "Văn bản đa dạng thức",
      "settings": {
        "full_width": {
          "label": "Làm cho mục có chiều rộng đầy đủ"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Ở giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Vị trí nội dung trên màn hình nền",
          "info": "Vị trí được tự động tối ưu hóa cho thiết bị di động."
        },
        "content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Ở giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung"
        }
      },
      "blocks": {
        "heading": {
          "name": "Tiêu đề",
          "settings": {
            "heading": {
              "label": "Tiêu đề"
            }
          }
        },
        "text": {
          "name": "Văn bản",
          "settings": {
            "text": {
              "label": "Mô tả"
            }
          }
        },
        "buttons": {
          "name": "Nút",
          "settings": {
            "button_label_1": {
              "label": "Nhãn nút thứ nhất",
              "info": "Để trống nhãn này để ẩn nút."
            },
            "button_link_1": {
              "label": "Liên kết trên nút thứ nhất"
            },
            "button_style_secondary_1": {
              "label": "Sử dụng kiểu nút viền ngoài"
            },
            "button_label_2": {
              "label": "Nhãn nút thứ hai",
              "info": "Để trống nhãn này để ẩn nút."
            },
            "button_link_2": {
              "label": "Liên kết trên nút thứ hai"
            },
            "button_style_secondary_2": {
              "label": "Sử dụng kiểu nút viền ngoài"
            }
          }
        },
        "caption": {
          "name": "Phụ đề",
          "settings": {
            "text": {
              "label": "Văn bản"
            },
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Tiêu đề phụ"
              },
              "options__2": {
                "label": "Chữ viết hoa"
              }
            },
            "caption_size": {
              "label": "Cỡ chữ",
              "options__1": {
                "label": "Nhỏ"
              },
              "options__2": {
                "label": "Trung bình"
              },
              "options__3": {
                "label": "Lớn"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Văn bản đa dạng thức"
      }
    },
    "apps": {
      "name": "Ứng dụng",
      "settings": {
        "include_margins": {
          "label": "Đặt lề của mục giống lề của chủ đề"
        }
      },
      "presets": {
        "name": "Ứng dụng"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Tiêu đề"
        },
        "cover_image": {
          "label": "Ảnh bìa"
        },
        "video_url": {
          "label": "URL",
          "placeholder": "Sử dụng URL Youtube hoặc Vimeo",
          "info": "Video phát trong trang."
        },
        "description": {
          "label": "Văn bản thay thế cho video",
          "info": "Mô tả video cho khách hàng bằng trình đọc màn hình. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"
        },
        "image_padding": {
          "label": "Thêm vùng đệm ảnh",
          "info": "Chọn vùng đệm ảnh nếu bạn không muốn cắt ảnh bìa."
        },
        "full_width": {
          "label": "Tạo chiều rộng đầy đủ cho mục"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Sản phẩm nổi bật",
      "blocks": {
        "text": {
          "name": "Văn bản",
          "settings": {
            "text": {
              "label": "Văn bản"
            },
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề"
              },
              "options__3": {
                "label": "Chữ viết hoa"
              }
            }
          }
        },
        "title": {
          "name": "Tiêu đề"
        },
        "price": {
          "name": "Giá"
        },
        "quantity_selector": {
          "name": "Hộp chọn số lượng"
        },
        "variant_picker": {
          "name": "Trình chọn mẫu mã",
          "settings": {
            "picker_type": {
              "label": "Loại",
              "options__1": {
                "label": "Menu thả xuống"
              },
              "options__2": {
                "label": "Ô chọn"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Nút Mua",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Hiển thị nút thanh toán động",
              "info": "Khi sử dụng phương thức thanh toán được hỗ trợ trong cửa hàng của bạn, khách hàng sẽ thấy tùy chọn ưu tiên của mình, như PayPal hoặc Apple Pay. [Tìm hiểu thêm](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Mô tả"
        },
        "share": {
          "name": "Chia sẻ",
          "settings": {
            "featured_image_info": {
              "content": "Nếu bạn đưa liên kết vào bài đăng trên truyền thông xã hội, hình ảnh nổi bật của trang sẽ hiển thị giống hình ảnh xem trước. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Hình ảnh xem trước có chứa tiêu đề và mô tả của cửa hàng. [Tìm hiểu thêm](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Văn bản"
            }
          }
        },
        "custom_liquid": {
          "name": "Liquid tùy chỉnh",
          "settings": {
            "custom_liquid": {
              "label": "Liquid tùy chỉnh"
            }
          }
        },
        "rating": {
          "name": "Thứ hạng sản phẩm",
          "settings": {
            "paragraph": {
              "content": "Nếu muốn hiển thị thứ hạng, hãy thêm ứng dụng xếp hạng sản phẩm. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Kiểu văn bản",
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề phụ"
              },
              "options__3": {
                "label": "Chữ viết hoa"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Sản phẩm"
        },
        "secondary_background": {
          "label": "Hiển thị nền phụ"
        },
        "header": {
          "content": "Phương tiện",
          "info": "Tìm hiểu thêm về [loại phương tiện](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Bật vòng lặp video"
        },
        "hide_variants": {
          "label": "Ẩn phương tiện của các mẫu mã không được chọn trên máy tính để bàn"
        },
        "media_position": {
          "label": "Vị trí phương tiện trên màn hình nền",
          "info": "Vị trí được tự động tối ưu hóa cho thiết bị di động.",
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Bên phải"
          }
        }
      },
      "presets": {
        "name": "Sản phẩm nổi bật"
      }
    },
    "email-signup-banner": {
      "name": "Biểu ngữ đăng ký nhận email",
      "settings": {
        "paragraph": {
          "content": "Mỗi gói đăng ký qua email sẽ tạo một tài khoản khách hàng. [Tìm hiểu thêm](https://help.shopify.com/manual/customers)"
        },
        "image": {
          "label": "Ảnh nền"
        },
        "show_background_image": {
          "label": "Hiển thị ảnh nền"
        },
        "show_text_box": {
          "label": "Hiện vùng chứa trên màn hình nền"
        },
        "image_overlay_opacity": {
          "label": "Độ chắn sáng của lớp phủ hình ảnh"
        },
        "color_scheme": {
          "info": "Có thể nhìn thấy khi vùng chứa hiển thị."
        },
        "show_text_below": {
          "label": "Hiển thị nội dung dưới hình ảnh trên thiết bị di động",
          "info": "Để có kết quả tốt nhất, hãy sử dụng hình ảnh có tỷ lệ khung hình 16:9. [Tìm hiểu thêm](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "image_height": {
          "label": "Chiều cao biểu ngữ",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Nhỏ"
          },
          "options__3": {
            "label": "Trung bình"
          },
          "options__4": {
            "label": "Lớn"
          },
          "info": "Để có kết quả tốt nhất, hãy sử dụng hình ảnh có tỷ lệ khung hình 16:9. [Tìm hiểu thêm](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Trên cùng bên trái"
          },
          "options__2": {
            "label": "Trên cùng ở giữa"
          },
          "options__3": {
            "label": "Trên cùng bên phải"
          },
          "options__4": {
            "label": "Ở giữa bên trái"
          },
          "options__5": {
            "label": "Chính giữa"
          },
          "options__6": {
            "label": "Ở giữa bên phải"
          },
          "options__7": {
            "label": "Dưới cùng bên trái"
          },
          "options__8": {
            "label": "Dưới cùng ở giữa"
          },
          "options__9": {
            "label": "Dưới cùng bên phải"
          },
          "label": "Vị trí nội dung trên màn hình nền"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên màn hình nền"
        },
        "header": {
          "content": "Bố cục trên thiết bị di động"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên thiết bị di động"
        }
      },
      "blocks": {
        "heading": {
          "name": "Tiêu đề",
          "settings": {
            "heading": {
              "label": "Tiêu đề"
            }
          }
        },
        "paragraph": {
          "name": "Đoạn",
          "settings": {
            "paragraph": {
              "label": "Mô tả"
            },
            "text_style": {
              "options__1": {
                "label": "Nội dung"
              },
              "options__2": {
                "label": "Tiêu đề"
              },
              "label": "Kiểu văn bản"
            }
          }
        },
        "email_form": {
          "name": "Mẫu email"
        }
      },
      "presets": {
        "name": "Biểu ngữ đăng ký nhận email"
      }
    },
    "slideshow": {
      "name": "Bản trình chiếu",
      "settings": {
        "layout": {
          "label": "Bố cục",
          "options__1": {
            "label": "Độ rộng đầy đủ"
          },
          "options__2": {
            "label": "Lưới"
          }
        },
        "slide_height": {
          "label": "Chiều cao trang chiếu",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh đầu tiên"
          },
          "options__2": {
            "label": "Nhỏ"
          },
          "options__3": {
            "label": "Trung bình"
          },
          "options__4": {
            "label": "Lớn"
          }
        },
        "slider_visual": {
          "label": "Kiểu phân trang",
          "options__1": {
            "label": "Bộ đếm"
          },
          "options__2": {
            "label": "Chấm"
          },
          "options__3": {
            "label": "Số"
          }
        },
        "auto_rotate": {
          "label": "Tự động xoay vòng các trang chiếu"
        },
        "change_slides_speed": {
          "label": "Đổi trang chiếu sau mỗi"
        },
        "mobile": {
          "content": "Bố cục di động"
        },
        "show_text_below": {
          "label": "Hiển thị nội dung bên dưới hình ảnh trên thiết bị di động"
        },
        "accessibility": {
          "content": "Khả năng truy cập",
          "label": "Mô tả bản trình chiếu",
          "info": "Mô tả bản trình chiếu cho khách hàng bằng trình đọc màn hình."
        }
      },
      "blocks": {
        "slide": {
          "name": "Trang chiếu",
          "settings": {
            "image": {
              "label": "Hình ảnh"
            },
            "heading": {
              "label": "Tiêu đề"
            },
            "subheading": {
              "label": "Tiêu đề phụ"
            },
            "button_label": {
              "label": "Nhãn nút",
              "info": "Để trống nhãn này để ẩn nút."
            },
            "link": {
              "label": "Liên kết trên nút"
            },
            "secondary_style": {
              "label": "Sử dụng kiểu nút viền ngoài"
            },
            "box_align": {
              "label": "Vị trí nội dung trên màn hình nền",
              "options__1": {
                "label": "Trên cùng bên trái"
              },
              "options__2": {
                "label": "Trên cùng ở giữa"
              },
              "options__3": {
                "label": "Trên cùng bên phải"
              },
              "options__4": {
                "label": "Ở giữa bên trái"
              },
              "options__5": {
                "label": "Chính giữa"
              },
              "options__6": {
                "label": "Ở giữa bên phải"
              },
              "options__7": {
                "label": "Dưới cùng bên trái"
              },
              "options__8": {
                "label": "Dưới cùng ở giữa"
              },
              "options__9": {
                "label": "Dưới cùng bên phải"
              },
              "info": "Vị trí được tự động tối ưu hóa cho thiết bị di động."
            },
            "show_text_box": {
              "label": "Hiện vùng chứa trên màn hình nền"
            },
            "text_alignment": {
              "label": "Căn chỉnh nội dung trên màn hình nền",
              "option_1": {
                "label": "Bên trái"
              },
              "option_2": {
                "label": "Giữa"
              },
              "option_3": {
                "label": "Bên phải"
              }
            },
            "image_overlay_opacity": {
              "label": "Độ chắn sáng của lớp phủ hình ảnh"
            },
            "color_scheme": {
              "info": "Có thể nhìn thấy khi vùng chứa hiển thị."
            },
            "text_alignment_mobile": {
              "label": "Căn chỉnh nội dung trên thiết bị di động",
              "options__1": {
                "label": "Bên trái"
              },
              "options__2": {
                "label": "Giữa"
              },
              "options__3": {
                "label": "Bên phải"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Bản trình chiếu"
      }
    },
    "collapsible_content": {
      "name": "Nội dung có thể thu gọn",
      "settings": {
        "caption": {
          "label": "Phụ đề"
        },
        "heading": {
          "label": "Tiêu đề"
        },
        "heading_alignment": {
          "label": "Căn chỉnh tiêu đề",
          "options__1": {
            "label": "Trái"
          },
          "options__2": {
            "label": "Giữa"
          },
          "options__3": {
            "label": "Phải"
          }
        },
        "layout": {
          "label": "Bố cục",
          "options__1": {
            "label": "Không có khoảng chứa nào"
          },
          "options__2": {
            "label": "Khoảng chứa hàng"
          },
          "options__3": {
            "label": "Khoảng chứa mục"
          }
        },
        "open_first_collapsible_row": {
          "label": "Mở hàng có thể thu gọn đầu tiên"
        },
        "header": {
          "content": "Bố cục hình ảnh"
        },
        "image": {
          "label": "Hình ảnh"
        },
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Nhỏ"
          },
          "options__3": {
            "label": "Lớn"
          }
        },
        "desktop_layout": {
          "label": "Bố cục màn hình nền",
          "options__1": {
            "label": "Hình ảnh trước"
          },
          "options__2": {
            "label": "Hình ảnh thứ hai"
          },
          "info": "Hình ảnh luôn xuất hiện trước trên thiết bị di động."
        },
        "container_color_scheme": {
          "label": "Bảng màu cho khoảng chửa",
          "info": "Hiển thị khi đặt Bố cục thành khoảng chứa Hàng hoặc khoảng chứa Mục."
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Hàng có thể thu gọn",
          "settings": {
            "heading": {
              "info": "Bao gồm tiêu đề giải thích nội dung.",
              "label": "Tiêu đề"
            },
            "row_content": {
              "label": "Nội dung hàng"
            },
            "page": {
              "label": "Nội dung hàng trong trang"
            },
            "icon": {
              "label": "Biểu tượng",
              "options__1": {
                "label": "Không"
              },
              "options__2": {
                "label": "Táo"
              },
              "options__3": {
                "label": "Chuối"
              },
              "options__4": {
                "label": "Chai"
              },
              "options__5": {
                "label": "Hộp"
              },
              "options__6": {
                "label": "Cà rốt"
              },
              "options__7": {
                "label": "Bong bóng trò chuyện"
              },
              "options__8": {
                "label": "Dấu kiểm"
              },
              "options__9": {
                "label": "Bìa kẹp hồ sơ"
              },
              "options__10": {
                "label": "Sữa"
              },
              "options__11": {
                "label": "Không chứa sữa"
              },
              "options__12": {
                "label": "Máy sấy"
              },
              "options__13": {
                "label": "Mắt"
              },
              "options__14": {
                "label": "Lửa"
              },
              "options__15": {
                "label": "Không chứa gluten"
              },
              "options__16": {
                "label": "Trái tim"
              },
              "options__17": {
                "label": "Bàn là"
              },
              "options__18": {
                "label": "Lá"
              },
              "options__19": {
                "label": "Da"
              },
              "options__20": {
                "label": "Tia sét"
              },
              "options__21": {
                "label": "Son"
              },
              "options__22": {
                "label": "Ổ khóa"
              },
              "options__23": {
                "label": "Ghim bản đồ"
              },
              "options__24": {
                "label": "Không chứa hạt"
              },
              "options__25": {
                "label": "Quần"
              },
              "options__26": {
                "label": "Dấu chân"
              },
              "options__27": {
                "label": "Hạt tiêu"
              },
              "options__28": {
                "label": "Nước hoa"
              },
              "options__29": {
                "label": "Máy bay"
              },
              "options__30": {
                "label": "Thực vật"
              },
              "options__31": {
                "label": "Thẻ ghi giá"
              },
              "options__32": {
                "label": "Dấu hỏi"
              },
              "options__33": {
                "label": "Tái chế"
              },
              "options__34": {
                "label": "Đơn hàng trả lại"
              },
              "options__35": {
                "label": "Thước kẻ"
              },
              "options__36": {
                "label": "Đĩa ăn"
              },
              "options__37": {
                "label": "Áo sơ mi"
              },
              "options__38": {
                "label": "Giày"
              },
              "options__39": {
                "label": "Hình chiếu"
              },
              "options__40": {
                "label": "Hoa tuyết"
              },
              "options__41": {
                "label": "Ngôi sao"
              },
              "options__42": {
                "label": "Đồng hồ bấm giờ"
              },
              "options__43": {
                "label": "Xe tải"
              },
              "options__44": {
                "label": "Giặt"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Nội dung có thể thu gọn"
      }
    },
    "main-account": {
      "name": "Tài khoản"
    },
    "main-activate-account": {
      "name": "Kích hoạt tài khoản"
    },
    "main-addresses": {
      "name": "Địa chỉ"
    },
    "main-login": {
      "name": "Đăng nhập"
    },
    "main-order": {
      "name": "Đơn hàng"
    },
    "main-register": {
      "name": "Đăng ký"
    },
    "main-reset-password": {
      "name": "Đặt lại mật khẩu"
    },
    "related-products": {
      "name": "Sản phẩm liên quan",
      "settings": {
        "heading": {
          "label": "Tiêu đề"
        },
        "products_to_show": {
          "label": "Số lượng sản phẩm hiển thị tối đa"
        },
        "columns_desktop": {
          "label": "Số cột trên máy tính để bàn"
        },
        "paragraph__1": {
          "content": "Đề xuất động sử dụng thông tin về đơn hàng và sản phẩm để thay đổi và cải thiện theo thời gian. [Tìm hiểu thêm](https://help.shopify.com/themes/development/recommended-products)"
        },
        "header__2": {
          "content": "Thẻ sản phẩm"
        },
        "image_ratio": {
          "label": "Tỷ lệ hình ảnh",
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Dọc"
          },
          "options__3": {
            "label": "Vuông"
          }
        },
        "show_secondary_image": {
          "label": "Hiển thị hình ảnh thứ cấp khi di chuột đến"
        },
        "show_vendor": {
          "label": "Hiển thị nhà cung cấp"
        },
        "show_rating": {
          "label": "Hiển thị thứ hạng sản phẩm",
          "info": "Nếu muốn hiển thị thứ hạng, hãy thêm ứng dụng xếp hạng sản phẩm. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        },
        "columns_mobile": {
          "label": "Số cột trên thiết bị di động",
          "options__1": {
            "label": "1 cột"
          },
          "options__2": {
            "label": "2 cột"
          }
        }
      }
    },
    "multirow": {
      "name": "Multirow",
      "settings": {
        "image": {
          "label": "Hình ảnh"
        },
        "image_height": {
          "options__1": {
            "label": "Điều chỉnh theo hình ảnh"
          },
          "options__2": {
            "label": "Nhỏ"
          },
          "options__3": {
            "label": "Trung bình"
          },
          "options__4": {
            "label": "Lớn"
          },
          "label": "Chiều cao hình ảnh"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Nhỏ"
          },
          "options__2": {
            "label": "Trung bình"
          },
          "options__3": {
            "label": "Lớn"
          },
          "label": "Chiều rộng hình ảnh trên màn hình nền",
          "info": "Hình ảnh được tự động tối ưu hóa cho thiết bị di động."
        },
        "heading_size": {
          "options__1": {
            "label": "Nhỏ"
          },
          "options__2": {
            "label": "Trung bình"
          },
          "options__3": {
            "label": "Lớn"
          },
          "label": "Cỡ tiêu đề"
        },
        "text_style": {
          "options__1": {
            "label": "Nội dung"
          },
          "options__2": {
            "label": "Tiêu đề phụ"
          },
          "label": "Kiểu văn bản"
        },
        "button_style": {
          "options__1": {
            "label": "Nút đặc"
          },
          "options__2": {
            "label": "Nút viền ngoài"
          },
          "label": "Kiểu nút"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Ở giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên màn hình nền"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Bên trên"
          },
          "options__2": {
            "label": "Ở giữa"
          },
          "options__3": {
            "label": "Bên dưới"
          },
          "label": "Vị trí nội dung trên màn hình nền",
          "info": "Vị trí được tự động tối ưu hóa cho thiết bị di động."
        },
        "image_layout": {
          "options__1": {
            "label": "Thay thế từ bên trái"
          },
          "options__2": {
            "label": "Thay thế từ bên phải"
          },
          "options__3": {
            "label": "Đã căn trái"
          },
          "options__4": {
            "label": "Đã căn phải"
          },
          "label": "Vị trí hình ảnh trên màn hình nền",
          "info": "Vị trí được tự động tối ưu hóa cho thiết bị di động."
        },
        "container_color_scheme": {
          "label": "Bảng màu cho khoảng chứa"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Bên trái"
          },
          "options__2": {
            "label": "Ở giữa"
          },
          "options__3": {
            "label": "Bên phải"
          },
          "label": "Căn chỉnh nội dung trên thiết bị di động"
        },
        "header_mobile": {
          "content": "Bố cục trên thiết bị di động"
        }
      },
      "blocks": {
        "row": {
          "name": "Hàng",
          "settings": {
            "image": {
              "label": "Hình ảnh"
            },
            "caption": {
              "label": "Phụ đề"
            },
            "heading": {
              "label": "Tiêu đề"
            },
            "text": {
              "label": "Văn bản"
            },
            "button_label": {
              "label": "Nhãn nút"
            },
            "button_link": {
              "label": "Liên kết trên nút"
            }
          }
        }
      },
      "presets": {
        "name": "Multirow"
      }
    }
  }
}
