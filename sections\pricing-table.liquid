{{ 'section-pricing-table.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .pricing-table {
    align-items: {{ section.settings.cards_alignment }};
    column-gap: {{ section.settings.desktop_spacing }}px;
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="page-width section-{{ section.id }}-padding">
    <div class="content-rte center animate-item animate-item--child index-0">
      {% assign content_index = 0 %}
      {%- unless section.settings.title == blank -%}
        {% assign content_index = 1 %}
        <h2 class="{{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
          {{ section.settings.title }}
        </h2>
      {%- endunless -%}
      {% if section.settings.text != blank %}
        {% assign content_index = 1 %}
        <div class="rte">
          {{ section.settings.text }}
        </div>
      {% endif %}
    </div>
    <div class="pricing-table{% if content_index == 1 %} pricing-table--has-content{% endif %}">
      {% for block in section.blocks %}
        {% liquid
          if block.settings.product != blank
            assign index = block.settings.variant_index | minus: 1
            assign chosen_variant = block.settings.product.variants[index]
          endif
          if block.settings.displayed_image == 'variant_image'
            assign displayed_image = chosen_variant.featured_image
          elsif block.settings.displayed_image == 'custom'
            assign displayed_image = block.settings.custom_image
          endif
        %}
        <style>
          .color-scheme-{{ section.id }}-{{ block.id }}.color-custom {
            --color-background: {{ block.settings.custom_colors_background.red }}, {{ block.settings.custom_colors_background.green }}, {{ block.settings.custom_colors_background.blue }};
            --gradient-background: {% if block.settings.custom_gradient_background != blank %}{{ block.settings.custom_gradient_background }}{% else %}{{ block.settings.custom_colors_background }}{% endif %};
            --color-foreground: {{ block.settings.custom_colors_text.red }}, {{ block.settings.custom_colors_text.green }}, {{ block.settings.custom_colors_text.blue }};
            --color-button: {{ block.settings.custom_colors_solid_button_background.red }}, {{ block.settings.custom_colors_solid_button_background.green }}, {{ block.settings.custom_colors_solid_button_background.blue }};
            --color-button-text: {{ block.settings.custom_colors_solid_button_text.red }}, {{ block.settings.custom_colors_solid_button_text.green }}, {{ block.settings.custom_colors_solid_button_text.blue }};
            --color-base-outline-button-labels: {{ block.settings.custom_colors_outline_button.red }}, {{ block.settings.custom_colors_outline_button.green }}, {{ block.settings.custom_colors_outline_button.blue }};
          }
          .accent-color-scheme-{{ section.id }}-{{ block.id }}.color-custom {
            --color-background: {{ block.settings.custom_accent_color.red }}, {{ block.settings.custom_accent_color.green }}, {{ block.settings.custom_accent_color.blue }};
            --color-foreground: {{ block.settings.custom_accent_text_color.red }}, {{ block.settings.custom_accent_text_color.green }}, {{ block.settings.custom_accent_text_color.blue }};
          }
          .color-scheme-{{ section.id }}-{{ block.id }}.accent-custom {
            --accent-color: {{ block.settings.custom_accent_color.red }}, {{ block.settings.custom_accent_color.green }}, {{ block.settings.custom_accent_color.blue }};
          }
        </style>
        <div
          class="plan-card plan-card-{{ block.settings.card_style }}{% if block.settings.displayed_image != 'hidden' %} plan-card--has-image{% endif %} color-scheme-{{ section.id }}-{{ block.id }} color-{{ block.settings.color_scheme }} gradient accent-{{ block.settings.accent_color }} animate-item animate-item--child"
          style="--index:{{ forloop.index0 | plus: content_index }};"
        >
          {% if block.settings.badge != blank %}
            <span class="plan-card__badge color-{{ block.settings.badge_color_scheme }}">
              {{ block.settings.badge }}
            </span>
          {% endif %}
          <div class="plan-card__top{% unless block.settings.card_style == 'style-1' %} accent-color-scheme-{{ section.id }}-{{ block.id }} color-{{ block.settings.accent_color }}{% endunless %}">
            {% if displayed_image != blank %}
              <div class='plan-card__image'>
                {%- capture sizes -%}
                  320px
                {%- endcapture -%}
                {{
                  displayed_image
                  | image_url: width: 1500
                  | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                }}
              </div>
            {% endif %}
            {% unless block.settings.custom_icon == blank and block.settings.icon == 'none' %}
              <div class="plan-card__icon plan-card__icon--{{ section.settings.icon_size }}{% if block.settings.card_style == 'style-3' %} color-scheme-{{ section.id }}-{{ block.id }} color-{{ block.settings.color_scheme }}{% endif %}">
                {% if block.settings.custom_icon != blank %}
                  {{ block.settings.custom_icon | image_url: width: 600 | image_tag: loading: 'lazy', width: 500 }}
                {% else %}
                  {% render 'material-icon', icon: block.settings.icon, filled: block.settings.filled_icon %}
                {% endif %}
              </div>
            {% endunless %}
            {% if block.settings.title != blank %}
              <h3 class="plan-card__title">
                {{ block.settings.title }}
              </h3>
            {% endif %}
            {% if block.settings.price != blank %}
              <div class="plan-card__price-container{% if block.settings.card_style == 'style-1' %} accent-color-scheme-{{ section.id }}-{{ block.id }} color-{{ block.settings.accent_color }}{% elsif block.settings.card_style == 'style-2' %} color-scheme-{{ section.id }}-{{ block.id }} color-{{ block.settings.color_scheme }}{% endif %}">
                <span class="plan-card__price">
                  {% if chosen_variant != blank %}
                    {% capture price_money %}{{ chosen_variant.price | money }}{% endcapture %}
                    {{ block.settings.price | replace: '[price]', price_money }}
                  {% else %}
                    {{ block.settings.price }}
                  {% endif %}
                </span>
                {% if block.settings.price_text != blank %}
                  <span class="plan-card__price-text">
                    {{ block.settings.price_text }}
                  </span>
                {% endif %}
              </div>
            {% endif %}
          </div>
          <div class="plan-card__bottom{% unless block.settings.custom_icon == blank and block.settings.icon == 'none' %} plan-card__bottom--icon-{{ section.settings.icon_size }}{% endunless %}">
            {% if block.settings.description != blank %}
              <div class="pricing-plan__desc">
                {{ block.settings.description }}
              </div>
            {% endif %}
            {% if block.settings.benefits != blank %}
              {% if block.settings.benefits contains ',' %}
                {% assign plan_benefits = block.settings.benefits
                  | remove: '<p>'
                  | remove: '</p>'
                  | strip
                  | split: ','
                %}

                <div class="pricing-plan__benefits {{ block.settings.benefits_alignment }}">
                  {% for benefit in plan_benefits %}
                    <div class="pricing-plan__benefit-item">
                      <div class="pricing-plan__benefit-item__icon accent-color-scheme-{{ section.id }}-{{ block.id }} color-{{ block.settings.accent_color }}">
                        <svg
                          class="comparison-table__checkmark"
                          id="Layer_3"
                          data-name="Layer 3"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 25.45 25.43"
                          fill="currentColor"
                        >
                          <polygon points="25.45 0 24.06 0 9.5 15.27 2.12 10.24 0 12.55 9.28 25.43 25.45 2.48 25.45 0"/>
                        </svg>
                      </div>
                      <p class="pricing-plan__benefit-item__text">
                        {{ benefit }}
                      </p>
                    </div>
                  {% endfor %}
                </div>
              {% else %}
                <div class="pricing-plan__desc {{ block.settings.desc_alignment }}">
                  {{ block.settings.benefits }}
                </div>
              {% endif %}
            {% endif %}
            {%- if block.settings.button_label != blank -%}
              <div class="plan-card__btn">
                {% if block.settings.button_function == 'link' %}
                  <a
                    {% if block.settings.link %}
                      href="{{ block.settings.link }}"
                    {% else %}
                      role="link" aria-disabled="true"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}{% if block.settings.button_full_width %} button--full-width{% endif %}"
                  >
                    {{- block.settings.button_label | escape -}}
                  </a>
                {% else %}
                  {% if block.settings.product != blank %}
                    <product-form>
                      {% assign product_form_id = 'section-product-form-'
                        | append: section.id
                        | append: block.id
                      %}
                      {% assign product = block.settings.product %}
                      {%- form 'product',
                        product,
                        id: product_form_id,
                        class: 'form',
                        novalidate: 'novalidate',
                        data-type: 'add-to-cart-form'
                      -%}
                        <input
                          type="hidden"
                          name="id"
                          value="{{ chosen_variant.id }}"
                          disabled
                          class="product-variant-id"
                        >
                        <button
                          type="submit"
                          name="add"
                          class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}{% if block.settings.button_full_width %} button--full-width{% endif %}"
                          {% if chosen_variant.available == false or chosen_variant.available == nil %}
                            disabled
                          {% endif %}
                        >
                          <span>
                            {{- block.settings.button_label | escape -}}
                          </span>
                          <div class="loading-overlay__spinner hidden">
                            <svg
                              aria-hidden="true"
                              focusable="false"
                              class="spinner"
                              viewBox="0 0 66 66"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                            </svg>
                          </div>
                        </button>
                      {%- endform -%}
                    </product-form>
                  {% else %}
                    <h4 class='h5 center'>Assign a product in block settings</h4>
                  {% endif %}
                {% endif %}
              </div>
            {%- endif -%}
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Pricing table",
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Pricing table",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "select",
      "id": "icon_size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium",
      "label": "Icon Size"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "range",
      "id": "desktop_spacing",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Desktop spacing",
      "default": 40
    },
    {
      "type": "select",
      "id": "cards_alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Top"
        },
        {
          "value": "center",
          "label": "Middle"
        },
        {
          "value": "flex-end",
          "label": "Bottom"
        }
      ],
      "default": "flex-start",
      "label": "Vertical alignment"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#121212",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "plan",
      "name": "Pricing plan",
      "settings": [
        {
          "type": "header",
          "content": "Product & variant"
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "number",
          "id": "variant_index",
          "label": "Displayed variant index",
          "default": 1,
          "info": "Use if you wish to use the section to display different variants of the same products as different cards."
        },
        {
          "type": "header",
          "content": "Card & content"
        },
        {
          "type": "select",
          "id": "card_style",
          "options": [
            {
              "value": "style-1",
              "label": "Style 1"
            },
            {
              "value": "style-2",
              "label": "Style 2"
            },
            {
              "value": "style-3",
              "label": "Style 3"
            }
          ],
          "default": "style-1",
          "label": "Card style"
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "background-1",
          "label": "Color scheme",
          "info": "Custom color scheme is edited at the bottom of block settings."
        },
        {
          "type": "select",
          "id": "accent_color",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Text"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "accent-1",
          "label": "Accent color",
          "info": "Custom accent color is edited at the bottom of block settings."
        },
        {
          "type": "text",
          "id": "badge",
          "label": "Badge text"
        },
        {
          "type": "select",
          "id": "badge_color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "Accent 1"
            },
            {
              "value": "accent-2",
              "label": "Accent 2"
            },
            {
              "value": "background-1",
              "label": "Background 1"
            },
            {
              "value": "background-2",
              "label": "Background 2"
            },
            {
              "value": "inverse",
              "label": "Inverse"
            }
          ],
          "default": "inverse",
          "label": "Badge color scheme"
        },
        {
          "type": "select",
          "id": "displayed_image",
          "options": [
            {
              "value": "hidden",
              "label": "Hidden"
            },
            {
              "value": "custom",
              "label": "Custom"
            },
            {
              "value": "variant_image",
              "label": "Variant image"
            }
          ],
          "default": "hidden",
          "label": "Displayed image"
        },
        {
          "type": "image_picker",
          "id": "custom_image",
          "label": "Custom image"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Plan"
        },
        {
          "type": "text",
          "id": "price",
          "label": "Price text",
          "default": "[price]",
          "info": "Use [price] to display the price of the product (variant)"
        },
        {
          "type": "text",
          "id": "price_text",
          "label": "Price caption",
          "default": "a month"
        },
        {
          "type": "text",
          "id": "icon",
          "default": "sell",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon"
        },
        {
          "type": "text",
          "id": "description",
          "label": "Description"
        },
        {
          "type": "select",
          "id": "desc_alignment",
          "label": "Description alignment",
          "options": [
            {
              "value": "",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "richtext",
          "id": "benefits",
          "label": "Benefits",
          "default": "<p>Plan benefit,Plan benefit,Plan benefit,Plan benefit</p>",
          "info": "Separate benefits with comma to display them as list"
        },
        {
          "type": "select",
          "id": "button_function",
          "options": [
            {
              "value": "link",
              "label": "Link"
            },
            {
              "value": "atc",
              "label": "Add to Cart"
            }
          ],
          "default": "link",
          "label": "Button function"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "t:sections.slideshow.blocks.slide.settings.button_label.label",
          "default": "Get started",
          "info": "t:sections.slideshow.blocks.slide.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.slideshow.blocks.slide.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "button_full_width",
          "label": "Button full width",
          "default": false
        },
        {
          "type": "header",
          "content": "Custom color scheme",
          "info": "Applied if Color scheme setting is set to Custom."
        },
        {
          "type": "color",
          "id": "custom_colors_background",
          "default": "#FFFFFF",
          "label": "Background color"
        },
        {
          "type": "color_background",
          "id": "custom_gradient_background",
          "label": "Gradient background",
          "info": "If added, replaces Background color when applicable."
        },
        {
          "type": "color",
          "id": "custom_colors_text",
          "default": "#2E2A39",
          "label": "Text"
        },
        {
          "type": "color",
          "id": "custom_colors_solid_button_background",
          "default": "#dd1d1d",
          "label": "Solid button background"
        },
        {
          "type": "color",
          "id": "custom_colors_solid_button_text",
          "default": "#ffffff",
          "label": "Solid button label"
        },
        {
          "type": "color",
          "id": "custom_colors_outline_button",
          "default": "#dd1d1d",
          "label": "Outline button"
        },
        {
          "type": "header",
          "content": "Custom accent color",
          "info": "Applied if Accent color setting is set to Custom."
        },
        {
          "type": "color",
          "id": "custom_accent_color",
          "default": "#dd1d1d",
          "label": "Accent color"
        },
        {
          "type": "color",
          "id": "custom_accent_text_color",
          "default": "#FFFFFF",
          "label": "Accent text color",
          "info": "Applied to text inside an element with the accent color"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Pricing table",
      "blocks": [
        {
          "type": "plan"
        },
        {
          "type": "plan"
        },
        {
          "type": "plan"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
