{% comment %}
  Bundle System Test Page
  Create a page with handle "bundle-test" to use this template
{% endcomment %}

<div class="page-width section-padding">
  <h1 class="title h1">Bundle System Test Page</h1>
  
  <div class="bundle-test-info">
    <h2>System Status</h2>
    <div id="system-status">
      <p>Loading system status...</p>
    </div>
  </div>

  <div class="bundle-test-products">
    <h2>Test Products</h2>
    <p>The following products should have "Purchase Type" options hidden:</p>
    
    <div class="test-products-grid">
      {% assign test_products = collections.all.products | where: 'options', 'Purchase Type' | limit: 6 %}
      {% for product in test_products %}
        <div class="test-product-card">
          <h3>{{ product.title }}</h3>
          <p>Handle: {{ product.handle }}</p>
          <p>Options: {{ product.options | join: ', ' }}</p>
          <p>Variants:</p>
          <ul>
            {% for variant in product.variants %}
              <li>
                {{ variant.title }} - {{ variant.price | money }}
                {% if variant.option1 == 'Bundle' or variant.option2 == 'Bundle' or variant.option3 == 'Bundle' %}
                  <strong>(Bundle Variant - Should be hidden)</strong>
                {% endif %}
              </li>
            {% endfor %}
          </ul>
          <a href="{{ product.url }}" class="button">View Product</a>
        </div>
      {% endfor %}
    </div>
  </div>

  <div class="bundle-test-cart">
    <h2>Cart Test</h2>
    <div id="cart-test-results">
      <button id="test-cart-btn" class="button">Test Cart Bundle Sync</button>
      <button id="test-variant-hiding-btn" class="button">Test Variant Hiding</button>
      <div id="cart-test-output"></div>
    </div>
  </div>

  <div class="bundle-test-performance">
    <h2>Performance & Error Monitoring</h2>
    <div id="performance-monitor">
      <p>Monitoring JavaScript errors and performance...</p>
      <div id="error-log"></div>
    </div>
  </div>
</div>

<style>
  .bundle-test-info,
  .bundle-test-products,
  .bundle-test-cart {
    margin: 3rem 0;
    padding: 2rem;
    border: 1px solid rgba(var(--color-foreground), 0.1);
    border-radius: 8px;
  }

  .test-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .test-product-card {
    padding: 1.5rem;
    border: 1px solid rgba(var(--color-foreground), 0.1);
    border-radius: 4px;
    background: rgba(var(--color-background), 0.5);
  }

  .test-product-card h3 {
    margin: 0 0 1rem 0;
    color: rgb(var(--color-foreground));
  }

  .test-product-card p {
    margin: 0.5rem 0;
    font-size: 1.4rem;
  }

  .test-product-card ul {
    margin: 0.5rem 0;
    padding-left: 2rem;
  }

  .test-product-card li {
    margin: 0.25rem 0;
    font-size: 1.3rem;
  }

  #system-status {
    font-family: monospace;
    background: rgba(var(--color-foreground), 0.05);
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
  }

  #cart-test-output {
    margin-top: 1rem;
    font-family: monospace;
    background: rgba(var(--color-foreground), 0.05);
    padding: 1rem;
    border-radius: 4px;
    min-height: 100px;
  }

  .status-ok {
    color: #28a745;
  }

  .status-error {
    color: #dc3545;
  }

  .status-warning {
    color: #ffc107;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Test system status
  function checkSystemStatus() {
    const statusDiv = document.getElementById('system-status');
    let status = [];

    // Check if bundle scripts are loaded
    if (window.HiddenVariantBundles) {
      status.push('<span class="status-ok">✓ Hidden Variant Bundles script loaded</span>');
    } else {
      status.push('<span class="status-error">✗ Hidden Variant Bundles script NOT loaded</span>');
    }

    if (window.bundleCartSync) {
      status.push('<span class="status-ok">✓ Bundle Cart Sync script loaded</span>');
    } else {
      status.push('<span class="status-error">✗ Bundle Cart Sync script NOT loaded</span>');
    }

    // Check if bundle section exists
    const bundleSection = document.querySelector('hidden-variant-bundles');
    if (bundleSection) {
      status.push('<span class="status-ok">✓ Bundle section found on page</span>');
    } else {
      status.push('<span class="status-warning">⚠ Bundle section not found (add it to test)</span>');
    }

    // Check for Purchase Type options in DOM (multiple selectors)
    const purchaseTypeSelectors = [
      '[data-option-name="Purchase Type"]',
      '[data-option-name="purchase type"]',
      'select option[value*="Purchase Type"]',
      'input[name*="Purchase Type"]',
      '.product-option:contains("Purchase Type")'
    ];

    let foundPurchaseTypeOptions = 0;
    purchaseTypeSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        foundPurchaseTypeOptions += elements.length;
      } catch (e) {
        // Ignore selector errors
      }
    });

    if (foundPurchaseTypeOptions === 0) {
      status.push('<span class="status-ok">✓ Purchase Type options are hidden</span>');
    } else {
      status.push('<span class="status-error">✗ Purchase Type options are visible (' + foundPurchaseTypeOptions + ' found)</span>');
    }

    // Check for bundle items in cart
    fetch('/cart.js')
      .then(response => response.json())
      .then(cart => {
        const bundleItems = cart.items.filter(item => item.properties && item.properties._bundle_id);
        if (bundleItems.length > 0) {
          status.push('<span class="status-ok">✓ Bundle items found in cart (' + bundleItems.length + ')</span>');
        } else {
          status.push('<span class="status-warning">⚠ No bundle items in cart</span>');
        }

        // Update status display
        statusDiv.innerHTML = status.join('<br>');
      })
      .catch(error => {
        status.push('<span class="status-error">✗ Error checking cart: ' + error.message + '</span>');
        statusDiv.innerHTML = status.join('<br>');
      });

    statusDiv.innerHTML = status.join('<br>');
  }

  // Test cart functionality
  function testCartSync() {
    const output = document.getElementById('cart-test-output');
    output.innerHTML = 'Testing cart synchronization...<br>';

    // Get current cart
    fetch('/cart.js')
      .then(response => response.json())
      .then(cart => {
        output.innerHTML += 'Current cart items: ' + cart.item_count + '<br>';
        
        // Check for bundle items
        const bundleItems = cart.items.filter(item => item.properties && item.properties._bundle_id);
        output.innerHTML += 'Bundle items in cart: ' + bundleItems.length + '<br>';
        
        if (bundleItems.length > 0) {
          output.innerHTML += 'Bundle IDs found:<br>';
          const bundleIds = [...new Set(bundleItems.map(item => item.properties._bundle_id))];
          bundleIds.forEach(id => {
            const itemsInBundle = bundleItems.filter(item => item.properties._bundle_id === id);
            output.innerHTML += '- ' + id + ' (' + itemsInBundle.length + ' items)<br>';
          });
        }

        output.innerHTML += '<br>Cart sync system status: ';
        if (window.bundleCartSync) {
          output.innerHTML += '<span class="status-ok">Active</span><br>';
        } else {
          output.innerHTML += '<span class="status-error">Not loaded</span><br>';
        }
      })
      .catch(error => {
        output.innerHTML += '<span class="status-error">Error fetching cart: ' + error.message + '</span><br>';
      });
  }

  // Initialize tests
  checkSystemStatus();

  // Set up test buttons
  document.getElementById('test-cart-btn').addEventListener('click', testCartSync);
  document.getElementById('test-variant-hiding-btn').addEventListener('click', testVariantHiding);

  // Set up error monitoring
  setupErrorMonitoring();

  // Auto-refresh status every 5 seconds
  setInterval(checkSystemStatus, 5000);

  // Test variant hiding functionality
  function testVariantHiding() {
    const output = document.getElementById('cart-test-output');
    output.innerHTML = 'Testing variant hiding...<br>';

    // Test if any Purchase Type options are visible
    const selectors = [
      'select[name="id"] option',
      '.product-form__input select option',
      '.variant-picker option',
      '.product-variant-picker option'
    ];

    let foundBundleVariants = 0;
    selectors.forEach(selector => {
      try {
        const options = document.querySelectorAll(selector);
        options.forEach(option => {
          const text = option.textContent || option.innerText || '';
          if (text.toLowerCase().includes('bundle')) {
            foundBundleVariants++;
            output.innerHTML += 'Found bundle variant: ' + text + '<br>';
          }
        });
      } catch (e) {
        // Ignore selector errors
      }
    });

    if (foundBundleVariants === 0) {
      output.innerHTML += '<span class="status-ok">✓ No bundle variants visible to customers</span><br>';
    } else {
      output.innerHTML += '<span class="status-error">✗ Found ' + foundBundleVariants + ' visible bundle variants</span><br>';
    }
  }

  // Set up error monitoring
  function setupErrorMonitoring() {
    const errorLog = document.getElementById('error-log');
    const errors = [];

    // Capture JavaScript errors
    window.addEventListener('error', (event) => {
      const error = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        timestamp: new Date().toISOString()
      };
      errors.push(error);
      updateErrorLog();
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = {
        message: 'Unhandled Promise Rejection: ' + event.reason,
        timestamp: new Date().toISOString()
      };
      errors.push(error);
      updateErrorLog();
    });

    function updateErrorLog() {
      if (errors.length === 0) {
        errorLog.innerHTML = '<span class="status-ok">No JavaScript errors detected</span>';
      } else {
        errorLog.innerHTML = '<span class="status-error">JavaScript Errors (' + errors.length + '):</span><br>';
        errors.slice(-5).forEach(error => {
          errorLog.innerHTML += '<small>' + error.timestamp + ': ' + error.message + '</small><br>';
        });
      }
    }

    updateErrorLog();
  }
});
</script>
