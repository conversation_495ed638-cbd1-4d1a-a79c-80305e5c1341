/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Renkler",
      "settings": {
        "colors_solid_button_labels": {
          "label": "Sabit düğme etiketi",
          "info": "Vurgu renklerinde ön plan rengi olarak kullanılır."
        },
        "colors_accent_1": {
          "label": "1. Vurgu",
          "info": "Sabit düğme arka planı için kullanılır."
        },
        "colors_accent_2": {
          "label": "2. Vurgu"
        },
        "header__1": {
          "content": "Birinc<PERSON> renkler"
        },
        "header__2": {
          "content": "İkincil renkler"
        },
        "colors_text": {
          "label": "Metin",
          "info": "Arka plan renklerinde ön plan rengi olarak kullanılır."
        },
        "colors_outline_button_labels": {
          "label": "Dış çizgi düğmesi",
          "info": "Metin bağlantıları için de kullanılır."
        },
        "colors_background_1": {
          "label": "1. Arka plan"
        },
        "colors_background_2": {
          "label": "2. Arka plan"
        },
        "gradient_accent_1": {
          "label": "1. Vurgu gradyanı"
        },
        "gradient_accent_2": {
          "label": "2. Vurgu gradyanı"
        },
        "gradient_background_1": {
          "label": "1. Arka plan gradyanı"
        },
        "gradient_background_2": {
          "label": "2. Arka plan gradyanı"
        }
      }
    },
    "typography": {
      "name": "Tipografi",
      "settings": {
        "type_header_font": {
          "label": "Yazı tipi",
          "info": "Farklı bir yazı tipi seçmeniz mağazanızın hızını etkileyebilir. [Sistem yazı tipleri hakkında daha fazla bilgi edinin.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "header__1": {
          "content": "Başlıklar"
        },
        "header__2": {
          "content": "Gövde"
        },
        "type_body_font": {
          "label": "Yazı tipi",
          "info": "Farklı bir yazı tipi seçmeniz mağazanızın hızını etkileyebilir. [Sistem yazı tipleri hakkında daha fazla bilgi edinin.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"
        },
        "heading_scale": {
          "label": "Yazı boyutu ölçeği"
        },
        "body_scale": {
          "label": "Yazı boyutu ölçeği"
        }
      }
    },
    "styles": {
      "name": "Simgeler",
      "settings": {
        "accent_icons": {
          "options__3": {
            "label": "Dış çizgi düğmesi"
          },
          "options__4": {
            "label": "Metin rengi"
          },
          "label": "Renk"
        }
      }
    },
    "social-media": {
      "name": "Sosyal medya",
      "settings": {
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https://twitter.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "https://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Sosyal medya hesapları"
        }
      }
    },
    "currency_format": {
      "name": "Para birimi biçimi",
      "settings": {
        "content": "Para birimi kodları",
        "currency_code_enabled": {
          "label": "Para birimi kodlarını göster"
        },
        "paragraph": "Sepet ve ödeme ücretleri her zaman para birimi kodlarını gösterir. Örnek: 1,00 USD."
      }
    },
    "layout": {
      "name": "Düzen",
      "settings": {
        "page_width": {
          "label": "Sayfa genişliği"
        },
        "spacing_sections": {
          "label": "Şablon bölümleri arasındaki alan"
        },
        "header__grid": {
          "content": "Izgara"
        },
        "paragraph__grid": {
          "content": "Birden fazla sütun veya satır içeren alanları etkiler."
        },
        "spacing_grid_horizontal": {
          "label": "Yatay boşluk"
        },
        "spacing_grid_vertical": {
          "label": "Dikey boşluk"
        }
      }
    },
    "search_input": {
      "name": "Arama davranışı",
      "settings": {
        "header": {
          "content": "Arama önerileri"
        },
        "predictive_search_enabled": {
          "label": "Arama önerilerini etkinleştir"
        },
        "predictive_search_show_vendor": {
          "label": "Ürün satıcısını göster",
          "info": "Arama önerileri etkinleştirildiğinde görünür."
        },
        "predictive_search_show_price": {
          "label": "Ürün fiyatını göster",
          "info": "Arama önerileri etkinleştirildiğinde görünür."
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Kenarlık"
        },
        "header__shadow": {
          "content": "Gölge"
        },
        "blur": {
          "label": "Bulanıklık"
        },
        "corner_radius": {
          "label": "Köşe yarıçapı"
        },
        "horizontal_offset": {
          "label": "Yatay dengeleme"
        },
        "vertical_offset": {
          "label": "Dikey dengeleme"
        },
        "thickness": {
          "label": "Kalınlık"
        },
        "opacity": {
          "label": "Opaklık"
        },
        "image_padding": {
          "label": "Görsel dolgusu"
        },
        "text_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Metin hizalaması"
        }
      }
    },
    "badges": {
      "name": "Rozetler",
      "settings": {
        "position": {
          "options__1": {
            "label": "Alt sol"
          },
          "options__2": {
            "label": "Alt sağ"
          },
          "options__3": {
            "label": "Üst sol"
          },
          "options__4": {
            "label": "Üst sağ"
          },
          "label": "Kartlardaki konum"
        },
        "sale_badge_color_scheme": {
          "label": "İndirim rozeti renk şeması"
        },
        "sold_out_badge_color_scheme": {
          "label": "Tükendi rozeti renk şeması"
        }
      }
    },
    "buttons": {
      "name": "Düğmeler"
    },
    "variant_pills": {
      "name": "Varyasyon seçenekleri",
      "paragraph": "Varyasyon seçenekleri, ürün varyasyonlarınızı göstermenin bir yoludur. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"
    },
    "inputs": {
      "name": "Girdiler"
    },
    "content_containers": {
      "name": "İçerik kapsayıcıları"
    },
    "popups": {
      "name": "Açılır menüler ve pencereler",
      "paragraph": "Gezinme açılır menüleri, açılır pencere modları ve sepet açılır pencereleri gibi alanları etkiler."
    },
    "media": {
      "name": "Medya"
    },
    "drawers": {
      "name": "Çekmeceler"
    },
    "cart": {
      "name": "Sepet",
      "settings": {
        "cart_type": {
          "label": "Sepet türü",
          "drawer": {
            "label": "Çekmece"
          },
          "page": {
            "label": "Sayfa"
          },
          "notification": {
            "label": "Açılır pencere bildirimi"
          }
        },
        "show_vendor": {
          "label": "Satıcıyı göster"
        },
        "show_cart_note": {
          "label": "Sepet notunu etkinleştir"
        },
        "cart_drawer": {
          "header": "Sepet çekmecesi",
          "collection": {
            "label": "Koleksiyon",
            "info": "Sepet çekmecesi boşken görünür."
          }
        }
      }
    },
    "cards": {
      "name": "Ürün kartları",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standart"
          },
          "options__2": {
            "label": "Kart"
          },
          "label": "Stil"
        }
      }
    },
    "collection_cards": {
      "name": "Koleksiyon kartları",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standart"
          },
          "options__2": {
            "label": "Kart"
          },
          "label": "Stil"
        }
      }
    },
    "blog_cards": {
      "name": "Blog kartları",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standart"
          },
          "options__2": {
            "label": "Kart"
          },
          "label": "Stil"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Masaüstü logo genişliği",
          "info": "Logo genişliği, mobil için otomatik olarak optimize edilir."
        },
        "favicon": {
          "label": "Favicon görseli",
          "info": "Ölçeği 32 x 32 piksele düşürülür"
        }
      }
    },
    "brand_information": {
      "name": "Marka bilgileri",
      "settings": {
        "brand_headline": {
          "label": "Başlık"
        },
        "brand_description": {
          "label": "Açıklama"
        },
        "brand_image": {
          "label": "Görsel"
        },
        "brand_image_width": {
          "label": "Görsel genişliği"
        },
        "paragraph": {
          "content": "Mağazanızın altbilgisine bir marka açıklaması ekleyin."
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Bölüm dolgusu",
        "padding_top": "Üst dolgu",
        "padding_bottom": "Alt dolgu"
      },
      "spacing": "Boşluk",
      "colors": {
        "accent_1": {
          "label": "1. Vurgu"
        },
        "accent_2": {
          "label": "2. Vurgu"
        },
        "background_1": {
          "label": "1. Arka plan"
        },
        "background_2": {
          "label": "2. Arka plan"
        },
        "inverse": {
          "label": "Ters"
        },
        "label": "Renk şeması",
        "has_cards_info": "Kart renk şemasını değiştirmek için tema ayarlarınızı güncelleyin."
      },
      "heading_size": {
        "label": "Başlık boyutu",
        "options__1": {
          "label": "Küçük"
        },
        "options__2": {
          "label": "Orta"
        },
        "options__3": {
          "label": "Büyük"
        },
        "options__4": {
          "label": "Çok büyük"
        }
      }
    },
    "announcement-bar": {
      "name": "Duyuru çubuğu",
      "blocks": {
        "announcement": {
          "name": "Duyuru",
          "settings": {
            "text": {
              "label": "Metin"
            },
            "text_alignment": {
              "label": "Metin hizalaması",
              "options__1": {
                "label": "Sol"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Sağ"
              }
            },
            "link": {
              "label": "Bağlantı"
            }
          }
        }
      }
    },
    "collage": {
      "name": "Kolaj",
      "settings": {
        "heading": {
          "label": "Başlık"
        },
        "desktop_layout": {
          "label": "Masaüstü düzeni",
          "options__1": {
            "label": "Sol geniş blok"
          },
          "options__2": {
            "label": "Sağ geniş blok"
          }
        },
        "mobile_layout": {
          "label": "Mobil düzen",
          "options__1": {
            "label": "Kolaj"
          },
          "options__2": {
            "label": "Sütun"
          }
        },
        "card_styles": {
          "label": "Kart stili",
          "info": "Ürün, koleksiyon ve blog kartı stilleri tema ayarlarınızdan güncellenebilir.",
          "options__1": {
            "label": "Bireysel kart stilleri kullanın"
          },
          "options__2": {
            "label": "Hepsinin stilini ürün kartı şeklinde ayarla"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Görsel"
            }
          }
        },
        "product": {
          "name": "Ürün",
          "settings": {
            "product": {
              "label": "Ürün"
            },
            "secondary_background": {
              "label": "İkincil arka planı göster"
            },
            "second_image": {
              "label": "Üstüne gelindiğinde ikinci görseli göster"
            }
          }
        },
        "collection": {
          "name": "Koleksiyon",
          "settings": {
            "collection": {
              "label": "Koleksiyon"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Kapak görseli"
            },
            "video_url": {
              "label": "URL",
              "info": "Bölümde başka bloklar varsa video açılır pencerede oynatılır.",
              "placeholder": "YouTube veya Vimeo URL'si kullanın"
            },
            "description": {
              "label": "Video alternatif metni",
              "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"
            }
          }
        }
      },
      "presets": {
        "name": "Kolaj"
      }
    },
    "collection-list": {
      "name": "Koleksiyon listesi",
      "settings": {
        "title": {
          "label": "Başlık"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          },
          "info": "Koleksiyonlarınızı düzenleyerek görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/products/collections)"
        },
        "swipe_on_mobile": {
          "label": "Mobil cihazda kaydırmayı etkinleştir"
        },
        "show_view_all": {
          "label": "Liste gösterilenden daha fazla koleksiyon içeriyorsa \"Tümünü görüntüle\" düğmesini etkinleştir"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        },
        "columns_mobile": {
          "label": "Mobildeki sütun sayısı",
          "options__1": {
            "label": "1 sütun"
          },
          "options__2": {
            "label": "2 sütun"
          }
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Koleksiyon",
          "settings": {
            "collection": {
              "label": "Koleksiyon"
            }
          }
        }
      },
      "presets": {
        "name": "Koleksiyon listesi"
      }
    },
    "contact-form": {
      "name": "İletişim Formu",
      "presets": {
        "name": "İletişim formu"
      }
    },
    "custom-liquid": {
      "name": "Özel Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Özel Liquid",
          "info": "Gelişmiş özelleştirmeler oluşturmak için uygulama parçacıkları veya başka Liquid kodu ekleyin."
        }
      },
      "presets": {
        "name": "Özel Liquid"
      }
    },
    "featured-blog": {
      "name": "Blog gönderileri",
      "settings": {
        "heading": {
          "label": "Başlık"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Gösterilecek blog gönderisi sayısı"
        },
        "show_view_all": {
          "label": "Blog gösterilenden daha fazla blog gönderisi içeriyorsa \"Tümünü görüntüle\" düğmesini etkinleştir"
        },
        "show_image": {
          "label": "Öne çıkan görseli göster",
          "info": "En iyi sonuçlar için 3:2 en-boy oranına sahip bir görsel kullanın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "show_date": {
          "label": "Tarihi göster"
        },
        "show_author": {
          "label": "Yazarı göster"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        }
      },
      "presets": {
        "name": "Blog gönderileri"
      }
    },
    "featured-collection": {
      "name": "Öne çıkan koleksiyon",
      "settings": {
        "title": {
          "label": "Başlık"
        },
        "collection": {
          "label": "Koleksiyon"
        },
        "products_to_show": {
          "label": "Gösterilecek maksimum ürün sayısı"
        },
        "show_view_all": {
          "label": "Koleksiyon gösterilenden daha fazla ürün içeriyorsa \"Tümünü görüntüle\"yi etkinleştirin"
        },
        "header": {
          "content": "Ürün kartı"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcıyı göster"
        },
        "show_rating": {
          "label": "Ürün puanlarını göster",
          "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        },
        "description": {
          "label": "Açıklama"
        },
        "show_description": {
          "label": "Yöneticiden koleksiyon açıklamasını göster"
        },
        "description_style": {
          "label": "Açıklama stili",
          "options__1": {
            "label": "Gövde"
          },
          "options__2": {
            "label": "Alt yazı"
          },
          "options__3": {
            "label": "Büyük harf"
          }
        },
        "view_all_style": {
          "label": "\"Tümünü görüntüle\" stili",
          "options__1": {
            "label": "Bağlantı"
          },
          "options__2": {
            "label": "Dış çizgi düğmesi"
          },
          "options__3": {
            "label": "Sabit düğme"
          }
        },
        "enable_desktop_slider": {
          "label": "Carousel'i masa üstünde etkinleştir"
        },
        "full_width": {
          "label": "Ürünleri tam genişlikli yap"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        },
        "columns_mobile": {
          "label": "Mobildeki sütun sayısı",
          "options__1": {
            "label": "1 sütun"
          },
          "options__2": {
            "label": "2 sütun"
          }
        },
        "swipe_on_mobile": {
          "label": "Mobil cihazda kaydırmayı etkinleştir"
        },
        "enable_quick_buy": {
          "label": "Hızlı ekleme düğmesini etkinleştir",
          "info": "Açılır pencere ve çekmece sepet türü için optimumdur."
        }
      },
      "presets": {
        "name": "Öne çıkan koleksiyon"
      }
    },
    "footer": {
      "name": "Altbilgi",
      "blocks": {
        "link_list": {
          "name": "Menü",
          "settings": {
            "heading": {
              "label": "Başlık"
            },
            "menu": {
              "label": "Menü",
              "info": "Yalnızca üst taraftaki menü öğeleri gösterilir."
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "heading": {
              "label": "Başlık"
            },
            "subtext": {
              "label": "Alt metin"
            }
          }
        },
        "brand_information": {
          "name": "Marka bilgileri",
          "settings": {
            "paragraph": {
              "content": "Marka bilgileriniz bu blokta görünür. [Marka bilgilerini düzenleyin.](/editor?context=theme&category=brand%20information)"
            },
            "header__1": {
              "content": "Sosyal medya simgeleri"
            },
            "show_social": {
              "label": "Sosyal medya simgelerini göster",
              "info": "Sosyal medya hesaplarınızın görüntülenmesi için bu hesapları [tema ayarlarınızda](/editor?context=theme&category=social%20media) bağlayın."
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "E-posta kaydını göster"
        },
        "newsletter_heading": {
          "label": "Başlık"
        },
        "header__1": {
          "content": "E-posta Kaydı",
          "info": "\"Kabul edilen pazarlama\" müşteri listenize otomatik olarak eklenen aboneler. [Daha fazla bilgi edinin](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "header__2": {
          "content": "Sosyal medya simgeleri",
          "info": "Sosyal medya hesaplarınızın görüntülenmesi için bu hesapları [tema ayarlarınızda](/editor?context=theme&category=social%20media) bağlayın."
        },
        "show_social": {
          "label": "Sosyal medya simgelerini göster"
        },
        "header__3": {
          "content": "Ülke/bölge seçici"
        },
        "header__4": {
          "info": "Ülke/bölge eklemek için [pazar ayarlarınıza ](/admin/settings/markets) gidin."
        },
        "enable_country_selector": {
          "label": "Ülke/bölge seçiciyi etkinleştir"
        },
        "header__5": {
          "content": "Dil seçici"
        },
        "header__6": {
          "info": "Dil eklemek için [dil ayarlarınıza](/admin/settings/languages) gidin."
        },
        "enable_language_selector": {
          "label": "Dil seçiciyi etkinleştir"
        },
        "header__7": {
          "content": "Ödeme yöntemleri"
        },
        "payment_enable": {
          "label": "Ödeme simgelerini göster"
        },
        "margin_top": {
          "label": "Üst kenar boşluğu"
        },
        "header__8": {
          "content": "Politika bağlantıları",
          "info": "Mağaza politikası eklemek için [politika ayarlarınıza](/admin/settings/legal) gidin."
        },
        "show_policy": {
          "label": "Politika bağlantılarını göster"
        },
        "header__9": {
          "content": "Follow on Shop",
          "info": "Shop uygulamasında, vitrininiz için takip düğmesini gösterin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        },
        "enable_follow_on_shop": {
          "label": "Follow on Shop'u etkinleştir"
        }
      }
    },
    "header": {
      "name": "Üstbilgi",
      "settings": {
        "logo_position": {
          "label": "Masaüstü logo konumu",
          "options__1": {
            "label": "Orta sol"
          },
          "options__2": {
            "label": "Üst sol"
          },
          "options__3": {
            "label": "Üst orta"
          },
          "options__4": {
            "label": "Orta kısmın ortası"
          }
        },
        "menu": {
          "label": "Menü"
        },
        "show_line_separator": {
          "label": "Ayırıcı satırı göster"
        },
        "margin_bottom": {
          "label": "Alt kenar boşluğu"
        },
        "menu_type_desktop": {
          "label": "Masaüstü menü türü",
          "info": "Menü türü, mobil cihazlar için otomatik olarak optimize edilir.",
          "options__1": {
            "label": "Açılır menü"
          },
          "options__2": {
            "label": "Mega menü"
          }
        },
        "mobile_layout": {
          "content": "Mobil düzen"
        },
        "mobile_logo_position": {
          "label": "Mobil logo konumu",
          "options__1": {
            "label": "Orta"
          },
          "options__2": {
            "label": "Sol"
          }
        },
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Logonuzu şurada düzenleyin: [tema ayarları](/editor?context=theme&category=logo)."
        },
        "sticky_header_type": {
          "label": "Sabit üstbilgi",
          "options__1": {
            "label": "Yok"
          },
          "options__2": {
            "label": "Yukarı kaydırıldığında"
          },
          "options__3": {
            "label": "Her zaman"
          },
          "options__4": {
            "label": "Her zaman, logo boyutunu küçült"
          }
        }
      }
    },
    "image-banner": {
      "name": "Görsel banner'ı",
      "settings": {
        "image": {
          "label": "İlk görsel"
        },
        "image_2": {
          "label": "İkinci görsel"
        },
        "color_scheme": {
          "info": "Kapsayıcı gösterildiğinde görünür."
        },
        "stack_images_on_mobile": {
          "label": "Mobilde görselleri üst üste ekle"
        },
        "adapt_height_first_image": {
          "label": "Bölüm yüksekliğini ilk görselin boyutuna uyarla",
          "info": "İşaretlendiğinde görüntü banner'ı yükseklik ayarının üzerine yazar."
        },
        "show_text_box": {
          "label": "Masaüstünde kapsayıcıyı göster"
        },
        "image_overlay_opacity": {
          "label": "Görsel yer paylaşımı opaklığı"
        },
        "header": {
          "content": "Mobil Düzen"
        },
        "show_text_below": {
          "label": "Mobil cihaz üzerinde kapsayıcıyı göster"
        },
        "image_height": {
          "label": "Banner yüksekliği",
          "options__1": {
            "label": "İlk görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "info": "En iyi sonuçlar için 3:2 en-boy oranına sahip bir görsel kullanın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
          "options__4": {
            "label": "Büyük"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst Sol"
          },
          "options__2": {
            "label": "Üst Orta"
          },
          "options__3": {
            "label": "Üst Sağ"
          },
          "options__4": {
            "label": "Orta Sol"
          },
          "options__5": {
            "label": "Orta Kısmın Ortası"
          },
          "options__6": {
            "label": "Orta Sağ"
          },
          "options__7": {
            "label": "Alt Sol"
          },
          "options__8": {
            "label": "Alt Orta"
          },
          "options__9": {
            "label": "Alt Sağ"
          },
          "label": "Masaüstü içerik konumu"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Masaüstü içerik hizalaması"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Mobil içerik hizalaması"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık"
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "Açıklama"
            },
            "text_style": {
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              },
              "label": "Metin stili"
            }
          }
        },
        "buttons": {
          "name": "Düğmeler",
          "settings": {
            "button_label_1": {
              "label": "İlk düğme etiketi",
              "info": "Düğmeyi gizlemek için etiketi boş bırakın."
            },
            "button_link_1": {
              "label": "İlk düğme bağlantısı"
            },
            "button_style_secondary_1": {
              "label": "Dış çizgi düğme stilini kullan"
            },
            "button_label_2": {
              "label": "İkinci düğme etiketi",
              "info": "Düğmeyi gizlemek için etiketi boş bırakın."
            },
            "button_link_2": {
              "label": "İkinci düğme bağlantısı"
            },
            "button_style_secondary_2": {
              "label": "Dış çizgi düğme stilini kullan"
            }
          }
        }
      },
      "presets": {
        "name": "Görsel banner'ı"
      }
    },
    "image-with-text": {
      "name": "Metin içeren görsel",
      "settings": {
        "image": {
          "label": "Görsel"
        },
        "height": {
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "label": "Görsel yüksekliği",
          "options__4": {
            "label": "Büyük"
          }
        },
        "layout": {
          "options__1": {
            "label": "Önce görsel"
          },
          "options__2": {
            "label": "Görsel 2"
          },
          "label": "Masaüstü görsel yerleşimi",
          "info": "Önce görsel, varsayılan mobil düzendir."
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Küçük"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Büyük"
          },
          "label": "Masaüstü görsel genişliği",
          "info": "Görsel, mobil cihazlar için otomatik olarak optimize edilir."
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Masaüstü içerik hizalaması"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Alt"
          },
          "label": "Masaüstü içerik konumu"
        },
        "content_layout": {
          "options__1": {
            "label": "Çakışma yok"
          },
          "options__2": {
            "label": "Çakışma"
          },
          "label": "İçerik düzeni"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Mobil içerik hizalaması"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık"
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "İçerik"
            },
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              }
            }
          }
        },
        "button": {
          "name": "Düğme",
          "settings": {
            "button_label": {
              "label": "Düğme etiketi",
              "info": "Düğmeyi gizlemek için etiketi boş bırakın."
            },
            "button_link": {
              "label": "Düğme bağlantısı"
            }
          }
        },
        "caption": {
          "name": "Alt yazı",
          "settings": {
            "text": {
              "label": "Metin"
            },
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Alt yazı"
              },
              "options__2": {
                "label": "Büyük harf"
              }
            },
            "caption_size": {
              "label": "Metin boyutu",
              "options__1": {
                "label": "Küçük"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Büyük"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Metin içeren görsel"
      }
    },
    "main-article": {
      "name": "Blog gönderisi",
      "blocks": {
        "featured_image": {
          "name": "Öne çıkan görsel",
          "settings": {
            "image_height": {
              "label": "Öne çıkan görsel yüksekliği",
              "options__1": {
                "label": "Görsele uyarla"
              },
              "options__2": {
                "label": "Küçük"
              },
              "options__3": {
                "label": "Orta"
              },
              "info": "En iyi sonuçlar için 16:9 en-boy oranına sahip bir görsel kullanın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)",
              "options__4": {
                "label": "Büyük"
              }
            }
          }
        },
        "title": {
          "name": "Başlık",
          "settings": {
            "blog_show_date": {
              "label": "Tarihi göster"
            },
            "blog_show_author": {
              "label": "Yazarı göster"
            }
          }
        },
        "content": {
          "name": "İçerik"
        },
        "share": {
          "name": "Paylaş",
          "settings": {
            "featured_image_info": {
              "content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, önizleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Mağaza başlığı ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Metin"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blog gönderileri",
      "settings": {
        "header": {
          "content": "Blog gönderisi kartı"
        },
        "show_image": {
          "label": "Öne çıkan görseli göster"
        },
        "paragraph": {
          "content": "Blog gönderilerinizi düzenleyerek alıntılarınızı değiştirin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"
        },
        "show_date": {
          "label": "Tarihi göster"
        },
        "show_author": {
          "label": "Yazarı göster"
        },
        "layout": {
          "label": "Masaüstü düzeni",
          "options__1": {
            "label": "Izgara"
          },
          "options__2": {
            "label": "Kolaj"
          },
          "info": "Gönderiler, mobil cihazda üst üste eklenir."
        },
        "image_height": {
          "label": "Öne çıkan görsel yüksekliği",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          },
          "info": "En iyi sonuçlar için 3:2 en-boy oranına sahip bir görsel kullanın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-cart-footer": {
      "name": "Alt toplam",
      "blocks": {
        "subtotal": {
          "name": "Alt toplam fiyatı"
        },
        "buttons": {
          "name": "Ödeme düğmesi"
        }
      }
    },
    "main-cart-items": {
      "name": "Ürünler"
    },
    "main-collection-banner": {
      "name": "Koleksiyon banner'ı",
      "settings": {
        "paragraph": {
          "content": "Koleksiyonunuzu düzenleyerek açıklama veya görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Koleksiyon açıklamasını görüntüle"
        },
        "show_collection_image": {
          "label": "Koleksiyon görselini görüntüle",
          "info": "En iyi sonuçlar için 16:9 en-boy oranına sahip bir görsel kullanın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Ürün ızgarası",
      "settings": {
        "products_per_page": {
          "label": "Sayfa başına ürün"
        },
        "enable_filtering": {
          "label": "Filtrelemeyi etkinleştir",
          "info": "Search & Discovery uygulamasıyla filtreleri özelleştirin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Sıralamayı etkinleştir"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcıyı göster"
        },
        "header__1": {
          "content": "Filtreleme ve sıralama"
        },
        "header__3": {
          "content": "Ürün kartı"
        },
        "enable_tags": {
          "label": "Filtrelemeyi etkinleştir",
          "info": "Search & Discovery uygulamasıyla filtreleri özelleştirin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "show_rating": {
          "label": "Ürün puanlarını göster",
          "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        },
        "columns_mobile": {
          "label": "Mobildeki sütun sayısı",
          "options__1": {
            "label": "1 sütun"
          },
          "options__2": {
            "label": "2 sütun"
          }
        },
        "enable_quick_buy": {
          "label": "Hızlı ekleme düğmesini etkinleştir",
          "info": "Açılır pencere ve çekmece sepet türü için optimumdur."
        },
        "filter_type": {
          "label": "Masaüstü filtre düzeni",
          "options__1": {
            "label": "Yatay"
          },
          "options__2": {
            "label": "Dikey"
          },
          "options__3": {
            "label": "Çekmece"
          },
          "info": "Çekmece, varsayılan mobil düzendir."
        }
      }
    },
    "main-list-collections": {
      "name": "Koleksiyonlar listesi sayfası",
      "settings": {
        "title": {
          "label": "Başlık"
        },
        "sort": {
          "label": "Koleksiyonları sıralama ölçütü:",
          "options__1": {
            "label": "Alfabetik olarak, A-Z"
          },
          "options__2": {
            "label": "Alfabetik olarak, Z-A"
          },
          "options__3": {
            "label": "Tarih, yeniden eskiye"
          },
          "options__4": {
            "label": "Tarih, eskiden yeniye"
          },
          "options__5": {
            "label": "Ürün sayısı, yüksekten düşüğe"
          },
          "options__6": {
            "label": "Ürün sayısı, düşükten yükseğe"
          }
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          },
          "info": "Koleksiyonlarınızı düzenleyerek görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/products/collections)"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        },
        "columns_mobile": {
          "label": "Mobildeki sütun sayısı",
          "options__1": {
            "label": "1 sütun"
          },
          "options__2": {
            "label": "2 sütun"
          }
        }
      }
    },
    "main-page": {
      "name": "Sayfa"
    },
    "main-password-footer": {
      "name": "Parola altbilgisi"
    },
    "main-password-header": {
      "name": "Parola üstbilgisi",
      "settings": {
        "logo_header": {
          "content": "Logo"
        },
        "logo_help": {
          "content": "Logonuzu tema ayarlarında düzenleyin."
        }
      }
    },
    "main-product": {
      "blocks": {
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "Metin"
            },
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        },
        "title": {
          "name": "Başlık"
        },
        "price": {
          "name": "Fiyat"
        },
        "quantity_selector": {
          "name": "Adet seçici"
        },
        "variant_picker": {
          "name": "Varyasyon seçici",
          "settings": {
            "picker_type": {
              "label": "Tür",
              "options__1": {
                "label": "Açılır liste"
              },
              "options__2": {
                "label": "Seçenekler"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Satın al düğmeleri",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dinamik ödeme düğmelerini göster",
              "info": "Müşteriler, mağazanızda bulunan ödeme yöntemlerini kullanarak PayPal veya Apple Pay gibi tercih ettikleri seçeneği görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "pickup_availability": {
          "name": "Teslim alım stok durumu"
        },
        "description": {
          "name": "Açıklama"
        },
        "share": {
          "name": "Paylaş",
          "settings": {
            "featured_image_info": {
              "content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, önizleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."
            },
            "title_info": {
              "content": "Mağaza başlığı ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."
            },
            "text": {
              "label": "Metin"
            }
          }
        },
        "collapsible_tab": {
          "name": "Daraltılabilir satır",
          "settings": {
            "heading": {
              "info": "İçeriği açıklayan bir başlık ekleyin.",
              "label": "Başlık"
            },
            "content": {
              "label": "Satır içeriği"
            },
            "page": {
              "label": "Sayfadan alınan satır içeriği"
            },
            "icon": {
              "options__1": {
                "label": "Hiçbiri"
              },
              "options__2": {
                "label": "Elma"
              },
              "options__3": {
                "label": "Muz"
              },
              "options__4": {
                "label": "Şişe"
              },
              "options__5": {
                "label": "Kutu"
              },
              "options__6": {
                "label": "Havuç"
              },
              "options__7": {
                "label": "Sohbet balonu"
              },
              "options__8": {
                "label": "Onay işareti"
              },
              "options__9": {
                "label": "Pano"
              },
              "options__10": {
                "label": "Süt ürünü"
              },
              "options__11": {
                "label": "Süt ürünü içermez"
              },
              "options__12": {
                "label": "Kurutucu"
              },
              "options__13": {
                "label": "Göz"
              },
              "options__14": {
                "label": "Ateş"
              },
              "options__15": {
                "label": "Glütensiz"
              },
              "options__16": {
                "label": "Kalp"
              },
              "options__17": {
                "label": "Ütü"
              },
              "options__18": {
                "label": "Yaprak"
              },
              "options__19": {
                "label": "Deri"
              },
              "options__20": {
                "label": "Şimşek"
              },
              "options__21": {
                "label": "Ruj"
              },
              "options__22": {
                "label": "Kilit"
              },
              "options__23": {
                "label": "Harita pini"
              },
              "options__24": {
                "label": "Kabuklu yemişsiz"
              },
              "label": "Simge",
              "options__25": {
                "label": "Pantolon"
              },
              "options__26": {
                "label": "Pati izi"
              },
              "options__27": {
                "label": "Biber"
              },
              "options__28": {
                "label": "Parfüm"
              },
              "options__29": {
                "label": "Uçak"
              },
              "options__30": {
                "label": "Bitki"
              },
              "options__31": {
                "label": "Fiyat etiketi"
              },
              "options__32": {
                "label": "Soru işareti"
              },
              "options__33": {
                "label": "Geri dönüşüm"
              },
              "options__34": {
                "label": "İade"
              },
              "options__35": {
                "label": "Cetvel"
              },
              "options__36": {
                "label": "Servis tabağı"
              },
              "options__37": {
                "label": "Gömlek"
              },
              "options__38": {
                "label": "Ayakkabı"
              },
              "options__39": {
                "label": "Silüet"
              },
              "options__40": {
                "label": "Kar tanesi"
              },
              "options__41": {
                "label": "Yıldız"
              },
              "options__42": {
                "label": "Kronometre"
              },
              "options__43": {
                "label": "Kamyon"
              },
              "options__44": {
                "label": "Yıkama"
              }
            }
          }
        },
        "popup": {
          "name": "Açılır pencere",
          "settings": {
            "link_label": {
              "label": "Bağlantı etiketi"
            },
            "page": {
              "label": "Sayfa"
            }
          }
        },
        "custom_liquid": {
          "name": "Özel liquid",
          "settings": {
            "custom_liquid": {
              "label": "Özel liquid",
              "info": "Gelişmiş özelleştirmeler oluşturmak için uygulama parçacıkları veya başka Liquid kodu ekleyin."
            }
          }
        },
        "rating": {
          "name": "Ürün puanı",
          "settings": {
            "paragraph": {
              "content": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Tamamlayıcı ürünler",
          "settings": {
            "paragraph": {
              "content": "Tamamlayıcı ürünleri seçmek için Search & Discovery uygulamasını ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Başlık"
            },
            "make_collapsible_row": {
              "label": "Daraltılabilir satır olarak göster"
            },
            "icon": {
              "info": "Daraltılabilir satır gösterildiğinde görünür."
            },
            "product_list_limit": {
              "label": "Gösterilecek maksimum ürün sayısı"
            },
            "products_per_page": {
              "label": "Sayfa başına ürün sayısı"
            },
            "pagination_style": {
              "label": "Sayfalara ayırma stili",
              "options": {
                "option_1": "Noktalar",
                "option_2": "Sayaç",
                "option_3": "Numaralar"
              }
            },
            "product_card": {
              "heading": "Ürün kartı"
            },
            "image_ratio": {
              "label": "Görsel oranı",
              "options": {
                "option_1": "Portre",
                "option_2": "Kare"
              }
            },
            "enable_quick_add": {
              "label": "Hızlı ekleme düğmesini etkinleştir"
            }
          }
        },
        "icon_with_text": {
          "name": "Metin içeren simge",
          "settings": {
            "layout": {
              "label": "Düzen",
              "options__1": {
                "label": "Yatay"
              },
              "options__2": {
                "label": "Dikey"
              }
            },
            "content": {
              "label": "İçerik",
              "info": "Her sütun veya satır için bir simge seçin ya da görsel ekleyin."
            },
            "heading": {
              "info": "Simge sütununu gizlemek için başlık etiketini boş bırakın."
            },
            "icon_1": {
              "label": "İlk simge"
            },
            "image_1": {
              "label": "İlk görsel"
            },
            "heading_1": {
              "label": "İlk başlık"
            },
            "icon_2": {
              "label": "İkinci simge"
            },
            "image_2": {
              "label": "İkinci görsel"
            },
            "heading_2": {
              "label": "İkinci başlık"
            },
            "icon_3": {
              "label": "Üçüncü simge"
            },
            "image_3": {
              "label": "Üçüncü görsel"
            },
            "heading_3": {
              "label": "Üçüncü başlık"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        },
        "inventory": {
          "name": "Envanter durumu",
          "settings": {
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            },
            "inventory_threshold": {
              "label": "Düşük envanter eşiği",
              "info": "Mevcutsa her zaman stokta göstermek için 0 değerini seçin"
            },
            "show_inventory_quantity": {
              "label": "Envanter sayımını göster"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Medya",
          "info": "Şunun hakkında daha fazla bilgi edinin: [medya türleri.](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Video döngüsünü etkinleştir"
        },
        "enable_sticky_info": {
          "label": "Masaüstünde sabit içeriği etkinleştir"
        },
        "hide_variants": {
          "label": "Bir varyasyon seçtikten sonra diğer varyasyonların medyasını gizleyin"
        },
        "gallery_layout": {
          "label": "Masaüstü düzeni",
          "options__1": {
            "label": "Üst üste"
          },
          "options__2": {
            "label": "2 sütun"
          },
          "options__3": {
            "label": "Küçük resimler"
          },
          "options__4": {
            "label": "Küçük resim döngüsü"
          }
        },
        "media_size": {
          "label": "Masaüstü medya genişliği",
          "info": "Medya, mobil cihazlar için otomatik olarak optimize edilir.",
          "options__1": {
            "label": "Küçük"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Büyük"
          }
        },
        "mobile_thumbnails": {
          "label": "Mobil düzen",
          "options__1": {
            "label": "2 sütun"
          },
          "options__2": {
            "label": "Küçük resimleri göster"
          },
          "options__3": {
            "label": "Küçük resimleri gizle"
          }
        },
        "media_position": {
          "label": "Masaüstü medya konumu",
          "info": "Konum, mobil cihazlar için otomatik olarak optimize edilir.",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Sağ"
          }
        },
        "image_zoom": {
          "label": "Görsel yakınlaştırma",
          "info": "Ligtbox'ı mobilde açmak için tıklayın ve imleci varsayılan seçeneklerin üzerine getirin.",
          "options__1": {
            "label": "Lightbox'ı aç"
          },
          "options__2": {
            "label": "Tıkla ve imleci üzerine getir"
          },
          "options__3": {
            "label": "Yakınlaştırma yok"
          }
        },
        "constrain_to_viewport": {
          "label": "Medyayı ekran yüksekliğiyle sınırla"
        },
        "media_fit": {
          "label": "Medya sığdırma",
          "options__1": {
            "label": "Orijinal"
          },
          "options__2": {
            "label": "Doldur"
          }
        }
      },
      "name": "Ürün bilgileri"
    },
    "main-search": {
      "name": "Arama sonuçları",
      "settings": {
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcıyı göster"
        },
        "header__1": {
          "content": "Ürün kartı"
        },
        "header__2": {
          "content": "Blog kartı",
          "info": "Blog kartı stilleri, arama sonuçlarındaki sayfa kartlarına da uygulanır. Kart stillerini değiştirmek için tema ayarlarınızı güncelleyin."
        },
        "article_show_date": {
          "label": "Tarihi göster"
        },
        "article_show_author": {
          "label": "Yazarı göster"
        },
        "show_rating": {
          "label": "Ürün puanlarını göster",
          "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        },
        "columns_mobile": {
          "label": "Mobildeki sütun sayısı",
          "options__1": {
            "label": "1 sütun"
          },
          "options__2": {
            "label": "2 sütun"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Çoklu sütun",
      "settings": {
        "title": {
          "label": "Başlık"
        },
        "image_width": {
          "label": "Görsel genişliği",
          "options__1": {
            "label": "Sütun genişliğinin üçte biri"
          },
          "options__2": {
            "label": "Sütun genişliğinin yarısı"
          },
          "options__3": {
            "label": "Sütun genişliğinin tamamı"
          }
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          },
          "options__4": {
            "label": "Yuvarlak"
          }
        },
        "column_alignment": {
          "label": "Sütun hizalaması",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          }
        },
        "background_style": {
          "label": "İkincil arka plan",
          "options__1": {
            "label": "Hiçbiri"
          },
          "options__2": {
            "label": "Sütun arka planı olarak göster"
          }
        },
        "button_label": {
          "label": "Düğme etiketi"
        },
        "button_link": {
          "label": "Düğme bağlantısı"
        },
        "swipe_on_mobile": {
          "label": "Mobil cihazda kaydırmayı etkinleştir"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        },
        "columns_mobile": {
          "label": "Mobildeki sütun sayısı",
          "options__1": {
            "label": "1 sütun"
          },
          "options__2": {
            "label": "2 sütun"
          }
        }
      },
      "blocks": {
        "column": {
          "name": "Sütun",
          "settings": {
            "image": {
              "label": "Görsel"
            },
            "title": {
              "label": "Başlık"
            },
            "text": {
              "label": "Açıklama"
            },
            "link_label": {
              "label": "Bağlantı etiketi"
            },
            "link": {
              "label": "Bağlantı"
            }
          }
        }
      },
      "presets": {
        "name": "Çoklu sütun"
      }
    },
    "newsletter": {
      "name": "E-posta kaydı",
      "settings": {
        "full_width": {
          "label": "Bölümü tam genişlikli yap"
        },
        "paragraph": {
          "content": "Her e-posta aboneliği bir müşteri hesabı oluşturur. [Daha fazla bilgi edinin](https://help.shopify.com/manual/customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık"
            }
          }
        },
        "paragraph": {
          "name": "Alt başlık",
          "settings": {
            "paragraph": {
              "label": "Açıklama"
            }
          }
        },
        "email_form": {
          "name": "E-posta formu"
        }
      },
      "presets": {
        "name": "E-posta kaydı"
      }
    },
    "page": {
      "name": "Sayfa",
      "settings": {
        "page": {
          "label": "Sayfa"
        }
      },
      "presets": {
        "name": "Sayfa"
      }
    },
    "rich-text": {
      "name": "Zengin metin",
      "settings": {
        "full_width": {
          "label": "Bölümü tam genişlikli yap"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Masaüstü içerik konumu",
          "info": "Konum, mobil cihazlar için otomatik olarak optimize edilir."
        },
        "content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "İçerik hizalaması"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık"
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "Açıklama"
            }
          }
        },
        "buttons": {
          "name": "Düğmeler",
          "settings": {
            "button_label_1": {
              "label": "İlk düğme etiketi",
              "info": "Düğmeyi gizlemek için etiketi boş bırakın."
            },
            "button_link_1": {
              "label": "İlk düğme bağlantısı"
            },
            "button_style_secondary_1": {
              "label": "Dış çizgi düğme stilini kullan"
            },
            "button_label_2": {
              "label": "İkinci düğme etiketi",
              "info": "Düğmeyi gizlemek için etiketi boş bırakın."
            },
            "button_link_2": {
              "label": "İkinci düğme bağlantısı"
            },
            "button_style_secondary_2": {
              "label": "Dış çizgi düğme stilini kullan"
            }
          }
        },
        "caption": {
          "name": "Alt yazı",
          "settings": {
            "text": {
              "label": "Metin rengi"
            },
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Alt yazı"
              },
              "options__2": {
                "label": "Büyük harf"
              }
            },
            "caption_size": {
              "label": "Metin boyutu",
              "options__1": {
                "label": "Küçük"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Büyük"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Zengin metin"
      }
    },
    "apps": {
      "name": "Uygulamalar",
      "settings": {
        "include_margins": {
          "label": "Bölüm kenar boşluklarını temayla aynı yap"
        }
      },
      "presets": {
        "name": "Uygulamalar"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Başlık"
        },
        "cover_image": {
          "label": "Kapak görseli"
        },
        "video_url": {
          "label": "URL",
          "placeholder": "YouTube veya Vimeo URL'si kullanın",
          "info": "Video sayfada oynatılır."
        },
        "description": {
          "label": "Video alternatif metni",
          "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"
        },
        "image_padding": {
          "label": "Görsel dolgusu ekle",
          "info": "Kapak görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."
        },
        "full_width": {
          "label": "Bölümü tam genişlikli yap"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Öne çıkan ürün",
      "blocks": {
        "text": {
          "name": "Metin",
          "settings": {
            "text": {
              "label": "Metin"
            },
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        },
        "title": {
          "name": "Başlık"
        },
        "price": {
          "name": "Fiyat"
        },
        "quantity_selector": {
          "name": "Adet seçici"
        },
        "variant_picker": {
          "name": "Varyasyon seçici",
          "settings": {
            "picker_type": {
              "label": "Tür",
              "options__1": {
                "label": "Açılır menü"
              },
              "options__2": {
                "label": "Seçenekler"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Satın al düğmeleri",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dinamik ödeme düğmelerini göster",
              "info": "Müşteriler, mağazanızda bulunan ödeme yöntemlerini kullanarak PayPal veya Apple Pay gibi tercih ettikleri seçeneği görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Açıklama"
        },
        "share": {
          "name": "Paylaş",
          "settings": {
            "featured_image_info": {
              "content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, önizleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Mağaza başlığı ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Metin rengi"
            }
          }
        },
        "custom_liquid": {
          "name": "Özel liquid",
          "settings": {
            "custom_liquid": {
              "label": "Özel liquid"
            }
          }
        },
        "rating": {
          "name": "Ürün puanı",
          "settings": {
            "paragraph": {
              "content": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Ürün"
        },
        "secondary_background": {
          "label": "İkincil arka planı göster"
        },
        "header": {
          "content": "Medya",
          "info": "Şunun hakkında daha fazla bilgi edinin: [medya türleri](https://help.shopify.com/manual/products/product-media)"
        },
        "enable_video_looping": {
          "label": "Video döngüsünü etkinleştir"
        },
        "hide_variants": {
          "label": "Masaüstünde seçimi kaldırılmış varyasyonların medyasını gizle"
        },
        "media_position": {
          "label": "Masaüstü medya konumu",
          "info": "Konum, mobil cihazlar için otomatik olarak optimize edilir.",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Sağ"
          }
        }
      },
      "presets": {
        "name": "Öne çıkan ürün"
      }
    },
    "email-signup-banner": {
      "name": "E-posta kaydı banner'ı",
      "settings": {
        "paragraph": {
          "content": "Her e-posta aboneliği bir müşteri hesabı oluşturur. [Daha fazla bilgi edinin](https://help.shopify.com/manual/customers)"
        },
        "image": {
          "label": "Arka plan resmi"
        },
        "show_background_image": {
          "label": "Arka plan resmini göster"
        },
        "show_text_box": {
          "label": "Masaüstünde kapsayıcıyı göster"
        },
        "image_overlay_opacity": {
          "label": "Görsel yer paylaşımı opaklığı"
        },
        "color_scheme": {
          "info": "Kapsayıcı gösterildiğinde görünür."
        },
        "show_text_below": {
          "label": "Mobil cihaz üzerinde görselin altındaki içeriği göster",
          "info": "En iyi sonuçlar için 16:9 en-boy oranına sahip bir görsel kullanın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "image_height": {
          "label": "Banner yüksekliği",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          },
          "info": "En iyi sonuçlar için 16:9 en-boy oranına sahip bir görsel kullanın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst Sol"
          },
          "options__2": {
            "label": "Üst Orta"
          },
          "options__3": {
            "label": "Üst Sağ"
          },
          "options__4": {
            "label": "Orta Sol"
          },
          "options__5": {
            "label": "Orta Kısmın Ortası"
          },
          "options__6": {
            "label": "Orta Sağ"
          },
          "options__7": {
            "label": "Alt Sol"
          },
          "options__8": {
            "label": "Alt Orta"
          },
          "options__9": {
            "label": "Alt Sağ"
          },
          "label": "Masaüstü içerik konumu"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Masaüstü içerik hizalaması"
        },
        "header": {
          "content": "Mobil Düzen"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Mobil içerik hizalaması"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık"
            }
          }
        },
        "paragraph": {
          "name": "Paragraf",
          "settings": {
            "paragraph": {
              "label": "Açıklama"
            },
            "text_style": {
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "label": "Metin stili"
            }
          }
        },
        "email_form": {
          "name": "E-posta formu"
        }
      },
      "presets": {
        "name": "E-posta kaydı banner'ı"
      }
    },
    "slideshow": {
      "name": "Slayt gösterisi",
      "settings": {
        "layout": {
          "label": "Düzen",
          "options__1": {
            "label": "Tam genişlik"
          },
          "options__2": {
            "label": "Izgara"
          }
        },
        "slide_height": {
          "label": "Slayt yüksekliği",
          "options__1": {
            "label": "İlk görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          }
        },
        "slider_visual": {
          "label": "Sayfalara ayırma stili",
          "options__1": {
            "label": "Sayaç"
          },
          "options__2": {
            "label": "Noktalar"
          },
          "options__3": {
            "label": "Numaralar"
          }
        },
        "auto_rotate": {
          "label": "Slaytları otomatik olarak döndür"
        },
        "change_slides_speed": {
          "label": "Slaytları şu zaman aralığında değiştir:"
        },
        "show_text_below": {
          "label": "Mobil cihaz üzerinde görsellerin altındaki içeriği göster"
        },
        "mobile": {
          "content": "Mobil düzen"
        },
        "accessibility": {
          "content": "Erişilebilirlik",
          "label": "Slayt gösterisi açıklaması",
          "info": "Ekran koruyucu kullanan müşteriler için slayt gösterisini açıklayın."
        }
      },
      "blocks": {
        "slide": {
          "name": "Slayt",
          "settings": {
            "image": {
              "label": "Görsel"
            },
            "heading": {
              "label": "Başlık"
            },
            "subheading": {
              "label": "Alt başlık"
            },
            "button_label": {
              "label": "Düğme etiketi",
              "info": "Düğmeyi gizlemek için etiketi boş bırakın."
            },
            "link": {
              "label": "Düğme bağlantısı"
            },
            "secondary_style": {
              "label": "Dış çizgi düğme stilini kullan"
            },
            "box_align": {
              "label": "Masaüstü içerik konumu",
              "options__1": {
                "label": "Üst sol"
              },
              "options__2": {
                "label": "Üst orta"
              },
              "options__3": {
                "label": "Üst sağ"
              },
              "options__4": {
                "label": "Orta sol"
              },
              "options__5": {
                "label": "Orta kısmın ortası"
              },
              "options__6": {
                "label": "Orta sağ"
              },
              "options__7": {
                "label": "Alt sol"
              },
              "options__8": {
                "label": "Alt orta"
              },
              "options__9": {
                "label": "Alt sağ"
              },
              "info": "Konum, mobil cihazlar için otomatik olarak optimize edilir."
            },
            "show_text_box": {
              "label": "Masaüstünde kapsayıcıyı göster"
            },
            "text_alignment": {
              "label": "Masaüstü içerik hizalaması",
              "option_1": {
                "label": "Sol"
              },
              "option_2": {
                "label": "Orta"
              },
              "option_3": {
                "label": "Sağ"
              }
            },
            "image_overlay_opacity": {
              "label": "Görsel yer paylaşımı opaklığı"
            },
            "color_scheme": {
              "info": "Kapsayıcı gösterildiğinde görünür."
            },
            "text_alignment_mobile": {
              "label": "Mobil içerik hizalaması",
              "options__1": {
                "label": "Sol"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Sağ"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Slayt gösterisi"
      }
    },
    "collapsible_content": {
      "name": "Daraltılabilir içerik",
      "settings": {
        "caption": {
          "label": "Alt yazı"
        },
        "heading": {
          "label": "Başlık"
        },
        "heading_alignment": {
          "label": "Başlık hizalaması",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          }
        },
        "layout": {
          "label": "Düzen",
          "options__1": {
            "label": "Kapsayıcı yok"
          },
          "options__2": {
            "label": "Satır kapsayıcı"
          },
          "options__3": {
            "label": "Bölüm kapsayıcı"
          }
        },
        "container_color_scheme": {
          "label": "Kapsayıcı renk şeması",
          "info": "Düzen; satır veya bölüm kapsayıcısına ayarlandığında görünür."
        },
        "open_first_collapsible_row": {
          "label": "İlk daraltılabilir satırı aç"
        },
        "header": {
          "content": "Görsel düzeni"
        },
        "image": {
          "label": "Görsel"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Büyük"
          }
        },
        "desktop_layout": {
          "label": "Masaüstü düzeni",
          "options__1": {
            "label": "Önce görsel"
          },
          "options__2": {
            "label": "İkinci olarak görsel"
          },
          "info": "Mobilde her zaman önce görsel görünür."
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Daraltılabilir satır",
          "settings": {
            "heading": {
              "info": "İçeriği açıklayan bir başlık ekleyin.",
              "label": "Başlık"
            },
            "row_content": {
              "label": "Satır içeriği"
            },
            "page": {
              "label": "Sayfadan alınan satır içeriği"
            },
            "icon": {
              "label": "Simge",
              "options__1": {
                "label": "Hiçbiri"
              },
              "options__2": {
                "label": "Elma"
              },
              "options__3": {
                "label": "Muz"
              },
              "options__4": {
                "label": "Şişe"
              },
              "options__5": {
                "label": "Kutu"
              },
              "options__6": {
                "label": "Havuç"
              },
              "options__7": {
                "label": "Sohbet balonu"
              },
              "options__8": {
                "label": "Onay işareti"
              },
              "options__9": {
                "label": "Pano"
              },
              "options__10": {
                "label": "Süt ürünü"
              },
              "options__11": {
                "label": "Süt ürünü içermez"
              },
              "options__12": {
                "label": "Kurutucu"
              },
              "options__13": {
                "label": "Göz"
              },
              "options__14": {
                "label": "Ateş"
              },
              "options__15": {
                "label": "Glütensiz"
              },
              "options__16": {
                "label": "Kalp"
              },
              "options__17": {
                "label": "Ütü"
              },
              "options__18": {
                "label": "Yaprak"
              },
              "options__19": {
                "label": "Deri"
              },
              "options__20": {
                "label": "Şimşek"
              },
              "options__21": {
                "label": "Ruj"
              },
              "options__22": {
                "label": "Kilit"
              },
              "options__23": {
                "label": "Harita pini"
              },
              "options__24": {
                "label": "Kabuklu yemişsiz"
              },
              "options__25": {
                "label": "Pantolon"
              },
              "options__26": {
                "label": "Pati izi"
              },
              "options__27": {
                "label": "Biber"
              },
              "options__28": {
                "label": "Parfüm"
              },
              "options__29": {
                "label": "Uçak"
              },
              "options__30": {
                "label": "Bitki"
              },
              "options__31": {
                "label": "Fiyat etiketi"
              },
              "options__32": {
                "label": "Soru işareti"
              },
              "options__33": {
                "label": "Geri dönüşüm"
              },
              "options__34": {
                "label": "İade"
              },
              "options__35": {
                "label": "Cetvel"
              },
              "options__36": {
                "label": "Servis tabağı"
              },
              "options__37": {
                "label": "Gömlek"
              },
              "options__38": {
                "label": "Ayakkabı"
              },
              "options__39": {
                "label": "Silüet"
              },
              "options__40": {
                "label": "Kar tanesi"
              },
              "options__41": {
                "label": "Yıldız"
              },
              "options__42": {
                "label": "Kronometre"
              },
              "options__43": {
                "label": "Kamyon"
              },
              "options__44": {
                "label": "Yıkama"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Daraltılabilir içerik"
      }
    },
    "main-account": {
      "name": "Hesap"
    },
    "main-activate-account": {
      "name": "Hesap etkinleştirme"
    },
    "main-addresses": {
      "name": "Adresler"
    },
    "main-login": {
      "name": "Giriş bilgileri"
    },
    "main-order": {
      "name": "Sipariş"
    },
    "main-register": {
      "name": "Kayıt"
    },
    "main-reset-password": {
      "name": "Parola sıfırlama"
    },
    "related-products": {
      "name": "Alakalı ürünler",
      "settings": {
        "heading": {
          "label": "Başlık"
        },
        "products_to_show": {
          "label": "Gösterilecek maksimum ürün sayısı"
        },
        "columns_desktop": {
          "label": "Masaüstündeki sütun sayısı"
        },
        "paragraph__1": {
          "content": "Dinamik önerilerin zamanla değişmesi ve gelişmesi için sipariş ve ürün bilgileri kullanılır. [Daha fazla bilgi edinin](https://help.shopify.com/themes/development/recommended-products)"
        },
        "header__2": {
          "content": "Ürün kartı"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcıyı göster"
        },
        "show_rating": {
          "label": "Ürün puanlarını göster",
          "info": "Puan göstermek için bir ürün puanlandırma uygulaması ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        },
        "columns_mobile": {
          "label": "Mobildeki sütun sayısı",
          "options__1": {
            "label": "1 sütun"
          },
          "options__2": {
            "label": "2 sütun"
          }
        }
      }
    },
    "multirow": {
      "name": "Çok satırlı",
      "settings": {
        "image": {
          "label": "Görsel"
        },
        "image_height": {
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          },
          "label": "Görsel yüksekliği"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Küçük"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Büyük"
          },
          "label": "Masaüstü görsel genişliği",
          "info": "Görsel, mobil cihazlar için otomatik olarak optimize edilir."
        },
        "heading_size": {
          "options__1": {
            "label": "Küçük"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Büyük"
          },
          "label": "Başlık boyutu"
        },
        "text_style": {
          "options__1": {
            "label": "Gövde"
          },
          "options__2": {
            "label": "Alt yazı"
          },
          "label": "Metin stili"
        },
        "button_style": {
          "options__1": {
            "label": "Sabit düğme"
          },
          "options__2": {
            "label": "Dış çizgi düğmesi"
          },
          "label": "Düğme stili"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Masaüstü içerik hizalaması"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Alt"
          },
          "label": "Masaüstü içerik konumu",
          "info": "Konum, mobil cihazlar için otomatik olarak optimize edilir."
        },
        "image_layout": {
          "options__1": {
            "label": "Alternatif (soldan)"
          },
          "options__2": {
            "label": "Alternatif (sağdan)"
          },
          "options__3": {
            "label": "Sola hizalanmış"
          },
          "options__4": {
            "label": "Sağa hizalanmış"
          },
          "label": "Masaüstü görsel yerleşimi",
          "info": "Görsel, mobil cihazlar için otomatik olarak optimize edilir."
        },
        "container_color_scheme": {
          "label": "Kapsayıcı renk şeması"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Mobil içerik hizalaması"
        },
        "header_mobile": {
          "content": "Mobil Düzen"
        }
      },
      "blocks": {
        "row": {
          "name": "Satır",
          "settings": {
            "image": {
              "label": "Görsel"
            },
            "caption": {
              "label": "Alt yazı"
            },
            "heading": {
              "label": "Başlık"
            },
            "text": {
              "label": "Metin"
            },
            "button_label": {
              "label": "Düğme etiketi"
            },
            "button_link": {
              "label": "Düğme bağlantısı"
            }
          }
        }
      },
      "presets": {
        "name": "Çok satırlı"
      }
    }
  }
}
