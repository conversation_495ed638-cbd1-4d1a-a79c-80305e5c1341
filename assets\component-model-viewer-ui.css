.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  background: rgb(var(--color-background));
  border-color: rgba(var(--color-foreground), 0.04);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
  color: rgba(var(--color-foreground), 0.9);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
  color: rgba(var(--color-foreground), 0.55);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active,
.shopify-model-viewer-ui
  .shopify-model-viewer-ui__button--control.focus-visible:focus {
  color: rgba(var(--color-foreground), 0.55);
  background: rgba(var(--color-foreground), 0.04);
}

.shopify-model-viewer-ui
  .shopify-model-viewer-ui__button--control:not(:last-child):after {
  border-color: rgba(var(--color-foreground), 0.04);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
  border-radius: 50%;
  color: rgb(var(--color-foreground));
  background: rgb(var(--color-background));
  border-color: rgba(var(--color-foreground), 0.1);
  transform: translate(-50%, -50%) scale(1);
  transition: transform var(--duration-short) ease,
    color var(--duration-short) ease;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__poster-control-icon {
  width: 4.8rem;
  height: 4.8rem;
  margin-top: 0.3rem;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover,
.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
  transform: translate(-50%, -50%) scale(1.1);
}
