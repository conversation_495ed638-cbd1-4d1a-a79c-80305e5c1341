{% case icon %}
  {% when 'like' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 16 16">
      <defs>
        <linearGradient id="gradient-{{ id }}" x1="8" x2="8" y2="16" gradientUnits="userSpaceOnUse">
          <stop stop-color="#18AFFF"/>
          <stop offset="1" stop-color="#0062DF"/>
        </linearGradient>
      </defs>
      <path fill="url(#gradient-{{ id }})" d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0Z"/>
      <path fill="#fff" d="M12.162 7.338c.176.123.338.245.338.674 0 .43-.229.604-.474.725.1.163.132.36.089.546-.077.344-.392.611-.672.69.121.194.159.385.015.62-.185.295-.346.407-1.058.407H7.5c-.988 0-1.5-.546-1.5-1V7.665c0-1.23 1.467-2.275 1.467-3.13L7.361 3.47c-.005-.065.008-.224.058-.27.08-.079.301-.2.635-.2.218 0 .363.041.534.123.581.277.732.978.732 1.542 0 .271-.414 1.083-.47 1.364 0 0 .867-.192 1.879-.199 1.061-.006 1.749.19 1.749.842 0 .261-.219.523-.316.666ZM3.6 7h.8a.6.6 0 0 1 .6.6v3.8a.6.6 0 0 1-.6.6h-.8a.6.6 0 0 1-.6-.6V7.6a.6.6 0 0 1 .6-.6Z"/>
    </svg>
  {% when 'love' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 16 16">
      <defs>
        <linearGradient id="gradient-{{ id }}" x1="8" x2="8" y2="16" gradientUnits="userSpaceOnUse">
          <stop stop-color="#FF6680"/>
          <stop offset="1" stop-color="#E61739"/>
        </linearGradient>
      </defs>
      <path fill="url(#gradient-{{ id }})" d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0Z"></path>
      <path fill="#fff" d="M10.473 4C8.275 4 8 5.824 8 5.824S7.726 4 5.528 4c-2.114 0-2.73 2.222-2.472 3.41C3.736 10.55 8 12.75 8 12.75s4.265-2.2 4.945-5.34c.257-1.188-.36-3.41-2.472-3.41Z"></path>
    </svg>
  {% when 'care' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16">
      <linearGradient id="gradient-{{ id }}a" x1="-2.313" x2="-2.313" y1="19.862" y2="20.738" gradientTransform="matrix(16 0 0 -16 45 333)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#f28a2d"></stop>
         <stop offset="1" stop-color="#fde86f"></stop>
      </linearGradient>
      <path fill="url(#gradient-{{ id }}a)" fill-rule="evenodd" d="M16 8c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}b" cx="-2.313" cy="20.313" r=".5" gradientTransform="matrix(16 0 0 -16 45 333)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#f28a2d" stop-opacity="0"></stop>
         <stop offset="1" stop-color="#f08423" stop-opacity=".34"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}b)" fill-rule="evenodd" d="M16 8c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}c" cx="-2.644" cy="20.358" r=".101" gradientTransform="matrix(14.5998 6.5456 5.063 -11.2928 -62.74 255.526)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#f28a2d" stop-opacity=".5"></stop>
         <stop offset="1" stop-color="#f28a2d" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}c)" fill-rule="evenodd" d="M16 8c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}d" cx="-2.227" cy="19.541" r=".283" gradientTransform="matrix(12.5663 -9.904 -3.6032 -4.5717 110.263 79.053)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#f28a2d" stop-opacity=".5"></stop>
         <stop offset="1" stop-color="#f28a2d" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}d)" fill-rule="evenodd" d="M16 8c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}e" cx="-2.531" cy="19.776" r=".107" gradientTransform="matrix(15.7394 -2.8762 -.572 -3.1299 56.242 56.647)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#d45f00" stop-opacity=".15"></stop>
         <stop offset="1" stop-color="#f28a2d" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}e)" fill-rule="evenodd" d="M16 8c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}f" cx="-2.098" cy="20.131" r=".106" gradientTransform="matrix(15.6768 3.1995 .6363 -3.1176 30.972 71.62)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#d45f00" stop-opacity=".15"></stop>
         <stop offset="1" stop-color="#d45f00" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}f)" fill-rule="evenodd" d="M16 8c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z" clip-rule="evenodd"></path>
      <linearGradient id="gradient-{{ id }}g" x1="-1.619" x2="-1.619" y1="18.2" y2="16.681" gradientTransform="matrix(3.4035 0 0 -.9374 13.51 22.37)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#482314"></stop>
         <stop offset="1" stop-color="#9a4111"></stop>
      </linearGradient>
      <path fill="url(#gradient-{{ id }}g)" fill-rule="evenodd" d="M9.7 5.9c-.1-.3-3.3-.3-3.4 0-.******* 1.7.7s1.8-.4 1.7-.7z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}h" cx="-3.9" cy="18.924" r=".872" gradientTransform="matrix(0 -2.1326 -2.1327 0 45.352 -4.046)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#3b446b"></stop>
         <stop offset=".688" stop-color="#202340"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}h)" fill-rule="evenodd" d="M6 4.1c0 .7-.4.9-1 1-.6.1-1.1-.2-1.1-1 0-.6.3-1.4 1.1-1.4.7 0 1 .8 1 1.4z" clip-rule="evenodd"></path>
      <path fill="#4e506a" fill-rule="evenodd" d="M4.9 3.1c.*******-.1.5-.1.1-.3.2-.4 0s-.1-.3 0-.5c.2-.1.4-.1.5 0z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}i" cx="-3.914" cy="18.924" r=".872" gradientTransform="matrix(0 -2.1326 -2.1327 0 51.366 -4.077)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#3b446b"></stop>
         <stop offset=".688" stop-color="#202340"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}i)" fill-rule="evenodd" d="M10 4.1c0 .7.4.9 1.1 1 .6.1 1.1-.2 1.1-1 0-.6-.3-1.4-1.1-1.4S10 3.5 10 4.1z" clip-rule="evenodd"></path>
      <path fill="#4e506a" fill-rule="evenodd" d="M11 3.1c.1.1 0 .3-.1.5-.1.1-.3.1-.4 0s0-.3.1-.5c.2-.2.3-.2.4 0z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}j" cx="-5.202" cy="20.231" r=".298" gradientTransform="matrix(-.339 -1.3177 -6.1081 1.5713 126.811 -36.933)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#e38200"></stop>
         <stop offset="1" stop-color="#cd6700"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}j)" fill-rule="evenodd" d="M3.4 2.1c-.2.2 0 .*******-.3 1.8-.6 2.8-.5.3 0 .4 0 .3-.4 0-.3-.4-.5-1.2-.4-1.2.1-2 .7-2.2.9z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}k" cx="-4.247" cy="20.267" r=".314" gradientTransform="matrix(.2577 -1.3359 -7.9278 -1.5293 172.702 26.852)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#e38200"></stop>
         <stop offset="1" stop-color="#cd6700"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}k)" fill-rule="evenodd" d="M10.4 1.2c-.8-.1-1.2.1-1.2.4-.1.4 0 .4.3.4 1.1-.1 2.3.2 *******.2.5-.2.3-.4s-1-.8-2.2-.9z" clip-rule="evenodd"></path>
      <linearGradient id="gradient-{{ id }}l" x1="-2.17" x2="-2.407" y1="20.358" y2="19.647" gradientTransform="matrix(9.7496 0 0 -9.079 27.91 194.578)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#f34462"></stop>
         <stop offset="1" stop-color="#cc0820"></stop>
      </linearGradient>
      <path fill="url(#gradient-{{ id }}l)" fill-rule="evenodd" d="M9.7 8.5c-2.1-.6-2.8.8-2.8.8S7.1 7.7 5 7c-2-.6-3.2 1.3-3.3 2.4-.2 2.5 2 5.3 2.8 *******.******* 1.2-.3 4.6-1.4 5.9-3.6.5-1.1.6-3.3-1.4-3.9z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}m" cx="-1.839" cy="20.363" r=".29" gradientTransform="matrix(8.51 3.1636 3.1637 -8.51 -39.932 190.042)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#ff7091" stop-opacity=".7"></stop>
         <stop offset="1" stop-color="#fe6d8e" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}m)" fill-rule="evenodd" d="M9.7 8.5c-2.1-.6-2.8.8-2.8.8S7.1 7.7 5 7c-2-.6-3.2 1.3-3.3 2.4-.2 2.5 2 5.3 2.8 *******.******* 1.2-.3 4.6-1.4 5.9-3.6.5-1.1.6-3.3-1.4-3.9z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}n" cx="-2.308" cy="20.509" r=".29" gradientTransform="matrix(8.51 3.1636 3.1637 -8.51 -40.975 191.442)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#ff7091" stop-opacity=".7"></stop>
         <stop offset="1" stop-color="#fe6d8e" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}n)" fill-rule="evenodd" d="M9.7 8.5c-2.1-.6-2.8.8-2.8.8S7.1 7.7 5 7c-2-.6-3.2 1.3-3.3 2.4-.2 2.5 2 5.3 2.8 *******.******* 1.2-.3 4.6-1.4 5.9-3.6.5-1.1.6-3.3-1.4-3.9z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}o" cx="-2.505" cy="20.75" r=".249" gradientTransform="matrix(-1.8271 8.8932 12.246 2.5158 -254.697 -18.163)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#9c0600"></stop>
         <stop offset="1" stop-color="#9c0600" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}o)" fill-rule="evenodd" d="M9.7 8.5c-2.1-.6-2.8.8-2.8.8S7.1 7.7 5 7c-2-.6-3.2 1.3-3.3 2.4-.2 2.5 2 5.3 2.8 *******.******* 1.2-.3 4.6-1.4 5.9-3.6.5-1.1.6-3.3-1.4-3.9z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}p" cx="-1.547" cy="20.349" r=".15" gradientTransform="matrix(7.812 4.6261 5.8059 -9.8043 -94.645 218.657)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#9c0600" stop-opacity=".5"></stop>
         <stop offset="1" stop-color="#9c0600" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}p)" fill-rule="evenodd" d="M9.7 8.5c-2.1-.6-2.8.8-2.8.8S7.1 7.7 5 7c-2-.6-3.2 1.3-3.3 2.4-.2 2.5 2 5.3 2.8 *******.******* 1.2-.3 4.6-1.4 5.9-3.6.5-1.1.6-3.3-1.4-3.9z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}q" cx="-2.763" cy="20.429" r=".13" gradientTransform="matrix(8.5228 -3.1289 -4.0321 -10.983 107.977 224.84)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#9c0600" stop-opacity=".5"></stop>
         <stop offset="1" stop-color="#9c0600" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}q)" fill-rule="evenodd" d="M9.7 8.5c-2.1-.6-2.8.8-2.8.8S7.1 7.7 5 7c-2-.6-3.2 1.3-3.3 2.4-.2 2.5 2 5.3 2.8 *******.******* 1.2-.3 4.6-1.4 5.9-3.6.5-1.1.6-3.3-1.4-3.9z" clip-rule="evenodd"></path>
      <radialGradient id="gradient-{{ id }}r" cx="-1.795" cy="20.148" r=".175" gradientTransform="matrix(7.5205 5.0863 5.5088 -8.1451 -88.557 187.152)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#9c0600" stop-opacity=".999"></stop>
         <stop offset="1" stop-color="#9c0600" stop-opacity="0"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}r)" fill-rule="evenodd" d="M9.7 8.5c-2.1-.6-2.8.8-2.8.8S7.1 7.7 5 7c-2-.6-3.2 1.3-3.3 2.4-.2 2.5 2 5.3 2.8 *******.******* 1.2-.3 4.6-1.4 5.9-3.6.5-1.1.6-3.3-1.4-3.9z" clip-rule="evenodd"></path>
      <defs>
         <filter id="gradient-{{ id }}s" width="6.9" height="5.4" x="-.2" y="7.2" filterUnits="userSpaceOnUse">
            <feColorMatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></feColorMatrix>
         </filter>
      </defs>
      <mask id="gradient-{{ id }}u" width="6.9" height="5.4" x="-.2" y="7.2" maskUnits="userSpaceOnUse">
         <path fill="#fff" fill-rule="evenodd" d="M16 8c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z" clip-rule="evenodd" filter="url(#gradient-{{ id }}s)"></path>
      </mask>
      <radialGradient id="gradient-{{ id }}t" cx="-2.204" cy="20.844" r="1.226" gradientTransform="matrix(4.3582 3.2271 3.227 -4.3582 -57.739 105.424)" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#eda83a"></stop>
         <stop offset="1" stop-color="#ffdc5e"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}t)" fill-rule="evenodd" d="M1.3 7.7c-.5-.5-1.4-.8-1.5.4-.1.9.3 2.5 1.4 3.4 2.8 2.2 5.3 1 5.4-.6.1-1.2-1.4-1.1-1.8-1.1v-.1c.1-.1.3-.2.4-.3.4-.3.2-.8-.3-.7-.1 0-1.3.4-2.1.1-.8-.3-.9-.6-1.5-1.1z" clip-rule="evenodd" mask="url(#gradient-{{ id }}u)"></path>
      <radialGradient id="gradient-{{ id }}v" cx="15.654" cy="7.737" r="8.846" gradientUnits="userSpaceOnUse">
         <stop offset="0" stop-color="#eda83a"></stop>
         <stop offset="1" stop-color="#ffdc5e"></stop>
      </radialGradient>
      <path fill="url(#gradient-{{ id }}v)" d="M14.3 7.8c.3-.6.8-.4 1.1-.*******.4.7 1 0 1.5-.2 2.9-1.5 4.3-2.3 2.6-6.2 2.2-6.6.6-.3-1.2 1.1-1.4 1.6-1.4v-.1c-.2-.1-.3-.2-.5-.3-.4-.3-.3-.9.2-.8.6.1 1.4.3 2 .2 1.9-.2 2.2-1.7 3-3.2z"></path>
    </svg>
  {% when 'haha' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 16 16">
      <path fill="url(#gradient-{{ id }}a)" d="M16 8A8 8 0 1 1-.001 8 8 8 0 0 1 16 8"></path>
      <path fill="url(#gradient-{{ id }}b)" d="M3 8.008C3 10.023 4.006 14 8 14c3.993 0 5-3.977 5-5.992C13 7.849 11.39 7 8 7c-3.39 0-5 .849-5 1.008Z"></path>
      <path fill="url(#gradient-{{ id }}c)" d="M4.541 12.5c.804.995 1.907 1.5 3.469 1.5 1.563 0 2.655-.505 3.459-1.5-.551-.588-1.599-1.5-3.459-1.5s-2.917.912-3.469 1.5Z"></path>
      <path fill="#2A3755" d="M6.213 4.144c.263.188.502.455.41.788-.071.254-.194.369-.422.37-.78.012-1.708.256-2.506.613-.065.029-.197.088-.332.085-.124-.003-.251-.058-.327-.237-.067-.157-.073-.388.276-.598.545-.33 1.257-.48 1.909-.604-.41-.303-.85-.56-1.315-.768-.427-.194-.38-.457-.323-.6.127-.317.609-.196 1.078.026a9 9 0 0 1 1.552.925Zm3.577 0a8.955 8.955 0 0 1 1.55-.925c.47-.222.95-.343 1.078-.026.057.143.104.406-.323.6a7.028 7.028 0 0 0-1.313.768c.65.123 1.363.274 1.907.604.349.21.342.44.276.598-.077.18-.203.234-.327.237-.135.003-.267-.056-.332-.085-.797-.357-1.725-.6-2.504-.612-.228-.002-.351-.117-.422-.37-.091-.333.147-.6.41-.788v-.001Z"></path>
      <defs>
       <linearGradient id="gradient-{{ id }}a" x1="8" x2="8" y1="1.64" y2="16" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FEEA70"></stop>
        <stop offset="1" stop-color="#F69B30"></stop>
       </linearGradient>
       <linearGradient id="gradient-{{ id }}b" x1="8" x2="8" y1="7" y2="14" gradientUnits="userSpaceOnUse">
        <stop stop-color="#472315"></stop>
        <stop offset="1" stop-color="#8B3A0E"></stop>
       </linearGradient>
       <linearGradient id="gradient-{{ id }}c" x1="8.005" x2="8.005" y1="11" y2="13.457" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FC607C"></stop>
        <stop offset="1" stop-color="#D91F3A"></stop>
       </linearGradient>
      </defs>
    </svg>
  {% when 'wow' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 16 16">
      <g clip-path="url(#gradient-{{ id }}a)">
         <path fill="url(#gradient-{{ id }}b)" d="M16 8A8 8 0 1 1-.001 8 8 8 0 0 1 16 8"></path>
         <path fill="url(#gradient-{{ id }}c)" d="M5.643 10.888C5.485 12.733 6.37 14 8 14c1.63 0 2.515-1.267 2.357-3.112C10.2 9.042 9.242 8 8 8c-1.242 0-2.2 1.042-2.357 2.888Z"></path>
         <path fill="url(#gradient-{{ id }}d)" d="M3.5 5.5c0-.828.559-1.5 1.25-1.5S6 4.672 6 5.5C6 6.329 5.441 7 4.75 7S3.5 6.329 3.5 5.5Zm6.5 0c0-.828.56-1.5 1.25-1.5.691 0 1.25.672 1.25 1.5 0 .829-.559 1.5-1.25 1.5C10.56 7 10 6.329 10 5.5Z"></path>
         <path fill="#000" d="M3.5 5.5c0-.828.559-1.5 1.25-1.5S6 4.672 6 5.5C6 6.329 5.441 7 4.75 7S3.5 6.329 3.5 5.5Zm6.5 0c0-.828.56-1.5 1.25-1.5.691 0 1.25.672 1.25 1.5 0 .829-.559 1.5-1.25 1.5C10.56 7 10 6.329 10 5.5Z" filter="url(#gradient-{{ id }}e)"></path>
         <path fill="#4E506A" d="M4.481 4.567c.**************.232.47-.057.217-.254.36-.44.317-.186-.042-.29-.252-.232-.47.057-.216.254-.36.44-.317Zm6.659.063c.205.047.321.28.258.52-.064.243-.282.4-.49.354-.205-.046-.322-.28-.258-.52.063-.243.282-.4.49-.354Z"></path>
         <path fill="#000" d="M11.068 1.696c.052-.005.104-.007.157-.007.487 0 .99.204 1.372.562a.368.368 0 0 1-.087.594.344.344 0 0 1-.387-.06c-.275-.26-.656-.4-.992-.37a.8.8 0 0 0-.59.332.346.346 0 0 1-.49.068.368.368 0 0 1-.068-.507 1.49 1.49 0 0 1 1.085-.612Zm-7.665.555c.371-.353.86-.553 1.372-.562a1.49 1.49 0 0 1 1.242.619.369.369 0 0 1-.066.507.347.347 0 0 1-.492-.068.8.8 0 0 0-.59-.331c-.335-.031-.717.11-.992.369a.344.344 0 0 1-.496-.024.368.368 0 0 1 .022-.51Z" filter="url(#gradient-{{ id }}f)"></path>
         <path fill="url(#gradient-{{ id }}g)" d="M11.068 1.696c.052-.005.104-.007.157-.007.487 0 .99.204 1.372.562a.368.368 0 0 1-.087.594.344.344 0 0 1-.387-.06c-.275-.26-.656-.4-.992-.37a.8.8 0 0 0-.59.332.346.346 0 0 1-.49.068.368.368 0 0 1-.068-.507 1.49 1.49 0 0 1 1.085-.612Zm-7.665.555c.371-.353.86-.553 1.372-.562a1.49 1.49 0 0 1 1.242.619.369.369 0 0 1-.066.507.347.347 0 0 1-.492-.068.8.8 0 0 0-.59-.331c-.335-.031-.717.11-.992.369a.344.344 0 0 1-.496-.024.368.368 0 0 1 .022-.51Z"></path>
      </g>
      <defs>
         <linearGradient id="gradient-{{ id }}b" x1="8" x2="8" y1="1.64" y2="16" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FEEA70"></stop>
            <stop offset="1" stop-color="#F69B30"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}c" x1="8" x2="8" y1="8" y2="14" gradientUnits="userSpaceOnUse">
            <stop stop-color="#472315"></stop>
            <stop offset="1" stop-color="#8B3A0E"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}d" x1="8" x2="8" y1="4" y2="7" gradientUnits="userSpaceOnUse">
            <stop stop-color="#191A33"></stop>
            <stop offset=".872" stop-color="#3B426A"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}g" x1="8" x2="8" y1="1.688" y2="2.888" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E78E0D"></stop>
            <stop offset="1" stop-color="#CB6000"></stop>
         </linearGradient>
         <filter id="gradient-{{ id }}e" width="9" height="3" x="3.5" y="4" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
            <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
            <feOffset></feOffset>
            <feGaussianBlur stdDeviation=".5"></feGaussianBlur>
            <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0980392 0 0 0 0 0.101961 0 0 0 0 0.2 0 0 0 0.819684 0"></feColorMatrix>
            <feBlend in2="shape" result="effect1_innerShadow"></feBlend>
         </filter>
         <filter id="gradient-{{ id }}f" width="15.422" height="7.199" x=".289" y="-.312" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
            <feOffset dy="1"></feOffset>
            <feGaussianBlur stdDeviation="1.5"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.803922 0 0 0 0 0.388235 0 0 0 0 0.00392157 0 0 0 0.145679 0"></feColorMatrix>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow"></feBlend>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feBlend>
         </filter>
         <clipPath id="gradient-{{ id }}a">
            <path fill="#fff" d="M0 0h16v16H0z"></path>
         </clipPath>
      </defs>
    </svg>
  {% when 'sad' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 16 16">
      <path fill="url(#gradient-{{ id }}a)" d="M16 8A8 8 0 1 1-.001 8 8 8 0 0 1 16 8"></path>
      <path fill="url(#gradient-{{ id }}b)" d="M5.333 12.765c0 .**************.235.351 0 .836-.625 2.417-.625s2.067.625 2.417.625c.156 0 .25-.098.25-.235C10.667 12.368 9.828 11 8 11c-1.828 0-2.667 1.368-2.667 1.765Z"></path>
      <path fill="url(#gradient-{{ id }}c)" d="M3.599 8.8c0-.81.509-1.466 1.134-1.466.627 0 1.134.656 1.134 1.466 0 .338-.09.65-.238.898a.492.492 0 0 1-.301.225c-.14.037-.353.077-.595.077-.243 0-.453-.04-.595-.077a.49.49 0 0 1-.3-.225 1.741 1.741 0 0 1-.24-.898Zm6.534 0c0-.81.508-1.466 1.133-1.466.627 0 1.134.656 1.134 1.466 0 .338-.09.65-.238.898a.49.49 0 0 1-.301.225c-.39.101-.8.101-1.19 0a.49.49 0 0 1-.3-.225 1.74 1.74 0 0 1-.238-.898Z"></path>
      <path fill="#000" d="M3.599 8.8c0-.81.509-1.466 1.134-1.466.627 0 1.134.656 1.134 1.466 0 .338-.09.65-.238.898a.492.492 0 0 1-.301.225c-.14.037-.353.077-.595.077-.243 0-.453-.04-.595-.077a.49.49 0 0 1-.3-.225 1.741 1.741 0 0 1-.24-.898Zm6.534 0c0-.81.508-1.466 1.133-1.466.627 0 1.134.656 1.134 1.466 0 .338-.09.65-.238.898a.49.49 0 0 1-.301.225c-.39.101-.8.101-1.19 0a.49.49 0 0 1-.3-.225 1.74 1.74 0 0 1-.238-.898Z" filter="url(#gradient-{{ id }}d)"></path>
      <path fill="#4E506A" d="M4.616 7.986c.128.125.136.372.017.55-.12.179-.32.223-.448.097-.128-.125-.135-.372-.017-.55.12-.18.32-.222.448-.097Zm6.489 0c.128.125.136.372.018.55-.12.179-.32.223-.45.097-.127-.125-.134-.372-.015-.55.119-.18.319-.222.447-.097Z"></path>
      <path fill="url(#gradient-{{ id }}e)" d="M4.157 5.153c.332-.153.596-.22.801-.22.277 0 .451.12.55.307.175.329.096.4-.198.459-1.106.224-2.217.942-2.699 1.39-.3.28-.589-.03-.436-.274.154-.244.774-1.105 1.982-1.662Zm6.335.087c.1-.187.273-.306.55-.306.206 0 .47.066.801.219 1.208.557 1.828 1.418 1.981 1.662.153.244-.134.554-.435.274-.483-.448-1.593-1.166-2.7-1.39-.294-.058-.37-.13-.197-.46Z"></path>
      <path fill="url(#gradient-{{ id }}f)" d="M13.5 16c-.828 0-1.5-.748-1.5-1.671 0-.922.356-1.545.643-2.147.598-1.258.716-1.432.857-1.432.141 0 .259.174.857 1.432.287.602.643 1.225.643 2.147 0 .923-.672 1.671-1.5 1.671Z"></path>
      <path fill="url(#gradient-{{ id }}g)" d="M13.5 13.606c-.328 0-.594-.296-.594-.66 0-.366.141-.613.255-.852.236-.498.283-.566.34-.566.055 0 .102.068.338.566.114.24.255.486.255.851s-.266.661-.594.661"></path>
      <defs>
         <linearGradient id="gradient-{{ id }}a" x1="8" x2="8" y1="1.64" y2="16" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FEEA70"></stop>
            <stop offset="1" stop-color="#F69B30"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}b" x1="8" x2="8" y1="11" y2="13" gradientUnits="userSpaceOnUse">
            <stop stop-color="#472315"></stop>
            <stop offset="1" stop-color="#8B3A0E"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}c" x1="7.999" x2="7.999" y1="7.334" y2="10" gradientUnits="userSpaceOnUse">
            <stop stop-color="#191A33"></stop>
            <stop offset=".872" stop-color="#3B426A"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}e" x1="8" x2="8" y1="4.934" y2="7.199" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E78E0D"></stop>
            <stop offset="1" stop-color="#CB6000"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}f" x1="13.5" x2="13.5" y1="15.05" y2="11.692" gradientUnits="userSpaceOnUse">
            <stop stop-color="#35CAFC"></stop>
            <stop offset="1" stop-color="#007EDB"></stop>
         </linearGradient>
         <linearGradient id="gradient-{{ id }}g" x1="13.5" x2="13.5" y1="11.528" y2="13.606" gradientUnits="userSpaceOnUse">
            <stop stop-color="#6AE1FF" stop-opacity=".287"></stop>
            <stop offset="1" stop-color="#A8E3FF" stop-opacity=".799"></stop>
         </linearGradient>
         <filter id="gradient-{{ id }}d" width="8.801" height="2.666" x="3.599" y="7.334" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
            <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
            <feOffset></feOffset>
            <feGaussianBlur stdDeviation=".5"></feGaussianBlur>
            <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite>
            <feColorMatrix values="0 0 0 0 0.0411227 0 0 0 0 0.0430885 0 0 0 0 0.0922353 0 0 0 0.819684 0"></feColorMatrix>
            <feBlend in2="shape" result="effect1_innerShadow"></feBlend>
         </filter>
      </defs>
    </svg>
  {% when 'angry' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 16 16">
      <path fill="url(#gradient-{{ id }}a)" d="M16 8A8 8 0 1 1-.001 8 8 8 0 0 1 16 8"></path>
      <path fill="#000" d="M5.2 13.551c0 .528 1.253.444 2.8.444 1.546 0 2.8.084 2.8-.444 0-.636-1.254-1.051-2.8-1.051-1.547 0-2.8.415-2.8 1.051Z" filter="url(#b)"></path>
      <path fill="url(#gradient-{{ id }}c)" d="M5.2 13.551c0 .528 1.253.444 2.8.444 1.546 0 2.8.084 2.8-.444 0-.636-1.254-1.051-2.8-1.051-1.547 0-2.8.415-2.8 1.051Z"></path>
      <path fill="url(#gradient-{{ id }}d)" d="M3.6 9.831c0-.79.538-1.43 1.2-1.43.663 0 1.2.64 1.2 1.43 0 .33-.093.633-.252.874a.527.527 0 0 1-.318.22c-.15.036-.373.075-.63.075s-.481-.039-.63-.075a.524.524 0 0 1-.318-.22 1.588 1.588 0 0 1-.252-.874Zm6.4 0c0-.79.537-1.43 1.2-1.43.662 0 1.2.64 1.2 1.43 0 .33-.094.633-.252.874a.524.524 0 0 1-.318.22c-.207.05-.418.075-.63.075-.257 0-.48-.039-.63-.075a.53.53 0 0 1-.32-.22 1.596 1.596 0 0 1-.25-.874Z"></path>
      <path fill="#000" d="M3.6 9.831c0-.79.538-1.43 1.2-1.43.663 0 1.2.64 1.2 1.43 0 .33-.093.633-.252.874a.527.527 0 0 1-.318.22c-.15.036-.373.075-.63.075s-.481-.039-.63-.075a.524.524 0 0 1-.318-.22 1.588 1.588 0 0 1-.252-.874Zm6.4 0c0-.79.537-1.43 1.2-1.43.662 0 1.2.64 1.2 1.43 0 .33-.094.633-.252.874a.524.524 0 0 1-.318.22c-.207.05-.418.075-.63.075-.257 0-.48-.039-.63-.075a.53.53 0 0 1-.32-.22 1.596 1.596 0 0 1-.25-.874Z" filter="url(#gradient-{{ id }}e)"></path>
      <path fill="#4F4F67" d="M4.968 9.333a.33.33 0 0 1 .007.07c0 .202-.176.367-.394.367-.217 0-.393-.165-.393-.366 0-.083.03-.16.08-.221.224.053.46.104.7.15Zm5.927.437c-.211 0-.383-.153-.393-.348.258-.038.515-.085.765-.136.014.038.021.078.02.119 0 .2-.175.365-.393.365Z"></path>
      <path fill="#000" d="M9 7.6c0-.446.163-.6.445-.6.28 0 .414.276.506 1.066 1.128 0 3.038-.534 3.222-.534.178 0 .277.085.317.267.035.158-.023.308-.221.4-.621.287-2.443.935-3.984.935-.168 0-.285-.086-.285-.301V7.6Zm-2.951.466C6.14 7.276 6.275 7 6.555 7c.282 0 .445.154.445.6v1.233c0 .215-.117.301-.285.301-1.541 0-3.363-.648-3.984-.935-.198-.092-.256-.242-.221-.4.04-.182.14-.267.317-.267.184 0 2.094.534 3.222.534Z" filter="url(#gradient-{{ id }}f)"></path>
      <path fill="url(#gradient-{{ id }}g)" d="M9 7.6c0-.446.163-.6.445-.6.28 0 .414.276.506 1.066 1.128 0 3.038-.534 3.222-.534.178 0 .277.085.317.267.035.158-.023.308-.221.4-.621.287-2.443.935-3.984.935-.168 0-.285-.086-.285-.301V7.6Zm-2.951.466C6.14 7.276 6.275 7 6.555 7c.282 0 .445.154.445.6v1.233c0 .215-.117.301-.285.301-1.541 0-3.363-.648-3.984-.935-.198-.092-.256-.242-.221-.4.04-.182.14-.267.317-.267.184 0 2.094.534 3.222.534Z"></path>
      <defs>
        <linearGradient id="gradient-{{ id }}a" x1="8" x2="8" y2="10.751" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E04300"></stop>
          <stop offset="1" stop-color="#FFA320"></stop>
        </linearGradient>
        <linearGradient id="gradient-{{ id }}c" x1="8" x2="8" y1="12.703" y2="14" gradientUnits="userSpaceOnUse">
          <stop stop-color="#3D0D00"></stop>
          <stop offset="1" stop-color="#661C04"></stop>
        </linearGradient>
        <linearGradient id="gradient-{{ id }}d" x1="8" x2="8" y1="8.4" y2="11" gradientUnits="userSpaceOnUse">
          <stop stop-color="#191A33"></stop>
          <stop offset=".872" stop-color="#3B426A"></stop>
        </linearGradient>
        <linearGradient id="gradient-{{ id }}g" x1="11.615" x2="11.615" y1="9.333" y2="7" gradientUnits="userSpaceOnUse">
          <stop stop-color="#9A2F00"></stop>
          <stop offset="1" stop-color="#D44800"></stop>
        </linearGradient>
        <filter id="gradient-{{ id }}b" width="7.6" height="3.5" x="4.2" y="12.5" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
          <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
          <feOffset dy="1"></feOffset>
          <feGaussianBlur stdDeviation=".5"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 1 0 0 0 0 0.509681 0 0 0 0 0 0 0 0 0.371207 0"></feColorMatrix>
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow"></feBlend>
          <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feBlend>
        </filter>
        <filter id="gradient-{{ id }}e" width="8.8" height="2.6" x="3.6" y="8.4" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
          <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
          <feOffset></feOffset>
          <feGaussianBlur stdDeviation=".5"></feGaussianBlur>
          <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite>
          <feColorMatrix values="0 0 0 0 0.0387428 0 0 0 0 0.0406183 0 0 0 0 0.0875053 0 0 0 1 0"></feColorMatrix>
          <feBlend in2="shape" result="effect1_innerShadow"></feBlend>
        </filter>
        <filter id="gradient-{{ id }}f" width="11.199" height="2.834" x="2.4" y="7" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
          <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
          <feOffset dy=".6"></feOffset>
          <feGaussianBlur stdDeviation=".05"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0.565875 0 0 0 0 0.151272 0 0 0 0 0 0 0 0 0.15024 0"></feColorMatrix>
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow"></feBlend>
          <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feBlend>
        </filter>
      </defs>
    </svg>
{% endcase %}
