

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557276193259717464.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557276193259717464.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557276193259717464.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557276193259717464.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557276193259717464.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557276193259717464.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557276193259717464.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557276193259717464.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557276193259717464.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557276193259717464.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-557276193259717464.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557276193259717464.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557276193259717464.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557276193259717464.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557276193259717464.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557276193259717464.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557276193259717464.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557276193259717464.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557276193259717464.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557276193259717464.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557276193259717464.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557276193259717464.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557276193259717464.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557276193259717464.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557276193259717464.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557276193259717464.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557276193259717464.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557276193259717464.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-557276193259717464.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557276193259717464.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557276193259717464.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-557276193259717464.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557276193259717464.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-557276193259717464.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557276193259717464.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557276193259717464.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557276193259717464.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557276193259717464.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-557276193259717464 .gp-relative{position:relative}.gps-557276193259717464 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557276193259717464 .gp-mb-0{margin-bottom:0}.gps-557276193259717464 .gp-flex{display:flex}.gps-557276193259717464 .gp-grid{display:grid}.gps-557276193259717464 .\!gp-hidden{display:none!important}.gps-557276193259717464 .gp-hidden{display:none}.gps-557276193259717464 .gp-max-w-full{max-width:100%}.gps-557276193259717464 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557276193259717464 .gp-flex-col{flex-direction:column}.gps-557276193259717464 .gp-gap-y-0{row-gap:0}.gps-557276193259717464 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259717464 .gp-duration-200{transition-duration:.2s}.gps-557276193259717464 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-557276193259717464 .tablet\:\!gp-hidden{display:none!important}.gps-557276193259717464 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-557276193259717464 .mobile\:\!gp-hidden{display:none!important}.gps-557276193259717464 .mobile\:gp-hidden{display:none}}.gps-557276193259717464 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gqthkbgkvy" data-id="gqthkbgkvy"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:auto;--pb:auto;--mb-mobile:-190px;--pt-mobile:auto;--pb-mobile:var(--g-s-2xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gqthkbgkvy gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gVEsyjHv4d gp-relative gp-flex gp-flex-col"
    >
      
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 2",
    "tag": "section",
    "class": "gps-557276193259717464 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=557276193259717464)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
