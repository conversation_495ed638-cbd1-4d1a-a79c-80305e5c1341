<style>
    #announcement {
        text-align: center;
        padding: 5px 0;
        color: white;
        width: 100%;
        background-color: #006B6B;
    }

    #announcement.active {
        display: block;
    }

    .announcementBodyDesktop {
      display: flex;
      max-width: 1000px;
      margin: 0 auto;
      gap: 25px;
      justify-content: center;
      align-items: center;
      font-size: 15px;
    }

    .announcementBodyWrapItem {
        width: fit-content;
        min-height: 100%;
        height: 100%;
        text-align: center;
        display: flex;
        gap: 5px;
        justify-content: center;
        align-items: center;
        font-size: 6px;
        text-transform: uppercase;
    }

    .design2__wrapper{
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 6px;
    }
  
    .announcementBodyDesktop__button{
    font-size: 8px;
    font-weight: 700;
    line-height: normal;
    padding: 8px 10px;
    border-radius: 6px;
    text-decoration: none;
    background: #C7393C;
    color: #ffffff;
    }

      .announcementBodyDesktop__newRelease{
      font-size: 14px;
      font-weight: 600;
      line-height: normal;
      padding: 8px 10px;
      border-radius: 6px;
      background: #ffffff;
      color: #080D3F;
    }

    .announcementBodyWrapItemTxt {
        white-space: nowrap;
        font-size: 14px;
    }

    .announcementBodyWrapItemDivider {
        width: 1px;
        height: 15px;
        background-color: white;
    }

    #announcementBody {
        display: none;
    }

    @media (max-width: 1000px) {
        #announcementBody {
            width: 100%;
            padding: 0 24px;
            font-size: 16px;
        }

        #announcementBody {
            font-size: 18px;
            margin: 0 auto;
            background-color: unset;
            border: none;
            display: flex;
        }

        #announcementBodyWrap {
            -webkit-transition-timing-function: linear !important;
            transition-timing-function: linear !important;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .announcementBodyWrapItem:after {
            content: "";
            display: block;
            width: 1px;
            height: 15px;
            margin: 0 auto 0 40px;
            background-color: white;
        }
      .announcementBodyDesktop__button,
      .announcementBodyDesktop,
      .announcementBodyDesktop__newRelease{
        font-size: 12px;
      }
      .announcementBodyDesktop{
        gap: 8px;
      }
    }
</style>

<style>
    #countdown {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
    }
    .time {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #006B6B;
      border-radius: 2px;
      width: 28px !important;
      height: 32px !important;
    }
    .time span:first-child {
      font-size: 14px;
      font-weight: 700;
      line-height: 12px;
      text-align: center;
      margin-bottom: 3px;
      color: #fffff;
    }
    .time span:last-child {
      font-size: 9px;
      font-weight: 400;
      line-height: 8px;
      text-align: center;
      color: #fffff;
    }
    .announcement-title {
      display: inline-block;
      width: auto;
      white-space: nowrap;
    }
    @media (max-width: 767.98px) {
      .announcement-title {
        width: 250px;
        white-space: normal;
        word-wrap: break-word;
      }
      .design1__span{
        width: 200px;
      }
    }
</style>

<div id="announcement" {% if section.settings.announcement %} class="active" {% endif %}>
    {% case section.settings.design %}
    {% when "design1" %}
      <style>
        .announcementBodyDesktop{
          gap: 18px;
        }
      </style>
        <div class="announcementBodyDesktop">
            <span class="design1__span">{{ section.settings.design1_message }}</span>
              <span id="countdown">
                <span id="days"></span>
                <span id="hours"></span>
                <span id="minutes"></span>
                <span id="seconds"></span>
              </span>
          
        </div>{% when "design2" %}
        <!-- General Announcement Design -->
        <div class="announcementBodyDesktop">
        <div class="design2__wrapper">
            <img src="{{ section.settings.design2_image | image_url }}" style="height:12px;width:12px;">
            <span>{{ section.settings.design2_message }}</span>
        </div>
            <a href="{{ section.settings.link }}"> 
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M1 2H3.5V3H1V7H5V4.5H6V7C6 7.26522 5.89464 7.51957 5.70711 7.70711C5.51957 7.89464 5.26522 8 5 8H1C0.734784 8 0.48043 7.89464 0.292893 7.70711C0.105357 7.51957 0 7.26522 0 7V3C0 2.73478 0.105357 2.48043 0.292893 2.29289C0.48043 2.10536 0.734784 2 1 2ZM6.307 1H4V0H8V4H7V1.721L4.332 4.389L3.625 3.682L6.307 1Z" fill="white"/>
              </svg>
            </a>
        </div>
    {% when "design3" %}
        <!-- Wellness Challenge Design -->
        <div class="announcementBodyDesktop">
            <span>{{ section.settings.design3_message }}</span>
            <a href="{{ section.settings.link }}" class="announcementBodyDesktop__button">{{ section.settings.design3_buttontext }}</a>
        </div>
    {% when "design4" %}
        <!-- New Product Release Design -->
        <div class="announcementBodyDesktop">
        <div class="announcementBodyDesktop__newRelease">
          New Release
        </div>
            <span>{{ section.settings.design4_message }}</span>
            <a href="{{ section.settings.link }}"> 
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M1 2H3.5V3H1V7H5V4.5H6V7C6 7.26522 5.89464 7.51957 5.70711 7.70711C5.51957 7.89464 5.26522 8 5 8H1C0.734784 8 0.48043 7.89464 0.292893 7.70711C0.105357 7.51957 0 7.26522 0 7V3C0 2.73478 0.105357 2.48043 0.292893 2.29289C0.48043 2.10536 0.734784 2 1 2ZM6.307 1H4V0H8V4H7V1.721L4.332 4.389L3.625 3.682L6.307 1Z" fill="white"/>
              </svg>
            </a>
        </div>
    {% else %}
    {% endcase %}
</div>

<script>
    document.addEventListener("DOMContentLoaded", async function () {
        new Swiper(`#announcementBody`, {
            spaceBetween: 40,
            centeredSlides: true,
            speed: {{ section.settings.speed }},
            autoplay: {
                delay: 0,
            },
            loop: true,
            slidesPerView: 'auto',
            allowTouchMove: false,
            disableOnInteraction: true,
        });
    }())
</script>

<!-- Countdown Script -->
<script defer>
  document.addEventListener('DOMContentLoaded', function () {
    function startCountdown(endTime) {
      function updateCountdown() {
        const now = new Date().getTime();
        const distance = endTime - now;
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        document.getElementById('days').innerHTML =
          `<div class="time"><span>${days}</span><span>DAYS</span></div>`;
        document.getElementById('hours').innerHTML =
          `<div class="time"><span>${hours}</span><span>HRS</span></div>`;
        document.getElementById('minutes').innerHTML =
          `<div class="time"><span>${minutes}</span><span>MIN</span></div>`;
        document.getElementById('seconds').innerHTML =
          `<div class="time"><span>${seconds}</span><span>SEC</span></div>`;
        
        if (distance < 0) {
          clearInterval(interval);
          document.getElementById('countdown').innerHTML = "EXPIRED";
        }
      }
      
      updateCountdown();
      const interval = setInterval(updateCountdown, 1000);
    }
    
    // Dynamically set end time from schema settings
    const year = {{ section.settings.countdown_year | json }};
    const month = {{ section.settings.countdown_month | json }} - 1; // zero-indexed in JS
    const day = {{ section.settings.countdown_day | json }};
    const hour = {{ section.settings.countdown_hour | json }};
    const minute = {{ section.settings.countdown_minute | json }};

    const endTime = new Date(year, month, day, hour, minute, 0, 0).getTime();
    startCountdown(endTime);
  });
</script>

{% schema %}
{
  "name": "Announcement Bar",
  "settings": [
    {
      "type": "checkbox",
      "id": "announcement",
      "label": "Show Announcement Bar",
      "default": true
    },
    {
      "type": "range",
      "id": "speed",
      "label": "Speed",
      "min": 0,
      "max": 9000,
      "step": 500,
      "default": 5000,
      "info": "Higher number = slower speed"
    },
{
      "type": "select",
      "id": "design",
      "label": "Select Design",
      "options": [
        { "value": "design1", "label": "Sale With Countdown" },
        { "value": "design2", "label": "No Sale - General v1" },
        { "value": "design3", "label": "No Sale - General v2" },
        { "value": "design4", "label": "New Product Release" }
      ],
      "default": "design1"
    },
    {
      "type": "inline_richtext",
      "id": "design1_message",
      "label": "Design 1: Sale With Countdown Message",
      "default": "<strong>New Year Special</strong> Up to 20% off"
    },
    {
      "type": "number",
      "id": "countdown_day",
      "label": "Countdown Day",
      "default": 31
    },
    {
      "type": "number",
      "id": "countdown_month",
      "label": "Countdown Month",
      "default": 12
    },
    {
      "type": "number",
      "id": "countdown_year",
      "label": "Countdown Year",
      "default": 2024
    },
    {
      "type": "number",
      "id": "countdown_hour",
      "label": "Countdown Hour",
      "default": 23
    },
    {
      "type": "number",
      "id": "countdown_minute",
      "label": "Countdown Minute",
      "default": 59
    },
    {
      "type": "inline_richtext",
      "id": "design2_message",
      "label": "Design 2: No Sale - General v1 Message",
      "default": "Join 15,000+ Happy Customers"
    },
    {
      "type": "image_picker",
      "id": "design2_image",
      "label": "Design 2: No Sale - General Image"
    },
    {
      "type": "inline_richtext",
      "id": "design3_message",
      "label": "Design 3: No Sale - General v2 Message",
      "default": "Join our New Year Challenge"
    },
    {
      "type": "text",
      "id": "design3_buttontext",
      "label": "Design 3: Button Text",
      "default": "Join Now"
    },
    {
      "type": "inline_richtext",
      "id": "design4_message",
      "label": "Design 4: New Product Release Message",
      "default": "Our 2025 Collection Launched"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Link"
    }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text"
        }
      ]
    }
  ]
}
{% endschema %}