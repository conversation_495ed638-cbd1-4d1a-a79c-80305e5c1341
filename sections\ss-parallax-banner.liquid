{% comment %}
---------------------------------------------------------
Copyright © 2023 Section Store. All rights reserved.
Unauthorized copying, modification, distribution, or use
of this code or any portion of it, is strictly prohibited.
Violators will be prosecuted to the fullest extent of the law.
For inquiries or permissions, contact <EMAIL>
---------------------------------------------------------
{% endcomment %}

{%- liquid 
  assign padding_horizontal = section.settings.padding_horizontal
  assign padding_horizontal_mobile = section.settings.padding_horizontal_mobile
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign border_color = section.settings.border_color
  assign border_thickness = section.settings.border_thickness
  assign margin_top = section.settings.margin_top
  assign margin_bottom = section.settings.margin_bottom
  assign background_color = section.settings.background_color
  assign background_gradient = section.settings.background_gradient
  assign full_width = section.settings.full_width
  assign content_width = section.settings.content_width
  assign lazy = section.settings.lazy

  assign parallax = section.settings.parallax
  assign parallax_mobile = section.settings.parallax_mobile

  assign banner_height = section.settings.banner_height
  assign banner_fixed_height = section.settings.banner_fixed_height
  assign banner_fixed_height_mobile = section.settings.banner_fixed_height_mobile

  assign image = section.settings.image
  assign image_mobile = section.settings.image_mobile

  assign overlay = section.settings.overlay
  assign overlay_color = section.settings.overlay_color

  assign content_maxwidth_mobile = section.settings.content_maxwidth_mobile
  assign content_maxwidth = section.settings.content_maxwidth
  assign content_vertical_position_mobile = section.settings.content_vertical_position_mobile
  assign content_vertical_position = section.settings.content_vertical_position
  assign content_horizontal_position_mobile = section.settings.content_horizontal_position_mobile
  assign content_horizontal_position = section.settings.content_horizontal_position
  assign content_padding_vertical_mobile = section.settings.content_padding_vertical_mobile
  assign content_padding_vertical = section.settings.content_padding_vertical
  assign content_padding_horizontal_mobile = section.settings.content_padding_horizontal_mobile
  assign content_padding_horizontal = section.settings.content_padding_horizontal

  assign text_align_mobile = ""
  if content_horizontal_position_mobile == "start"
    assign text_align_mobile = "left"
  elsif content_horizontal_position_mobile == "center"
    assign text_align_mobile = "center"
  else
  assign text_align_mobile = "right"
  endif

  assign text_align = ""
  if content_horizontal_position == "start"
    assign text_align = "left"
  elsif content_horizontal_position == "center"
    assign text_align = "center"
  else
  assign text_align = "right"
  endif

  
-%}

{%- style -%}
  
  .section-{{ section.id }} {
    border-top: solid {{ border_color }} {{ border_thickness }}px;
    border-bottom: solid {{ border_color }} {{ border_thickness }}px;
    margin-top: {{ margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ margin_bottom | times: 0.75 | round: 0 }}px;
    overflow: hidden;
  }
  
  .section-{{ section.id }}-settings {
    margin: 0 auto;
    padding-top: {{ padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: {{ padding_horizontal_mobile }}rem;
    padding-right: {{ padding_horizontal_mobile }}rem;
    height: 100%;
    position: relative;
  }

  .banner-image-{{ section.id }} {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .banner-image-inner-{{ section.id }} {
    height: 100%;
  }

  .banner-image-{{ section.id }} img,
  .banner-image-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .banner-image-{{ section.id }} svg {
    background-color: #e8e8e8;
  }

  .banner-image-mobile-{{ section.id }},
  .banner-image-desktop-{{ section.id }} {
    height: 100%;
  }

  .banner-image-desktop-{{ section.id }} {
    display: none;
  }

  .banner-image-{{ section.id }}:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: {{ overlay_color | hex_to_rgba: overlay }};
    z-index: 1;
  }

  .banner-body-{{ section.id }} {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: {{ content_vertical_position_mobile }};
    align-items: {{ content_horizontal_position_mobile }};
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
  }

  .banner-content-{{ section.id }} {
    width: 100%;
    max-width: {{ content_maxwidth_mobile }}%;
    padding: {{ content_padding_vertical_mobile }}px {{ content_padding_horizontal_mobile }}px;
    text-align: {{ text_align_mobile }};
  }

  .banner-button-{{ section.id }} {
    margin: 10px;
  }

  .banner-parallax-mobile-{{ section.id }} {
    display: block;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
  }

  .banner-parallax-mobile-{{ section.id }} img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .banner-parallax-mobile-{{ section.id }} * {
    height: 100%;
  }

  .banner-parallax-desktop-{{ section.id }} > * {
    height: 100%;
  }
  
  @media(min-width: 1024px) {

    .section-{{ section.id }} {
      margin-top: {{ margin_top }}px;
      margin-bottom: {{ margin_bottom }}px;
    }
    
    .section-{{ section.id }}-settings {
      padding: 0 5rem;
      padding-top: {{ padding_top }}px;
      padding-bottom: {{ padding_bottom }}px;
      padding-left: {{ padding_horizontal }}rem;
      padding-right: {{ padding_horizontal }}rem;
    }

    .banner-image-desktop-{{ section.id }} {
      display: block;
    }

    .banner-image-mobile-{{ section.id }} {
      display: none;
    }

    .banner-body-{{ section.id }} {
      justify-content: {{ content_vertical_position }};
      align-items: {{ content_horizontal_position }};
    }

    .banner-content-{{ section.id }} {
      max-width: {{ content_maxwidth }}%;
      padding: {{ content_padding_vertical }}px {{ content_padding_horizontal }}px;
      text-align: {{ text_align }};
    }
  }

  @media only screen and (hover: none) and (pointer: coarse){
    .banner-parallax-{{ section.id }} {
        background-attachment: scroll;
    }
  }  
  
{%- endstyle -%}

{% unless full_width %}
  <style>
    .section-{{ section.id }}-settings {
      max-width: {{ content_width }}rem;
    }
  </style>
{% endunless %}

{% if banner_height == "adapt_first" %}
  <style>
    .banner-{{ section.id }} {
      height: 100vh;
    }
  </style>
{% elsif banner_height == "auto_height" %}
  <style>

   .banner-{{ section.id }} {
      height: auto;
    }

    .banner-body-{{ section.id }} {
      position: relative;
    }

  </style>
{% else %}
  <style>
    .banner-{{ section.id }} {
      height: {{ banner_fixed_height_mobile }}px;
    }
  
    @media(min-width: 768px) {
      .banner-{{ section.id }} {
        height: {{ banner_fixed_height }}px;
      }
    }
  </style>
{% endif %}

{% for block in section.blocks %}

  {% if block.type == "heading" %}

    <style>
      
      {{ block.settings.heading_font | font_face: font_display: 'swap' }}
      
      .banner-heading-{{ block.id }} {
        margin-top: {{ block.settings.heading_mt | times: 0.75 | round: 0 }}px;
        text-align: {{ text_align_mobile }};
      }
    
      .banner-heading-{{ block.id }} * {
        margin: 0;
        font-size: {{ block.settings.heading_size_mobile }}px;
        color: {{ block.settings.heading_color }};
        line-height: {{ block.settings.heading_height }}%;
        text-transform: unset;
      }

      @media(min-width: 1024px) {
        
        .banner-heading-{{ block.id }} {
          margin-top: {{ block.settings.heading_mt }}px;
          text-align: {{ text_align }};
        }
      
        .banner-heading-{{ block.id }} * {
          font-size: {{ block.settings.heading_size }}px;
        }
      }
    </style>

    {% if block.settings.heading_custom %}
      <style>
        .banner-heading-{{ block.id }} * {
          font-family: {{ block.settings.heading_font.family }}, {{ block.settings.heading_font.fallback_families }};
          font-weight: {{ block.settings.heading_font.weight }};
          font-style: {{ block.settings.heading_font.style }};
        }
      </style>
    {% endif %}

  {% elsif block.type == "text" %}

    <style>

      {{ block.settings.text_font | font_face: font_display: 'swap' }}

      .banner-text-{{ block.id }} {
        margin-top: {{ block.settings.text_mt | times: 0.75 | round: 0 }}px;
        text-align: {{ text_align_mobile }};
      }
    
      .banner-text-{{ block.id }} * {
        margin: 0;
        font-size: {{ block.settings.text_size_mobile }}px;
        color: {{ block.settings.text_color }};
        line-height: {{ block.settings.text_height }}%;
        text-transform: unset;
      }

      @media(min-width: 1024px) {
        .banner-text-{{ block.id }} {
          margin-top: {{ block.settings.text_mt }}px;
          text-align: {{ text_align }};
        }
    
        .banner-text-{{ block.id }} * {
          font-size: {{ block.settings.text_size }}px;
        }
      }
      
    </style>

    {% if block.settings.text_custom %}
      <style>
        .banner-text-{{ block.id }} * {
          font-family: {{ block.settings.text_font.family }}, {{ block.settings.text_font.fallback_families }};
          font-weight: {{ block.settings.text_font.weight }};
          font-style: {{ block.settings.text_font.style }};
        }
      </style>
    {% endif %}

  {% elsif block.type == "button" %}

    <style>
      
      {{ block.settings.button_font | font_face: font_display: 'swap' }}

      .banner-button-{{ block.id }} {
        margin-top: {{ block.settings.button_mt | times: 0.75 | round: 0 }}px !important;
        font-size: {{ block.settings.button_size_mobile }}px;
        font-weight: 700;
        color: {{ block.settings.button_color }};
        line-height: {{ block.settings.button_height }}%;
        text-transform: unset;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        max-width: fit-content;
        text-align: center;
        text-transform: unset;
        text-decoration: none;
        padding: {{ block.settings.button_padding_vertical | times: 0.75 | round: 0 }}px {{ block.settings.button_padding_horizontal | times: 0.75 | round: 0 }}px;
        border-radius: {{ block.settings.button_radius }}px;
        gap: {{ block.settings.button_gap | times: 0.75 | round: 0 }}px;
        transition: all 0.25s ease 0s;
        position: relative;
        z-index: 2;
      }

      .banner-button-{{ block.id }}:hover {
        color: {{ block.settings.button_hover_color }};
        transition: all 0.25s ease 0s;
      }
    
      .banner-button-icon-{{ block.id }} {
        display: block;
        width: 16px;
        height: 16px;
      }
    
      .banner-button-icon-{{ block.id }} svg {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    
      .banner-button-{{ block.id }} svg path {
        stroke: {{ block.settings.button_color }};
        transition: all 0.25s ease 0s;
      }
    
      .banner-button-{{ block.id }}:hover svg path {
        stroke: {{ block.settings.button_hover_color }};
        transition: all 0.25s ease 0s;
      }

      @media(min-width: 1024px) {
        
        .banner-button-{{ block.id }} {
          margin-top: {{ block.settings.button_mt }}px !important;
          gap: {{ block.settings.button_gap }}px;
          padding: {{ block.settings.button_padding_vertical }}px {{ block.settings.button_padding_horizontal }}px;
          font-size: {{ block.settings.button_size }}px;
        }
        
      }
      
    </style>

    {% if block.settings.button_custom %}
      <style>
        .banner-button-{{ block.id }} {
          font-family: {{ block.settings.button_font.family }}, {{ block.settings.button_font.fallback_families }};
          font-weight: {{ block.settings.button_font.weight }};
          font-style: {{ block.settings.button_font.style }};
        }
      </style>
    {% endif %}

    {% if block.settings.button_ellipse %}
      <style>
        .banner-button-{{ block.id }} {
          border-radius: 100% / 100%;
        }
      </style>
    {% endif %}

    {% if block.settings.button_shadow %}
      <style>
        .banner-button-{{ block.id }} {
          box-shadow: 0 8px 16px 2px {{ block.settings.button_shadow_color | hex_to_rgba: 0.33 }};
        }
      </style>
    {% endif %}

    {% if block.settings.button_style == "non_outline" or block.settings.button_style == "non_outline_arrow" %}
      <style>
        .banner-button-{{ block.id }} {
          background-color: {{ block.settings.button_bg_color }};
        }
    
        .banner-button-{{ block.id }}:hover {
          background-color: {{ block.settings.button_bg_hover_color }};
        }
      </style>
    {% elsif block.settings.button_style == "outline" or block.settings.button_style == "outline_arrow" %}
      <style>
        .banner-button-{{ block.id }} {
          background-color: {{ block.settings.button_bg_color }};
          border: {{ block.settings.button_border_thickness }}px solid {{ block.settings.button_border_color }};
        }
    
        .banner-button-{{ block.id }}:hover {
          background-color: {{ block.settings.button_bg_hover_color }};
          border: {{ block.settings.button_border_thickness }}px solid {{ block.settings.button_border_hover_color }};
        }
      </style>
    {% endif %}
    
  {% endif %}
  
{% endfor %}

<div class="section-{{ section.id }} banner-{{ section.id }}" style="background-color:{{ background_color }}; background-image: {{ background_gradient }};">
    <div class="section-{{ section.id }}-settings">
      <div class="banner-image-{{ section.id }}">
        <div class="banner-image-mobile-{{ section.id }} {% if parallax_mobile %}banner-parallax-mobile-{{ section.id }}{% endif %}">
          {% if image_mobile != blank %}
            <img src="{{ image_mobile | image_url }}" alt="{{ image_mobile.alt }}" {% if lazy %}loading="lazy"{% endif %}>
          {% elsif image != blank %}
            <img src="{{ image | image_url }}" alt="{{ image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
          {% else %}
            {{ 'lifestyle-2' | placeholder_svg_tag  }}
          {% endif %}
        </div>
        <div class="banner-image-desktop-{{ section.id }} {% if parallax %}banner-parallax-desktop-{{ section.id }}{% endif %}">
          {% if image != blank %}
            <img src="{{ image | image_url }}" alt="{{ image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
          {% else %}
            {{ 'lifestyle-2' | placeholder_svg_tag  }}
          {% endif %}
        </div>
      </div>

      <div class="banner-body-{{ section.id }}">

        <div class="banner-content-{{ section.id }}">
          {% for block in section.blocks %}
            {% if block.type == "heading" %}
              
              {% if block.settings.heading != blank %}
                <div class="banner-heading-{{ block.id }}">{{ block.settings.heading }}</div>
              {% endif %}
  
            {% elsif block.type == "text" %}
              
              {% if block.settings.text != blank %}
                <div class="banner-text-{{ block.id }}">{{ block.settings.text }}</div>
              {% endif %}
  
            {% elsif block.type == "button" %}
              
              {% if block.settings.button != blank %}
                <a href="{{ block.settings.button_url }}" class="banner-button-{{ block.id }} banner-button-{{ section.id }}">
                  {{ block.settings.button }}
                  {% if block.settings.button_style == "non_outline_arrow" or block.settings.button_style == "outline_arrow" %}
                    <span class="banner-button-icon-{{ block.id }}">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.75 12H20.25" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M13.5 5.25L20.25 12L13.5 18.75" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </span>
                  {% endif %}
                </a>
              {% endif %}
  
            {% endif %}
            
          {% endfor %}
        </div>
      </div>
    </div>
</div>
                          
{% if parallax_mobile or parallax %}
  <script src="https://cdn.jsdelivr.net/npm/simple-parallax-js@5.5.1/dist/simpleParallax.min.js"></script>
  <script>
    function initParallax() {
      var image = document.querySelector('.banner-parallax-mobile-{{ section.id }} img');
      if(image) {
        new simpleParallax(image, {
          scale: 1.5
        }); 
      }

      var image_desktop = document.querySelector('.banner-parallax-desktop-{{ section.id }} img');
      if(image_desktop) {
        new simpleParallax(image_desktop, {
          scale: 1.5
        }); 
      }
    }

    document.addEventListener('DOMContentLoaded', initParallax);
    
    if (Shopify.designMode) {
      document.addEventListener('shopify:section:unload', initParallax);
      document.addEventListener('shopify:section:load', initParallax);
    }
  </script>
{% endif %}

{% schema %}
  {
    "name": "SS - Parallax Banner",
    "settings": [
      {
        "type": "header",
        "content": "Banner Settings"
      },
      {
        "type": "checkbox",
        "id": "parallax",
        "label": "Enable Parallax Effect (Desktop)",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "parallax_mobile",
        "label": "Enable Parallax Effect (Mobile)",
        "default": false
      },
      {
        "type": "select",
        "id": "banner_height",
        "label": "Banner Height",
        "default": "adapt_first",
        "options": [
          {
            "label": "Adapt to first",
            "value": "adapt_first"
          },
          {
            "label": "Fixed Height",
            "value": "fixed_height"
          },
          {
            "label": "Auto Height",
            "value": "auto_height"
          }
        ]
      },
      {
        "type": "range",
        "id": "banner_fixed_height",
        "min": 200,
        "max": 1000,
        "step": 10,
        "unit": "px",
        "label": "Banner Fixed Height",
        "default": 600
      },
      {
        "type": "range",
        "id": "banner_fixed_height_mobile",
        "min": 100,
        "max": 1000,
        "step": 10,
        "unit": "px",
        "label": "Banner Fixed Height - Mobile",
        "default": 500
      },
      {
        "type": "header",
        "content": "Image Settings"
      },
      {
        "type": "image_picker",
        "id": "image",
        "label": "Image"
      },
      {
        "type": "image_picker",
        "id": "image_mobile",
        "label": "Image - Mobile"
      },
      {
        "type": "range",
        "id": "overlay",
        "min": 0,
        "max": 1,
        "step": 0.1,
        "label": "Image Overlay Opacity",
        "default": 0.2
      },
      {
        "type": "header",
        "content": "Content Settings"
      },
      {
         "type": "range",
         "id": "content_maxwidth",
         "min": 20,
         "max": 100,
         "step": 1,
         "unit": "%",
         "label": "Content Width",
         "default": 65
      },
      {
         "type": "range",
         "id": "content_maxwidth_mobile",
         "min": 10,
         "max": 100,
         "step": 1,
         "unit": "%",
         "label": "Content Width - Mobile",
         "default": 100
      },
      {
        "type": "select",
        "id": "content_horizontal_position",
        "label": "Content Horizontal Position",
        "default": "center",
        "options": [
          {
            "label": "Left",
            "value": "start"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "end"
          }
        ]
      },
      {
        "type": "select",
        "id": "content_horizontal_position_mobile",
        "label": "Content Horizontal Position - Mobile",
        "default": "center",
        "options": [
          {
            "label": "Left",
            "value": "start"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "end"
          }
        ]
      },
      {
        "type": "select",
        "id": "content_vertical_position",
        "label": "Content Vertical Position",
        "info": "If 'Banner Height' is not Auto Height!",
        "default": "center",
        "options": [
          {
            "label": "Top",
            "value": "start"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Bottom",
            "value": "end"
          }
        ]
      },
      {
        "type": "select",
        "id": "content_vertical_position_mobile",
        "label": "Content Vertical Position - Mobile",
        "default": "center",
        "info": "If 'Banner Height' is not Auto Height!",
        "options": [
          {
            "label": "Top",
            "value": "start"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Bottom",
            "value": "end"
          }
        ]
      },
      {
        "type": "range",
        "id": "content_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Content Padding Vertical",
        "default": 32
      },
      {
        "type": "range",
        "id": "content_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Content Padding Vertical - Mobile",
        "default": 20
      },
      {
         "type": "range",
         "id": "content_padding_horizontal",
         "min": 0,
         "max": 150,
         "step": 2,
         "unit": "px",
         "label": "Content Padding Horizontal",
         "default": 20
      },
      {
         "type": "range",
         "id": "content_padding_horizontal_mobile",
         "min": 0,
         "max": 150,
         "step": 2,
         "unit": "px",
         "label": "Content Padding Horizontal - Mobile",
         "default": 20
      },
      {
        "type": "header",
        "content": "Section Colors"
      },
      {
        "type": "color",
        "label": "Overlay Color",
        "id": "overlay_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Section background",
        "id": "background_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color_background",
        "id": "background_gradient",
        "label": "Section background gradient"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "border_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Section margin (outside)"
      },
      {
        "type": "range",
        "id": "margin_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin top",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_bottom",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin bottom",
        "default": 0
      },
      {
        "type": "header",
        "content": "Section padding (inside)"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Padding top",
        "default": 36
      },
      {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Padding bottom",
         "default": 36
      },
      {
        "type": "range",
        "id": "padding_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Padding sides",
        "default": 5
      },
      {
        "type": "range",
        "id": "padding_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Padding sides mobile",
        "default": 1.5
      },
      {
        "type": "header",
        "content": "Section Settings"
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": true
      },
      {
        "type": "range",
        "id": "content_width",
        "min": 0,
        "max": 400,
        "step": 10,
        "unit": "rem",
        "label": "Section content width",
        "default": 120
      },
      {
        "type": "range",
        "id": "border_thickness",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "lazy",
        "label": "Lazy load",
        "info": "Lazy load images for speed optimisation",
        "default": true
      }
    ],
    "blocks": [
      {
        "type": "heading",
        "name": "Heading",
        "limit": 1,
        "settings": [
          {
            "type": "header",
            "content": "Heading Settings"
          },
          {
            "type": "richtext",
            "id": "heading",
            "label": "Heading",
            "default": "<h2>Parallax Banner</h2>"
          },
          {
            "type": "checkbox",
            "id": "heading_custom",
            "label": "Use Custom Font",
            "default": false
          },
          {
            "type": "font_picker",
            "id": "heading_font",
            "label": "Heading Font Family",
            "default": "josefin_sans_n4"
          },
          {
            "type": "range",
            "id": "heading_size",
            "min": 0,
            "max": 72,
            "step": 2,
            "unit": "px",
            "label": "Heading Size",
            "default": 40
          },
          {
            "type": "range",
            "id": "heading_size_mobile",
            "min": 0,
            "max": 72,
            "step": 2,
            "unit": "px",
            "label": "Heading Size - Mobile",
            "default": 30
          },
          {
            "type": "range",
            "id": "heading_height",
            "min": 50,
            "max": 200,
            "step": 10,
            "unit": "%",
            "label": "Heading Line Height",
            "default": 130
          },
          {
            "type": "range",
            "id": "heading_mt",
            "min": 0,
            "max": 100,
            "step": 2,
            "unit": "px",
            "label": "Heading Margin Top",
            "default": 0
          },
          {
            "type": "color",
            "id": "heading_color",
            "label": "Heading Color",
            "default": "#FFFFFF"
          }
        ]
      },
      {
        "type": "text",
        "limit": 1,
        "name": "Text",
        "settings": [
          {
            "type": "header",
            "content": "Text Settings"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Add your own custom content.</p>"
          },
          {
            "type": "checkbox",
            "id": "text_custom",
            "label": "Use Custom Font",
            "default": false
          },
          {
            "type": "font_picker",
            "id": "text_font",
            "label": "Text Font Family",
            "default": "josefin_sans_n4"
          },
          {
            "type": "range",
            "id": "text_size",
            "min": 0,
            "max": 72,
            "step": 2,
            "unit": "px",
            "label": "Text Size",
            "default": 18
          },
          {
            "type": "range",
            "id": "text_size_mobile",
            "min": 0,
            "max": 72,
            "step": 2,
            "unit": "px",
            "label": "Text Size - Mobile",
            "default": 16
          },
          {
            "type": "range",
            "id": "text_height",
            "min": 50,
            "max": 200,
            "step": 10,
            "unit": "%",
            "label": "Text Line Height",
            "default": 150
          },
          {
            "type": "range",
            "id": "text_mt",
            "min": 0,
            "max": 100,
            "step": 2,
            "unit": "px",
            "label": "Text Margin Top",
            "default": 16
          },
          {
            "type": "color",
            "label": "Text Color",
            "id": "text_color",
            "default": "#FFFFFF"
          }
        ]
      },
      {
        "type": "button",
        "name": "Button",
        "limit": 2,
        "settings": [
          {
            "type": "header",
            "content": "Button Settings"
          },
          {
            "type": "text",
            "id": "button",
            "label": "Button Text",
            "default": "SHOP NOW"
          },
          {
            "type": "url",
            "id": "button_url",
            "label": "Button URL"
          },
          {
            "type": "checkbox",
            "id": "button_custom",
            "label": "Use Custom Font",
            "default": false
          },
          {
            "type": "font_picker",
            "id": "button_font",
            "label": "Button Font Family",
            "default": "josefin_sans_n4"
          },
          {
            "type": "range",
            "id": "button_size",
            "min": 0,
            "max": 72,
            "step": 2,
            "unit": "px",
            "label": "Button Size",
            "default": 14
          },
          {
            "type": "range",
            "id": "button_size_mobile",
            "min": 0,
            "max": 72,
            "step": 2,
            "unit": "px",
            "label": "Button Size - Mobile",
            "default": 14
          },
          {
            "type": "range",
            "id": "button_height",
            "min": 50,
            "max": 200,
            "step": 10,
            "unit": "%",
            "label": "Button Line Height",
            "default": 100
          },
          {
            "type": "range",
            "id": "button_padding_vertical",
            "min": 0,
            "max": 100,
            "step": 2,
            "unit": "px",
            "label": "Button Padding Vertical",
            "default": 16
          },
          {
            "type": "range",
            "id": "button_padding_vertical_mobile",
            "min": 0,
            "max": 100,
            "step": 2,
            "unit": "px",
            "label": "Button Padding Vertical - Mobile",
            "default": 16
          },
          {
             "type": "range",
             "id": "button_padding_horizontal",
             "min": 0,
             "max": 100,
             "step": 2,
             "unit": "px",
             "label": "Button Padding Horizontal",
             "default": 50
          },
          {
             "type": "range",
             "id": "button_padding_horizontal_mobile",
             "min": 0,
             "max": 100,
             "step": 2,
             "unit": "px",
             "label": "Button Padding Horizontal - Mobile",
             "default": 50
          },
          {
             "type": "range",
             "id": "button_radius",
             "min": 0,
             "max": 100,
             "step": 2,
             "unit": "px",
             "label": "Button Border Radius",
             "default": 100
          },
          {
             "type": "range",
             "id": "button_border_thickness",
             "min": 0,
             "max": 10,
             "step": 1,
             "unit": "px",
             "label": "Button Border Thickness",
             "default": 1
          },
          {
            "type": "range",
            "id": "button_mt",
            "min": 0,
            "max": 100,
            "step": 2,
            "unit": "px",
            "label": "Button Margin Top",
            "default": 16
          },
          {
            "type": "range",
            "id": "button_gap",
            "min": 0,
            "max": 100,
            "step": 2,
            "unit": "px",
            "label": "Button Gap",
            "info": "If 'Use Button Icon' is selected!",
            "default": 28
          },
          {
            "type": "checkbox",
            "id": "button_ellipse",
            "label": "Use Button Ellipse Form",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "button_shadow",
            "label": "Use Button Box Shadow",
            "default": false
          },
          {
            "type": "select",
            "id": "button_style",
            "label": "Button Style",
            "default": "outline",
            "options": [
              {
                "label": "Link",
                "value": "link"
              },
              {
                "label": "Non-Outline",
                "value": "non_outline"
              },
              {
                "label": "Non-Outline & Arrow",
                "value": "non_outline_arrow"
              },
              {
                "label": "Outline",
                "value": "outline"
              },
              {
                "label": "Outline & Arrow",
                "value": "outline_arrow"
              }
            ]
          },
          {
            "type": "header",
            "content": "Button Colors"
          },
          {
            "type": "color",
            "label": "Button Color",
            "id": "button_color",
            "default": "#FFFFFF"
          },
          {
            "type": "color",
            "label": "Button Hover Color",
            "id": "button_hover_color",
            "default": "#FFFFFF"
          },
          {
            "type": "color",
            "label": "Button Background Color",
            "id": "button_bg_color",
            "default": "#000000"
          },
          {
            "type": "color",
            "label": "Button Background Hover Color",
            "id": "button_bg_hover_color",
            "default": "#000000"
          },
          {
            "type": "color",
            "label": "Button Border Color",
            "id": "button_border_color",
            "default": "#000000"
          },
          {
            "type": "color",
            "label": "Button Border Hover Color",
            "id": "button_border_hover_color",
            "default": "#000000"
          },
          {
            "type": "color",
            "label": "Button Shadow Color",
            "id": "button_shadow_color",
            "default": "#000000"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "SS - Parallax Banner",
        "blocks": [
          {
            "type": "heading"
          },
          {
            "type": "text"
          },
          {
            "type": "button"
          }
        ]
      }
    ]
  }
{% endschema %}