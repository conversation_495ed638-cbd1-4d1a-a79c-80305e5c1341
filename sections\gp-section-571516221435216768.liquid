

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-571516221435216768.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-571516221435216768.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-571516221435216768.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-571516221435216768.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-571516221435216768.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-571516221435216768.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-571516221435216768.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-571516221435216768.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-571516221435216768.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-571516221435216768.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-571516221435216768.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-571516221435216768.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-571516221435216768.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-571516221435216768.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-571516221435216768.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-571516221435216768.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-571516221435216768.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-571516221435216768.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-571516221435216768.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-571516221435216768.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-571516221435216768.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-571516221435216768.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-571516221435216768.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-571516221435216768.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-571516221435216768.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-571516221435216768.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-571516221435216768.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-571516221435216768.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-571516221435216768.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-571516221435216768.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-571516221435216768.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-571516221435216768.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-571516221435216768.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-571516221435216768.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-571516221435216768.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-571516221435216768.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-571516221435216768.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-571516221435216768.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-571516221435216768.gps.gpsil [style*="--z:"]{z-index:var(--z)}@media only screen and (max-width:1024px){.gps-571516221435216768.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-571516221435216768.gps.gpsil [style*="--bgi-tablet:"]{background-image:var(--bgi-tablet)}.gps-571516221435216768.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-571516221435216768.gps.gpsil [style*="--pc-tablet:"]{place-content:var(--pc-tablet)}.gps-571516221435216768.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-571516221435216768.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-571516221435216768.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-571516221435216768.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-571516221435216768.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-571516221435216768.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-571516221435216768.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-571516221435216768.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-571516221435216768.gps.gpsil [style*="--bga-mobile:"]{background-attachment:var(--bga-mobile)}.gps-571516221435216768.gps.gpsil [style*="--bgc-mobile:"]{background-color:var(--bgc-mobile)}.gps-571516221435216768.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-571516221435216768.gps.gpsil [style*="--bgp-mobile:"]{background-position:var(--bgp-mobile)}.gps-571516221435216768.gps.gpsil [style*="--bgr-mobile:"]{background-repeat:var(--bgr-mobile)}.gps-571516221435216768.gps.gpsil [style*="--bgs-mobile:"]{background-size:var(--bgs-mobile)}.gps-571516221435216768.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-571516221435216768.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-571516221435216768.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-571516221435216768.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-571516221435216768.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-571516221435216768.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-571516221435216768.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-571516221435216768.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-571516221435216768 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-571516221435216768 .gp-absolute{position:absolute}.gps-571516221435216768 .gp-relative{position:relative}.gps-571516221435216768 .gp-z-1{z-index:1}.gps-571516221435216768 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-571516221435216768 .gp-mb-0{margin-bottom:0}.gps-571516221435216768 .gp-flex{display:flex}.gps-571516221435216768 .gp-inline-flex{display:inline-flex}.gps-571516221435216768 .gp-grid{display:grid}.gps-571516221435216768 .\!gp-hidden{display:none!important}.gps-571516221435216768 .gp-hidden{display:none}.gps-571516221435216768 .gp-h-full{height:100%}.gps-571516221435216768 .gp-w-full{width:100%}.gps-571516221435216768 .gp-max-w-full{max-width:100%}.gps-571516221435216768 .gp-shrink-0{flex-shrink:0}.gps-571516221435216768 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-571516221435216768 .gp-flex-row-reverse{flex-direction:row-reverse}.gps-571516221435216768 .gp-flex-col{flex-direction:column}.gps-571516221435216768 .gp-items-center{align-items:center}.gps-571516221435216768 .\!gp-justify-center{justify-content:center!important}.gps-571516221435216768 .gp-justify-center{justify-content:center}.gps-571516221435216768 .gp-gap-y-0{row-gap:0}.gps-571516221435216768 .gp-overflow-hidden{overflow:hidden}.gps-571516221435216768 .gp-break-words{overflow-wrap:break-word}.gps-571516221435216768 .gp-rounded-none{border-radius:0}.gps-571516221435216768 .gp-bg-g-brand{background-color:var(--g-c-brand)}.gps-571516221435216768 .gp-text-center{text-align:center}.gps-571516221435216768 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-571516221435216768 .gp-no-underline{text-decoration-line:none}.gps-571516221435216768 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-571516221435216768 .gp-duration-200{transition-duration:.2s}.gps-571516221435216768 .gp-duration-300{transition-duration:.3s}.gps-571516221435216768 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-571516221435216768 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-571516221435216768 .hover\:gp-bg-g-highlight:hover{background-color:var(--g-c-highlight)}}.gps-571516221435216768 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-571516221435216768 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-571516221435216768 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-571516221435216768 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-571516221435216768 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-571516221435216768 .tablet\:gp-absolute{position:absolute}.gps-571516221435216768 .tablet\:\!gp-hidden{display:none!important}.gps-571516221435216768 .tablet\:gp-hidden{display:none}.gps-571516221435216768 .tablet\:\!gp-content-stretch{align-content:stretch!important}.gps-571516221435216768 .tablet\:\!gp-justify-center{justify-content:center!important}}@media (max-width:767px){.gps-571516221435216768 .mobile\:gp-relative{position:relative}.gps-571516221435216768 .mobile\:\!gp-hidden{display:none!important}.gps-571516221435216768 .mobile\:gp-hidden{display:none}.gps-571516221435216768 .mobile\:\!gp-content-stretch{align-content:stretch!important}.gps-571516221435216768 .mobile\:\!gp-justify-center{justify-content:center!important}}@media (max-width:1024px){.gps-571516221435216768 .tablet\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}@media (max-width:767px){.gps-571516221435216768 .mobile\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}.gps-571516221435216768 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-571516221435216768 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-571516221435216768 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-571516221435216768 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-571516221435216768 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-571516221435216768 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gf33wE-bc2" data-id="gf33wE-bc2"
        style="--blockPadding:base;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgc-mobile:transparent;--bgi:url();--bgi-mobile:url();--bgp:50% 50%;--bgp-mobile:50% 50%;--bgs:cover;--bgs-mobile:cover;--bgr:no-repeat;--bgr-mobile:no-repeat;--bga:scroll;--bga-mobile:scroll"
        class="gf33wE-bc2 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gDtD922qRY gp-relative gp-flex gp-flex-col"
    >
      
    <div class="gp-flex gp-w-full !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center" style="--h:auto;--h-tablet:auto;--h-mobile:auto;--d:none;--d-tablet:none;--d-mobile:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"type":"image","color":"bg-2","image":{"src":"https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/","width":1920,"height":900},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll","lazyLoad":false},"tablet":{"type":"image","color":"bg-2","image":{"src":"https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/","width":1920,"height":900},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll","lazyLoad":false},"mobile":{"type":"image","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0823/8599/4056/files/gempages_523685320072364842-10f57274-ea6a-4751-908e-a72d1eb80b53.png","width":1080,"height":1920,"backupFileKey":"gempages_523685320072364842-10f57274-ea6a-4751-908e-a72d1eb80b53.png","storage":"FILE_CONTENT"},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll","lazyLoad":false}},"uid":"gpJZZuRY1o","enableParallax":false,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"center"},"contentPosition2Col":{"desktop":"center"},"aspectRatio":{"desktop":"16/9","mobile":"auto"}}'
        gp-href=""
        
        class="gpJZZuRY1o gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--d:none;--d-tablet:none;--d-mobile:flex;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:500px;--aspect:auto;--aspect-tablet:auto;--aspect-mobile:500/1000"
        data-id="gpJZZuRY1o"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center"
        style="--h:auto;--h-tablet:auto;--h-mobile:auto;--aspect:auto;--aspect-tablet:auto;--aspect-mobile:500/1000;--bs:solid;--bw:0px 0px 0px 0px;--bc:#000000;--bgc:var(--g-c-bg-2);--bgc-mobile:var(--g-c-bg-2);--shadow:none"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_523685320072364842-10f57274-ea6a-4751-908e-a72d1eb80b53.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/" />
      <img
        title
        class="adaptive-hero-banner"
        src="https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:500/1000;--d:block;--d-tablet:block;--d-mobile:none;--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:500px;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0 round 0px 0px 0px 0px)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background"
              style="--bgi:url(https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/);--bgi-tablet:url(https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/);--bgi-mobile:url({{ "gempages_523685320072364842-10f57274-ea6a-4751-908e-a72d1eb80b53.png" | file_url }});--bgc:var(--g-c-bg-2);--bgc-mobile:var(--g-c-bg-2);--bgp:50% 50%;--bgp-mobile:50% 50%;--bgs:cover;--bgs-mobile:cover;--bgr:no-repeat;--bgr-mobile:no-repeat;--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          
          </div>
          
       
      
    <div
      id data-id
        style="--pl:0px;--pr:0px;--pt:32px;--pb:32px;--pl-tablet:16px;--pr-tablet:16px;--pt-tablet:16px;--pb-tablet:16px;--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:center;--gtc:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full gp-absolute tablet:gp-absolute mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-center gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:center"
      class="goSFE9Sa_V gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--mb-mobile:300px;--ta:center"
    
  >
    <style>
    .gu-20HqwL-.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .gu-20HqwL-:hover::before {
      
      
    }

    .gu-20HqwL-:hover .gp-button-icon {
      color: undefined;
    }

     .gu-20HqwL- .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gu-20HqwL-:hover .gp-button-price {
      color: undefined;
    }

    .gu-20HqwL- .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gu-20HqwL- .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gu-20HqwL-:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="https://www.elouris.com/products/firmandtone" target="_self" data-id="gu-20HqwL-" aria-label="<p>Shop Now&nbsp;</p>"
      
      data-state="idle"
      class="gu-20HqwL- gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-bg-g-brand hover:gp-bg-g-highlight gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--bg:var(--g-c-brand, brand);--hvr-bg:var(--g-c-highlight, highlight);--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex gp-flex-row-reverse">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--ml:8px;--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817550824440168">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M224.49,136.49l-72,72a12,12,0,0,1-17-17L187,140H40a12,12,0,0,1,0-24H187L135.51,64.48a12,12,0,0,1,17-17l72,72A12,12,0,0,1,224.49,136.49Z" /></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggu-20HqwL-_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 2",
    "tag": "section",
    "class": "gps-571516221435216768 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=571516221435216768)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggu-20HqwL-_label","label":"ggu-20HqwL-_label","default":"<p>Shop Now&nbsp;</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
