{%- if section.settings.image_height == 'adapt' and section.settings.image != blank -%}
  {%- style -%}
    @media screen and (max-width: 749px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .banner__media::before,
      #Banner-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
        {% if section.settings.image_2 != blank and section.settings.image_1_visibility == 'mobile-hidden' %}
          padding-bottom: {{ 1 | divided_by: section.settings.image_2.aspect_ratio | times: 100 }}%;
        {% else %}
          padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;
        {% endif %}
        content: '';
        display: block;
      }
    }

    @media screen and (min-width: 750px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .banner__media::before {
        padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {%- endstyle -%}
{%- endif -%}

{%- style -%}
  #Banner-{{ section.id }}::after {
    opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
  }
{%- endstyle -%}

{% if section.settings.display_id %}
  <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
    <span class="copy-text">Click to copy section ID</span>
    <span class="copy-success">ID copied successfully!</span>
  </copy-button>
{% endif %}
{% if section.settings.url != blank %}<a href='{{ section.settings.url }}'>{% endif %}
  <div
    id="Banner-{{ section.id }}"
    class="banner banner--content-align-{{ section.settings.desktop_content_alignment }} banner--content-align-mobile-{{ section.settings.mobile_content_alignment }} banner--{{ section.settings.image_height }}{% if section.settings.stack_images_on_mobile and section.settings.image != blank and section.settings.image_2 != blank %} banner--stacked{% endif %}{% if section.settings.image_height == 'adapt' and section.settings.image != blank %} banner--adapt{% endif %}{% if section.settings.show_text_below %} banner--mobile-bottom{%- endif -%}{% if section.settings.show_text_box == false %} banner--desktop-transparent{% endif %} content-for-grouping {{ section.settings.visibility }}"
  >
    {%- if section.settings.image != blank -%}
      <div class="banner__media media {{ section.settings.image_1_visibility }}{% if section.settings.image == blank and section.settings.image_2 == blank %} placeholder{% endif %}{% if section.settings.image_2 != blank and section.settings.image_2_visibility != 'desktop-hidden' %} banner__media-half{% endif %}">
        {%- liquid
          assign image_height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
          if section.settings.image_2 != blank
            assign image_class = 'banner__media-image-half'
          endif
          if section.settings.image_2 != blank and section.settings.stack_images_on_mobile and section.settings.image_2_visibility != 'desktop-hidden'
            assign sizes = '(min-width: 750px) 50vw, 100vw'
          elsif section.settings.image_2 != blank and section.settings.image_2_visibility != 'desktop-hidden'
            assign sizes = '50vw'
          else
            assign sizes = '100vw'
          endif
        -%}
        {{
          section.settings.image
          | image_url: width: 3840
          | image_tag:
            loading: 'lazy',
            width: section.settings.image.width,
            height: image_height,
            class: image_class,
            sizes: sizes,
            widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
        }}
      </div>
    {%- elsif section.settings.image_2 == blank -%}
      <div class="banner__media media{% if section.settings.image == blank and section.settings.image_2 == blank %} placeholder{% endif %}{% if section.settings.image_2 != blank %} banner__media-half{% endif %}">
        {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
      </div>
    {%- endif -%}
    {%- if section.settings.image_2 != blank -%}
      <div class="banner__media media {{ section.settings.image_2_visibility }}{% if section.settings.image != blank and section.settings.image_2_visibility != 'desktop-hidden' %} banner__media-half{% endif %}">
        {%- liquid
          assign image_height_2 = section.settings.image_2.width | divided_by: section.settings.image_2.aspect_ratio
          if section.settings.image != blank
            assign image_class_2 = 'banner__media-image-half'
          endif
          if section.settings.image != blank and section.settings.stack_images_on_mobile
            assign sizes = '(min-width: 750px) 50vw, 100vw'
          elsif section.settings.image != blank and section.settings.image_1_visibility != 'mobile-hidden'
            assign sizes = '50vw'
          else
            assign sizes = '100vw'
          endif
        -%}
        {{
          section.settings.image_2
          | image_url: width: 3840
          | image_tag:
            loading: 'lazy',
            width: section.settings.image_2.width,
            height: image_height_2,
            class: image_class_2,
            sizes: sizes,
            widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
        }}
      </div>
    {%- endif -%}
    <div class="banner__content banner__content--{{ section.settings.desktop_content_position }} page-width">
      <div class="banner__box banner--transparent-{{ section.settings.transparent_container_color }} content-container content-container--full-width-mobile color-{{ section.settings.color_scheme }} gradient">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'heading' -%}
              <h2 class="banner__heading {{ block.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ block.settings.title_highlight_color }}' {{ block.shopify_attributes }}>
                <span>{{ block.settings.title }}</span>
              </h2>
            {% when 'rating_stars' %}
              {% render 'rating-stars-block', block: block, margins: false, block_attributes: true %}
            {% when 'trustpilot_stars' %}
              {% render 'trustpilot-stars-block', block: block, margins: false, block_attributes: true %}
            {%- when 'text' -%}
              <div class="banner__text {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                <span>{{ block.settings.text | escape }}</span>
              </div>
            {%- when 'buttons' -%}
              <div
                class="banner__buttons{% if block.settings.button_label_1 != blank and block.settings.button_label_2 != blank %} banner__buttons--multiple{% endif %}"
                {{ block.shopify_attributes }}
              >
                {%- if block.settings.button_label_1 != blank -%}
                  <a
                    {% if block.settings.button_link_1 == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link_1 }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary_1 %} button--secondary{% else %} button--primary{% endif %}"
                  >
                    {{- block.settings.button_label_1 | escape -}}
                  </a>
                {%- endif -%}
                {%- if block.settings.button_label_2 != blank -%}
                  <a
                    {% if block.settings.button_link_2 == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link_2 }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary_2 %} button--secondary{% else %} button--primary{% endif %}"
                  >
                    {{- block.settings.button_label_2 | escape -}}
                  </a>
                {%- endif -%}
              </div>
            {% when 'atc_button' %}
              <div class="banner__buttons" {{ block.shopify_attributes }}>
                {% if block.settings.atc_product == blank %}
                  <button
                    id="SectionAtcBtn-{{ section.id }}"
                    type="button"
                    class="button main-product-atc button--has-spinner"
                    {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
                      disabled
                    {% endif %}
                  >
                    {{ block.settings.button_label }}
                    <div class="loading-overlay__spinner">
                      <svg
                        aria-hidden="true"
                        focusable="false"
                        class="spinner"
                        viewBox="0 0 66 66"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                      </svg>
                    </div>
                  </button>
                {% else %}
                  {% assign product_form_id = 'section-product-form-'
                    | append: section.id
                    | append: block.id
                  %}
                  {% render 'separate-atc-btn',
                    product: block.settings.atc_product,
                    product_form_id: product_form_id,
                    label: block.settings.button_label,
                    skip_cart: block.settings.atc_skip_cart
                  %}
                {% endif %}
              </div>
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div>
{% if section.settings.url != blank %}</a>{% endif %}

{% schema %}
{
  "name": "t:sections.image-banner.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "Images"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-banner.settings.image.label"
    },
    {
      "type": "select",
      "id": "image_1_visibility",
      "label": "Display first image on:",
      "options": [
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "default": "always-display"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "t:sections.image-banner.settings.image_2.label"
    },
    {
      "type": "select",
      "id": "image_2_visibility",
      "label": "Display second image on:",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "default": "always-display"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "%",
      "label": "t:sections.image-banner.settings.image_overlay_opacity.label",
      "default": 0
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-banner.settings.image_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-banner.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-banner.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-banner.settings.image_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.image-banner.settings.image_height.label",
      "info": "t:sections.image-banner.settings.image_height.info"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "url",
      "id": "url",
      "label": "Link",
      "info": "If added, the whole banner will be a clickable link. ATTENTION: Buttons block can NOT be combined with this. Nested links inside this whole banner link will cause errors."
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "top-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__4.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__5.label"
        },
        {
          "value": "middle-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__6.label"
        },
        {
          "value": "bottom-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__7.label"
        },
        {
          "value": "bottom-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__8.label"
        },
        {
          "value": "bottom-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__9.label"
        }
      ],
      "default": "middle-center",
      "label": "t:sections.image-banner.settings.desktop_content_position.label"
    },
    {
      "type": "checkbox",
      "id": "show_text_box",
      "default": true,
      "label": "t:sections.image-banner.settings.show_text_box.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.image-banner.settings.desktop_content_alignment.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Visible when container displayed."
    },
    {
      "type": "select",
      "id": "transparent_container_color",
      "label": "Transparent container text color",
      "options": [
        {
          "value": "white",
          "label": "White"
        },
        {
          "value": "black",
          "label": "Black"
        }
      ],
      "default": "white"
    },
    {
      "type": "header",
      "content": "t:sections.image-banner.settings.header.content"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.image-banner.settings.mobile_content_alignment.label"
    },
    {
      "type": "checkbox",
      "id": "stack_images_on_mobile",
      "default": true,
      "label": "t:sections.image-banner.settings.stack_images_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "show_text_below",
      "default": true,
      "label": "t:sections.image-banner.settings.show_text_below.label"
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.image-banner.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Image banner",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        }
      ]
    },
    {
      "type": "rating_stars",
      "name": "Rating stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 4.8
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#ffcc00",
          "label": "Stars color"
        },
        {
          "type": "select",
          "id": "bg_stars_style",
          "options": [
            {
              "value": "outlined",
              "label": "Outlined"
            },
            {
              "value": "full",
              "label": "Full"
            }
          ],
          "label": "Background stars style",
          "default": "full"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#ececec",
          "label": "Background stars color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        }
      ]
    },
    {
      "type": "trustpilot_stars",
      "name": "Trustpilot stars",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 0.1,
          "default": 5
        },
        {
          "type": "color",
          "id": "star_color",
          "default": "#00b67a",
          "label": "Active stars container color"
        },
        {
          "type": "color",
          "id": "bg_star_color",
          "default": "#c8c8c8",
          "label": "Background stars container color"
        },
        {
          "type": "color",
          "id": "star_symbol_color",
          "default": "#fff",
          "label": "Stars inside symbol color"
        },
        {
          "type": "inline_richtext",
          "id": "label",
          "label": "Text",
          "default": "(xxxx Reviews)"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 16,
          "label": "Text size"
        },
        {
          "type": "text",
          "id": "scroll_id",
          "label": "ID of the section to scroll to"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-banner.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Give customers details about the banner image(s) or content on the template.",
          "label": "t:sections.image-banner.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-banner.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "buttons",
      "name": "t:sections.image-banner.blocks.buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label_1",
          "default": "Button label",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_1.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_1.info"
        },
        {
          "type": "url",
          "id": "button_link_1",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_1.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_1",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_1.label"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "default": "Button label",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_2",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_2.label"
        }
      ]
    },
    {
      "type": "atc_button",
      "name":"Add to Cart button",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Add to Cart"
        },
        {
          "type": "product",
          "id": "atc_product",
          "label": "ATC Custom product",
          "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
        },
        {
          "type": "checkbox",
          "id": "atc_skip_cart",
          "label": "ATC Custom product skip cart"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-banner.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "buttons"
        }
      ]
    }
  ]
}
{% endschema %}
