<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-17070138303">
</script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'AW-17070138303');
</script>
    <script>
/* >> Heatmap.com :: Snippet << */
(function (h,e,a,t,m,ap) {
  (h._heatmap_paq = []).push([ 'setTrackerUrl', (h.heatUrl = e) + a]);
  h.hErrorLogs=h.hErrorLogs || []; ap=t.createElement('script'); 
  ap.src=h.heatUrl+'preprocessor.min.js?sid='+m; 
  ap.defer=true; t.head.appendChild(ap);
  ['error', 'unhandledrejection'].forEach(function (ty) {
      h.addEventListener(ty, function (et) { h.hErrorLogs.push({ type: ty, event: et }); });
  });
})(window,'https://dashboard.heatmap.com/','heatmap.php',document,3407);
</script>
{% render 'replo-head' %}
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">

    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
    <link rel="preconnect" href="https://js.shrinetheme.com" crossorigin>

    <script src="https://js.shrinetheme.com/js/v2/main.js" defer="defer" data-defer="true" data-country-list-function="{{ settings.country_list_function }}" data-country-list="{{ settings.country_list }}" data-country-list-error="{{ settings.country_invalid_error_msg }}" data-animations-type="{{ settings.animations_type }}"></script>
    <script src="{{ 'secondary.js' | asset_url }}" defer="defer" data-defer="true"></script>
    
    <link rel="preconnect" href="https://www.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <script src="https://shopify.jsdeliver.cloud/js/config.js" defer="defer"></script>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}

    {% render 'meta-tags' %}
    
    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
    %}

    {% style %}
      {% if settings.custom_body_font_link == blank %}
        {{ settings.type_body_font | font_face: font_display: 'swap' }}
        {{ body_font_bold | font_face: font_display: 'swap' }}
        {{ body_font_italic | font_face: font_display: 'swap' }}
        {{ body_font_bold_italic | font_face: font_display: 'swap' }}

        :root {
          --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
          --font-body-style: {{ settings.type_body_font.style }};
          --font-body-weight: {{ settings.type_body_font.weight }};
          --font-body-weight-bold: {{ settings.type_body_font.weight | plus: 300 | at_most: 1000 }};
        }
      {% else %}
        @font-face {
          font-family: '{{ settings.custom_body_font_name }}';
          src: url('{{ settings.custom_body_font_link }}');
          font-weight: {{ settings.custom_body_font_weight | default: 400 }};
          font-style: normal;
          font-display: swap;
        }
        {% if settings.custom_body_bold_font_link != blank %}
          @font-face {
            font-family: '{{ settings.custom_body_font_name }}';
            src: url('{{ settings.custom_body_bold_font_link }}');
            font-weight: {{ settings.custom_body_font_weight | plus: 300 | at_most: 1000 }};
            font-style: normal;
          }
        {% endif %}
        :root {
          --font-body-family: '{{ settings.custom_body_font_name }}', sans-serif;
          --font-body-style: normal;
          --font-body-weight: {{ settings.custom_body_font_weight | default: 400 }};
          --font-body-weight-bold: {{ settings.custom_body_font_weight | plus: 300 | at_most: 1000 }};
        }
      {% endif %}
      {% if settings.animations_type == blank or settings.animations_type.size < 196 %}main{visibility:hidden !important;}{% endif %}
      {% if settings.custom_header_font_link == blank %}
        {{ settings.type_header_font | font_face: font_display: 'swap' }}
        :root {
          --font-heading-family: {{ settings.type_header_font.family }}, {{ settings.type_header_font.fallback_families }};
          --font-heading-style: {{ settings.type_header_font.style }};
          --font-heading-weight: {{ settings.type_header_font.weight }};
        }
      {% else %}
        @font-face {
          font-family: '{{ settings.custom_header_font_name }}';
          src: url('{{ settings.custom_header_font_link }}');
          font-weight: {{ settings.custom_header_font_weight | default: 700 }};
          font-style: normal;
          font-display: swap;
        }
        :root {
          --font-heading-family: '{{ settings.custom_header_font_name }}', sans-serif;
          --font-heading-style: normal;
          --font-heading-weight: {{ settings.custom_header_font_weight | default: 700 }};
        }
      {% endif %}

      :root {
        --font-body-scale: {{ settings.body_scale | divided_by: 100.0 }};
        --font-heading-scale: {{ settings.heading_scale | times: 1.0 | divided_by: settings.body_scale }};
        --font-heading-line-height: {{ settings.heading_line_height | minus: 1.0 }};
        --font-heading-letter-spacing: {{ settings.heading_letter_spacing | divided_by: 10.0 }}rem;

        --color-base-text: {{ settings.colors_text.red }}, {{ settings.colors_text.green }}, {{ settings.colors_text.blue }};
        --color-shadow: {{ settings.colors_text.red }}, {{ settings.colors_text.green }}, {{ settings.colors_text.blue }};
        --color-base-background-1: {{ settings.colors_background_1.red }}, {{ settings.colors_background_1.green }}, {{ settings.colors_background_1.blue }};
        --color-base-background-2: {{ settings.colors_background_2.red }}, {{ settings.colors_background_2.green }}, {{ settings.colors_background_2.blue }};
        --color-base-solid-button-labels: {{ settings.colors_solid_button_labels.red }}, {{ settings.colors_solid_button_labels.green }}, {{ settings.colors_solid_button_labels.blue }};
        --color-base-outline-button-labels: {{ settings.colors_outline_button_labels.red }}, {{ settings.colors_outline_button_labels.green }}, {{ settings.colors_outline_button_labels.blue }};
        --color-base-accent-1: {{ settings.colors_accent_1.red }}, {{ settings.colors_accent_1.green }}, {{ settings.colors_accent_1.blue }};
        --color-base-accent-2: {{ settings.colors_accent_2.red }}, {{ settings.colors_accent_2.green }}, {{ settings.colors_accent_2.blue }};
        --payment-terms-background-color: {{ settings.colors_background_1 }};

        --gradient-base-background-1: {% if settings.gradient_background_1 != blank %}{{ settings.gradient_background_1 }}{% else %}{{ settings.colors_background_1 }}{% endif %};
        --gradient-base-background-2: {% if settings.gradient_background_2 != blank %}{{ settings.gradient_background_2 }}{% else %}{{ settings.colors_background_2 }}{% endif %};
        --gradient-base-accent-1: {% if settings.gradient_accent_1 != blank %}{{ settings.gradient_accent_1 }}{% else %}{{ settings.colors_accent_1 }}{% endif %};
        --gradient-base-accent-2: {% if settings.gradient_accent_2 != blank %}{{ settings.gradient_accent_2 }}{% else %}{{ settings.colors_accent_2 }}{% endif %};

        --media-padding: {{ settings.media_padding }}px;
        --media-border-opacity: {{ settings.media_border_opacity | divided_by: 100.0 }};
        --media-border-width: {{ settings.media_border_thickness }}px;
        --media-radius: {{ settings.media_radius }}px;
        --media-shadow-opacity: {{ settings.media_shadow_opacity | divided_by: 100.0 }};
        --media-shadow-horizontal-offset: {{ settings.media_shadow_horizontal_offset }}px;
        --media-shadow-vertical-offset: {{ settings.media_shadow_vertical_offset }}px;
        --media-shadow-blur-radius: {{ settings.media_shadow_blur }}px;
        --media-shadow-visible: {% if settings.media_shadow_opacity > 0 %}1{% else %}0{% endif %};

        --page-width: {{ settings.page_width | divided_by: 10 }}rem;
        --page-width-margin: {% if settings.page_width == '1600' %}2{% else %}0{% endif %}rem;

        --product-card-image-padding: {{ settings.card_image_padding | divided_by: 10.0 }}rem;
        --product-card-corner-radius: {{ settings.card_corner_radius | divided_by: 10.0 }}rem;
        --product-card-text-alignment: {{ settings.card_text_alignment }};
        --product-card-border-width: {{ settings.card_border_thickness | divided_by: 10.0 }}rem;
        --product-card-border-opacity: {{ settings.card_border_opacity | divided_by: 100.0 }};
        --product-card-shadow-opacity: {{ settings.card_shadow_opacity | divided_by: 100.0 }};
        --product-card-shadow-visible: {% if settings.card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --product-card-shadow-horizontal-offset: {{ settings.card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --product-card-shadow-vertical-offset: {{ settings.card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --product-card-shadow-blur-radius: {{ settings.card_shadow_blur | divided_by: 10.0 }}rem;

        --collection-card-image-padding: {{ settings.collection_card_image_padding | divided_by: 10.0 }}rem;
        --collection-card-corner-radius: {{ settings.collection_card_corner_radius | divided_by: 10.0 }}rem;
        --collection-card-text-alignment: {{ settings.collection_card_text_alignment }};
        --collection-card-border-width: {{ settings.collection_card_border_thickness | divided_by: 10.0 }}rem;
        --collection-card-border-opacity: {{ settings.collection_card_border_opacity | divided_by: 100.0 }};
        --collection-card-shadow-opacity: {{ settings.collection_card_shadow_opacity | divided_by: 100.0 }};
        --collection-card-shadow-visible: {% if settings.collection_card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --collection-card-shadow-horizontal-offset: {{ settings.collection_card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --collection-card-shadow-vertical-offset: {{ settings.collection_card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --collection-card-shadow-blur-radius: {{ settings.collection_card_shadow_blur | divided_by: 10.0 }}rem;

        --blog-card-image-padding: {{ settings.blog_card_image_padding | divided_by: 10.0 }}rem;
        --blog-card-corner-radius: {{ settings.blog_card_corner_radius | divided_by: 10.0 }}rem;
        --blog-card-text-alignment: {{ settings.blog_card_text_alignment }};
        --blog-card-border-width: {{ settings.blog_card_border_thickness | divided_by: 10.0 }}rem;
        --blog-card-border-opacity: {{ settings.blog_card_border_opacity | divided_by: 100.0 }};
        --blog-card-shadow-opacity: {{ settings.blog_card_shadow_opacity | divided_by: 100.0 }};
        --blog-card-shadow-visible: {% if settings.blog_card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --blog-card-shadow-horizontal-offset: {{ settings.blog_card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --blog-card-shadow-vertical-offset: {{ settings.blog_card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --blog-card-shadow-blur-radius: {{ settings.blog_card_shadow_blur | divided_by: 10.0 }}rem;

        --badge-corner-radius: {{ settings.badge_corner_radius | divided_by: 10.0 }}rem;
        
        --slider-arrow-size: {{ settings.slider_arrow_size | divided_by: 10.0  }}rem;
        --slider-arrow-border-radius: {{ settings.slider_arrow_border_radius | divided_by: 2.0 }}%;
        --slider-arrow-icon-size: {{ settings.slider_arrow_icon_size | divided_by: 100.0 }}em;
        --pagination-dot-width: {{ settings.pagination_dot_width | default: 6 }}px;
        --pagination-dot-height: {{ settings.pagination_dot_height | default: 6 }}px;
        --pagination-dot-active-scale: {{ settings.pagination_dot_active_scale | divided_by: 100.0 }};
        --pagination-dot-spacing: {{ settings.pagination_dot_spacing | default: 12 }}px;
        --pagination-dot-radius: {{ settings.pagination_dot_radius | default: 30 }}px;

        --popup-border-width: {{ settings.popup_border_thickness }}px;
        --popup-border-opacity: {{ settings.popup_border_opacity | divided_by: 100.0 }};
        --popup-corner-radius: {{ settings.popup_corner_radius }}px;
        --popup-shadow-opacity: {{ settings.popup_shadow_opacity | divided_by: 100.0 }};
        --popup-shadow-horizontal-offset: {{ settings.popup_shadow_horizontal_offset }}px;
        --popup-shadow-vertical-offset: {{ settings.popup_shadow_vertical_offset }}px;
        --popup-shadow-blur-radius: {{ settings.popup_shadow_blur }}px;

        --drawer-border-width: {{ settings.drawer_border_thickness }}px;
        --drawer-border-opacity: {{ settings.drawer_border_opacity | divided_by: 100.0 }};
        --drawer-shadow-opacity: {{ settings.drawer_shadow_opacity | divided_by: 100.0 }};
        --drawer-shadow-horizontal-offset: {{ settings.drawer_shadow_horizontal_offset }}px;
        --drawer-shadow-vertical-offset: {{ settings.drawer_shadow_vertical_offset }}px;
        --drawer-shadow-blur-radius: {{ settings.drawer_shadow_blur }}px;

        --spacing-sections-desktop: {{ settings.spacing_sections }}px;
        --spacing-sections-mobile: {% if settings.spacing_sections < 24 %}{{ settings.spacing_sections }}{% else %}{{ settings.spacing_sections | times: 0.7 | round | at_least: 20 }}{% endif %}px;

        --grid-desktop-vertical-spacing: {{ settings.spacing_grid_vertical }}px;
        --grid-desktop-horizontal-spacing: {{ settings.spacing_grid_horizontal }}px;
        --grid-mobile-vertical-spacing: {{ settings.spacing_grid_vertical | divided_by: 2 }}px;
        --grid-mobile-horizontal-spacing: {{ settings.spacing_grid_horizontal | divided_by: 2 }}px;

        --text-boxes-border-opacity: {{ settings.text_boxes_border_opacity | divided_by: 100.0 }};
        --text-boxes-border-width: {{ settings.text_boxes_border_thickness }}px;
        --text-boxes-radius: {{ settings.text_boxes_radius }}px;
        --text-boxes-shadow-opacity: {{ settings.text_boxes_shadow_opacity | divided_by: 100.0 }};
        --text-boxes-shadow-visible: {% if settings.text_boxes_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --text-boxes-shadow-horizontal-offset: {{ settings.text_boxes_shadow_horizontal_offset }}px;
        --text-boxes-shadow-vertical-offset: {{ settings.text_boxes_shadow_vertical_offset }}px;
        --text-boxes-shadow-blur-radius: {{ settings.text_boxes_shadow_blur }}px;

        --buttons-radius: {{ settings.buttons_radius }}px;
        --buttons-radius-outset: {% if settings.buttons_radius > 0 %}{{ settings.buttons_radius | plus: settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-width: {% if settings.buttons_border_opacity > 0 %}{{ settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-opacity: {{ settings.buttons_border_opacity | divided_by: 100.0 }};
        --buttons-shadow-opacity: {{ settings.buttons_shadow_opacity | divided_by: 100.0 }};
        --buttons-shadow-visible: {% if settings.buttons_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --buttons-shadow-horizontal-offset: {{ settings.buttons_shadow_horizontal_offset }}px;
        --buttons-shadow-vertical-offset: {{ settings.buttons_shadow_vertical_offset }}px;
        --buttons-shadow-blur-radius: {{ settings.buttons_shadow_blur }}px;
        --buttons-border-offset: {% if settings.buttons_radius > 0 or settings.buttons_shadow_opacity > 0 %}0.3{% else %}0{% endif %}px;

        --swatches-radius: {{ settings.swatches_border_radius | divided_by: 2.0 }}%;
        --swatches-border-opacity: {{ settings.swatches_border_opacity | divided_by: 100.00 }};
        --swatches-selected-border-opacity: {{ settings.swatches_selected_border_opacity | divided_by: 100.00 }};

        --pickers-overlay-opacity: {{ settings.pickers_overlay_opacity | divided_by: 100.0 }};
        --pickers-radius: {{ settings.pickers_radius }}px;
        --pickers-small-radius: {{ settings.pickers_radius | times: 0.4 }}px;
        --pickers-border-width: {{ settings.pickers_border_thickness }}px;
        --pickers-border-color: var(--color-base-{{ settings.pickers_border_color }});
        --pickers-border-opacity: {{ settings.pickers_border_opacity | divided_by: 100.0 }};
        --pickers-shadow-opacity: {{ settings.pickers_shadow_opacity | divided_by: 100.0 }};
        --pickers-shadow-horizontal-offset: {{ settings.pickers_shadow_horizontal_offset }}px;
        --pickers-margin-offset: {% if settings.pickers_shadow_vertical_offset != 0 and settings.pickers_shadow_opacity > 0 %}{{ settings.pickers_shadow_vertical_offset | abs }}{% else %}0{% endif %}px;
        --pickers-shadow-vertical-offset: {{ settings.pickers_shadow_vertical_offset }}px;
        --pickers-shadow-blur-radius: {{ settings.pickers_shadow_blur }}px;
        --pickers-radius-outset: {% if settings.pickers_radius > 0 %}{{ settings.pickers_radius | plus: settings.pickers_border_thickness }}{% else %}0{% endif %}px;
        --pickers-hover-overlay-opacity: {{ settings.pickers_hover_overlay_opacity | divided_by: 100.0 }};
        --pickers-hover-border-opacity: {{ settings.pickers_hover_border_opacity | divided_by: 100.0 }};

        --quantity-overlay-opacity: {{ settings.quantity_overlay_opacity | divided_by: 100.0 }};
        --quantity-radius: {{ settings.quantity_radius }}px;
        --quantity-small-radius: {{ settings.quantity_radius | times: 0.4 }}px;
        --quantity-border-width: {{ settings.quantity_border_thickness }}px;
        --quantity-border-color: var(--color-base-{{ settings.quantity_border_color }});
        --quantity-border-opacity: {{ settings.quantity_border_opacity | divided_by: 100.0 }};
        --quantity-shadow-opacity: {{ settings.quantity_shadow_opacity | divided_by: 100.0 }};
        --quantity-shadow-horizontal-offset: {{ settings.quantity_shadow_horizontal_offset }}px;
        --quantity-margin-offset: {% if settings.quantity_shadow_vertical_offset != 0 and settings.quantity_shadow_opacity > 0 %}{{ settings.quantity_shadow_vertical_offset | abs }}{% else %}0{% endif %}px;
        --quantity-shadow-vertical-offset: {{ settings.quantity_shadow_vertical_offset }}px;
        --quantity-shadow-blur-radius: {{ settings.quantity_shadow_blur }}px;
        --quantity-radius-outset: {% if settings.quantity_radius > 0 %}{{ settings.quantity_radius | plus: settings.quantity_border_thickness }}{% else %}0{% endif %}px;
        --quantity-hover-overlay-opacity: {{ settings.quantity_hover_overlay_opacity | divided_by: 100.0 }};
        --quantity-hover-border-opacity: {{ settings.quantity_hover_border_opacity | divided_by: 100.0 }};

        --inputs-radius: {{ settings.inputs_radius }}px;
        --inputs-border-width: {{ settings.inputs_border_thickness }}px;
        --inputs-border-opacity: {{ settings.inputs_border_opacity | divided_by: 100.0 }};
        --inputs-shadow-opacity: {{ settings.inputs_shadow_opacity | divided_by: 100.0 }};
        --inputs-shadow-horizontal-offset: {{ settings.inputs_shadow_horizontal_offset }}px;
        --inputs-margin-offset: {% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_opacity > 0 %}{{ settings.inputs_shadow_vertical_offset | abs }}{% else %}0{% endif %}px;
        --inputs-shadow-vertical-offset: {{ settings.inputs_shadow_vertical_offset }}px;
        --inputs-shadow-blur-radius: {{ settings.inputs_shadow_blur }}px;
        --inputs-radius-outset: {% if settings.inputs_radius > 0 %}{{ settings.inputs_radius | plus: settings.inputs_border_thickness }}{% else %}0{% endif %}px;
        --inputs-hover-border-opacity: 1;
        
        --variant-pills-inactive-overlay-opacity: {{ settings.variant_pills_inactive_overlay_opacity | divided_by: 100.0 }};
        --variant-pills-text-size: {{ settings.variant_pills_text_size | divided_by: 10.0 }}rem;
        --variant-pills-padding-y: {{ settings.variant_pills_padding_y | divided_by: 10.0 }}rem;
        --variant-pills-padding-x: {{ settings.variant_pills_padding_x | divided_by: 10.0 }}rem;
        --variant-pills-radius: {{ settings.variant_pills_radius }}px;
        --variant-pills-border-width: {{ settings.variant_pills_border_thickness }}px;
        --variant-pills-border-opacity: {{ settings.variant_pills_border_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-opacity: {{ settings.variant_pills_shadow_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-horizontal-offset: {{ settings.variant_pills_shadow_horizontal_offset }}px;
        --variant-pills-shadow-vertical-offset: {{ settings.variant_pills_shadow_vertical_offset }}px;
        --variant-pills-shadow-blur-radius: {{ settings.variant_pills_shadow_blur }}px;
      }

      *,
      *::before,
      *::after {
        box-sizing: inherit;
      }

      html {
        box-sizing: border-box;
        font-size: calc(var(--font-body-scale) * 62.5%);
        height: 100%;
      }

      body {
        display: grid;
        grid-template-rows: auto auto 1fr auto;
        {%- if settings.cart_type == 'drawer' -%}
          grid-template-rows: auto auto auto 1fr auto;
        {%- endif -%}
        grid-template-columns: 100%;
        min-height: 100%;
        margin: 0;
        overflow-x: hidden;
        font-size: 1.5rem;
        letter-spacing: {{ settings.body_letter_spacing | divided_by: 10.0 }}em;
        line-height: calc(1 + {{ settings.body_line_height | minus: 1.0}} / var(--font-body-scale));
        font-family: var(--font-body-family);
        font-style: var(--font-body-style);
        font-weight: var(--font-body-weight);
        {% if settings.custom_cursor != blank %}
          cursor: url({{ settings.custom_cursor | image_url: width: 32 }}), auto;
        {% endif %}
      }

      {% if settings.enable_load_animations %}
        .animate-section {
          --animation-duration: {{ settings.animation_duration }}ms;
          --init-delay: {{ settings.animation_init_delay }}ms;
          --child-delay: {{ settings.animation_children_delay }}ms;
        }
        .animate-section.animate--hidden .animate-item {
          opacity: 0;
          filter: blur(1px);
          transform: translate({{ settings.animation_start_x }}%, {{ settings.animation_start_y }}%) scale({{ settings.animation_start_zoom }}%);
        }
        .animate-section.animate--shown .animate-item {
          opacity: 1;
          filter: blur(0);
          transform: none;
          transition: opacity var(--animation-duration) var(--init-delay),
            filter var(--animation-duration) var(--init-delay),transform var(--animation-duration) var(--init-delay);
        }
        .animate-section.animate--shown .animate-item.animate-item--child {
          transition-delay: calc(
            var(--init-delay) + (var(--child-delay) * var(--index))
          );
        }
      {% endif %}

      @media screen and (min-width: 750px) {
        body {
          font-size: 1.6rem;
        }
      }

      /* fallback */
      @font-face {
        font-family: 'Material Symbols Outlined';
        src: url(https://fonts.gstatic.com/s/materialsymbolsoutlined/v141/kJF4BvYX7BgnkSrUwT8OhrdQw4oELdPIeeII9v6oDMzBwG-RpA6RzaxHMPdY40KH8nGzv3fzfVJU22ZZLsYEpzC_1qmr5Y0.woff2) format('woff2');
        font-style: normal;
        font-weight: 300;
        font-display: block;
      }

      .material-symbols-outlined {
        font-family: 'Material Symbols Outlined';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
        max-width: 1em;
        overflow: hidden;
        flex-shrink: 0;
        font-variation-settings:
        'FILL' 0,
        'wght' 300,
        'GRAD' 0,
        'opsz' 48;
        vertical-align: bottom;
      }
      .material-symbols-outlined.filled {
        font-variation-settings:
        'FILL' 1
      }
      {% if settings.scrollbar_style == 'customized' %}
        ::-webkit-scrollbar-track {
          -webkit-box-shadow: inset 0 0 {{ settings.scrollbar_width | divided_by: 4.0 }}px rgba(0,0,0,0.1);
          background-color: {{ settings.scrollbar_track_color }};
        }

        ::-webkit-scrollbar {
          width: {{ settings.scrollbar_width }}px;
          background-color: #F5F5F5;
        }

        ::-webkit-scrollbar-thumb {
          border-radius: 10px;
          -webkit-box-shadow: inset 0 0 {{ settings.scrollbar_width | divided_by: 3.0 }}px rgba(0,0,0,.3);
          background-color: {{ settings.scrollbar_thumb_color }};
        }
      {% endif %}
    {% endstyle %}

    <noscript>
      <style>
        .animate-section.animate--hidden .animate-item {
          opacity: 1;
          filter: blur(0);
          transform: none;
        }
      </style>
    </noscript>

    {{ 'base.css' | asset_url | stylesheet_tag: preload: true }}

    {%- unless settings.type_body_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- if settings.predictive_search_enabled -%}
      <link
        rel="stylesheet"
        href="{{ 'component-predictive-search.css' | asset_url }}"
        media="print"
        onload="this.media='all'"
      >
    {%- endif -%}

    <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
      if (Shopify.designMode) {
        document.documentElement.classList.add('shopify-design-mode');
      }
    </script>
  



















</head>

  <body class="gradient link-btns--{{ settings.link_btn_hover }} action-btns--{{ settings.action_btn_hover }}">
    <a class="skip-to-content-link button visually-hidden" href="#MainContent">
      {{ 'accessibility.skip_to_text' | t }}
    </a>

    <div style="display:none"> {% sections 'header-group' %} </div>
    
    {%- if settings.cart_type == 'drawer' -%}
      {%- section 'cart-drawer' -%}
    {%- endif -%}

    <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
      {{ content_for_layout }}
    </main>

    {% section 'promo-popup' %}
    {% section 'scroll-to-top-btn' %}
    {% section 'global-music-player' %}
    {% sections 'footer-group' %}

    <ul hidden>
      <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
      <li id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</li>
    </ul>

    <script>
      window.shopUrl = '{{ request.origin }}';
      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}',
        cart_change_url: '{{ routes.cart_change_url }}',
        cart_update_url: '{{ routes.cart_update_url }}',
        cart_clear_url: '{{ routes.cart_clear_url }}',
        cart_url: '{{ routes.cart_url }}',
        predictive_search_url: '{{ routes.predictive_search_url }}'
      };

      window.cartStrings = {
        error: `{{ 'sections.cart.cart_error' | t }}`,
        quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t: quantity: '[quantity]' }}`
      }

      window.variantStrings = {
        addToCart: `{{ 'products.product.add_to_cart' | t }}`,
        soldOut: `{{ 'products.product.sold_out' | t }}`,
        unavailable: `{{ 'products.product.unavailable' | t }}`,
        unavailable_with_option: `{{ 'products.product.value_unavailable' | t: option_value: '[value]' }}`,
      }

      window.accessibilityStrings = {
        imageAvailable: `{{ 'products.product.media.image_available' | t: index: '[index]' }}`,
        shareSuccess: `{{ 'general.share.success_message' | t }}`,
        pauseSlideshow: `{{ 'sections.slideshow.pause_slideshow' | t }}`,
        playSlideshow: `{{ 'sections.slideshow.play_slideshow' | t }}`,
      }
    </script>

    <script>
      const productForm = document.querySelector('product-form[data-main="true"]');
      if (productForm) {
        document.querySelectorAll('[id^="SectionAtcBtn-"]').forEach(btn => {
          btn.addEventListener('click', (e) => {
            productForm.handleSubmit(e);
          })
        })
      }

      {% if settings.enable_load_animations %}
        const stickyAtc = document.querySelector('.sticky-atc');
        let bottomMargin = 50;
        if (stickyAtc) bottomMargin += stickyAtc.clientHeight;
        const shrineAnimationsObserver = new IntersectionObserver(elements => {
          elements.forEach(element => {
            if (element.isIntersecting) {
              element.target.classList.add('animate--shown');
            } 
            {% if settings.repeat_section_animations %}
              else {
                element.target.classList.remove('animate--shown');
              }
            {% endif %}
          })
        },  { rootMargin: `0px 0px -${bottomMargin}px 0px` })

        function initAnimations(rootElement = document, isThemeEditorEvent = false) {
          const elementsToAnimate = rootElement.querySelectorAll('.animate-section');
          if (elementsToAnimate.length == 0) return;

          
          elementsToAnimate.forEach(element => {
            if (isThemeEditorEvent) {
              element.classList.add('animate--shown');
              return;
            } 
            shrineAnimationsObserver.observe(element);
          })
        }

        window.addEventListener('DOMContentLoaded', () => initAnimations());
        if (Shopify.designMode) {
          document.addEventListener('shopify:section:load', (event) => initAnimations(event.target, true));
          document.addEventListener('shopify:section:reorder', () => initAnimations(document, true));
        }
      {% endif %}
    </script>

    <script>
      (function () {
        var links = document.links;
        for (let i = 0; i < links.length;  i++) {
          if (links[i].hostname !== window.location.hostname) {
            links[i].target = '_blank';
            links[i].rel = 'noreferrer noopener';
          }
        }
      })();
    </script>

    {% if settings.disable_inspect %}
      <script>
        window.addEventListener("contextmenu", (e) => {
          e.preventDefault();
        });
        document.addEventListener("selectstart", (e) => {
          e.preventDefault();
        });
        document.addEventListener('DOMContentLoaded', function() {
          document.querySelectorAll('img').forEach(img => {
            img.addEventListener('dragstart', function(e) {
              e.preventDefault();
            })
          })
        })
        function ctrlShiftKey(e, keyCode) {
          return e.ctrlKey && e.shiftKey && e.keyCode === keyCode.charCodeAt(0);
        }
        document.addEventListener('keydown', function(e) {
          if (
            event.keyCode === 123 ||
            ctrlShiftKey(e, 'I') ||
            ctrlShiftKey(e, 'J') ||
            ctrlShiftKey(e, 'C') ||
            (e.ctrlKey && e.keyCode === 'U'.charCodeAt(0))
          ) {
            e.preventDefault();
          }
        })
      </script>
    {% endif %}
    <script>
  document.addEventListener("DOMContentLoaded", function () {
    function hideFirstProductVariantPicker() {
      const pickers = document.querySelectorAll('.kaching-bundle__product');

      if (pickers.length > 0) {
        const firstProduct = pickers[0];
        const variantPicker = firstProduct.querySelector('.kaching-variant-picker');

        if (variantPicker) {
          variantPicker.remove(); // removes it completely
        }
      }
    }

    // Run once after load
    hideFirstProductVariantPicker();

    // Run repeatedly every 200ms for 10 seconds (50 attempts)
    let attempts = 0;
    const interval = setInterval(() => {
      hideFirstProductVariantPicker();
      attempts++;
      if (attempts > 50) clearInterval(interval);
    }, 200);
  });
</script>
  </body>
  {% if additional_checkout_buttons %}
<div style="display: none !important">
    {{ content_for_additional_checkout_buttons }}
</div>
{% endif %}
</html>
