/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "animations_type": "LcFvXx0NxkSAHsQzBjVZh17MD9hteXRDHArB9pCk3GIRsHYzHkADB6GdZCdVChNp6v9GKo2UJzUCtue8dXQtY527raiQs6VYr8wKCrYV92GoRzQTldtq9Y6HVwBKEGa0ZfDRMcdNLHhkx9J4N+OcoLDlIfIKIyvLZD5chuLFVTc8JWbsj7ZTYf/TWmlkSkR5rk65bs5DodZ/fY8RD1nClnUM+DllhP8KsGBycIwqF6XMaZA2O9DJTnL8WiaMkmmHIsINqrxcXH2vxC9PEWoXa8Rz81FFpFWmdUvjfbLHOZL8VxrmRWRRjQuIin/Cz3ENJ0L4yUeAyMIsCcHl1nCy1vurl5HrDd2rRmtzuwrwrVmAgzZV8CFyCWH6gRxLY1u6wNOKpo5osCmgUNqI1kUrxQ==",
    "disable_inspect": true,
    "country_list_function": "block",
    "country_list": "",
    "logo": "shopify://shop_images/Untitled-other_2.png",
    "logo_width": 160,
    "mobile_logo_width": 130,
    "favicon": "shopify://shop_images/Untitled_design_98a6d5ca-a865-42f0-9b7d-31edcbca2e92.png",
    "colors_solid_button_labels": "#ffffff",
    "colors_accent_1": "#439d8f",
    "gradient_accent_1": "",
    "colors_accent_2": "#006b6b",
    "gradient_accent_2": "",
    "colors_text": "#121212",
    "colors_outline_button_labels": "#dd1d1d",
    "colors_background_1": "#ffffff",
    "gradient_background_1": "",
    "colors_background_2": "#ffffff",
    "gradient_background_2": "",
    "type_header_font": "poppins_n6",
    "custom_header_font_name": "",
    "custom_header_font_weight": 700,
    "heading_scale": 100,
    "heading_letter_spacing": 0,
    "type_body_font": "dm_sans_n4",
    "body_scale": 100,
    "body_letter_spacing": 0.6,
    "enable_load_animations": true,
    "repeat_section_animations": false,
    "badge_position": "bottom left",
    "badge_corner_radius": 6,
    "sale_badge_color_scheme": "accent-2",
    "sale_badge_text": "SAVE [percentage]",
    "sale_basge_discount_icon": true,
    "sold_out_badge_color_scheme": "inverse",
    "slider_arrow_size": 30,
    "accent_icons": "text",
    "page_width": 1400,
    "spacing_sections": 0,
    "spacing_grid_horizontal": 40,
    "spacing_grid_vertical": 40,
    "link_btn_hover": "arrow",
    "action_btn_hover": "center",
    "buttons_border_thickness": 2,
    "buttons_border_opacity": 100,
    "buttons_radius": 6,
    "buttons_shadow_opacity": 0,
    "buttons_shadow_horizontal_offset": 0,
    "buttons_shadow_vertical_offset": 4,
    "buttons_shadow_blur": 5,
    "swatches_border_radius": 100,
    "swatches_border_opacity": 0,
    "swatches_selected_border_opacity": 50,
    "variant_pills_accent_color": "text",
    "variant_pills_bold_text": false,
    "variant_pills_border_thickness": 1,
    "variant_pills_border_opacity": 55,
    "variant_pills_radius": 40,
    "variant_pills_shadow_opacity": 0,
    "variant_pills_shadow_horizontal_offset": 0,
    "variant_pills_shadow_vertical_offset": 4,
    "variant_pills_shadow_blur": 5,
    "pickers_border_color": "accent-1",
    "pickers_text_color": "text",
    "pickers_overlay_color": "accent-1",
    "pickers_overlay_opacity": 8,
    "pickers_border_thickness": 1,
    "pickers_border_opacity": 20,
    "pickers_radius": 8,
    "pickers_hover_overlay_opacity": 10,
    "pickers_hover_border_opacity": 55,
    "quantity_color_scheme": "background-1",
    "quantity_border_color": "accent-1",
    "quantity_text_color": "text",
    "quantity_overlay_color": "accent-1",
    "quantity_overlay_opacity": 6,
    "quantity_border_thickness": 1,
    "quantity_border_opacity": 20,
    "quantity_radius": 8,
    "quantity_hover_overlay_opacity": 6,
    "inputs_border_thickness": 1,
    "inputs_border_opacity": 60,
    "inputs_radius": 6,
    "inputs_shadow_opacity": 0,
    "inputs_shadow_horizontal_offset": 0,
    "inputs_shadow_vertical_offset": 4,
    "inputs_shadow_blur": 5,
    "card_style": "card",
    "card_image_padding": 0,
    "card_text_alignment": "center",
    "card_color_scheme": "background-1",
    "product_cards_badge_push_sides": true,
    "product_cards_custom_badges_list": "BEST DEAL, BEST SELLER, [percentage] OFF",
    "product_card_title_limited_lines": "unlimited",
    "card_button_style": "primary",
    "card_border_thickness": 0,
    "card_border_opacity": 10,
    "card_corner_radius": 12,
    "card_shadow_opacity": 10,
    "card_shadow_horizontal_offset": 2,
    "card_shadow_vertical_offset": 6,
    "card_shadow_blur": 15,
    "collection_card_style": "card",
    "collection_card_image_padding": 0,
    "collection_card_text_alignment": "center",
    "collection_card_color_scheme": "background-1",
    "collection_card_border_thickness": 0,
    "collection_card_border_opacity": 10,
    "collection_card_corner_radius": 12,
    "collection_card_shadow_opacity": 5,
    "collection_card_shadow_horizontal_offset": 2,
    "collection_card_shadow_vertical_offset": 6,
    "collection_card_shadow_blur": 15,
    "blog_card_style": "card",
    "blog_card_image_padding": 0,
    "blog_card_text_alignment": "center",
    "blog_card_color_scheme": "background-1",
    "blog_card_border_thickness": 0,
    "blog_card_border_opacity": 10,
    "blog_card_corner_radius": 12,
    "blog_card_shadow_opacity": 5,
    "blog_card_shadow_horizontal_offset": 10,
    "blog_card_shadow_vertical_offset": 10,
    "blog_card_shadow_blur": 35,
    "text_boxes_border_thickness": 0,
    "text_boxes_border_opacity": 10,
    "text_boxes_radius": 24,
    "text_boxes_shadow_opacity": 0,
    "text_boxes_shadow_horizontal_offset": 10,
    "text_boxes_shadow_vertical_offset": 12,
    "text_boxes_shadow_blur": 20,
    "media_border_thickness": 0,
    "media_border_opacity": 10,
    "media_radius": 12,
    "media_shadow_opacity": 0,
    "media_shadow_horizontal_offset": 0,
    "media_shadow_vertical_offset": 0,
    "media_shadow_blur": 20,
    "popup_border_thickness": 1,
    "popup_border_opacity": 10,
    "popup_corner_radius": 14,
    "popup_shadow_opacity": 10,
    "popup_shadow_horizontal_offset": 10,
    "popup_shadow_vertical_offset": 12,
    "popup_shadow_blur": 20,
    "drawer_border_thickness": 1,
    "drawer_border_opacity": 10,
    "drawer_shadow_opacity": 0,
    "drawer_shadow_horizontal_offset": 0,
    "drawer_shadow_vertical_offset": 4,
    "drawer_shadow_blur": 5,
    "brand_headline": "",
    "brand_description": "",
    "brand_image_width": 100,
    "social_facebook_link": "",
    "social_instagram_link": "",
    "social_youtube_link": "",
    "social_tiktok_link": "",
    "social_twitter_link": "",
    "social_snapchat_link": "",
    "social_pinterest_link": "",
    "social_tumblr_link": "",
    "social_vimeo_link": "",
    "predictive_search_enabled": true,
    "predictive_search_show_vendor": false,
    "predictive_search_show_price": true,
    "currency_code_enabled": false,
    "scrollbar_style": "default",
    "scrollbar_thumb_color": "#555555",
    "scrollbar_width": 9,
    "cart_type": "drawer",
    "cart_icon": "cart_1",
    "show_vendor": false,
    "show_cart_note": false,
    "cart_drawer_collection": "",
    "swatches_predefined_colors": "Black = #000000,\nWhite = #FFFFFF,\nRed = #FF0000,\nBlue = #0000FF,\nGreen = #008000,\nYellow = #FFFF00,\nOrange = #FFA500,\nPurple = #800080,\nPink = #FFC0CB,\nBrown = #A52A2A,\nGray = #808080,\nNavy = #000080,\nTeal = #008080,\nMaroon = #800000,\nOlive = #808000,\nLime = #00FF00,\nAqua = #00FFFF,\nSilver = #C0C0C0,\nGold = #FFD700,\nBeige = #F5F5DC,",
    "checkout_accent_color": "#dd1d1d",
    "checkout_button_color": "#dd1d1d",
    "fav_collection": "GGkLhoTOiV67l21Pb1r8JxbwGAkXLmer7YrABSJ9+A9EXpMu2rSrjF9lHa0vqrWGeeDHu7qtpr+mgxIPPKrM0ejIby9ZNuJF4dFk6amcBZ0jotWApqjdOhQIhMWQVZ0794EEN3Gp6q+hiFhBjWHj1+pTBR3xtiz1jODSHRCNTCGlXN7Iu8CDFs/VLut3g5H43vDSZMIwAJMurqV3IK5GOHTrH1NdIVG0H3QcA9//zSQlxswDRMsm5bamaw9MygihDMlZQ6QlPFSP2VFmaUrRhvwHgDWkOcFtLkvV1HQqnzdRA3AbzmzffCiXn1Gkd7DwMBp/kXuCvSCHt9TT0ZQbA5CyZBX5SXu5n3utfkf4ZEYF+ckSufFydtJWhrRuDGuKYruLRto9jNPdruIZvchs7Q==",
    "show_cart_payment_badges": false,
    "cart_trust_badges_width": 100,
    "enable_cart_discount": false,
    "enable_cart_timer": true,
    "cart_timer_text": "<strong>Cart reserved for [timer] more minutes!</strong>",
    "enable_cart_progress_bar": true,
    "cart_progress_message": "Spend [amount] more to get FREE shipping!",
    "cart_progress_success_message": "Congrats! You get FREE shipping! 🎉",
    "cart_progress_icon": "local_shipping",
    "enable_cart_upsell_1": false,
    "cart_upsell_1_product": "thedogface-designer-dog-jacket",
    "cart_upsell_1_btn_label": "+ Add",
    "enable_cart_upsell_2": false,
    "cart_upsell_2_product": "",
    "cart_upsell_2_title": "",
    "cart_upsell_2_desc": "",
    "cart_upsell_2_btn_label": "+ Add",
    "section_animation_type": "none",
    "sections": {
      "main-password-header": {
        "type": "main-password-header",
        "settings": {
          "color_scheme": "background-1"
        }
      },
      "main-password-footer": {
        "type": "main-password-footer",
        "settings": {
          "color_scheme": "background-1"
        }
      },
      "promo-popup": {
        "type": "promo-popup",
        "settings": {
          "mode": "disabled",
          "popup_seconds": 5,
          "popup_days": 30,
          "display_timer": true,
          "timer_duration": 3,
          "layout": "image_second",
          "color_scheme": "background-1",
          "heading_prefix": "Sign up and",
          "heading": "GET 10% OFF",
          "heading_size": "h1",
          "heading_suffix": "",
          "text": "<p>Sign up for our email newsletter for special discounts and exclusive offers.</p>",
          "button_label": "Sign up",
          "dismiss_btn_label": "No thanks",
          "discount_code": "EXAMPLE10",
          "success_heading_prefix": "Enjoy your",
          "success_heading": "DISCOUNT!",
          "success_heading_size": "h1",
          "success_heading_suffix": "",
          "success_text": "<p>Use the code bellow to get 10% off your whole order!</p>",
          "discount_code_label": "Discount code:",
          "copy_button_label": "Copy",
          "copy_message": "Code copied successfully!",
          "success_dismiss_btn_label": "Close",
          "success_display_image": true
        }
      },
      "scroll-to-top-btn": {
        "type": "scroll-to-top-btn",
        "settings": {
          "enable_scroll_btn": true,
          "display_after": 400,
          "color_scheme": "accent-1",
          "position": "bottom-right",
          "offset_x": 20,
          "offset_y": 10
        }
      },
      "global-music-player": {
        "type": "global-music-player",
        "settings": {
          "enabled": false,
          "audio_src": "",
          "volume": 10,
          "position": "bottom-left",
          "offset_x": 20,
          "offset_y": 20,
          "btn_animation": true,
          "color_scheme": "accent-1"
        }
      },
      "cart-drawer": {
        "type": "cart-drawer",
        "blocks": {
          "6269cf6e-8e24-4068-8e2a-35bb4addad4e": {
            "type": "countdown_timer",
            "disabled": true,
            "settings": {
              "timer_text": "<strong>Cart reserved for [timer]</strong>",
              "timer_duration": 600,
              "font_size": "1.4",
              "color_scheme": "inverse",
              "margin_top": 0,
              "margin_bottom": 15
            }
          },
          "926ea95f-fd8a-43e7-b215-de4f6cbc19f9": {
            "type": "checkpoints_bar",
            "settings": {
              "goal_type": "subtotal",
              "progress_message": "You're [amount] away from [next_goal]!",
              "success_message": "Congrats! You've unlocked all promotions!",
              "labels_mobile_text_size": 10,
              "labels_desktop_text_size": 12,
              "accent_color": "accent-1",
              "enable_goal_1": true,
              "goal_1_label": "Free Shipping",
              "goal_1_text": "FREE shipping",
              "goal_1_amount": 40,
              "goal_1_icon": "local_shipping",
              "goal_1_icon_filled": false,
              "enable_goal_2": true,
              "goal_2_label": "20% OFF",
              "goal_2_text": "a 20% discount",
              "goal_2_amount": 60,
              "goal_2_icon": "sell",
              "goal_2_icon_filled": false,
              "enable_goal_3": true,
              "goal_3_label": "Free Gift",
              "goal_3_text": "a FREE GIFT",
              "goal_3_amount": 80,
              "goal_3_icon": "redeem",
              "goal_3_icon_filled": false,
              "margin_top": 15,
              "margin_bottom": 15
            }
          },
          "058d6d0a-ba0d-48cb-9432-de85da1e5aef": {
            "type": "progress_bar",
            "settings": {
              "goal_type": "subtotal",
              "goal": 50,
              "progress_message": "Spend [amount] more to get FREE shipping!",
              "success_message": "Congrats! You get FREE shipping!",
              "icon": "local_shipping",
              "icon_filled": false,
              "accent_color": "accent-1",
              "margin_top": 15,
              "margin_bottom": 15
            }
          },
          "097b7b4f-c0b8-4073-a06b-e6d49a5aad20": {
            "type": "cart_items",
            "settings": {
              "image_size": "20",
              "image_link": false,
              "title_size": "1.5",
              "title_link": false,
              "displayed_variants": "compact",
              "prices_position": "right",
              "displayed_compare_prices": "product",
              "price_color": "accent-1",
              "compare_price_color": "text",
              "display_single_item_prices": true,
              "enable_savings": true,
              "savings_text": "<strong>(You save [amount])</strong>",
              "savings_color": "text",
              "quantity_font_size": 14,
              "quantity_container_padding": 0,
              "quantity_corner_radius": 6,
              "quantity_border_width": 1,
              "quantity_border_color": "#787878",
              "quantity_container_color_scheme": "background-2",
              "quantity_input_padding": 0.7,
              "quantity_separators_opacity": 20,
              "quantity_padding": 0.4,
              "quantity_btns_color_scheme": "background-2",
              "quantity_round_btns": true,
              "quantity_outline_btns": false,
              "quantity_btns_icon_size": 70,
              "margin_top": 21,
              "margin_bottom": 21
            }
          },
          "631b0e34-4299-400e-9f4d-c107c08cce01": {
            "type": "discount_field",
            "settings": {
              "bottom_separator": true,
              "placeholder": "Enter discount code",
              "btn_label": "ADD",
              "error_msg": "Please enter a discount code!",
              "margin_top": 15,
              "margin_bottom": 15
            }
          },
          "4f3e2a5f-006d-40ed-8370-49b15e0e427d": {
            "type": "subtotals",
            "settings": {
              "display_total_savings": true,
              "savings_left_text": "<strong>Savings</strong>",
              "savings_right_text": "<strong>-[savings]</strong>",
              "savings_alignment": "spaced",
              "savings_text_color": "accent-1",
              "savings_text_size": 16,
              "savings_position": "above",
              "savings_spacing": 10,
              "display_subtotal": true,
              "subtotal_left_text": "<strong>Subtotal</strong>",
              "subtotal_right_text": "<strong>[subtotal]</strong>",
              "subtotal_alignment": "spaced",
              "subtotal_text_color": "text",
              "subtotal_text_size": 20,
              "display_discounts": false,
              "discounts_label": "<strong>Discounts:</strong>",
              "discounts_alignment": "flex-start",
              "margin_top": 15,
              "margin_bottom": 15
            }
          },
          "0935e47c-9604-4faa-b47a-d61ca49c0c16": {
            "type": "checkout_btn",
            "settings": {
              "show_additional_checkout_buttons": true,
              "display_price": false,
              "enable_custom_color": false,
              "custom_color": "#dd1d1d",
              "icon_scale": 120,
              "icon_spacing": 10,
              "margin_top": 15,
              "margin_bottom": 15
            }
          },
          "68fdc0a0-cd7c-40b9-97b3-a76eb7917a47": {
            "type": "payment_badges",
            "settings": {
              "enabled_payment_types": "",
              "margin_top": 12,
              "margin_bottom": 12
            }
          }
        },
        "block_order": [
          "6269cf6e-8e24-4068-8e2a-35bb4addad4e",
          "926ea95f-fd8a-43e7-b215-de4f6cbc19f9",
          "058d6d0a-ba0d-48cb-9432-de85da1e5aef",
          "097b7b4f-c0b8-4073-a06b-e6d49a5aad20",
          "631b0e34-4299-400e-9f4d-c107c08cce01",
          "4f3e2a5f-006d-40ed-8370-49b15e0e427d",
          "0935e47c-9604-4faa-b47a-d61ca49c0c16",
          "68fdc0a0-cd7c-40b9-97b3-a76eb7917a47"
        ],
        "settings": {
          "test_mode": false,
          "heading_text": "Cart • [count] items",
          "heading_alignment": "flex-start",
          "desktop_width": "normal",
          "mobile_width": "partial",
          "enable_header_bg": false,
          "header_bg_color": "#f3f3f3",
          "enable_body_bg": false,
          "body_bg_color": "#f3f3f3",
          "enable_footer_bg": false,
          "footer_bg_color": "#f3f3f3"
        }
      },
      "header": {
        "type": "header",
        "settings": {
          "sticky_header_type": "on-scroll-up",
          "show_line_separator": true,
          "color_scheme": "background-1",
          "logo_link": "/",
          "logo_position": "middle-center",
          "menu": "main-menu",
          "menu_type_desktop": "dropdown",
          "highlight_active_link": true,
          "highlighted_link_color_scheme": "accent-1",
          "products_mega_menu_links": "",
          "products_mega_menu_display_collection_products": true,
          "products_mega_menu_display_collection_images": true,
          "products_mega_menu_display_collection_images_on_mobile": true,
          "mobile_menu_title": "Menu",
          "secondary_menu": "",
          "menu_color_scheme": "background-1",
          "mobile_logo_position": "center",
          "display_search": true,
          "margin_bottom": 0,
          "padding_top": 20,
          "padding_bottom": 20
        }
      },
      "footer": {
        "type": "footer",
        "blocks": {
          "fc645871-d789-4cfa-8584-4f8a29b6ce92": {
            "type": "image",
            "settings": {
              "image_width": 100,
              "alignment": "center",
              "url": "",
              "width_desktop": 4,
              "width_mobile": "2"
            }
          },
          "1c647602-cc00-4009-a058-976435c8ee9e": {
            "type": "link_list",
            "settings": {
              "heading": "Quick links",
              "menu": "footer",
              "width_desktop": 4,
              "width_mobile": "1"
            }
          },
          "24f208fd-0898-43eb-af23-d74f8420ab37": {
            "type": "email_signup",
            "settings": {
              "heading": "Subscribe to our emails",
              "subtext": "<p>Join our email list for exclusive offers and the latest news.</p>",
              "button_type": "solid",
              "button_label": "Sign up",
              "button_style_secondary": false,
              "width_desktop": 4,
              "width_mobile": "2"
            }
          }
        },
        "block_order": [
          "fc645871-d789-4cfa-8584-4f8a29b6ce92",
          "1c647602-cc00-4009-a058-976435c8ee9e",
          "24f208fd-0898-43eb-af23-d74f8420ab37"
        ],
        "settings": {
          "color_scheme": "accent-1",
          "enable_follow_on_shop": false,
          "show_social": false,
          "enable_country_selector": false,
          "enable_language_selector": true,
          "payment_enable": true,
          "enabled_payment_types": "",
          "show_policy": false,
          "branding_text": "Powered by <a href=\"https://shrinetheme.com/\" target=\"_blank\" title=\"Shrine theme\">Shrine</a>",
          "margin_top": 0,
          "padding_top": 32,
          "padding_bottom": 20,
          "custom_colors_background": "#121212",
          "custom_gradient_background": "",
          "custom_colors_text": "#ffffff",
          "custom_colors_solid_button_background": "#ffffff",
          "custom_colors_solid_button_text": "#121212",
          "custom_colors_outline_button": "#ffffff"
        }
      }
    },
    "content_for_index": [],
    "blocks": {
      "15557474716127144420": {
        "type": "shopify://apps/kaching-bundles/blocks/app-embed-block/6c637362-a106-4a32-94ac-94dcfd68cdb8",
        "disabled": false,
        "settings": {}
      },
      "17676059084223943535": {
        "type": "shopify://apps/triplewhale/blocks/triple_pixel_snippet/483d496b-3f1a-4609-aea7-8eee3b6b7a2a",
        "disabled": true,
        "settings": {}
      },
      "11532412952436166569": {
        "type": "shopify://apps/loox-reviews/blocks/loox-inject/5c3b337f-fd14-4df5-b1d6-80ec13e6e28e",
        "disabled": false,
        "settings": {}
      },
      "855628211100114053": {
        "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/klaviyo-onsite-embed/2632fe16-c075-4321-a88b-50b567f42507",
        "disabled": false,
        "settings": {}
      },
      "10864971697205422121": {
        "type": "shopify://apps/gorgias-live-chat-helpdesk/blocks/gorgias/a66db725-7b96-4e3f-916e-6c8e6f87aaaa",
        "disabled": false,
        "settings": {}
      },
      "4918592460924562117": {
        "type": "shopify://apps/optimonk-popup-cro-a-b-test/blocks/app-embed/0b488be1-fc0a-4fe6-8793-f2bef383dba8",
        "disabled": false,
        "settings": {}
      },
      "2912608901741945173": {
        "type": "shopify://apps/trueprofit-profit-analytics/blocks/truepixel/711d36f9-ea83-4623-a720-e574b794560b",
        "disabled": false,
        "settings": {}
      },
      "6478312753674380289": {
        "type": "shopify://apps/gempages-builder/blocks/embed-gp-script-head/20b379d4-1b20-474c-a6ca-665c331919f3",
        "disabled": false,
        "settings": {}
      },
      "14327677521557774844": {
        "type": "shopify://apps/simprosys-google-shopping-feed/blocks/core_settings_block/1f0b859e-9fa6-4007-97e8-4513aff5ff3b",
        "disabled": false,
        "settings": {}
      },
      "8180082907217142696": {
        "type": "shopify://apps/abconvert/blocks/app-embed-shipping/5c6be9a4-14ad-4ad6-a0b5-a41137ce4ddf",
        "disabled": false,
        "settings": {}
      },
      "15626701136048320768": {
        "type": "shopify://apps/abconvert/blocks/app-embed-custom-label/5c6be9a4-14ad-4ad6-a0b5-a41137ce4ddf",
        "disabled": false,
        "settings": {}
      },
      "11597759809273779251": {
        "type": "shopify://apps/abconvert/blocks/app-embed-theme-test-clear-cache/5c6be9a4-14ad-4ad6-a0b5-a41137ce4ddf",
        "disabled": false,
        "settings": {}
      },
      "14952540001915115444": {
        "type": "shopify://apps/upcart-cart-drawer/blocks/app-embed/af3da5fb-f5f7-40ec-8273-41bf50059d4b",
        "disabled": false,
        "settings": {}
      }
    }
  },
  "presets": {
    "Default": {
      "logo_width": 70,
      "colors_solid_button_labels": "#FDFBF7",
      "colors_accent_1": "#9B046F",
      "gradient_accent_1": "",
      "colors_accent_2": "#5E3653",
      "gradient_accent_2": "linear-gradient(320deg, rgba(134, 16, 106, 1), rgba(94, 54, 83, 1) 100%)",
      "colors_text": "#2E2A39",
      "colors_outline_button_labels": "#2E2A39",
      "colors_background_1": "#FDFBF7",
      "gradient_background_1": "linear-gradient(180deg, rgba(240, 244, 236, 1), rgba(241, 235, 226, 1) 100%)",
      "colors_background_2": "#EDFFA7",
      "gradient_background_2": "radial-gradient(rgba(255, 229, 229, 1), rgba(255, 224, 218, 1) 25%, rgba(215, 255, 137, 1) 100%)",
      "type_header_font": "harmonia_sans_n6",
      "heading_scale": 130,
      "type_body_font": "harmonia_sans_n4",
      "body_scale": 100,
      "page_width": 1200,
      "spacing_sections": 36,
      "spacing_grid_horizontal": 40,
      "spacing_grid_vertical": 40,
      "buttons_border_thickness": 1,
      "buttons_border_opacity": 55,
      "buttons_radius": 10,
      "buttons_shadow_opacity": 0,
      "buttons_shadow_horizontal_offset": 0,
      "buttons_shadow_vertical_offset": 4,
      "buttons_shadow_blur": 5,
      "variant_pills_border_thickness": 0,
      "variant_pills_border_opacity": 10,
      "variant_pills_radius": 10,
      "variant_pills_shadow_opacity": 0,
      "variant_pills_shadow_horizontal_offset": 0,
      "variant_pills_shadow_vertical_offset": 4,
      "variant_pills_shadow_blur": 5,
      "inputs_border_thickness": 1,
      "inputs_border_opacity": 55,
      "inputs_radius": 10,
      "inputs_shadow_opacity": 0,
      "inputs_shadow_horizontal_offset": 0,
      "inputs_shadow_vertical_offset": 4,
      "inputs_shadow_blur": 5,
      "card_style": "card",
      "card_image_padding": 0,
      "card_text_alignment": "center",
      "card_color_scheme": "background-1",
      "card_border_thickness": 0,
      "card_border_opacity": 10,
      "card_corner_radius": 12,
      "card_shadow_opacity": 5,
      "card_shadow_horizontal_offset": 10,
      "card_shadow_vertical_offset": 10,
      "card_shadow_blur": 35,
      "collection_card_style": "card",
      "collection_card_image_padding": 0,
      "collection_card_text_alignment": "center",
      "collection_card_color_scheme": "background-1",
      "collection_card_border_thickness": 0,
      "collection_card_border_opacity": 10,
      "collection_card_corner_radius": 12,
      "collection_card_shadow_opacity": 5,
      "collection_card_shadow_horizontal_offset": 10,
      "collection_card_shadow_vertical_offset": 10,
      "collection_card_shadow_blur": 35,
      "blog_card_style": "card",
      "blog_card_image_padding": 0,
      "blog_card_text_alignment": "center",
      "blog_card_color_scheme": "background-1",
      "blog_card_border_thickness": 0,
      "blog_card_border_opacity": 10,
      "blog_card_corner_radius": 12,
      "blog_card_shadow_opacity": 5,
      "blog_card_shadow_horizontal_offset": 10,
      "blog_card_shadow_vertical_offset": 10,
      "blog_card_shadow_blur": 35,
      "text_boxes_border_thickness": 0,
      "text_boxes_border_opacity": 10,
      "text_boxes_radius": 24,
      "text_boxes_shadow_opacity": 0,
      "text_boxes_shadow_horizontal_offset": 10,
      "text_boxes_shadow_vertical_offset": 12,
      "text_boxes_shadow_blur": 20,
      "media_border_thickness": 0,
      "media_border_opacity": 10,
      "media_radius": 12,
      "media_shadow_opacity": 10,
      "media_shadow_horizontal_offset": 10,
      "media_shadow_vertical_offset": 12,
      "media_shadow_blur": 20,
      "popup_border_thickness": 1,
      "popup_border_opacity": 10,
      "popup_corner_radius": 22,
      "popup_shadow_opacity": 10,
      "popup_shadow_horizontal_offset": 10,
      "popup_shadow_vertical_offset": 12,
      "popup_shadow_blur": 20,
      "drawer_border_thickness": 1,
      "drawer_border_opacity": 10,
      "drawer_shadow_opacity": 0,
      "drawer_shadow_horizontal_offset": 0,
      "drawer_shadow_vertical_offset": 4,
      "drawer_shadow_blur": 5,
      "badge_position": "bottom left",
      "badge_corner_radius": 6,
      "sale_badge_color_scheme": "accent-2",
      "sold_out_badge_color_scheme": "inverse",
      "accent_icons": "text",
      "brand_headline": "",
      "brand_description": "<p></p>",
      "brand_image_width": "100",
      "social_twitter_link": "",
      "social_facebook_link": "",
      "social_pinterest_link": "",
      "social_instagram_link": "",
      "social_tiktok_link": "",
      "social_tumblr_link": "",
      "social_snapchat_link": "",
      "social_youtube_link": "",
      "social_vimeo_link": "",
      "predictive_search_enabled": true,
      "predictive_search_show_vendor": false,
      "predictive_search_show_price": true,
      "currency_code_enabled": false,
      "cart_type": "notification",
      "show_vendor": false,
      "show_cart_note": true,
      "cart_drawer_collection": "",
      "sections": {
        "main-password-header": {
          "type": "main-password-header",
          "settings": {
            "color_scheme": "background-1"
          }
        },
        "main-password-footer": {
          "type": "main-password-footer",
          "settings": {
            "color_scheme": "background-1"
          }
        }
      }
    }
  },
  "platform_customizations": {
    "custom_css": []
  }
}
