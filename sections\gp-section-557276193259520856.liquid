

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557276193259520856.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557276193259520856.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557276193259520856.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557276193259520856.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557276193259520856.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557276193259520856.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557276193259520856.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557276193259520856.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557276193259520856.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557276193259520856.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-557276193259520856.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557276193259520856.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557276193259520856.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557276193259520856.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557276193259520856.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557276193259520856.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557276193259520856.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557276193259520856.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557276193259520856.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557276193259520856.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557276193259520856.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-557276193259520856.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557276193259520856.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557276193259520856.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557276193259520856.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557276193259520856.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557276193259520856.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557276193259520856.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557276193259520856.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557276193259520856.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557276193259520856.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557276193259520856.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-557276193259520856.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557276193259520856.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-557276193259520856.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557276193259520856.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557276193259520856.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-557276193259520856.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-557276193259520856.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557276193259520856.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557276193259520856.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-557276193259520856.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557276193259520856.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557276193259520856.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557276193259520856.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557276193259520856.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-557276193259520856 .gp-g-subheading-3{font-family:var(--g-sh3-ff);font-size:var(--g-sh3-size);font-style:var(--g-sh3-fs);font-weight:var(--g-sh3-weight);letter-spacing:var(--g-sh3-ls);line-height:var(--g-sh3-lh)}.gps-557276193259520856 .gp-relative{position:relative}.gps-557276193259520856 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557276193259520856 .gp-mb-0{margin-bottom:0}.gps-557276193259520856 .gp-flex{display:flex}.gps-557276193259520856 .gp-grid{display:grid}.gps-557276193259520856 .\!gp-hidden{display:none!important}.gps-557276193259520856 .gp-hidden{display:none}.gps-557276193259520856 .gp-max-w-full{max-width:100%}.gps-557276193259520856 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557276193259520856 .gp-flex-col{flex-direction:column}.gps-557276193259520856 .gp-gap-y-0{row-gap:0}.gps-557276193259520856 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557276193259520856 .gp-duration-200{transition-duration:.2s}.gps-557276193259520856 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-557276193259520856 .tablet\:\!gp-hidden{display:none!important}.gps-557276193259520856 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-557276193259520856 .mobile\:\!gp-hidden{display:none!important}.gps-557276193259520856 .mobile\:gp-hidden{display:none}}.gps-557276193259520856 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557276193259520856 .\[\&_p\]\:gp-inline p{display:inline}.gps-557276193259520856 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557276193259520856 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gaVwthrm7V" data-id="gaVwthrm7V"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:auto;--pb:auto;--pt-mobile:auto;--pb-mobile:var(--g-s-2xl);--pr-mobile:0px;--pt-tablet:auto;--pb-tablet:2px;--pr-tablet:0px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gaVwthrm7V gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g7Yd9AjGvj gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g7d9HaxtkR" data-id="g7d9HaxtkR"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g7d9HaxtkR gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g_-Fg21r89 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gWh-teGTbd">
    <div
      parentTag="Col"
        class="gWh-teGTbd "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt-mobile:var(--g-s-l);--pt-mobile:10px;--pb-mobile:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-subheading-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--size:32px;--size-tablet:32px;--size-mobile:24px;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pl:0px;--pr:0px;--pt:0px;--pb:0px;overflow:hidden"
        >{{ section.settings.ggWh-teGTbd_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 5",
    "tag": "section",
    "class": "gps-557276193259520856 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/85f518-88/apps/gempages-cro/app/shopify/edit?pageType=GP_INDEX&editorId=557276193192149848&sectionId=557276193259520856)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggWh-teGTbd_text","label":"ggWh-teGTbd_text","default":"Transformed Skin, Happy Customers"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
