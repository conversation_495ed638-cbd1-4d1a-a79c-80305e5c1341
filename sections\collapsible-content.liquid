{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
  .container-color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_contaner_colors_background.red }}, {{ section.settings.custom_contaner_colors_background.green }}, {{ section.settings.custom_contaner_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_container_gradient_background != blank %}{{ section.settings.custom_container_gradient_background }}{% else %}{{ section.settings.custom_contaner_colors_background }}{% endif %};
    --color-foreground: {{ section.settings.custom_contaner_colors_text.red }}, {{ section.settings.custom_contaner_colors_text.green }}, {{ section.settings.custom_contaner_colors_text.blue }};
  }
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="collapsible-content collapsible-{{ section.settings.layout }}-layout isolate{% if section.settings.layout == 'section' %} page-width{% elsif section.settings.layout == 'none' %} content-container content-container--full-width{% endif %}">
    <div class="collapsible-content__wrapper section-{{ section.id }}-padding{% if section.settings.layout == 'section' %} content-container container-color-scheme-{{ section.id }} color-{{ section.settings.container_color_scheme }} gradient{% endif %}">
      <div class="{% if section.settings.image == blank %}collapsible-content-wrapper-narrow{% else %}page-width{% endif %}">
        <div class="collapsible-content__header animate-item animate-item--child index-0" style="text-align: {{ section.settings.heading_alignment }};">
          {%- if section.settings.caption != blank -%}
            <p class="caption-with-letter-spacing">{{ section.settings.caption }}</p>
          {% endif %}
          {%- if section.settings.title != blank -%}
            <h2 class="collapsible-content__heading {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
              {{ section.settings.title }}
            </h2>
          {%- else -%}
            <h2 class="visually-hidden">{{ 'accessibility.collapsible_content_title' | t }}</h2>
          {% endif %}
        </div>
        <div class="grid grid--1-col grid--2-col-tablet collapsible-content__grid{% if section.settings.desktop_layout == 'image_second' %} collapsible-content__grid--reverse{% endif %}">
          {%- if section.settings.image != blank or section.settings.video != blank -%}
            <div class="grid__item collapsible-content__grid-item animate-item animate-item--child index-1">
              <div
                class="collapsible-content__media collapsible-content__media--{{ section.settings.image_ratio }} media global-media-settings gradient"
                {% if section.settings.image_ratio == 'adapt' and section.settings.video != blank %}
                  style="padding-bottom: {{ 1 | divided_by: section.settings.video.aspect_ratio | times: 100 }}%;"
                {% elsif section.settings.image_ratio == 'adapt' %}
                  style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"
                {% endif %}
              >
                {% if section.settings.video != blank %}
                  {% liquid
                    assign autoplay = false
                    if section.settings.video_muted_autoplay
                      assign autoplay = true
                    endif
                  %}
                  <internal-video data-autoplay='{{ autoplay }}' data-no-play-btn="{{ autoplay }}">
                    <video 
                      width="100%" 
                      height="auto" 
                      preload='metadata'
                      poster="{{ section.settings.video.preview_image | image_url }}"
                      {% if autoplay %}
                        muted autoplay loop
                      {% endif %}
                      playsinline disablepictureinpicture
                    >
                      {% for source in section.settings.video.sources %}
                        <source 
                          {% if autoplay %}data-{% endif %}src="{{ source.url }}" 
                          type="{{ source.mime_type }}"
                        >
                      {% endfor %}
                    </video>
                    <button class="internal-video__play"{% if autoplay %} style='visibility: hidden;'{% endif %}>
                      <div class="play-button color-accent-1">
                        {%- render 'icon-play' -%}
                      </div>
                    </button>
                  </internal-video>
                {% else %}
                  {%- capture sizes -%}
                    (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                    (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
                  {%- endcapture -%}
                  {{
                    section.settings.image
                    | image_url: width: 1500
                    | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1250, 1500'
                  }}
                {% endif %}
              </div>
            </div>
          {% endif %}
          <div class="grid__item">
            {%- for block in section.blocks -%}
              <div
                class="accordion animate-item animate-item--child{% if section.settings.layout == 'row' %} content-container container-color-scheme-{{ section.id }} color-{{ section.settings.container_color_scheme }} gradient{% endif %} accordion--{{ section.settings.row_heading_size }}{% if section.settings.display_top_border %} accordion--top-border{% endif %}"
                style="--index:{{ forloop.index }};"
                {{ block.shopify_attributes }}
              >
                <details class='accordion__details'{% if section.settings.open_first_collapsible_row and forloop.first %} open{% endif %}>
                  <summary class="accordion__summary">
                    <div class="summary__title">
                      {% if block.settings.custom_icon != blank %}
                        <img
                          src="{{ block.settings.custom_icon | image_url }}"
                          {% if block.settings.custom_icon.alt != blank %}
                            alt="{{ block.settings.custom_icon.alt | escape }}"
                          {% else %}
                            role="presentation"
                          {% endif %}
                          height="auto"
                          width="auto"
                          loading="lazy"
                        >
                      {% else %}
                        {% render 'material-icon', icon: block.settings.icon, filled: block.settings.filled_icon %}
                      {% endif %}
                      <h2 class="h4 accordion__title">
                        {{ block.settings.heading | default: block.settings.page.title }}
                      </h2>
                    </div>
                    {% if section.settings.collapse_icon == 'carret' %}
                      {% render 'icon-caret' %}
                    {% else %}
                      {% render 'icon-plus' %}
                    {% endif %}
                  </summary>
                </details>
                <div class="accordion__content-wrapper">
                  <div
                    class="accordion__content rte"
                    id="CollapsibleAccordion-{{ block.id }}-{{ section.id }}"
                    role="region"
                    aria-labelledby="Summary-{{ block.id }}-{{ section.id }}"
                  >
                    {{ block.settings.row_content }}
                    {{ block.settings.page.content }}
                  </div>
                </div>
              </div>
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.collapsible_content.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "caption",
      "label": "t:sections.collapsible_content.settings.caption.label"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Collapsible content",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "select",
      "id": "heading_alignment",
      "label": "Heading alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.collapsible_content.settings.layout.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collapsible_content.settings.layout.options__1.label"
        },
        {
          "value": "row",
          "label": "t:sections.collapsible_content.settings.layout.options__2.label"
        },
        {
          "value": "section",
          "label": "t:sections.collapsible_content.settings.layout.options__3.label"
        }
      ],
      "default": "none"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "select",
      "id": "container_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-2",
      "label": "Container color scheme",
      "info": "Visible when Layout is set to Row or Section container. Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "checkbox",
      "id": "open_first_collapsible_row",
      "default": false,
      "label": "t:sections.collapsible_content.settings.open_first_collapsible_row.label"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.collapsible_content.settings.image.label"
    },
    {
      "type": "header",
      "content": "Or"
    },
    {
      "type": "video",
      "id": "video",
      "label": "Video",
      "info": "Use autoplay mp4 instead of GIFs & animated WEBPs."
    },
    {
      "type": "checkbox",
      "id": "video_muted_autoplay",
      "label": "Muted autoplay",
      "default": true,
      "info": "Use this instead of GIFs & animated WEBPs."
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to media"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "adapt",
      "label": "Media height"
    },
    {
      "type": "select",
      "id": "desktop_layout",
      "options": [
        {
          "value": "image_first",
          "label": "Media first"
        },
        {
          "value": "image_second",
          "label": "Media second"
        }
      ],
      "default": "image_second",
      "label": "Desktop layout",
      "info": "Media is always first on mobile."
    },
    {
      "type": "header",
      "content": "Rows"
    },
    {
      "type": "select",
      "id": "row_heading_size",
      "label": "Heading size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "collapse_icon",
      "label": "Collapse icon",
      "options": [
        {
          "value": "carret",
          "label": "Carret"
        },
        {
          "value": "plus",
          "label": "Plus"
        }
      ],
      "default": "carret"
    },
    {
      "type": "checkbox",
      "id": "display_top_border",
      "label": "Display top border",
      "default": true,
      "info": "This option is automatically optimized for stacked rows."
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "header",
      "content": "Custom container color scheme",
      "info": "Applied if Container color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_contaner_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_container_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "color",
      "id": "custom_contaner_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "collapsible_row",
      "name": "t:sections.collapsible_content.blocks.collapsible_row.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.heading.label",
          "info": "t:sections.collapsible_content.blocks.collapsible_row.settings.heading.info"
        },
        {
          "type": "text",
          "id": "icon",
          "default": "check_box",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon"
        },
        {
          "type": "richtext",
          "id": "row_content",
          "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.row_content.label"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.page.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.collapsible_content.presets.name",
      "blocks": [
        {
          "type": "collapsible_row"
        },
        {
          "type": "collapsible_row"
        },
        {
          "type": "collapsible_row"
        },
        {
          "type": "collapsible_row"
        }
      ]
    }
  ]
}
{% endschema %}
