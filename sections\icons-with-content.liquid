{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
    --color-button: {{ section.settings.custom_colors_solid_button_background.red }}, {{ section.settings.custom_colors_solid_button_background.green }}, {{ section.settings.custom_colors_solid_button_background.blue }};
    --color-button-text: {{ section.settings.custom_colors_solid_button_text.red }}, {{ section.settings.custom_colors_solid_button_text.green }}, {{ section.settings.custom_colors_solid_button_text.blue }};
    --color-base-outline-button-labels: {{ section.settings.custom_colors_outline_button.red }}, {{ section.settings.custom_colors_outline_button.green }}, {{ section.settings.custom_colors_outline_button.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="icons-with-content page-width section-{{ section.id }}-padding">
    <div class="icon-with-content__grid section-group__container__child-grid{% if section.settings.layout == 'text_first' %} icon-with-content__grid--desktop-reverse{% endif %}{% if section.settings.mobile_layout == 'text_first' %} icon-with-content__mobile-grid--reverse{% endif %}">
      <div
        class="icon-with-content__icons icon-with-content__icons--desktop-{{ section.settings.icons_desktop_layout }} icon-with-content__icons--mobile-{{ section.settings.icons_mobile_layout }} animate-item animate-item--child index-0"
      >
        {%- for block in section.blocks -%}
          {% case block.type %}
            {%- when 'icon' -%}
              <div class="icons-with-text__icon-item icons-with-text__icon-item--{{ section.settings.icon_position }} icons-with-text__icon-item--{{ section.settings.icon_text_alignment }}">
                {% if section.settings.icon_position != 'next-to-title' %}
                  <div class="icons-with-text__icon__icon icons-with-text__icon__icon--{{ section.settings.icon_size }} color-{{ section.settings.icon_color }}">
                    {%- if block.settings.image != blank -%}
                      <img
                        src="{{ block.settings.image | image_url: width: 300 }}"
                        {% if block.settings.image.alt != blank %}
                          alt="{{ block.settings.image.alt | escape }}"
                        {% else %}
                          role="presentation"
                        {% endif %}
                        height="auto"
                        width="auto"
                        loading="lazy"
                      >
                    {%- elsif block.settings.icon != 'none' -%}
                      {% render 'material-icon', icon: block.settings.icon, filled: block.settings.filled_icon %}
                    {%- endif -%}
                  </div>
                {% endif %}
                <div class="icons-with-text__icon__text">
                  <h3 class="icons-with-text__icon__title {{ section.settings.icon_heading_size }}">
                    {% if section.settings.icon_position == 'next-to-title' %}
                      <div class="icons-with-text__icon__icon icons-with-text__icon__icon--title icons-with-text__icon__icon--{{ section.settings.icon_size }} color-{{ section.settings.icon_color }}">
                        {%- if block.settings.image != blank -%}
                          <img
                            src="{{ block.settings.image | image_url: width: 300 }}"
                            {% if block.settings.image.alt != blank %}
                              alt="{{ block.settings.image.alt | escape }}"
                            {% else %}
                              role="presentation"
                            {% endif %}
                            height="auto"
                            width="auto"
                            loading="lazy"
                          >
                        {%- elsif block.settings.icon != 'none' -%}
                          {% render 'material-icon', icon: block.settings.icon, filled: block.settings.filled_icon %}
                        {%- endif -%}
                      </div>
                    {% endif %}
                    <span>
                      {{ block.settings.title }}
                    </span>
                  </h3>
                  {%- if block.settings.text != blank -%}
                    <div class="rte">
                      {{ block.settings.text }}
                    </div>
                  {%- endif -%}
                </div>
              </div>
          {% endcase %}
        {% endfor %}
      </div>
      <div class="icon-with-content__content{% if section.settings.hide_content_on_mobile %} icon-with-content__content--hide-on-mobile{% endif %} {{ section.settings.desktop_content_alignment }} animate-item animate-item--child index-0">
        {%- for block in section.blocks -%}
          {% case block.type %}
            {%- when 'heading' -%}
              <h2 class="icon-with-content__heading {{ block.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ block.settings.title_highlight_color }}' {{ block.shopify_attributes }}>
                {{ block.settings.title }}
              </h2>
            {%- when 'caption' -%}
              <p
                class="icon-with-content__text icon-with-content__text--caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
              >
                {{ block.settings.caption }}
              </p>
            {%- when 'text' -%}
              <div class="icon-with-content__text rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                {{ block.settings.text }}
              </div>
            {%- when 'button' -%}
              {%- if block.settings.button_label != blank -%}
                <a
                  {% if block.settings.button_link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.button_link }}"
                  {% endif %}
                  class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.button_label | escape }}
                </a>
              {%- endif -%}
            {% when 'image' %}
              <div class="icons-with-content__image">
                {%- capture sizes -%}
                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                  (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 30px) / 2)
                {%- endcapture -%}
                {{
                  block.settings.image
                  | image_url: width: 1500
                  | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                }}
              </div>
            {% when 'video' %}
              <div class="icons-with-content__image">
                {% liquid
                  assign autoplay = false
                  if block.settings.video_muted_autoplay
                    assign autoplay = true
                  endif
                %}
                <internal-video data-autoplay="{{ autoplay }}" data-no-play-btn="{{ autoplay }}">
                  <video 
                    width="100%" 
                    height="auto" 
                    preload='metadata'
                    poster="{{ block.settings.video.preview_image | image_url }}"
                    {% if autoplay %}
                      muted autoplay loop
                    {% endif %}
                    playsinline disablepictureinpicture
                  >
                    {% for source in block.settings.video.sources %}
                      <source 
                        {% if autoplay %}data-{% endif %}src="{{ source.url }}" 
                        type="{{ source.mime_type }}"
                      >
                    {% endfor %}
                  </video>
                  <button class="internal-video__play"{% if autoplay %} style='visibility: hidden;'{% endif %}>
                    <div class="play-button color-accent-1">
                      {%- render 'icon-play' -%}
                    </div>
                  </button>
                </internal-video>
              </div>
            {% when 'atc_button' %}
              {% if block.settings.atc_product == blank %}
                <button
                  id="SectionAtcBtn-{{ section.id }}"
                  type="button"
                  class="button main-product-atc button--has-spinner"
                  {% if product.selected_or_first_available_variant.available == false %}
                    disabled
                  {% endif %}
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.button_label }}
                  <div class="loading-overlay__spinner">
                    <svg
                      aria-hidden="true"
                      focusable="false"
                      class="spinner"
                      viewBox="0 0 66 66"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                    </svg>
                  </div>
                </button>
              {% else %}
                {% assign product_form_id = 'section-product-form-'
                  | append: section.id
                  | append: block.id
                %}
                {% render 'separate-atc-btn',
                  product: block.settings.atc_product,
                  product_form_id: product_form_id,
                  label: block.settings.button_label,
                  skip_cart: block.settings.atc_skip_cart
                %}
              {% endif %}
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Icons with content",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Icons section"
    },
    {
      "type": "select",
      "id": "icon_size",
      "label": "Icon size",
      "options": [
        {
          "value": "xs",
          "label": "Extra small"
        },
        {
          "value": "s",
          "label": "Small"
        },
        {
          "value": "m",
          "label": "Medium"
        },
        {
          "value": "l",
          "label": "Large"
        },
        {
          "value": "xl",
          "label": "Extra large"
        },
        {
          "value": "xxl",
          "label": "Double extra large"
        }
      ],
      "default": "m"
    },
    {
      "type": "select",
      "id": "icon_position",
      "options": [
        {
          "value": "above",
          "label": "Above title & text"
        },
        {
          "value": "next-to-title",
          "label": "Next to title"
        },
        {
          "value": "next-to-text",
          "label": "Next to text & title"
        }
      ],
      "label": "Icon position",
      "default": "next-to-title"
    },
    {
      "type": "select",
      "id": "icon_color",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "text",
          "label": "Text"
        }
      ],
      "default": "accent-1",
      "label": "Icon color"
    },
    {
      "type": "select",
      "id": "icon_heading_size",
      "options": [
        {
          "value": "h5",
          "label": "Extra small"
        },
        {
          "value": "h4",
          "label": "Small"
        },
        {
          "value": "h3",
          "label": "Medium"
        },
        {
          "value": "h2",
          "label": "Large"
        }
      ],
      "default": "h3",
      "label": "Heading size"
    },
    {
      "type": "select",
      "id": "icon_text_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Text alignment",
      "info": "Text is automatically centered if the icon position is set to above title & text."
    },
    {
      "type": "select",
      "id": "icons_desktop_layout",
      "options": [
        {
          "value": "1-column",
          "label": "1 column"
        },
        {
          "value": "2-columns",
          "label": "2 columns"
        }
      ],
      "default": "1-column",
      "label": "Desktop layout"
    },
    {
      "type": "select",
      "id": "icons_mobile_layout",
      "options": [
        {
          "value": "1-column",
          "label": "1 column"
        },
        {
          "value": "2-columns",
          "label": "2 columns"
        }
      ],
      "default": "1-column",
      "label": "Mobile layout"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Desktop content alignment"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "text_first",
          "label": "Content first"
        },
        {
          "value": "image_first",
          "label": "Content second"
        }
      ],
      "default": "image_first",
      "label": "Desktop content placement"
    },
    {
      "type": "select",
      "id": "mobile_layout",
      "options": [
        {
          "value": "text_first",
          "label": "Content first"
        },
        {
          "value": "image_first",
          "label": "Content second"
        }
      ],
      "default": "text_first",
      "label": "Mobile content placement"
    },
    {
      "type": "checkbox",
      "id": "hide_content_on_mobile",
      "label": "Hide content on mobile",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#2E2A39",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_background",
      "default": "#dd1d1d",
      "label": "Solid button background"
    },
    {
      "type": "color",
      "id": "custom_colors_solid_button_text",
      "default": "#ffffff",
      "label": "Solid button label"
    },
    {
      "type": "color",
      "id": "custom_colors_outline_button",
      "default": "#dd1d1d",
      "label": "Outline button"
    }
  ],
  "blocks": [
    {
      "type": "icon",
      "name": "Icon with text",
      "settings": [
        {
          "type": "text",
          "id": "icon",
          "default": "check_circle",
          "label": "Icon",
          "info": "[View all available icons](https:\/\/fonts.google.com\/icons?icon.set=Material+Symbols&icon.platform=web)."
        },
        {
          "type": "checkbox",
          "id": "filled_icon",
          "default": false,
          "label": "Filled icon"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Custom icon"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Icon with text",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an icon to focus on your chosen product, collection, or blog post</p>",
          "label": "Text"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.image-with-text.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Content heading",
          "label": "Heading",
          "info": "Bold certain words to highlight them with a different color."
        },
        {
          "type": "color",
          "id": "title_highlight_color",
          "label": "Heading highlight color",
          "default": "#6D388B"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.image-with-text.blocks.caption.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Add a tagline",
          "label": "t:sections.image-with-text.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.image-with-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair icons with content to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-with-text.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text.blocks.button.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.image-with-text.blocks.button.settings.button_label.label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
          "default": false
        }
      ]
    },
    {
      "type": "atc_button",
      "name":"Add to Cart button",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "label": "Button label",
          "default": "Add to Cart"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "limit": 1,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "product",
          "id": "atc_product",
          "label": "ATC Custom product",
          "info": "IMPORTANT: If empty, the button will add the main product FROM THE PRODUCT PAGE to cart (INCLUDING the selected variant/quantity, upsells etc.)"
        },
        {
          "type": "checkbox",
          "id": "atc_skip_cart",
          "label": "ATC Custom product skip cart"
        }
      ]
    },
    {
      "type": "video",
      "name": "Autoplay video",
      "limit": 1,
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "checkbox",
          "id": "video_muted_autoplay",
          "label": "Muted autoplay",
          "default": true,
          "info": "Use this instead of GIFs & animated WEBPs."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Icons with content",
      "blocks": [
        {
          "type": "icon"
        },
        {
          "type": "icon"
        },
        {
          "type": "icon"
        },
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
