/**
 * Hidden Variant Bundles System
 * Implements the "Hidden Variant" method for product bundles
 */

class HiddenVariantBundles extends HTMLElement {
  constructor() {
    super();
    this.sectionId = this.dataset.section;
    this.currentTab = 0;
    this.bundleProducts = new Map();
    this.bundleVariants = new Map();
    this.isLoading = false;
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadInitialTab();
  }

  setupEventListeners() {
    // Tab switching
    this.querySelectorAll('.bundle-tab').forEach((tab, index) => {
      tab.addEventListener('click', () => this.switchTab(index));
    });

    // Add to cart button
    this.querySelector('.bundle-add-to-cart')?.addEventListener('click', () => {
      this.addBundleToCart();
    });
  }

  async switchTab(tabIndex) {
    if (this.isLoading || tabIndex === this.currentTab) return;

    // Update tab UI
    this.querySelectorAll('.bundle-tab').forEach((tab, index) => {
      tab.classList.toggle('bundle-tab--active', index === tabIndex);
    });

    this.querySelectorAll('.bundle-tab-content').forEach((content, index) => {
      content.classList.toggle('bundle-tab-content--active', index === tabIndex);
    });

    this.currentTab = tabIndex;
    
    // Load products for this tab
    await this.loadTabProducts(tabIndex);
  }

  async loadInitialTab() {
    await this.loadTabProducts(0);
  }

  async loadTabProducts(tabIndex) {
    const tabContent = this.querySelector(`[data-tab-content="${tabIndex}"]`);
    if (!tabContent) return;

    const collectionHandle = tabContent.dataset.collection;
    const productElements = tabContent.querySelectorAll('.bundle-product');

    this.isLoading = true;
    this.updateLoadingState(true);

    try {
      // Load product data and find bundle variants for each product
      const productPromises = Array.from(productElements).map(async (productEl) => {
        const handle = productEl.dataset.productHandle;
        return this.loadProductData(handle, productEl);
      });

      await Promise.all(productPromises);
      this.updateBundleTotal();
      this.updateAddToCartButton();
    } catch (error) {
      console.error('Error loading bundle products:', error);
      this.showError('Failed to load bundle products. Please try again.');
    } finally {
      this.isLoading = false;
      this.updateLoadingState(false);
    }
  }

  async loadProductData(handle, productElement, retryCount = 0) {
    const maxRetries = 3;

    try {
      // Fetch product data via AJAX API with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`/products/${handle}.js`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch product ${handle}`);
      }

      const product = await response.json();

      // Validate product data
      if (!product || !product.variants || !Array.isArray(product.variants)) {
        throw new Error(`Invalid product data for ${handle}`);
      }

      // Find the Bundle variant
      const bundleVariant = this.findBundleVariant(product);

      if (bundleVariant) {
        // Validate bundle variant data
        if (!bundleVariant.id || bundleVariant.price === undefined || bundleVariant.price === null) {
          console.error(`Invalid bundle variant data for ${handle}:`, bundleVariant);
          productElement.classList.add('error');
          this.updateProductDisplay(productElement, product, null);
          return;
        }

        // Store the bundle variant data
        this.bundleVariants.set(handle, {
          id: bundleVariant.id,
          price: bundleVariant.price,
          available: bundleVariant.available !== false, // Default to true if undefined
          title: bundleVariant.title || `${product.title} (Bundle)`,
          inventory_quantity: bundleVariant.inventory_quantity,
          inventory_management: bundleVariant.inventory_management
        });

        // Update the product display with bundle pricing
        this.updateProductDisplay(productElement, product, bundleVariant);
        productElement.classList.remove('error');
      } else {
        // Product doesn't have a Bundle variant
        console.warn(`Product ${handle} does not have a Bundle variant`);
        productElement.classList.add('error');
        this.updateProductDisplay(productElement, product, null);
      }
    } catch (error) {
      console.error(`Error loading product ${handle} (attempt ${retryCount + 1}):`, error);

      // Retry logic for network errors
      if (retryCount < maxRetries && (
          error.name === 'AbortError' ||
          error.message.includes('fetch') ||
          error.message.includes('network')
        )) {
        console.log(`Retrying product ${handle} in ${(retryCount + 1) * 1000}ms...`);
        setTimeout(() => {
          this.loadProductData(handle, productElement, retryCount + 1);
        }, (retryCount + 1) * 1000);
        return;
      }

      productElement.classList.add('error');
      this.updateProductDisplay(productElement, null, null);
    }
  }

  findBundleVariant(product) {
    // Look for a variant where one of the options is "Bundle" (case-insensitive)
    if (!product || !product.variants || !Array.isArray(product.variants)) {
      console.warn('Invalid product data for bundle variant search');
      return null;
    }

    return product.variants.find(variant => {
      if (!variant) return false;

      // Safely handle potentially null/undefined option values
      const option1 = this.safeStringClean(variant.option1);
      const option2 = this.safeStringClean(variant.option2);
      const option3 = this.safeStringClean(variant.option3);

      return option1 === 'bundle' ||
             option2 === 'bundle' ||
             option3 === 'bundle';
    });
  }

  safeStringClean(value) {
    // Safely convert any value to a clean lowercase string
    if (value === null || value === undefined) return '';
    if (typeof value === 'string') return value.toLowerCase().trim();
    if (typeof value === 'number') return String(value).toLowerCase().trim();
    return String(value).toLowerCase().trim();
  }

  updateProductDisplay(productElement, product, bundleVariant) {
    const regularPriceEl = productElement.querySelector('.bundle-product__price-regular');
    const bundlePriceEl = productElement.querySelector('.bundle-product__price-bundle');

    if (bundleVariant) {
      // Show bundle price
      bundlePriceEl.textContent = this.formatMoney(bundleVariant.price);
      bundlePriceEl.style.display = 'inline';
      
      // If bundle price is different from regular price, show both
      if (bundleVariant.price !== product.variants[0].price) {
        regularPriceEl.style.display = 'inline';
      } else {
        regularPriceEl.style.display = 'none';
      }
    } else {
      // No bundle variant available
      bundlePriceEl.textContent = 'Not available in bundle';
      bundlePriceEl.style.display = 'inline';
      bundlePriceEl.style.color = '#dc3545';
      regularPriceEl.style.display = 'none';
    }
  }

  updateBundleTotal() {
    let total = 0;
    let hasValidProducts = false;
    let unavailableCount = 0;

    this.bundleVariants.forEach((variant, handle) => {
      if (variant && variant.available && typeof variant.price === 'number') {
        total += variant.price;
        hasValidProducts = true;
      } else {
        unavailableCount++;
        console.warn(`Bundle variant unavailable or invalid: ${handle}`, variant);
      }
    });

    const totalElement = this.querySelector('.bundle-summary__price');
    if (totalElement) {
      totalElement.textContent = this.formatMoney(total);
    }

    // Update button state and show warnings if needed
    const button = this.querySelector('.bundle-add-to-cart');
    if (button && unavailableCount > 0) {
      const originalText = button.dataset.originalText || button.textContent;
      button.dataset.originalText = originalText;

      if (hasValidProducts) {
        button.textContent = `${originalText} (${unavailableCount} unavailable)`;
      } else {
        button.textContent = 'Bundle Unavailable';
      }
    }

    return hasValidProducts;
  }

  updateAddToCartButton() {
    const button = this.querySelector('.bundle-add-to-cart');
    if (!button) return;

    const hasValidProducts = this.updateBundleTotal();
    button.disabled = !hasValidProducts || this.isLoading;
  }

  async addBundleToCart() {
    if (this.isLoading || this.bundleVariants.size === 0) return;

    this.isLoading = true;
    this.updateLoadingState(true);

    try {
      // Generate unique bundle ID
      const bundleId = `bundle-${Date.now()}`;
      
      // Prepare cart items with bundle variants
      const items = [];
      const unavailableProducts = [];

      this.bundleVariants.forEach((variant, handle) => {
        if (variant.available && variant.id) {
          items.push({
            id: variant.id,
            quantity: 1,
            properties: {
              '_bundle_id': bundleId,
              '_bundle_type': 'hidden_variant',
              '_bundle_handle': handle
            }
          });
        } else {
          unavailableProducts.push(handle);
        }
      });

      if (items.length === 0) {
        if (unavailableProducts.length > 0) {
          throw new Error(`Bundle products not available: ${unavailableProducts.join(', ')}`);
        } else {
          throw new Error('No products found in bundle');
        }
      }

      if (unavailableProducts.length > 0) {
        console.warn(`Some bundle products are unavailable: ${unavailableProducts.join(', ')}`);
      }

      // Add all items to cart in a single request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      const response = await fetch('/cart/add.js', {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ items })
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = 'Failed to add bundle to cart';
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.description) {
            errorMessage = errorData.description;
          }
        } catch (parseError) {
          // If we can't parse the error response, use the status text
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // Validate the response
      const result = await response.json();
      if (!result || (Array.isArray(result.items) && result.items.length !== items.length)) {
        console.warn('Cart add response may be incomplete:', result);
      }

      // Success - trigger cart update events
      this.dispatchEvent(new CustomEvent('bundle:added', {
        bubbles: true,
        detail: { bundleId, items }
      }));

      // Trigger multiple cart update events for theme compatibility
      setTimeout(() => {
        document.dispatchEvent(new CustomEvent('cart:updated'));
        document.dispatchEvent(new CustomEvent('cart:change'));
        document.dispatchEvent(new CustomEvent('cartUpdated'));

        // Theme-specific events
        if (window.theme && window.theme.cartUpdated) {
          window.theme.cartUpdated();
        }

        // Trigger cart drawer refresh if available
        if (window.refreshCartDrawer) {
          window.refreshCartDrawer();
        }

        // Dawn theme compatibility
        if (window.cartDrawer && window.cartDrawer.renderContents) {
          window.cartDrawer.renderContents();
        }
      }, 100);

      this.showSuccess('Bundle added to cart successfully!');
      
    } catch (error) {
      console.error('Error adding bundle to cart:', error);
      this.showError(error.message || 'Failed to add bundle to cart. Please try again.');
    } finally {
      this.isLoading = false;
      this.updateLoadingState(false);
    }
  }

  updateLoadingState(loading) {
    const button = this.querySelector('.bundle-add-to-cart');
    if (button) {
      button.classList.toggle('loading', loading);
      button.disabled = loading;
    }

    this.querySelectorAll('.bundle-product').forEach(product => {
      product.classList.toggle('loading', loading);
    });
  }

  showSuccess(message) {
    // You can customize this to match your theme's notification system
    console.log('Success:', message);
    // Example: show a toast notification
    if (window.showNotification) {
      window.showNotification(message, 'success');
    }
  }

  showError(message) {
    // You can customize this to match your theme's notification system
    console.error('Error:', message);
    // Example: show a toast notification
    if (window.showNotification) {
      window.showNotification(message, 'error');
    }
  }

  formatMoney(cents) {
    // Use Shopify's money format if available, otherwise fallback to basic formatting
    if (window.Shopify && window.Shopify.formatMoney) {
      return window.Shopify.formatMoney(cents, window.theme?.moneyFormat || '£{{amount}}');
    }

    // Fallback to browser's Intl API
    try {
      // Try to detect currency from shop data
      const currency = window.shop?.currency || 'GBP';
      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: currency
      }).format(cents / 100);
    } catch (error) {
      // Ultimate fallback - simple formatting
      return '£' + (cents / 100).toFixed(2);
    }
  }
}

// Register the custom element with comprehensive error handling
function registerCustomElement() {
  try {
    // Check if already registered
    if (customElements.get('hidden-variant-bundles')) {
      console.log('hidden-variant-bundles already registered');
      return true;
    }

    // Register the element
    customElements.define('hidden-variant-bundles', HiddenVariantBundles);
    console.log('hidden-variant-bundles registered successfully');
    return true;
  } catch (error) {
    console.error('Failed to register hidden-variant-bundles custom element:', error);

    // If registration fails, try to use the element anyway with fallback
    if (error.name === 'NotSupportedError') {
      console.warn('Custom elements not supported, using fallback initialization');
      // Initialize existing elements manually
      document.querySelectorAll('hidden-variant-bundles').forEach(element => {
        if (!element._bundleInitialized) {
          element._bundleInitialized = true;
          const instance = new HiddenVariantBundles();
          Object.setPrototypeOf(element, HiddenVariantBundles.prototype);
          instance.init.call(element);
        }
      });
      return false;
    }

    return false;
  }
}

// Attempt registration
registerCustomElement();

// Export for potential external use
window.HiddenVariantBundles = HiddenVariantBundles;

// Initialize any existing elements on page load
document.addEventListener('DOMContentLoaded', () => {
  const existingElements = document.querySelectorAll('hidden-variant-bundles');
  existingElements.forEach(element => {
    if (!element.initialized) {
      element.initialized = true;
      // Force initialization if needed
      if (typeof element.init === 'function') {
        element.init();
      }
    }
  });
});

// Also handle dynamic content loading
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeElements);
} else {
  initializeElements();
}

function initializeElements() {
  // Handle Shopify section reloads
  document.addEventListener('shopify:section:load', (event) => {
    const bundleElements = event.target.querySelectorAll('hidden-variant-bundles');
    bundleElements.forEach(element => {
      if (!element.initialized) {
        element.initialized = true;
      }
    });
  });
}
