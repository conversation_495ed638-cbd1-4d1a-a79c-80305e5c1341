{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }

    .logo-list-{{ section.id }} .logo-list__item svg, .logo-list-{{ section.id }} .logo-list__item, .logo-list-{{ section.id }} .logo-list__item img {
      height: {{ section.settings.desktop_logo_height }}px;
    }
  }

  @media screen and (max-width: 749px) {
    .logo-list-{{ section.id }} .logo-list__item svg, .logo-list-{{ section.id }} .logo-list__item, .logo-list-{{ section.id }} .logo-list__item img {
      height: {{ section.settings.mobile_logo_height }}px;
    }
  }

  .color-scheme-{{ section.id }}.color-custom {
    --color-background: {{ section.settings.custom_colors_background.red }}, {{ section.settings.custom_colors_background.green }}, {{ section.settings.custom_colors_background.blue }};
    --gradient-background: {% if section.settings.custom_gradient_background != blank %}{{ section.settings.custom_gradient_background }}{% else %}{{ section.settings.custom_colors_background }}{% endif %};
    {% if section.settings.custom_image_background != blank %}
      {% # theme-check-disable %}
      --gradient-background: url('{{ section.settings.custom_image_background | img_url: 'master' }}') center center / cover no-repeat;
      {% # theme-check-enable %}
    {% endif %}
    --color-foreground: {{ section.settings.custom_colors_text.red }}, {{ section.settings.custom_colors_text.green }}, {{ section.settings.custom_colors_text.blue }};
  }
  {% if section.settings.custom_mobile_image_background != blank %}
    @media screen and (max-width: 740px) {
      .color-scheme-{{ section.id }}.color-custom {
        {% # theme-check-disable %}
        --gradient-background: url('{{ section.settings.custom_mobile_image_background | img_url: 'master' }}') center center / cover no-repeat;
        {% # theme-check-enable %}
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="color-scheme-{{ section.id }} color-{{ section.settings.color_scheme }} gradient content-for-grouping animate-section animate--hidden {{ section.settings.visibility }}">
  {% if section.settings.display_id %}
    <copy-button class='section-id-btn button' data-content="#shopify-section-{{ section.id }}" data-success="false">
      <span class="copy-text">Click to copy section ID</span>
      <span class="copy-success">ID copied successfully!</span>
    </copy-button>
  {% endif %}
  <div class="logo-list-{{ section.id }} page-width section-{{ section.id }}-padding">
    <div class="logo-list-container logo-list-container-{{ section.settings.layout }}" style='--columns-mobile:{{ section.settings.columns_mobile }};'>
      {% assign content_index = 0 %}
      {% if section.settings.title != blank %}
        {% assign content_index = 1 %}
        <div class="logo-list__heading animate-item animate-item--child index-0">
          <h2 class="title {{ section.settings.heading_size }} title-with-highlight" style='--hightlight-color:{{ section.settings.title_highlight_color }}'>
            {{ section.settings.title }}
          </h2>
        </div>
      {% endif %}
      {% if section.settings.mobile_slider %}
        <splide-component
          data-type='loop'
          data-autoplay='{{ section.settings.autoplay }}'
          data-autoplay-speed='{{ section.settings.autoplay_speed }}'
          data-slides-mobile="{{ section.settings.columns_mobile }}"
          data-gap-mobile='0px'
          data-arrows='false'
          data-pagination='{{ section.settings.show_dots }}'
          data-destroy-desktop="true"
        >
      {% endif %}
        <div class="splide splide--mobile-dots-under desktop-destroy--flex">
          <div class="splide__track">
            <ul class="splide__list{% if section.settings.mobile_slider == false %} mobile-logo-list--grid{% endif %}">
              {% for block in section.blocks %}
                <li class="splide__slide">
                  <div class="splide__slide__container">
                    <{% if block.settings.link != blank %}a href="{{ block.settings.link }}" {% if block.settings.target_blank %}target="_blank" rel="noreferrer"{% endif %} {% else %}div{% endif %} class="logo-list__item{% if section.settings.gray_logos %} logo-list__item--gray{% endif %} animate-item animate-item--child" style="--index:{{ forloop.index0 | plus: content_index }};">
                      {% if block.settings.image != blank %}
                        <img
                          src="{{ block.settings.image | image_url }}"
                          {% if block.settings.image.alt != blank %}
                            alt="{{ block.settings.image.alt | escape }}"
                          {% else %}
                            role="presentation"
                          {% endif %}
                          height="auto"
                          width="auto"
                          loading="lazy"
                        >
                      {% else %}
                        {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                      {% endif %}
                    </{%- if block.settings.link != blank -%}a{% else %}div{%- endif -%}>
                  </div>
                </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      {% if section.settings.mobile_slider %}
        </splide-component>
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Featured on",
  "settings": [
    {
      "type": "checkbox",
      "id": "display_id",
      "label": "Display section ID",
      "info": "ID is used in the Sections group section to merge 2 sections. ID can also be put inside any button link and the button will scroll to this section.",
      "default": false
    },
    {
      "type": "select",
      "id": "visibility",
      "options": [
        {
          "value": "desktop-hidden",
          "label": "Mobile only"
        },
        {
          "value": "mobile-hidden",
          "label": "Desktop only"
        },
        {
          "value": "always-display",
          "label": "All devices"
        }
      ],
      "label": "Display on",
      "default": "always-display"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Featured on",
      "label": "Heading",
      "info": "Bold certain words to highlight them with a different color."
    },
    {
      "type": "color",
      "id": "title_highlight_color",
      "label": "Heading highlight color",
      "default": "#6D388B"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "checkbox",
      "id": "gray_logos",
      "label": "Gray logos",
      "default": false
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "background-1",
      "label": "Color scheme",
      "info": "Custom color scheme is edited at the bottom of section settings."
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "vertical",
          "label": "Vertical"
        },
        {
          "value": "horizontal",
          "label": "Horizontal"
        }
      ],
      "default": "horizontal",
      "label": "Desktop layout"
    },
    { 
      "type": "range",
      "id": "desktop_logo_height",
      "min": 30,
      "max": 150,
      "step": 10,
      "default": 50,
      "label": "Desktop logo height"
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2,
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
      "type": "range",
      "id": "mobile_logo_height",
      "min": 30,
      "max": 150,
      "step": 10,
      "default": 50,
      "label": "Mobile logo height"
    },
    {
      "type": "paragraph",
      "content": "ATTENTION: ONLY in the theme editor, pagination dots might duplicate after changing section settings. To overcome this, simply click Save. This has NO EFFECT on the published website."
    },
    {
      "type": "checkbox",
      "id": "mobile_slider",
      "default": true,
      "label": "Enable mobile slider"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 1,
      "max": 15,
      "step": 0.5,
      "default": 5,
      "unit": "sec",
      "label": "Autoplay speed"
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show dots",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom color scheme",
      "info": "Applied if Color scheme setting is set to Custom."
    },
    {
      "type": "color",
      "id": "custom_colors_background",
      "default": "#FFFFFF",
      "label": "Background color"
    },
    {
      "type": "color_background",
      "id": "custom_gradient_background",
      "label": "Gradient background",
      "info": "If added, replaces Background color when applicable."
    },
    {
      "type": "image_picker",
      "id": "custom_image_background",
      "label": "Image background"
    },
    {
      "type": "image_picker",
      "id": "custom_mobile_image_background",
      "label": "Mobile image background",
      "info": "If empty, the Image background will also be applied to mobile devices"
    },
    {
      "type": "color",
      "id": "custom_colors_text",
      "default": "#121212",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "logo",
      "name": "Logo",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Logo"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "checkbox",
          "id": "target_blank",
          "label": "Open link in a new tab",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Featured on",
      "blocks": [
        {
          "type": "logo"
        },
        {
          "type": "logo"
        },
        {
          "type": "logo"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}

